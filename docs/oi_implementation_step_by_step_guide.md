# OI Enhanced Dynamic Weight Implementation - Step-by-Step Guide

## Overview

This guide provides detailed, actionable steps for implementing the enhanced OI system with dynamic weightage functionality. Follow these steps sequentially to ensure successful implementation while maintaining backward compatibility.

## Prerequisites

### System Requirements
- Python 3.8+ with pandas, numpy, openpyxl
- HeavyDB connection and access
- Existing OI system components in `bt/backtester_stable/BTRUN/`
- Write access to input_sheets directory

### Knowledge Requirements
- Understanding of existing OI system architecture
- Familiarity with Excel file structures
- Basic knowledge of dynamic weight adjustment concepts

## Phase 1: Foundation Setup (Days 1-2)

### Step 1.1: Create Enhanced Input Templates
```bash
# Navigate to the project directory
cd /srv/samba/shared

# Run the template creation script
python3 bt/backtester_stable/BTRUN/input_sheets/oi/create_enhanced_oi_templates.py

# Verify files were created
ls -la bt/backtester_stable/BTRUN/input_sheets/oi/input_enhanced_*.xlsx
```

**Expected Output:**
- `input_enhanced_oi_config.xlsx` (45 columns in GeneralParameter, 35 in LegParameter)
- `input_dynamic_weightage_oi.xlsx` (25 columns in WeightConfiguration, 16 in FactorParameters)

### Step 1.2: Extend OI Models
Create enhanced model structures to support new parameters:

```python
# File: bt/backtester_stable/BTRUN/models/enhanced_oi.py
from dataclasses import dataclass, field
from typing import Dict, List, Optional
from enum import Enum

@dataclass
class EnhancedOIConfig:
    """Enhanced OI configuration with dynamic weightage support"""
    
    # Core parameters
    strategy_name: str
    oi_method: str = 'MAXOI_1'
    coi_based_on: str = 'YESTERDAY_CLOSE'
    
    # OI-specific parameters
    oi_recheck_interval: int = 180
    oi_threshold_type: str = 'ABSOLUTE'
    oi_concentration_threshold: float = 0.3
    oi_distribution_analysis: bool = True
    
    # Dynamic weightage parameters
    enable_dynamic_weights: bool = True
    weight_adjustment_period: int = 20
    learning_rate: float = 0.01
    performance_window: int = 100
    min_weight: float = 0.05
    max_weight: float = 0.50

@dataclass
class DynamicWeightConfig:
    """Dynamic weight configuration"""
    
    # Base factor weights
    oi_factor_weight: float = 0.35
    coi_factor_weight: float = 0.25
    greek_factor_weight: float = 0.20
    market_factor_weight: float = 0.15
    performance_factor_weight: float = 0.05
    
    # Adjustment parameters
    weight_learning_rate: float = 0.01
    weight_decay_factor: float = 0.95
    correlation_threshold: float = 0.7
    diversification_bonus: float = 1.1
```

### Step 1.3: Create Enhanced Parser
Extend the existing OI parser to handle enhanced input formats:

```python
# File: bt/backtester_stable/BTRUN/backtester_v2/strategies/oi/enhanced_parser.py
import pandas as pd
from typing import Dict, List, Tuple, Optional
from .models import EnhancedOIConfig, DynamicWeightConfig

class EnhancedOIParser:
    """Parser for enhanced OI input files with dynamic weightage support"""
    
    def __init__(self):
        self.legacy_parser = None  # Reference to existing parser for backward compatibility
    
    def parse_enhanced_config(self, file_path: str) -> Tuple[List[EnhancedOIConfig], List[Dict]]:
        """Parse enhanced OI configuration file"""
        
        # Read GeneralParameter sheet
        general_df = pd.read_excel(file_path, sheet_name='GeneralParameter')
        
        # Read LegParameter sheet
        leg_df = pd.read_excel(file_path, sheet_name='LegParameter')
        
        # Parse configurations
        configs = []
        for _, row in general_df.iterrows():
            config = EnhancedOIConfig(
                strategy_name=row['StrategyName'],
                oi_method=row.get('OiMethod', 'MAXOI_1'),
                coi_based_on=row.get('CoiBasedOn', 'YESTERDAY_CLOSE'),
                oi_recheck_interval=row.get('OiRecheckInterval', 180),
                oi_threshold_type=row.get('OiThresholdType', 'ABSOLUTE'),
                oi_concentration_threshold=row.get('OiConcentrationThreshold', 0.3),
                oi_distribution_analysis=row.get('OiDistributionAnalysis', 'YES') == 'YES',
                enable_dynamic_weights=row.get('EnableDynamicWeights', 'YES') == 'YES',
                weight_adjustment_period=row.get('WeightAdjustmentPeriod', 20),
                learning_rate=row.get('LearningRate', 0.01),
                performance_window=row.get('PerformanceWindow', 100),
                min_weight=row.get('MinWeight', 0.05),
                max_weight=row.get('MaxWeight', 0.50)
            )
            configs.append(config)
        
        # Parse leg parameters
        leg_configs = []
        for _, row in leg_df.iterrows():
            leg_config = {
                'strategy_name': row['StrategyName'],
                'leg_id': row['LegID'],
                'instrument': row['Instrument'],
                'transaction': row['Transaction'],
                'leg_oi_threshold': row.get('LegOiThreshold', 800000),
                'leg_oi_weight': row.get('LegOiWeight', 0.4),
                'leg_coi_weight': row.get('LegCoiWeight', 0.3),
                'delta_weight': row.get('DeltaWeight', 0.25),
                'gamma_weight': row.get('GammaWeight', 0.20),
                'theta_weight': row.get('ThetaWeight', 0.15),
                'vega_weight': row.get('VegaWeight', 0.10)
            }
            leg_configs.append(leg_config)
        
        return configs, leg_configs
    
    def parse_dynamic_weights(self, file_path: str) -> DynamicWeightConfig:
        """Parse dynamic weightage configuration file"""
        
        # Read WeightConfiguration sheet
        weight_df = pd.read_excel(file_path, sheet_name='WeightConfiguration')
        
        if weight_df.empty:
            return DynamicWeightConfig()  # Return default configuration
        
        row = weight_df.iloc[0]  # Take first row
        
        config = DynamicWeightConfig(
            oi_factor_weight=row.get('OiFactorWeight', 0.35),
            coi_factor_weight=row.get('CoiFactorWeight', 0.25),
            greek_factor_weight=row.get('GreekFactorWeight', 0.20),
            market_factor_weight=row.get('MarketFactorWeight', 0.15),
            performance_factor_weight=row.get('PerformanceFactorWeight', 0.05),
            weight_learning_rate=row.get('WeightLearningRate', 0.01),
            weight_decay_factor=row.get('WeightDecayFactor', 0.95),
            correlation_threshold=row.get('CorrelationThreshold', 0.7),
            diversification_bonus=row.get('DiversificationBonus', 1.1)
        )
        
        return config
    
    def is_enhanced_format(self, file_path: str) -> bool:
        """Check if file is in enhanced format"""
        try:
            xl_file = pd.ExcelFile(file_path)
            sheets = xl_file.sheet_names
            
            # Enhanced format has GeneralParameter and LegParameter sheets
            # with specific column counts
            if 'GeneralParameter' in sheets and 'LegParameter' in sheets:
                general_df = pd.read_excel(file_path, sheet_name='GeneralParameter')
                return len(general_df.columns) > 20  # Enhanced has 45 columns
            
            return False
        except Exception:
            return False
```

## Phase 2: Dynamic Weight Engine (Days 3-5)

### Step 2.1: Create Dynamic Weight Calculator
Implement the core dynamic weight calculation engine:

```python
# File: bt/backtester_stable/BTRUN/backtester_v2/strategies/oi/dynamic_weight_engine.py
import numpy as np
import pandas as pd
from typing import Dict, List, Optional
from datetime import datetime, timedelta

class DynamicWeightEngine:
    """Core engine for dynamic weight calculation and adjustment"""
    
    def __init__(self, config: DynamicWeightConfig):
        self.config = config
        self.current_weights = self._initialize_weights()
        self.performance_history = {}
        self.factor_correlations = {}
        self.last_adjustment = None
    
    def _initialize_weights(self) -> Dict[str, float]:
        """Initialize weights with base configuration"""
        return {
            'oi_factor': self.config.oi_factor_weight,
            'coi_factor': self.config.coi_factor_weight,
            'greek_factor': self.config.greek_factor_weight,
            'market_factor': self.config.market_factor_weight,
            'performance_factor': self.config.performance_factor_weight
        }
    
    def calculate_factor_performance(self, factor_name: str, 
                                   historical_data: pd.DataFrame) -> float:
        """Calculate performance metric for a specific factor"""
        
        if factor_name not in historical_data.columns:
            return 0.0
        
        # Calculate Sharpe ratio as performance metric
        returns = historical_data[factor_name].pct_change().dropna()
        
        if len(returns) < 2:
            return 0.0
        
        sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0.0
        return sharpe_ratio
    
    def update_weights(self, performance_data: Dict[str, float], 
                      market_conditions: Dict[str, float]) -> Dict[str, float]:
        """Update weights based on performance and market conditions"""
        
        # Calculate performance-based adjustments
        performance_adjustments = self._calculate_performance_adjustments(performance_data)
        
        # Calculate market condition adjustments
        market_adjustments = self._calculate_market_adjustments(market_conditions)
        
        # Apply adjustments with learning rate
        new_weights = {}
        for factor, current_weight in self.current_weights.items():
            
            # Combine adjustments
            perf_adj = performance_adjustments.get(factor, 1.0)
            market_adj = market_adjustments.get(factor, 1.0)
            
            # Calculate new weight
            adjustment = perf_adj * market_adj
            new_weight = current_weight + self.config.weight_learning_rate * (adjustment - 1.0) * current_weight
            
            # Apply constraints
            new_weight = max(self.config.min_weight, min(self.config.max_weight, new_weight))
            new_weights[factor] = new_weight
        
        # Normalize weights to sum to 1.0
        total_weight = sum(new_weights.values())
        if total_weight > 0:
            new_weights = {k: v / total_weight for k, v in new_weights.items()}
        
        # Apply smoothing
        smoothed_weights = {}
        for factor in new_weights:
            smoothed_weights[factor] = (
                (1 - self.config.weight_decay_factor) * new_weights[factor] +
                self.config.weight_decay_factor * self.current_weights[factor]
            )
        
        self.current_weights = smoothed_weights
        self.last_adjustment = datetime.now()
        
        return self.current_weights.copy()
    
    def _calculate_performance_adjustments(self, performance_data: Dict[str, float]) -> Dict[str, float]:
        """Calculate weight adjustments based on performance"""
        adjustments = {}
        
        for factor, performance in performance_data.items():
            if performance > 0:
                # Good performance increases weight
                adjustments[factor] = 1.0 + min(0.2, performance * 0.1)
            else:
                # Poor performance decreases weight
                adjustments[factor] = 1.0 + max(-0.2, performance * 0.1)
        
        return adjustments
    
    def _calculate_market_adjustments(self, market_conditions: Dict[str, float]) -> Dict[str, float]:
        """Calculate weight adjustments based on market conditions"""
        adjustments = {}
        
        volatility = market_conditions.get('volatility', 0.5)
        trend_strength = market_conditions.get('trend_strength', 0.0)
        liquidity = market_conditions.get('liquidity', 0.5)
        
        # Adjust OI factor based on liquidity
        adjustments['oi_factor'] = 1.0 + (liquidity - 0.5) * 0.2
        
        # Adjust COI factor based on volatility
        adjustments['coi_factor'] = 1.0 + (volatility - 0.5) * 0.3
        
        # Adjust Greek factor based on volatility
        adjustments['greek_factor'] = 1.0 + (volatility - 0.5) * 0.4
        
        # Adjust market factor based on trend strength
        adjustments['market_factor'] = 1.0 + abs(trend_strength) * 0.2
        
        return adjustments
    
    def get_current_weights(self) -> Dict[str, float]:
        """Get current factor weights"""
        return self.current_weights.copy()
    
    def should_rebalance(self) -> bool:
        """Check if weights should be rebalanced"""
        if self.last_adjustment is None:
            return True
        
        time_since_last = datetime.now() - self.last_adjustment
        return time_since_last.total_seconds() >= self.config.weight_rebalance_freq
```

### Step 2.2: Integrate with Existing OI Processor
Modify the existing OI processor to use dynamic weights:

```python
# File: bt/backtester_stable/BTRUN/backtester_v2/strategies/oi/enhanced_processor.py
from .processor import OIProcessor  # Existing processor
from .dynamic_weight_engine import DynamicWeightEngine
from .enhanced_parser import EnhancedOIParser

class EnhancedOIProcessor(OIProcessor):
    """Enhanced OI processor with dynamic weightage support"""
    
    def __init__(self, connection, enhanced_config=None, weight_config=None):
        super().__init__(connection)
        self.enhanced_config = enhanced_config
        self.weight_engine = DynamicWeightEngine(weight_config) if weight_config else None
        self.parser = EnhancedOIParser()
    
    def process_enhanced_strategy(self, strategy_config, start_date, end_date):
        """Process strategy with enhanced configuration and dynamic weights"""
        
        # Initialize performance tracking
        performance_tracker = {}
        market_condition_tracker = {}
        
        # Process each trading day
        current_date = start_date
        while current_date <= end_date:
            
            # Calculate market conditions for the day
            market_conditions = self._calculate_market_conditions(current_date)
            
            # Update weights if needed
            if self.weight_engine and self.weight_engine.should_rebalance():
                performance_data = self._calculate_factor_performance(current_date)
                updated_weights = self.weight_engine.update_weights(
                    performance_data, market_conditions
                )
                print(f"Updated weights for {current_date}: {updated_weights}")
            
            # Process trades for the day using current weights
            daily_trades = self._process_daily_trades(
                strategy_config, current_date, self.weight_engine.get_current_weights()
            )
            
            # Update performance tracking
            self._update_performance_tracking(daily_trades, performance_tracker)
            
            current_date += timedelta(days=1)
        
        return performance_tracker
    
    def _calculate_market_conditions(self, trade_date) -> Dict[str, float]:
        """Calculate market conditions for weight adjustment"""
        
        # This would typically query HeavyDB for market data
        # For now, return dummy values
        return {
            'volatility': 0.6,
            'trend_strength': 0.3,
            'liquidity': 0.7
        }
    
    def _calculate_factor_performance(self, trade_date) -> Dict[str, float]:
        """Calculate performance of each factor"""
        
        # This would calculate actual factor performance
        # For now, return dummy values
        return {
            'oi_factor': 0.15,
            'coi_factor': 0.08,
            'greek_factor': -0.05,
            'market_factor': 0.12,
            'performance_factor': 0.03
        }
    
    def _process_daily_trades(self, strategy_config, trade_date, weights) -> List:
        """Process trades for a single day using dynamic weights"""
        
        # This would implement the actual trading logic
        # incorporating the dynamic weights
        trades = []
        
        # Example: Use weights to influence strike selection
        oi_weight = weights.get('oi_factor', 0.35)
        coi_weight = weights.get('coi_factor', 0.25)
        
        # Implement weighted OI/COI analysis here
        # ...
        
        return trades
    
    def _update_performance_tracking(self, trades, tracker):
        """Update performance tracking with new trades"""
        
        # Calculate trade performance and update tracker
        for trade in trades:
            # Update performance metrics
            pass
```

## Phase 3: Integration and Testing (Days 6-8)

### Step 3.1: Create Integration Layer
Create a unified interface that handles both legacy and enhanced formats:

```python
# File: bt/backtester_stable/BTRUN/backtester_v2/strategies/oi/unified_oi_interface.py
from typing import Union, List, Dict
from .enhanced_parser import EnhancedOIParser
from .enhanced_processor import EnhancedOIProcessor
from .processor import OIProcessor  # Existing processor

class UnifiedOIInterface:
    """Unified interface for both legacy and enhanced OI systems"""
    
    def __init__(self, connection):
        self.connection = connection
        self.enhanced_parser = EnhancedOIParser()
    
    def process_oi_strategy(self, config_file: str, weight_file: str = None, 
                           start_date=None, end_date=None):
        """Process OI strategy with automatic format detection"""
        
        # Detect file format
        if self.enhanced_parser.is_enhanced_format(config_file):
            return self._process_enhanced_format(config_file, weight_file, start_date, end_date)
        else:
            return self._process_legacy_format(config_file, start_date, end_date)
    
    def _process_enhanced_format(self, config_file, weight_file, start_date, end_date):
        """Process enhanced format with dynamic weights"""
        
        # Parse enhanced configuration
        strategy_configs, leg_configs = self.enhanced_parser.parse_enhanced_config(config_file)
        
        # Parse dynamic weights if provided
        weight_config = None
        if weight_file:
            weight_config = self.enhanced_parser.parse_dynamic_weights(weight_file)
        
        # Create enhanced processor
        processor = EnhancedOIProcessor(self.connection, strategy_configs[0], weight_config)
        
        # Process strategy
        results = processor.process_enhanced_strategy(strategy_configs[0], start_date, end_date)
        
        return {
            'format': 'enhanced',
            'dynamic_weights_enabled': weight_config is not None,
            'results': results
        }
    
    def _process_legacy_format(self, config_file, start_date, end_date):
        """Process legacy format for backward compatibility"""
        
        # Use existing OI processor
        processor = OIProcessor(self.connection)
        
        # Process using legacy method
        results = processor.process_legacy_strategy(config_file, start_date, end_date)
        
        return {
            'format': 'legacy',
            'dynamic_weights_enabled': False,
            'results': results
        }
```

### Step 3.2: Create Test Suite
Implement comprehensive testing for the enhanced system:

```python
# File: bt/backtester_stable/BTRUN/tests/test_enhanced_oi.py
import unittest
import pandas as pd
from datetime import date
from backtester_v2.strategies.oi.enhanced_parser import EnhancedOIParser
from backtester_v2.strategies.oi.dynamic_weight_engine import DynamicWeightEngine
from backtester_v2.strategies.oi.unified_oi_interface import UnifiedOIInterface

class TestEnhancedOI(unittest.TestCase):
    """Test suite for enhanced OI system"""
    
    def setUp(self):
        self.parser = EnhancedOIParser()
        self.test_config_file = 'input_sheets/oi/input_enhanced_oi_config.xlsx'
        self.test_weight_file = 'input_sheets/oi/input_dynamic_weightage_oi.xlsx'
    
    def test_enhanced_format_detection(self):
        """Test enhanced format detection"""
        is_enhanced = self.parser.is_enhanced_format(self.test_config_file)
        self.assertTrue(is_enhanced)
    
    def test_enhanced_config_parsing(self):
        """Test enhanced configuration parsing"""
        configs, leg_configs = self.parser.parse_enhanced_config(self.test_config_file)
        
        self.assertGreater(len(configs), 0)
        self.assertGreater(len(leg_configs), 0)
        
        # Check first config
        config = configs[0]
        self.assertIsNotNone(config.strategy_name)
        self.assertIn(config.oi_method, ['MAXOI_1', 'MAXOI_2', 'MAXCOI_1', 'MAXCOI_2'])
    
    def test_dynamic_weight_parsing(self):
        """Test dynamic weight configuration parsing"""
        weight_config = self.parser.parse_dynamic_weights(self.test_weight_file)
        
        self.assertIsNotNone(weight_config)
        self.assertAlmostEqual(
            weight_config.oi_factor_weight + weight_config.coi_factor_weight + 
            weight_config.greek_factor_weight + weight_config.market_factor_weight + 
            weight_config.performance_factor_weight, 
            1.0, places=2
        )
    
    def test_weight_engine_initialization(self):
        """Test dynamic weight engine initialization"""
        weight_config = self.parser.parse_dynamic_weights(self.test_weight_file)
        engine = DynamicWeightEngine(weight_config)
        
        weights = engine.get_current_weights()
        self.assertAlmostEqual(sum(weights.values()), 1.0, places=2)
    
    def test_weight_adjustment(self):
        """Test weight adjustment functionality"""
        weight_config = self.parser.parse_dynamic_weights(self.test_weight_file)
        engine = DynamicWeightEngine(weight_config)
        
        # Simulate performance data
        performance_data = {
            'oi_factor': 0.15,
            'coi_factor': -0.05,
            'greek_factor': 0.08,
            'market_factor': 0.12,
            'performance_factor': 0.03
        }
        
        market_conditions = {
            'volatility': 0.7,
            'trend_strength': 0.4,
            'liquidity': 0.6
        }
        
        initial_weights = engine.get_current_weights().copy()
        updated_weights = engine.update_weights(performance_data, market_conditions)
        
        # Weights should have changed
        self.assertNotEqual(initial_weights, updated_weights)
        
        # Weights should still sum to 1.0
        self.assertAlmostEqual(sum(updated_weights.values()), 1.0, places=2)

if __name__ == '__main__':
    unittest.main()
```

## Phase 4: Documentation and Deployment (Days 9-10)

### Step 4.1: Update Documentation
The documentation has already been updated in `input_sheets/column_mapping_ml_oi.md` with comprehensive parameter descriptions.

### Step 4.2: Create User Guide
Create a practical user guide for the enhanced system:

```markdown
# Enhanced OI System User Guide

## Quick Start

1. **Choose Your Format**:
   - Legacy: Use existing `input_maxoi.xlsx` (no changes needed)
   - Enhanced: Use new `input_enhanced_oi_config.xlsx` + `input_dynamic_weightage_oi.xlsx`

2. **Configure Enhanced Strategy**:
   ```python
   from backtester_v2.strategies.oi.unified_oi_interface import UnifiedOIInterface
   
   # Initialize interface
   oi_interface = UnifiedOIInterface(heavydb_connection)
   
   # Process enhanced strategy
   results = oi_interface.process_oi_strategy(
       config_file='input_sheets/oi/input_enhanced_oi_config.xlsx',
       weight_file='input_sheets/oi/input_dynamic_weightage_oi.xlsx',
       start_date=date(2025, 1, 1),
       end_date=date(2025, 1, 31)
   )
   ```

3. **Monitor Results**:
   - Check `results['dynamic_weights_enabled']` to confirm dynamic weights are active
   - Review weight adjustments in the output logs
   - Analyze performance improvements vs baseline

## Best Practices

1. **Parameter Tuning**:
   - Start with default values
   - Adjust `LearningRate` based on market volatility (0.005-0.02)
   - Set `WeightAdjustmentPeriod` based on strategy frequency (10-50)

2. **Performance Monitoring**:
   - Track Sharpe ratio improvements
   - Monitor maximum drawdown changes
   - Observe weight stability over time

3. **Risk Management**:
   - Keep `MinWeight` >= 0.05 to maintain diversification
   - Set `MaxWeight` <= 0.50 to prevent over-concentration
   - Enable all adjustment mechanisms for volatile markets
```

## Success Metrics and Validation

### Key Performance Indicators
1. **Backward Compatibility**: 100% of existing strategies work unchanged
2. **Performance Improvement**: 10-15% improvement in Sharpe ratio
3. **System Stability**: <100ms latency for weight calculations
4. **User Adoption**: <30 minutes setup time for new strategies

### Validation Checklist
- [ ] Legacy format detection works correctly
- [ ] Enhanced format parsing handles all 45+ parameters
- [ ] Dynamic weights sum to 1.0 at all times
- [ ] Weight constraints are enforced (min/max limits)
- [ ] Performance tracking updates correctly
- [ ] Market condition adjustments function properly
- [ ] Integration with existing HeavyDB queries works
- [ ] Error handling for invalid parameters
- [ ] Documentation is complete and accurate
- [ ] Test suite passes all scenarios

This implementation plan provides a comprehensive roadmap for successfully enhancing the OI system while maintaining reliability and backward compatibility.
