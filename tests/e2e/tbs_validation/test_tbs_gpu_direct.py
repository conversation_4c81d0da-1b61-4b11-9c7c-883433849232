#!/usr/bin/env python3
"""
Direct test of TBS GPU backtester with real HeavyDB data
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging
import heavydb

# Add paths
sys.path.append('/srv/samba/shared')
sys.path.append('/srv/samba/shared/bt/backtester_stable')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_heavydb_connection():
    """Test HeavyDB connection and data availability."""
    logger.info("Testing HeavyDB connection...")
    
    try:
        conn = heavydb.connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        cursor = conn.cursor()
        
        # Check table structure
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        logger.info(f"Available tables: {[t[0] for t in tables]}")
        
        # Check nifty_option_chain structure
        cursor.execute("SHOW CREATE TABLE nifty_option_chain")
        schema = cursor.fetchone()
        logger.info("Table structure verified")
        
        # Check data for test period
        query = """
        SELECT 
            COUNT(*) as total_rows,
            MIN(trade_date) as min_date,
            MAX(trade_date) as max_date,
            COUNT(DISTINCT expiry_date) as unique_expiries
        FROM nifty_option_chain
        WHERE trade_date >= '2024-04-01' 
        AND trade_date <= '2024-04-07'
        """
        cursor.execute(query)
        result = cursor.fetchone()
        
        logger.info(f"Test period data:")
        logger.info(f"  Total rows: {result[0]:,}")
        logger.info(f"  Date range: {result[1]} to {result[2]}")
        logger.info(f"  Unique expiries: {result[3]}")
        
        # Check ATM strike availability
        query = """
        SELECT 
            trade_date,
            COUNT(DISTINCT strike) as strikes,
            MIN(strike) as min_strike,
            MAX(strike) as max_strike
        FROM nifty_option_chain
        WHERE trade_date = '2024-04-01'
        AND expiry_date = '2024-04-04'  -- Current week expiry
        GROUP BY trade_date
        """
        cursor.execute(query)
        result = cursor.fetchone()
        
        if result:
            logger.info(f"Strike availability on 2024-04-01:")
            logger.info(f"  Strikes: {result[1]}")
            logger.info(f"  Range: {result[2]} to {result[3]}")
            
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"HeavyDB connection failed: {e}")
        return False


def run_simple_tbs_test():
    """Run a simple TBS backtest using HeavyDB."""
    logger.info("\n=== Running Simple TBS Test ===")
    
    try:
        from BTRunPortfolio import BTRunPortfolio
        
        # Create simple test files
        test_dir = '/srv/samba/shared/test_results/tbs_gpu_direct'
        os.makedirs(test_dir, exist_ok=True)
        
        # Portfolio settings
        portfolio_data = {
            'PortfolioSetting': [{
                'StartDate': '01_04_2024',
                'EndDate': '01_04_2024',  # Single day test
                'IsTickBT': 'yes',
                'Enabled': 'yes',
                'PortfolioName': 'GPU_Direct_Test',
                'DataSource': 'DB',
                'DBConnectionString': 'heavydb://admin:HyperInteractive@localhost:6274/heavyai',
                'DBTableName': 'nifty_option_chain'
            }],
            'StrategySetting': [{
                'StrategyName': 'TEST_TBS',
                'StrategyType': 'TBS',
                'RunTogether': 'NO',
                'Amount': 10000
            }]
        }
        
        portfolio_file = os.path.join(test_dir, 'portfolio.xlsx')
        with pd.ExcelWriter(portfolio_file) as writer:
            pd.DataFrame(portfolio_data['PortfolioSetting']).to_excel(
                writer, sheet_name='PortfolioSetting', index=False
            )
            pd.DataFrame(portfolio_data['StrategySetting']).to_excel(
                writer, sheet_name='StrategySetting', index=False
            )
            
        # Strategy settings - simple ATM straddle
        strategy_data = {
            'GeneralParameter': [{
                'StrategyName': 'TEST_TBS',
                'Capital': 10000,
                'Enabled': 'yes',
                'ExpiryCriteria': 'CW',
                'MaxNoOfLegs': 2,
                'InstrumentName': 'NIFTY',
                'Symbol': 'NIFTY'
            }],
            'LegParameter': [
                {
                    'StrategyName': 'TEST_TBS',
                    'LegNo': 1,
                    'TransactionType': 'sell',
                    'OptionType': 'CE',
                    'StrikeMethod': 'ATM',
                    'Lots': 1
                },
                {
                    'StrategyName': 'TEST_TBS',
                    'LegNo': 2,
                    'TransactionType': 'sell',
                    'OptionType': 'PE',
                    'StrikeMethod': 'ATM',
                    'Lots': 1
                }
            ]
        }
        
        strategy_file = os.path.join(test_dir, 'strategy.xlsx')
        with pd.ExcelWriter(strategy_file) as writer:
            pd.DataFrame(strategy_data['GeneralParameter']).to_excel(
                writer, sheet_name='GeneralParameter', index=False
            )
            pd.DataFrame(strategy_data['LegParameter']).to_excel(
                writer, sheet_name='LegParameter', index=False
            )
            
        logger.info(f"Created test files in: {test_dir}")
        
        # Run backtest
        bt = BTRunPortfolio()
        result = bt.run(portfolio_file, strategy_file, test_dir)
        
        logger.info(f"Backtest result: {result}")
        
        # Check output
        import glob
        output_files = glob.glob(os.path.join(test_dir, '*.xlsx'))
        logger.info(f"Output files: {output_files}")
        
        return True
        
    except Exception as e:
        logger.error(f"TBS test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run direct GPU TBS test."""
    
    logger.info("=" * 80)
    logger.info("TBS GPU Direct Test")
    logger.info("=" * 80)
    
    # Test HeavyDB connection
    if not test_heavydb_connection():
        logger.error("Cannot proceed without HeavyDB")
        return
        
    # Run simple TBS test
    if run_simple_tbs_test():
        logger.info("\n✓ TBS GPU test successful")
    else:
        logger.info("\n✗ TBS GPU test failed")
        
    logger.info("\n" + "=" * 80)


if __name__ == "__main__":
    main()