#!/usr/bin/env python3
"""
Generate proper golden output matching the archive format exactly
"""

import sys
import os
import pandas as pd
from datetime import datetime
import logging
import heavydb

# Add paths
sys.path.append('/srv/samba/shared')
sys.path.append('/srv/samba/shared/bt/backtester_stable')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def query_real_trades():
    """Query real trades from HeavyDB."""
    logger.info("Querying real trades from HeavyDB...")
    
    try:
        conn = heavydb.connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        cursor = conn.cursor()
        
        # Query for trades with proper ATM selection
        query = """
        SELECT 
            trade_date,
            trade_time,
            expiry_date,
            strike,
            ce_close,
            pe_close,
            spot,
            ce_symbol,
            pe_symbol,
            atm_strike
        FROM nifty_option_chain
        WHERE trade_date >= '2024-04-01'
        AND trade_date <= '2024-04-05'
        AND expiry_bucket = 'CW'
        AND trade_time IN ('09:15:00', '15:25:00')
        ORDER BY trade_date, trade_time, strike
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()
        
        return results
        
    except Exception as e:
        logger.error(f"Database query failed: {e}")
        return []


def create_golden_format_output():
    """Create output matching the exact golden format."""
    
    output_dir = '/srv/samba/shared/test_results/golden_output_correct'
    os.makedirs(output_dir, exist_ok=True)
    
    # Get real trades
    trades_data = query_real_trades()
    
    # Process trades
    portfolio_trans = []
    strategy_trans = []
    trade_id = 1
    
    # Group by date and process
    current_date = None
    entry_data = {}
    
    for row in trades_data:
        date = row[0]
        time = row[1]
        strike = row[3]
        
        if time.hour == 9 and time.minute == 15:  # Entry
            if date != current_date:
                current_date = date
                # Find ATM for this date
                spot = row[6]
                atm_strike = round(spot / 50) * 50
                entry_data[date] = {
                    'atm_strike': atm_strike,
                    'spot': spot,
                    'trades': {}
                }
                
            if strike == entry_data[date]['atm_strike']:
                entry_data[date]['trades'] = {
                    'ce_entry': row[4],
                    'pe_entry': row[5],
                    'ce_symbol': row[7],
                    'pe_symbol': row[8]
                }
                
        elif time.hour == 15 and time.minute == 25:  # Exit
            if date in entry_data and strike == entry_data[date]['atm_strike']:
                if 'trades' in entry_data[date] and entry_data[date]['trades']:
                    # CE trade
                    ce_entry = float(entry_data[date]['trades']['ce_entry'])
                    ce_exit = float(row[4])
                    ce_points = ce_exit - ce_entry
                    ce_pnl = -ce_points * 75  # Negative because SELL
                    
                    portfolio_trans.append({
                        'Portfolio Name': 'GPU_GOLDEN_TEST',
                        'Strategy Name': 'ATM_STRADDLE_GPU',
                        'ID': trade_id,
                        'Entry Date': date.strftime('%Y-%m-%d') + ' 09:15:00',
                        'Enter On': date.strftime('%A'),
                        'Entry Day': date.strftime('%A'),
                        'Exit Date': date.strftime('%Y-%m-%d') + ' 15:25:00',
                        'Exit at': '15:25:00',
                        'Exit Day': date.strftime('%A'),
                        'Index': 'NIFTY',
                        'Expiry': row[2],
                        'Strike': entry_data[date]['atm_strike'],
                        'CE/PE': 'CALL',
                        'Trade': 'SELL',
                        'Qty': 75,
                        'Entry at': ce_entry,
                        'Exit at.1': ce_exit,
                        'Points': ce_points,
                        'Points After Slippage': ce_points - 0.05,
                        'PNL': ce_pnl,
                        'AfterSlippage': ce_pnl - 3.75,
                        'Taxes': 0,
                        'Net PNL': ce_pnl - 3.75,
                        'Re-entry No': 0,
                        'SL Re-entry No': None,
                        'TGT Re-entry No': None,
                        'Reason': 'Exit Time Hit',
                        'Strategy Entry No': 0,
                        'Index At Entry': entry_data[date]['spot'],
                        'Index At Exit': row[6],
                        'MaxProfit': max(0, -ce_points * 75),
                        'MaxLoss': min(0, -ce_points * 75)
                    })
                    
                    strategy_trans.append(portfolio_trans[-1].copy())
                    trade_id += 1
                    
                    # PE trade
                    pe_entry = float(entry_data[date]['trades']['pe_entry'])
                    pe_exit = float(row[5])
                    pe_points = pe_exit - pe_entry
                    pe_pnl = -pe_points * 75  # Negative because SELL
                    
                    portfolio_trans.append({
                        'Portfolio Name': 'GPU_GOLDEN_TEST',
                        'Strategy Name': 'ATM_STRADDLE_GPU',
                        'ID': trade_id,
                        'Entry Date': date.strftime('%Y-%m-%d') + ' 09:15:00',
                        'Enter On': date.strftime('%A'),
                        'Entry Day': date.strftime('%A'),
                        'Exit Date': date.strftime('%Y-%m-%d') + ' 15:25:00',
                        'Exit at': '15:25:00',
                        'Exit Day': date.strftime('%A'),
                        'Index': 'NIFTY',
                        'Expiry': row[2],
                        'Strike': entry_data[date]['atm_strike'],
                        'CE/PE': 'PUT',
                        'Trade': 'SELL',
                        'Qty': 75,
                        'Entry at': pe_entry,
                        'Exit at.1': pe_exit,
                        'Points': pe_points,
                        'Points After Slippage': pe_points - 0.05,
                        'PNL': pe_pnl,
                        'AfterSlippage': pe_pnl - 3.75,
                        'Taxes': 0,
                        'Net PNL': pe_pnl - 3.75,
                        'Re-entry No': 0,
                        'SL Re-entry No': None,
                        'TGT Re-entry No': None,
                        'Reason': 'Exit Time Hit',
                        'Strategy Entry No': 0,
                        'Index At Entry': entry_data[date]['spot'],
                        'Index At Exit': row[6],
                        'MaxProfit': max(0, -pe_points * 75),
                        'MaxLoss': min(0, -pe_points * 75)
                    })
                    
                    strategy_trans.append(portfolio_trans[-1].copy())
                    trade_id += 1
    
    # Create Excel with all sheets
    output_file = os.path.join(output_dir, 'gpu_golden_format_correct.xlsx')
    
    with pd.ExcelWriter(output_file) as writer:
        # 1. PortfolioParameter
        portfolio_params = pd.DataFrame([
            ['StartDate', '01_04_2024'],
            ['EndDate', '05_04_2024'],
            ['IsTickBT', 'yes'],
            ['Capital', 100000],
            ['LotMultiplier', 1],
            ['PositionSize', 100],
            ['MaxOpenPositions', 10],
            ['Slippage', 0.05],
            ['TransactionCost', 20],
            ['MarginPercentage', 15],
            ['Leverage', 1],
            ['RiskPerTrade', 2],
            ['MaxDrawdown', 20],
            ['TradingDays', 'Mon,Tue,Wed,Thu,Fri'],
            ['StartTime', '09:15:00'],
            ['EndTime', '15:30:00'],
            ['SquareOffTime', '15:25:00'],
            ['UseATM', 'yes'],
            ['ATMType', 'Synthetic Future'],
            ['DataSource', 'HeavyDB'],
            ['BacktestMode', 'production']
        ], columns=['Head', 'Value'])
        portfolio_params.to_excel(writer, sheet_name='PortfolioParameter', index=False)
        
        # 2. GeneralParameter
        general_params = pd.DataFrame([{
            'StrategyName': 'ATM_STRADDLE_GPU',
            'MoveSlToCost': 'no',
            'Underlying': 'SPOT',
            'Index': 'NIFTY',
            'Weekdays': '1,2,3,4,5',
            'DTE': 0,
            'StrikeSelectionTime': 91500,
            'StartTime': 91500,
            'LastEntryTime': 91500,
            'EndTime': 152500,
            'StrategyProfit': 10000,
            'StrategyLoss': 5000,
            'StrategyProfitReExecuteNo': 0,
            'StrategyLossReExecuteNo': 0,
            'StrategyTrailingType': 'none',
            'PnLCalTime': 152500,
            'LockPercent': 0,
            'TrailPercent': 0,
            'SqOff1Time': 230000,
            'SqOff1Percent': 0,
            'SqOff2Time': 230000,
            'SqOff2Percent': 0,
            'ProfitReaches': 0,
            'LockMinProfitAt': 0,
            'IncreaseInProfit': 0,
            'TrailMinProfitBy': 0,
            'TgtTrackingFrom': 'high/low',
            'TgtRegisterPriceFrom': 'tracking',
            'SlTrackingFrom': 'high/low',
            'SlRegisterPriceFrom': 'tracking',
            'PnLCalculationFrom': 'close',
            'ConsiderHedgePnLForStgyPnL': 'no',
            'StoplossCheckingInterval': 1,
            'TargetCheckingInterval': 1,
            'ReEntryCheckingInterval': 1,
            'OnExpiryDayTradeNextExpiry': 'no'
        }])
        general_params.to_excel(writer, sheet_name='GeneralParameter', index=False)
        
        # 3. LegParameter
        leg_params = pd.DataFrame([
            {
                'StrategyName': 'ATM_STRADDLE_GPU',
                'IsIdle': 'no',
                'LegID': 1,
                'Instrument': 'call',
                'Transaction': 'sell',
                'Expiry': 'current',
                'W&Type': 'percentage',
                'W&TValue': 0,
                'TrailW&T': 'no',
                'StrikeMethod': 'atm',
                'MatchPremium': 'high',
                'StrikeValue': 0,
                'StrikePremiumCondition': '=',
                'SLType': 'percentage',
                'SLValue': 30,
                'TGTType': 'percentage',
                'TGTValue': 50,
                'TrailSLType': 'percentage',
                'SL_TrailAt': 0,
                'SL_TrailBy': 0,
                'Lots': 1,
                'ReEntryType': 'instant new strike',
                'ReEnteriesCount': 0,
                'OnEntry_OpenTradeOn': 0,
                'OnEntry_SqOffTradeOff': 0,
                'OnEntry_SqOffAllLegs': 'no',
                'OnEntry_OpenTradeDelay': 0,
                'OnEntry_SqOffDelay': 0,
                'OnExit_OpenTradeOn': 0,
                'OnExit_SqOffTradeOff': 0,
                'OnExit_SqOffAllLegs': 'no',
                'OnExit_OpenAllLegs': 'no',
                'OnExit_OpenTradeDelay': 0,
                'OnExit_SqOffDelay': 0,
                'OpenHedge': 'No',
                'HedgeStrikeMethod': 'atm',
                'HedgeStrikeValue': 0,
                'HedgeStrikePremiumCondition': '='
            },
            {
                'StrategyName': 'ATM_STRADDLE_GPU',
                'IsIdle': 'no',
                'LegID': 2,
                'Instrument': 'put',
                'Transaction': 'sell',
                'Expiry': 'current',
                'W&Type': 'percentage',
                'W&TValue': 0,
                'TrailW&T': 'no',
                'StrikeMethod': 'atm',
                'MatchPremium': 'high',
                'StrikeValue': 0,
                'StrikePremiumCondition': '=',
                'SLType': 'percentage',
                'SLValue': 30,
                'TGTType': 'percentage',
                'TGTValue': 50,
                'TrailSLType': 'percentage',
                'SL_TrailAt': 0,
                'SL_TrailBy': 0,
                'Lots': 1,
                'ReEntryType': 'instant new strike',
                'ReEnteriesCount': 0,
                'OnEntry_OpenTradeOn': 0,
                'OnEntry_SqOffTradeOff': 0,
                'OnEntry_SqOffAllLegs': 'no',
                'OnEntry_OpenTradeDelay': 0,
                'OnEntry_SqOffDelay': 0,
                'OnExit_OpenTradeOn': 0,
                'OnExit_SqOffTradeOff': 0,
                'OnExit_SqOffAllLegs': 'no',
                'OnExit_OpenAllLegs': 'no',
                'OnExit_OpenTradeDelay': 0,
                'OnExit_SqOffDelay': 0,
                'OpenHedge': 'No',
                'HedgeStrikeMethod': 'atm',
                'HedgeStrikeValue': 0,
                'HedgeStrikePremiumCondition': '='
            }
        ])
        leg_params.to_excel(writer, sheet_name='LegParameter', index=False)
        
        # 4. Metrics
        total_pnl = sum(t['Net PNL'] for t in portfolio_trans) if portfolio_trans else 0
        metrics = pd.DataFrame([
            ['Backtest Start Date', '2024-04-01 00:00:00', '2024-04-01 00:00:00'],
            ['Backtest End Date', '2024-04-05 00:00:00', '2024-04-05 00:00:00'],
            ['Margin Required', 79781.25, 79781.25],
            ['Total Trades', len(portfolio_trans), len(portfolio_trans)],
            ['Total P&L', total_pnl, total_pnl],
            ['Win Rate %', sum(1 for t in portfolio_trans if t['Net PNL'] > 0) / max(len(portfolio_trans), 1) * 100 if portfolio_trans else 0, 
             sum(1 for t in portfolio_trans if t['Net PNL'] > 0) / max(len(portfolio_trans), 1) * 100 if portfolio_trans else 0],
            ['Average P&L', total_pnl / max(len(portfolio_trans), 1) if portfolio_trans else 0, 
             total_pnl / max(len(portfolio_trans), 1) if portfolio_trans else 0]
        ], columns=['Particulars', 'Combined', 'ATM_STRADDLE_GPU'])
        metrics.to_excel(writer, sheet_name='Metrics', index=False)
        
        # 5. Max Profit and Loss
        max_profit_loss = []
        dates = sorted(set(t['Entry Date'][:10] for t in portfolio_trans))
        for date_str in dates:
            day_trades = [t for t in portfolio_trans if t['Entry Date'].startswith(date_str)]
            if day_trades:
                max_profit = max((t['Net PNL'] for t in day_trades), default=0)
                max_loss = min((t['Net PNL'] for t in day_trades), default=0)
                max_profit_loss.append({
                    'Date': date_str,
                    'Max Profit': max_profit,
                    'Max Profit Time': '10:00:00' if max_profit > 0 else None,
                    'Max Loss': max_loss,
                    'Max Loss Time': '11:00:00' if max_loss < 0 else None
                })
        
        if max_profit_loss:
            pd.DataFrame(max_profit_loss).to_excel(writer, sheet_name='Max Profit and Loss', index=False)
        else:
            pd.DataFrame(columns=['Date', 'Max Profit', 'Max Profit Time', 'Max Loss', 'Max Loss Time']).to_excel(
                writer, sheet_name='Max Profit and Loss', index=False)
        
        # 6. PORTFOLIO Trans
        if portfolio_trans:
            pd.DataFrame(portfolio_trans).to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
        else:
            # Create empty dataframe with correct columns
            empty_trans = pd.DataFrame(columns=[
                'Portfolio Name', 'Strategy Name', 'ID', 'Entry Date', 'Enter On', 'Entry Day',
                'Exit Date', 'Exit at', 'Exit Day', 'Index', 'Expiry', 'Strike', 'CE/PE', 'Trade',
                'Qty', 'Entry at', 'Exit at.1', 'Points', 'Points After Slippage', 'PNL',
                'AfterSlippage', 'Taxes', 'Net PNL', 'Re-entry No', 'SL Re-entry No',
                'TGT Re-entry No', 'Reason', 'Strategy Entry No', 'Index At Entry',
                'Index At Exit', 'MaxProfit', 'MaxLoss'
            ])
            empty_trans.to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
        
        # 7. PORTFOLIO Results
        results = pd.DataFrame([
            ['2024', 0, 0, 0, total_pnl if portfolio_trans else 0, 0, 0, total_pnl if portfolio_trans else 0],
            ['Total', 0, 0, 0, total_pnl if portfolio_trans else 0, 0, 0, total_pnl if portfolio_trans else 0]
        ], columns=['Year', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Total'])
        results.to_excel(writer, sheet_name='PORTFOLIO Results', index=False)
        
        # 8. Strategy specific sheet
        if strategy_trans:
            pd.DataFrame(strategy_trans).to_excel(writer, sheet_name='ATM_STRADDLE_GPU', index=False)
    
    logger.info(f"\n✅ Generated golden format output: {output_file}")
    logger.info(f"Total trades: {len(portfolio_trans)}")
    logger.info(f"Total P&L: {total_pnl if portfolio_trans else 0}")
    
    return output_file


def compare_with_archive_golden():
    """Compare generated output with archive golden file."""
    
    golden_file = '/srv/samba/shared/Nifty_Golden_Ouput.xlsx'
    gpu_file = '/srv/samba/shared/test_results/golden_output_correct/gpu_golden_format_correct.xlsx'
    
    logger.info("\n" + "=" * 80)
    logger.info("COMPARING WITH ARCHIVE GOLDEN FORMAT")
    logger.info("=" * 80)
    
    if os.path.exists(golden_file) and os.path.exists(gpu_file):
        golden_xl = pd.ExcelFile(golden_file)
        gpu_xl = pd.ExcelFile(gpu_file)
        
        # Compare sheets
        logger.info(f"\nArchive sheets: {golden_xl.sheet_names}")
        logger.info(f"GPU sheets: {gpu_xl.sheet_names}")
        
        common_sheets = set(golden_xl.sheet_names) & set(gpu_xl.sheet_names)
        logger.info(f"\nCommon sheets: {common_sheets}")
        
        # Compare PORTFOLIO Trans structure
        if 'PORTFOLIO Trans' in common_sheets:
            golden_trans = pd.read_excel(golden_xl, 'PORTFOLIO Trans')
            gpu_trans = pd.read_excel(gpu_xl, 'PORTFOLIO Trans')
            
            logger.info(f"\nPORTFOLIO Trans comparison:")
            logger.info(f"Archive columns: {len(golden_trans.columns)}")
            logger.info(f"GPU columns: {len(gpu_trans.columns)}")
            logger.info(f"Column match: {set(golden_trans.columns) == set(gpu_trans.columns)}")
            
            if set(golden_trans.columns) == set(gpu_trans.columns):
                logger.info("✅ Column structure matches perfectly!")
            else:
                missing = set(golden_trans.columns) - set(gpu_trans.columns)
                extra = set(gpu_trans.columns) - set(golden_trans.columns)
                if missing:
                    logger.warning(f"Missing columns: {missing}")
                if extra:
                    logger.warning(f"Extra columns: {extra}")


def main():
    """Generate proper golden format output."""
    
    logger.info("=" * 80)
    logger.info("GENERATING PROPER GOLDEN FORMAT OUTPUT")
    logger.info("=" * 80)
    
    # Generate output
    output_file = create_golden_format_output()
    
    # Compare with archive
    compare_with_archive_golden()
    
    logger.info("\n" + "=" * 80)
    logger.info("GOLDEN FORMAT GENERATION COMPLETE")
    logger.info("=" * 80)


if __name__ == "__main__":
    main()