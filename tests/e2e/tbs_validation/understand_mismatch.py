#!/usr/bin/env python3
"""
Understand the mismatch between archive and GPU systems
"""

import pandas as pd
import os

def analyze_mismatch():
    """Analyze why there's a mismatch between systems."""
    
    print("=" * 80)
    print("UNDERSTANDING THE MISMATCH")
    print("=" * 80)
    
    # Load actual golden file
    golden_file = '/srv/samba/shared/Nifty_Golden_Ouput.xlsx'
    xl = pd.ExcelFile(golden_file)
    
    # Key findings from golden file
    print("\n1. GOLDEN FILE STRUCTURE (Archive System Output):")
    print("   - Has 9 sheets with comprehensive backtest results")
    print("   - Contains parameter sheets: PortfolioParameter, GeneralParameter, LegParameter")
    print("   - Contains result sheets: Metrics, Max Profit and Loss, PORTFOLIO Trans, PORTFOLIO Results")
    print("   - Has strategy-specific sheet")
    
    # Check PORTFOLIO Trans columns
    df_trans = pd.read_excel(xl, 'PORTFOLIO Trans')
    print("\n2. PORTFOLIO Trans COLUMNS IN GOLDEN FILE:")
    print(f"   Total columns: {len(df_trans.columns)}")
    print("   Key columns:")
    for col in ['Portfolio Name', 'Strategy Name', 'Entry Date', 'Exit Date', 'Entry at', 'Exit at.1', 'PNL', 'Net PNL']:
        print(f"   - {col}")
        
    # Notice the column differences
    print("\n3. KEY COLUMN DIFFERENCES:")
    print("   Archive System (Golden):")
    print("   - Uses 'Entry at' and 'Exit at.1' for prices")
    print("   - Has 'Exit at' for exit time")
    print("   - Has 32 columns with detailed information")
    print("   ")
    print("   GPU System (Current):")
    print("   - Uses 'Entry Price' and 'Exit Price'")
    print("   - Has only 13 columns")
    print("   - Missing many columns like Index At Entry, MaxProfit, MaxLoss, etc.")
    
    # Data format differences
    print("\n4. DATA FORMAT DIFFERENCES:")
    print("   Date Format:")
    print("   - Golden: '2025-04-03' (datetime)")
    print("   - GPU: '01_04_2024' (string)")
    print("   ")
    print("   Symbol Format:")
    print("   - Golden: Strike + CE/PE columns separately")
    print("   - GPU: Combined symbol like 'NIFTY04APR2422500CE'")
    
    # Missing sheets
    print("\n5. MISSING SHEETS IN GPU OUTPUT:")
    missing_sheets = set(xl.sheet_names) - {'PORTFOLIO Trans', 'Summary'}
    for sheet in missing_sheets:
        print(f"   - {sheet}")
        
    print("\n6. ROOT CAUSE:")
    print("   The GPU system is producing a simplified output format")
    print("   The Archive system produces the complete golden format with all sheets")
    print("   Need to ensure GPU system generates all required sheets and columns")
    
    # Check if archive system is actually using this format
    print("\n7. VERIFICATION:")
    sample_trans = df_trans[['Entry at', 'Exit at.1', 'PNL', 'Points']].head(3)
    print("   Sample data from golden file:")
    print(sample_trans.to_string(index=False))
    
    print("\n" + "=" * 80)
    print("CONCLUSION: GPU system needs to match the archive golden format exactly")
    print("=" * 80)

if __name__ == "__main__":
    analyze_mismatch()