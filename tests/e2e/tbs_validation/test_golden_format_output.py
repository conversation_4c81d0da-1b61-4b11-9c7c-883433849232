#!/usr/bin/env python3
"""
Test the GPU system with golden format output enabled
"""

import sys
import os
import pandas as pd
import subprocess
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_input_files():
    """Create test input files for GPU backtest."""
    
    test_dir = '/srv/samba/shared/test_results/golden_format_test'
    os.makedirs(test_dir, exist_ok=True)
    
    # Portfolio file
    portfolio_data = {
        'PortfolioSetting': [{
            'StartDate': '01_04_2024',
            'EndDate': '05_04_2024',
            'IsTickBT': 'YES',
            'Enabled': 'YES',
            'PortfolioName': 'GOLDEN_FORMAT_TEST',
            'PortfolioTarget': 50000,
            'PortfolioStoploss': 20000,
            'PortfolioTrailingType': 'trail profits',
            'PnLCalTime': 152500,
            'LockPercent': 50,
            'TrailPercent': 10,
            'Multiplier': 1.0,
            'SlippagePercent': 0.05
        }],
        'StrategySetting': [{
            'Enabled': 'YES',
            'PortfolioName': 'GOLDEN_FORMAT_TEST',
            'StrategyType': 'TBS',
            'StrategyExcelFilePath': 'test_strategy.xlsx'
        }]
    }
    
    portfolio_file = os.path.join(test_dir, 'test_portfolio.xlsx')
    with pd.ExcelWriter(portfolio_file) as writer:
        pd.DataFrame(portfolio_data['PortfolioSetting']).to_excel(
            writer, sheet_name='PortfolioSetting', index=False
        )
        pd.DataFrame(portfolio_data['StrategySetting']).to_excel(
            writer, sheet_name='StrategySetting', index=False
        )
    
    # Strategy file
    strategy_data = {
        'GeneralParameter': [{
            'StrategyName': 'ATM_STRADDLE',
            'Enabled': 'yes',
            'ExpiryCriteria': 'CW',
            'MaxNoOfLegs': 2,
            'HedgePositions': 'no',
            'ManageExpiredLegs': 'yes',
            'SquareOffOn': 'Time',
            'SquareOffTime': 152500,
            'PositionSizing': 'LotsSize',
            'LotsSize': 2,
            'StartTime': 91500,
            'EndTime': 152500,
            'Weekdays': '1,2,3,4,5',
            'InstrumentName': 'NIFTY',
            'StrikePriceNearBy': 50,
            'Symbol': 'NIFTY'
        }],
        'LegParameter': [
            {
                'StrategyName': 'ATM_STRADDLE',
                'LegNo': 1,
                'TransactionType': 'sell',
                'OptionType': 'CE',
                'StrikeMethod': 'ATM',
                'StrikeOffset': 0,
                'Lots': 2,
                'StopLoss': 30,
                'Target': 50,
                'SL_Type': 'percentage',
                'TGT_Type': 'percentage'
            },
            {
                'StrategyName': 'ATM_STRADDLE',
                'LegNo': 2,
                'TransactionType': 'sell',
                'OptionType': 'PE',
                'StrikeMethod': 'ATM',
                'StrikeOffset': 0,
                'Lots': 2,
                'StopLoss': 30,
                'Target': 50,
                'SL_Type': 'percentage',
                'TGT_Type': 'percentage'
            }
        ]
    }
    
    strategy_file = os.path.join(test_dir, 'test_strategy.xlsx')
    with pd.ExcelWriter(strategy_file) as writer:
        pd.DataFrame(strategy_data['GeneralParameter']).to_excel(
            writer, sheet_name='GeneralParameter', index=False
        )
        pd.DataFrame(strategy_data['LegParameter']).to_excel(
            writer, sheet_name='LegParameter', index=False
        )
    
    logger.info(f"Created test files in: {test_dir}")
    return test_dir, portfolio_file, strategy_file


def run_gpu_backtest_with_golden_format(portfolio_file, output_dir):
    """Run GPU backtest with golden format enabled."""
    
    logger.info("\n=== Running GPU Backtest with Golden Format ===")
    
    # Set environment variable to enable golden format
    env = os.environ.copy()
    env['USE_GOLDEN_FORMAT'] = 'true'
    env['PORTFOLIO_EXCEL'] = portfolio_file
    
    # Prepare output path
    output_path = os.path.join(output_dir, 'gpu_golden_output.xlsx')
    
    # Prepare command
    cmd = [
        'python3',
        '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py',
        '--legacy-excel',
        '--portfolio-excel', portfolio_file,
        '--output-path', output_path,
        '--output-dir', output_dir,
        '--debug'
    ]
    
    logger.info(f"Running command: {' '.join(cmd)}")
    logger.info("Environment: USE_GOLDEN_FORMAT=true")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd='/srv/samba/shared',
            env=env
        )
        
        if result.returncode == 0:
            logger.info("✓ GPU backtest completed successfully")
            
            # Check for output file
            if os.path.exists(output_path):
                logger.info(f"Output file generated: {output_path}")
                return output_path
            else:
                # Check for any output files
                import glob
                output_files = glob.glob(os.path.join(output_dir, '*.xlsx'))
                if output_files:
                    logger.info(f"Output files found: {output_files}")
                    return output_files[0]
                else:
                    logger.warning("No output files generated")
        else:
            logger.error(f"✗ GPU backtest failed: {result.stderr}")
            
    except Exception as e:
        logger.error(f"Error running GPU backtest: {e}")
        
    return None


def verify_golden_format(output_file):
    """Verify that output matches golden format."""
    
    logger.info("\n=== Verifying Golden Format ===")
    
    if not output_file or not os.path.exists(output_file):
        logger.error("Output file not found")
        return False
        
    # Load Excel file
    xl = pd.ExcelFile(output_file)
    
    # Expected sheets in golden format
    expected_sheets = [
        'PortfolioParameter',
        'GeneralParameter',
        'LegParameter',
        'Metrics',
        'Max Profit and Loss',
        'PORTFOLIO Trans',
        'PORTFOLIO Results'
    ]
    
    logger.info(f"Sheets in output: {xl.sheet_names}")
    
    # Check sheet presence
    missing_sheets = set(expected_sheets) - set(xl.sheet_names)
    if missing_sheets:
        logger.error(f"Missing sheets: {missing_sheets}")
        return False
    else:
        logger.info("✓ All required sheets present")
    
    # Check PORTFOLIO Trans columns
    trans_df = pd.read_excel(xl, 'PORTFOLIO Trans')
    expected_columns = [
        'Portfolio Name', 'Strategy Name', 'ID', 'Entry Date', 'Enter On', 'Entry Day',
        'Exit Date', 'Exit at', 'Exit Day', 'Index', 'Expiry', 'Strike', 'CE/PE', 'Trade',
        'Qty', 'Entry at', 'Exit at.1', 'Points', 'Points After Slippage', 'PNL',
        'AfterSlippage', 'Taxes', 'Net PNL', 'Re-entry No', 'SL Re-entry No',
        'TGT Re-entry No', 'Reason', 'Strategy Entry No', 'Index At Entry',
        'Index At Exit', 'MaxProfit', 'MaxLoss'
    ]
    
    missing_columns = set(expected_columns) - set(trans_df.columns)
    if missing_columns:
        logger.error(f"Missing columns in PORTFOLIO Trans: {missing_columns}")
        return False
    else:
        logger.info(f"✓ All {len(expected_columns)} columns present in PORTFOLIO Trans")
        
    # Check PortfolioParameter format
    param_df = pd.read_excel(xl, 'PortfolioParameter')
    if 'Head' in param_df.columns and 'Value' in param_df.columns:
        logger.info("✓ PortfolioParameter has correct format (Head/Value)")
    else:
        logger.error("✗ PortfolioParameter missing Head/Value columns")
        return False
        
    logger.info("\n✅ Golden format verification PASSED!")
    return True


def compare_with_archive_golden():
    """Compare generated output with archive golden file."""
    
    logger.info("\n=== Comparing with Archive Golden File ===")
    
    archive_golden = '/srv/samba/shared/Nifty_Golden_Ouput.xlsx'
    
    if os.path.exists(archive_golden):
        archive_xl = pd.ExcelFile(archive_golden)
        logger.info(f"Archive golden sheets: {archive_xl.sheet_names}")
        
        # Check PORTFOLIO Trans structure
        archive_trans = pd.read_excel(archive_xl, 'PORTFOLIO Trans')
        logger.info(f"Archive PORTFOLIO Trans columns: {len(archive_trans.columns)}")
        logger.info(f"Sample archive columns: {list(archive_trans.columns)[:5]}")
    else:
        logger.warning("Archive golden file not found")


def main():
    """Test GPU system with golden format output."""
    
    logger.info("=" * 80)
    logger.info("Testing GPU System with Golden Format Output")
    logger.info("=" * 80)
    
    # Create test files
    test_dir, portfolio_file, strategy_file = create_test_input_files()
    
    # Run GPU backtest
    output_file = run_gpu_backtest_with_golden_format(portfolio_file, test_dir)
    
    # Verify golden format
    if output_file:
        if verify_golden_format(output_file):
            logger.info("\n✅ GPU system successfully generates golden format!")
        else:
            logger.error("\n✗ Golden format verification failed")
    
    # Compare with archive
    compare_with_archive_golden()
    
    logger.info("\n" + "=" * 80)
    logger.info("Test Complete")
    logger.info("=" * 80)


if __name__ == "__main__":
    main()