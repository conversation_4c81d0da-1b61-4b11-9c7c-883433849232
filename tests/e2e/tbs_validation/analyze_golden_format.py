#!/usr/bin/env python3
"""
Analyze the actual golden file format from Nifty_Golden_Ouput.xlsx
"""

import pandas as pd
import os

def analyze_golden_format():
    """Analyze the actual golden file format."""
    
    golden_file = '/srv/samba/shared/Nifty_Golden_Ouput.xlsx'
    
    print("=" * 80)
    print("ANALYZING ACTUAL GOLDEN FILE FORMAT")
    print("=" * 80)
    
    if os.path.exists(golden_file):
        xl = pd.ExcelFile(golden_file)
        
        print(f"\nFile: {golden_file}")
        print(f"Total Sheets: {len(xl.sheet_names)}")
        print(f"Sheet Names: {xl.sheet_names}")
        
        # Analyze each sheet
        for sheet_name in xl.sheet_names:
            print(f"\n{'='*60}")
            print(f"SHEET: {sheet_name}")
            print(f"{'='*60}")
            
            df = pd.read_excel(xl, sheet_name)
            
            print(f"Rows: {len(df)}")
            print(f"Columns ({len(df.columns)}): {list(df.columns)}")
            
            # Show sample data
            if len(df) > 0:
                print(f"\nFirst 3 rows:")
                print(df.head(3).to_string(index=False))
                
                # Show data types
                print(f"\nColumn Data Types:")
                for col in df.columns:
                    print(f"  {col}: {df[col].dtype}")
                    
                # Check for specific columns
                if 'Symbol' in df.columns:
                    print(f"\nSample Symbols: {df['Symbol'].head(3).tolist()}")
                    
                if 'P&L' in df.columns:
                    print(f"\nTotal P&L: {df['P&L'].sum()}")
                    
    else:
        print(f"Golden file not found: {golden_file}")
        
    # Now compare with our generated files
    print("\n\n" + "=" * 80)
    print("COMPARING WITH GENERATED FILES")
    print("=" * 80)
    
    # Check GPU output
    gpu_file = '/srv/samba/shared/test_results/golden_output_gpu/gpu_golden_output.xlsx'
    if os.path.exists(gpu_file):
        gpu_xl = pd.ExcelFile(gpu_file)
        print(f"\nGPU Output Sheets: {gpu_xl.sheet_names}")
        
    # Check Archive output
    archive_file = '/srv/samba/shared/test_results/golden_output_archive/archive_golden_output.xlsx'
    if os.path.exists(archive_file):
        archive_xl = pd.ExcelFile(archive_file)
        print(f"Archive Output Sheets: {archive_xl.sheet_names}")
        
    print("\n" + "=" * 80)
    print("KEY FINDINGS")
    print("=" * 80)
    print("1. The actual golden file has multiple sheets")
    print("2. Need to match exact sheet structure")
    print("3. Need to verify column naming conventions")
    print("4. Need to ensure all required data is present")

if __name__ == "__main__":
    analyze_golden_format()