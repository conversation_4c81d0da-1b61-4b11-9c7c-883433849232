#!/usr/bin/env python3
"""
Create GPU backtester config file for TBS testing
"""

import json
import os
from datetime import datetime

def create_config(portfolio_file, strategy_file, output_dir):
    """Create config file for GPU backtester."""
    
    config = {
        "portfolio": {
            "excel_path": portfolio_file,
            "sheets": ["PortfolioSetting", "StrategySetting"]
        },
        "strategies": {
            "TBS": {
                "excel_path": strategy_file,
                "sheets": ["GeneralParameter", "LegParameter"]
            }
        },
        "output": {
            "directory": output_dir,
            "format": ["excel", "json"]
        },
        "execution": {
            "use_gpu": True,
            "workers": 1,
            "debug": True
        },
        "data_source": {
            "type": "heavydb",
            "connection": {
                "host": "localhost",
                "port": 6274,
                "user": "admin",
                "password": "HyperInteractive",
                "database": "heavyai"
            },
            "table": "nifty_option_chain"
        }
    }
    
    config_file = os.path.join(output_dir, 'gpu_config.json')
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
        
    return config_file

if __name__ == "__main__":
    # Test config creation
    test_dir = '/srv/samba/shared/test_results/real_data_comparison'
    portfolio = os.path.join(test_dir, 'test_portfolio_real.xlsx')
    strategy = os.path.join(test_dir, 'test_strategy_real.xlsx')
    output = os.path.join(test_dir, 'gpu_output')
    
    config_file = create_config(portfolio, strategy, output)
    print(f"Created config: {config_file}")