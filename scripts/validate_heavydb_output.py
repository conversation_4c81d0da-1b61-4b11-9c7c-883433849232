#!/usr/bin/env python3
"""Validate HeavyDB backtester output against expected results."""

import os
import sys
import pandas as pd
import json
from datetime import datetime

# Add project paths
sys.path.append('/srv/samba/shared')

def validate_output(output_file: str, expected_trades: int = 4, expected_pnl: float = -62.0):
    """Validate the output file against expected results."""
    
    print(f"\n{'='*60}")
    print(f"Validating HeavyDB Output: {output_file}")
    print(f"{'='*60}")
    
    if not os.path.exists(output_file):
        print(f"✗ Output file not found: {output_file}")
        return False
    
    try:
        # Check Excel sheets
        xl_file = pd.ExcelFile(output_file)
        sheets = xl_file.sheet_names
        
        print(f"\nSheets found: {len(sheets)}")
        for sheet in sheets:
            print(f"  - {sheet}")
        
        # Validate required sheets
        required_sheets = ['Metrics', 'PORTFOLIO Trans', 'PortfolioParameter', 
                         'GeneralParameter', 'LegParameter', 'PORTFOLIO Results']
        
        missing_sheets = [s for s in required_sheets if s not in sheets]
        if missing_sheets:
            print(f"\n✗ Missing required sheets: {missing_sheets}")
            return False
        else:
            print(f"\n✓ All required sheets present")
        
        # Check trades
        trans_df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
        actual_trades = len(trans_df)
        
        print(f"\nTrade Count:")
        print(f"  Expected: {expected_trades}")
        print(f"  Actual: {actual_trades}")
        
        if actual_trades == expected_trades:
            print(f"  ✓ Trade count matches")
        else:
            print(f"  ✗ Trade count mismatch")
            return False
        
        # Check P&L
        actual_pnl = trans_df['PnL'].sum() if 'PnL' in trans_df else 0
        pnl_diff = abs(actual_pnl - expected_pnl)
        
        print(f"\nTotal P&L:")
        print(f"  Expected: {expected_pnl:.2f}")
        print(f"  Actual: {actual_pnl:.2f}")
        print(f"  Difference: {pnl_diff:.2f}")
        
        if pnl_diff < 0.01:
            print(f"  ✓ P&L matches")
        else:
            print(f"  ✗ P&L mismatch")
            return False
        
        # Check metrics
        metrics_df = pd.read_excel(output_file, sheet_name='Metrics')
        combined_metrics = metrics_df[metrics_df['Strategy'] == 'Combined'] if 'Strategy' in metrics_df.columns else metrics_df
        
        print(f"\nMetrics:")
        print(f"  Rows: {len(combined_metrics)}")
        print(f"  Expected: 25")
        
        if len(combined_metrics) == 25:
            print(f"  ✓ Metrics count correct")
        else:
            print(f"  ✗ Metrics count incorrect")
            return False
        
        # Check trade details
        print(f"\nTrade Details:")
        for idx, row in trans_df.iterrows():
            print(f"  Trade {idx+1}: {row.get('Instrument Type', 'N/A')} {row.get('Side', 'N/A')} "
                  f"{row.get('Strike', 'N/A')} - Entry: {row.get('Entry Time', 'N/A')}, "
                  f"Exit: {row.get('Exit Time', 'N/A')}, P&L: {row.get('PnL', 0):.2f}")
        
        # All validations passed
        print(f"\n✓ All validations passed!")
        return True
        
    except Exception as e:
        print(f"\n✗ Error validating output: {e}")
        return False


def main():
    """Main execution."""
    
    # Check if we have a recent GPU output
    test_results_dir = '/srv/samba/shared/test_results'
    
    # Find the most recent GPU output
    gpu_files = [f for f in os.listdir(test_results_dir) if f.startswith('gpu_') and f.endswith('.xlsx')]
    
    if not gpu_files:
        print("No GPU output files found. Running a test first...")
        
        # Run a simple test
        import subprocess
        result = subprocess.run([
            'python3', '-m', 'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
            '--legacy-excel',
            '--portfolio-excel', 'bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx',
            '--output-path', 'test_results/gpu_TBS_validation.xlsx',
            '--start-date', '20250401',
            '--end-date', '20250401'
        ], cwd='/srv/samba/shared', capture_output=True, text=True)
        
        if result.returncode == 0:
            output_file = 'test_results/gpu_TBS_validation.xlsx'
        else:
            print(f"Failed to run test: {result.stderr}")
            return
    else:
        # Use the most recent file
        gpu_files.sort(key=lambda x: os.path.getmtime(os.path.join(test_results_dir, x)), reverse=True)
        output_file = os.path.join(test_results_dir, gpu_files[0])
    
    # Validate the output
    if validate_output(output_file):
        print(f"\n{'='*60}")
        print("✅ HeavyDB Backtester Output is Valid!")
        print(f"{'='*60}")
        
        # Save validation results
        validation_result = {
            'timestamp': datetime.now().isoformat(),
            'file': output_file,
            'status': 'PASSED',
            'trades': 4,
            'pnl': -62.0
        }
        
        with open('test_results/validation_result.json', 'w') as f:
            json.dump(validation_result, f, indent=2)
    else:
        print(f"\n{'='*60}")
        print("❌ HeavyDB Backtester Output Validation Failed")
        print(f"{'='*60}")


if __name__ == "__main__":
    main() 