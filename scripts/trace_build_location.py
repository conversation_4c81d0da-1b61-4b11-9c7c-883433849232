#!/usr/bin/env python3
"""Trace the exact location of build_trade_record in the control flow."""

with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

# Find key positions
leg_loop_line = None
risk_rules_if = None
build_trade_line = None
append_line = None

for i, line in enumerate(lines):
    if 'for leg in strategy.legs:' in line:
        leg_loop_line = i
    elif 'if leg.risk_rules' in line:
        risk_rules_if = i
    elif 'record = build_trade_record' in line:
        build_trade_line = i
    elif 'trade_records.append(record)' in line:
        append_line = i

print(f'Key line positions:')
print(f'  Leg loop: line {leg_loop_line+1 if leg_loop_line else "NOT FOUND"}')
print(f'  Risk rules if: line {risk_rules_if+1 if risk_rules_if else "NOT FOUND"}')
print(f'  build_trade_record: line {build_trade_line+1 if build_trade_line else "NOT FOUND"}')
print(f'  append: line {append_line+1 if append_line else "NOT FOUND"}')

# Analyze the control flow
if build_trade_line:
    print(f'\n=== Analyzing control flow around build_trade_record ===')
    
    # Look backwards from build_trade_record to find what if statements it's inside
    current_indent = len(lines[build_trade_line]) - len(lines[build_trade_line].lstrip())
    print(f'build_trade_record is at indent level {current_indent}')
    
    print('\nChecking parent blocks:')
    for i in range(build_trade_line-1, max(build_trade_line-100, 0), -1):
        line = lines[i]
        indent = len(line) - len(line.lstrip())
        stripped = line.strip()
        
        if indent < current_indent and stripped:
            if stripped.startswith('if '):
                print(f'  Line {i+1} ({indent} spaces): {stripped[:60]}')
                if 'risk_rules' in stripped:
                    print(f'    >>> FOUND: build_trade_record is inside risk_rules if block!')
            elif stripped.startswith('for '):
                print(f'  Line {i+1} ({indent} spaces): {stripped[:60]}')
            elif stripped.startswith('else:'):
                print(f'  Line {i+1} ({indent} spaces): {stripped}')

# Check where the risk_rules if block ends
if risk_rules_if and build_trade_line:
    print(f'\n=== Checking risk_rules block scope ===')
    risk_indent = len(lines[risk_rules_if]) - len(lines[risk_rules_if].lstrip())
    print(f'Risk rules if statement at line {risk_rules_if+1} with indent {risk_indent}')
    
    # Find where this if block ends
    for i in range(risk_rules_if+1, min(risk_rules_if+300, len(lines))):
        line = lines[i]
        indent = len(line) - len(line.lstrip())
        stripped = line.strip()
        
        if stripped and indent <= risk_indent:
            print(f'Risk rules block ends at line {i+1}')
            if i < build_trade_line:
                print('  >>> build_trade_record is AFTER the risk_rules block!')
            else:
                print('  >>> build_trade_record is INSIDE the risk_rules block!')
            break 