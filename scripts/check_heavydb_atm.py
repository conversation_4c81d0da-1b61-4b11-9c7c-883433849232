#!/usr/bin/env python3
"""
Check HeavyDB ATM calculation for April 1, 2025
"""

import subprocess
import sys
import os

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def check_heavydb_atm():
    """Check ATM calculation in HeavyDB"""
    print("Checking HeavyDB ATM Calculation for April 1, 2025")
    print("="*60)
    
    # Query to check ATM and related data
    query = """
    SELECT 
        trade_date,
        trade_time,
        spot,
        atm_strike,
        strike,
        ce_close,
        pe_close,
        ce_symbol,
        pe_symbol,
        expiry_date
    FROM nifty_option_chain 
    WHERE trade_date = '2025-04-01' 
    AND trade_time = '09:16:00'
    AND strike = atm_strike
    ORDER BY expiry_date
    LIMIT 5
    """
    
    cmd = [
        "/opt/heavyai/bin/heavysql",
        "-s", "127.0.0.1",
        "-u", "admin",
        "-p", "HyperInteractive",
        "-d", "heavyai",
        "-q",
        query
    ]
    
    print("Executing HeavyDB query...")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("\nHeavyDB ATM Data:")
            print(result.stdout)
            
            # Also check available strikes around ATM
            print("\n" + "="*60)
            print("Checking strikes around spot price...")
            
            # Query to show strikes near spot
            strikes_query = """
            SELECT DISTINCT
                strike,
                ce_close,
                pe_close,
                ABS(strike + ce_close - pe_close - spot) as synthetic_diff
            FROM nifty_option_chain 
            WHERE trade_date = '2025-04-01' 
            AND trade_time = '09:16:00'
            AND strike BETWEEN 23000 AND 24500
            AND ce_close > 0 AND pe_close > 0
            ORDER BY synthetic_diff
            LIMIT 10
            """
            
            cmd[6] = strikes_query
            result2 = subprocess.run(cmd, capture_output=True, text=True)
            if result2.returncode == 0:
                print("\nTop 10 strikes by synthetic future difference:")
                print(result2.stdout)
                
        else:
            print(f"Error querying HeavyDB: {result.stderr}")
    except Exception as e:
        print(f"Failed to query HeavyDB: {e}")
    
    # Also check if we have data for that date
    print("\n" + "="*60)
    print("Checking data availability...")
    
    count_query = """
    SELECT 
        COUNT(*) as total_rows,
        MIN(trade_time) as first_time,
        MAX(trade_time) as last_time
    FROM nifty_option_chain 
    WHERE trade_date = '2025-04-01'
    """
    
    cmd[6] = count_query
    result3 = subprocess.run(cmd, capture_output=True, text=True)
    if result3.returncode == 0:
        print("\nData availability for April 1, 2025:")
        print(result3.stdout)

if __name__ == "__main__":
    check_heavydb_atm() 