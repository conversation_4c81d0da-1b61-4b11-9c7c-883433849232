#!/usr/bin/env python3
"""Run legacy backtester with proper setup."""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def setup_legacy_environment():
    """Set up the environment for legacy backtester."""
    print("Setting up legacy environment...")
    
    # Create INPUT SHEETS directory in legacy location
    legacy_dir = Path("bt/archive/backtester_stable/BTRUN")
    input_sheets_dir = legacy_dir / "INPUT SHEETS"
    input_sheets_dir.mkdir(exist_ok=True)
    
    # Copy portfolio file with expected name
    src_portfolio = Path("bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx")
    dst_portfolio = input_sheets_dir / "INPUT PORTFOLIO.xlsx"
    shutil.copy2(src_portfolio, dst_portfolio)
    print(f"  Copied portfolio file to {dst_portfolio}")
    
    # Copy strategy file
    src_strategy = Path("bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx")
    dst_strategy = input_sheets_dir / "INPUT TBS MULTI LEGS.xlsx"
    shutil.copy2(src_strategy, dst_strategy)
    print(f"  Copied strategy file to {dst_strategy}")
    
    # Create Trades directory
    trades_dir = legacy_dir / "Trades"
    trades_dir.mkdir(exist_ok=True)
    
    return legacy_dir

def run_legacy_backtest(legacy_dir):
    """Run the legacy backtester."""
    print("\nRunning legacy backtester...")
    
    # Change to legacy directory
    original_dir = os.getcwd()
    os.chdir(legacy_dir)
    
    try:
        # Run the legacy script
        result = subprocess.run(
            [sys.executable, "BTRunPortfolio.py"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✓ Legacy backtester completed successfully")
            
            # Look for output files
            trades_dir = Path("Trades")
            output_files = list(trades_dir.glob("*.xlsx"))
            
            if output_files:
                print(f"\nFound {len(output_files)} output file(s):")
                for f in output_files:
                    print(f"  - {f.name}")
                    # Copy to main directory
                    dst = Path(original_dir) / f"legacy_output_{f.name}"
                    shutil.copy2(f, dst)
                    print(f"    Copied to: {dst}")
                return True
            else:
                print("  No output files found in Trades directory")
                
        else:
            print(f"✗ Legacy backtester failed with return code: {result.returncode}")
            print(f"Error output:\n{result.stderr}")
            
    except Exception as e:
        print(f"✗ Error running legacy backtester: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        os.chdir(original_dir)
    
    return False

def main():
    """Main execution."""
    print("="*60)
    print("Running Legacy Backtester with Proper Setup")
    print("="*60)
    
    # Set up environment
    legacy_dir = setup_legacy_environment()
    
    # Run backtester
    success = run_legacy_backtest(legacy_dir)
    
    if success:
        print("\n✓ Legacy backtester completed successfully")
        print("  Check for legacy_output_*.xlsx files in current directory")
    else:
        print("\n✗ Legacy backtester failed")

if __name__ == "__main__":
    main() 