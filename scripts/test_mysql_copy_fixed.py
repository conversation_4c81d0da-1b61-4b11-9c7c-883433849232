#!/usr/bin/env python3
"""
Test MySQL copy with SQLAlchemy for proper pandas integration
"""

import pandas as pd
from sqlalchemy import create_engine
import pymysql

# Remote MySQL connection details
REMOTE_URL = 'mysql+pymysql://mahesh:mahesh_123@************:3306/historicaldb'
LOCAL_URL = 'mysql+pymysql://mahesh:mahesh_123@localhost:3306/historicaldb'

def test_copy():
    """Test copying a small sample"""
    print("Testing MySQL copy with SQLAlchemy...")
    
    try:
        # Create engines
        remote_engine = create_engine(REMOTE_URL)
        local_engine = create_engine(LOCAL_URL)
        
        # Test query - just 100 rows
        query = "SELECT * FROM nifty_call WHERE date >= 220101 LIMIT 100"
        print(f"Executing: {query}")
        
        df = pd.read_sql(query, remote_engine)
        print(f"Retrieved {len(df)} rows")
        print(f"Columns: {list(df.columns)}")
        print(f"First row sample:")
        print(f"  Date: {df.iloc[0]['date']}, Time: {df.iloc[0]['time']}")
        print(f"  Symbol: {df.iloc[0]['symbol']}, Strike: {df.iloc[0]['strike']}")
        
        # Write to local
        print("\nWriting to local database...")
        df.to_sql('nifty_call', local_engine, if_exists='append', index=False, method='multi')
        
        # Verify
        count_query = "SELECT COUNT(*) as count FROM nifty_call"
        result = pd.read_sql(count_query, local_engine)
        print(f"Local nifty_call now has {result.iloc[0]['count']} rows")
        
        # Close connections
        remote_engine.dispose()
        local_engine.dispose()
        
        print("\nTest successful!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_copy() 