#!/usr/bin/env python3
"""
Test HeavyDB connection and basic queries.

This script tests the HeavyDB connection using the project's utilities
and verifies that basic queries work correctly.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_project_connection():
    """Test connection using project utilities."""
    print("Testing HeavyDB connection using project utilities...")
    
    try:
        from bt.backtester_stable.BTRUN.dal.heavydb_connection import get_connection, test_connection
        
        # Test basic connection
        if test_connection():
            print("✓ Basic connection test passed")
        else:
            print("✗ Basic connection test failed")
            return False
        
        # Get connection and run queries
        conn = get_connection()
        
        # Test 1: Simple query
        result = conn.execute("SELECT 1 as test_value")
        value = result.fetchone()[0]
        if value == 1:
            print("✓ Simple query test passed")
        else:
            print("✗ Simple query test failed")
            return False
        
        # Test 2: Check nse_holidays table
        result = conn.execute("SELECT COUNT(1) FROM nse_holidays")
        count = result.fetchone()[0]
        print(f"✓ NSE holidays table has {count} records")
        
        # Test 3: Check nifty_option_chain table structure
        result = conn.execute("SHOW TABLES")
        tables = [row[0] for row in result.fetchall()]
        
        if 'nifty_option_chain' in tables:
            print("✓ nifty_option_chain table exists")
            
            # Check available date range
            result = conn.execute("""
                SELECT MIN(trade_date), MAX(trade_date), COUNT(1)
                FROM nifty_option_chain
            """)
            row = result.fetchone()
            if row and row[2] > 0:
                print(f"✓ nifty_option_chain has data from {row[0]} to {row[1]} ({row[2]:,} records)")
                
                # Test specific date query
                result = conn.execute("""
                    SELECT COUNT(1)
                    FROM nifty_option_chain
                    WHERE trade_date = DATE '2024-01-03'
                """)
                count = result.fetchone()[0]
                print(f"✓ Records for 2024-01-03: {count:,}")
                
                if count > 0:
                    # Test ATM query
                    result = conn.execute("""
                        SELECT DISTINCT trade_time, spot, atm_strike
                        FROM nifty_option_chain
                        WHERE trade_date = DATE '2024-01-03'
                          AND trade_time = TIME '09:16:00'
                        LIMIT 1
                    """)
                    row = result.fetchone()
                    if row:
                        print(f"✓ ATM data for 2024-01-03 09:16:00: Spot={row[1]}, ATM={row[2]}")
                    else:
                        print("⚠ No ATM data found for 2024-01-03 09:16:00")
                        
                        # Check available times
                        result = conn.execute("""
                            SELECT DISTINCT trade_time
                            FROM nifty_option_chain
                            WHERE trade_date = DATE '2024-01-03'
                            ORDER BY trade_time
                            LIMIT 5
                        """)
                        times = [row[0] for row in result.fetchall()]
                        print(f"  Available times for 2024-01-03: {times}")
            else:
                print("⚠ nifty_option_chain table is empty")
        else:
            print("⚠ nifty_option_chain table not found")
            print(f"  Available tables: {tables}")
        
        return True
        
    except Exception as e:
        print(f"✗ Project connection test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_direct_connection():
    """Test direct connection as fallback."""
    print("\nTesting direct HeavyDB connection...")
    
    try:
        # Try heavydb library first
        try:
            from heavydb import connect
            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            print("✓ Connected using heavydb library")
        except ImportError:
            # Fall back to pymapd
            import pymapd
            conn = pymapd.connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                db_name='heavyai'
            )
            print("✓ Connected using pymapd library")
        
        # Test query
        result = conn.execute("SELECT 1 as test")
        value = result.fetchone()[0]
        if value == 1:
            print("✓ Direct connection query test passed")
            return True
        else:
            print("✗ Direct connection query test failed")
            return False
            
    except Exception as e:
        print(f"✗ Direct connection test failed: {e}")
        return False

def test_enhanced_connection():
    """Test enhanced connection with guardrails."""
    print("\nTesting enhanced HeavyDB connection with guardrails...")
    
    try:
        from bt.backtester_stable.BTRUN.dal.heavydb_connection_enhanced import get_connection
        
        # Get enhanced connection
        conn = get_connection(
            enforce_guardrails=True,
            warn_only=True,  # Just warnings for testing
            collect_metrics=True
        )
        
        # Test query with metrics
        query = """
        SELECT COUNT(1)
        FROM nifty_option_chain
        WHERE trade_date >= DATE '2024-01-01'
        """
        
        result, metrics = conn.execute_with_metrics(query)
        count = result.fetchone()[0]
        
        print(f"✓ Enhanced connection test passed")
        print(f"  Query executed in {metrics['execution_time_ms']:.2f} ms")
        print(f"  Query was optimized: {metrics['optimized']}")
        print(f"  Records found: {count:,}")
        
        # Show global metrics
        global_metrics = conn.get_global_metrics()
        print(f"  Total queries executed: {global_metrics['total_queries']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Enhanced connection test failed: {e}")
        return False

def main():
    """Main test function."""
    print("HeavyDB Connection Test")
    print("=" * 50)
    
    # Test project connection utilities
    project_success = test_project_connection()
    
    # Test direct connection
    direct_success = test_direct_connection()
    
    # Test enhanced connection
    enhanced_success = test_enhanced_connection()
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Project utilities: {'✓ PASS' if project_success else '✗ FAIL'}")
    print(f"Direct connection: {'✓ PASS' if direct_success else '✗ FAIL'}")
    print(f"Enhanced connection: {'✓ PASS' if enhanced_success else '✗ FAIL'}")
    
    if project_success:
        print("\n✓ HeavyDB connection is working properly!")
        print("You can now use the debug scripts with confidence.")
    else:
        print("\n✗ HeavyDB connection issues detected.")
        print("Please check HeavyDB service and connection parameters.")
        
        if direct_success:
            print("Direct connection works, so the issue is with project utilities.")
        else:
            print("Direct connection also failed, check HeavyDB service.")
    
    return 0 if (project_success and direct_success) else 1

if __name__ == "__main__":
    sys.exit(main()) 