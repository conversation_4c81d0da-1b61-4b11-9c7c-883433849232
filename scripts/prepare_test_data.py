#!/usr/bin/env python3
"""
Prepare test data files for Phase 2.D comprehensive legacy parity testing.
Updates dates in Excel files to use 2024 dates matching the local MySQL data.
"""

import os
import shutil
import openpyxl
from datetime import datetime
import argparse

def update_portfolio_dates(excel_file, start_date, end_date):
    """Update dates in portfolio Excel file."""
    wb = openpyxl.load_workbook(excel_file)
    
    # Update PortfolioSetting sheet
    if 'PortfolioSetting' in wb.sheetnames:
        ws = wb['PortfolioSetting']
        # Find rows with StartDate and EndDate
        for row in ws.iter_rows(min_row=2):
            if row[0].value == 'YES' or row[3].value == 'YES':  # Enabled column
                # Find StartDate and EndDate columns
                header_row = list(ws.iter_rows(min_row=1, max_row=1))[0]
                start_col = None
                end_col = None
                for idx, cell in enumerate(header_row):
                    if cell.value == 'StartDate':
                        start_col = idx + 1
                    elif cell.value == 'EndDate':
                        end_col = idx + 1
                
                if start_col and end_col:
                    ws.cell(row=row[0].row, column=start_col).value = start_date
                    ws.cell(row=row[0].row, column=end_col).value = end_date
    
    # Update TV Setting sheet if exists
    if 'Setting' in wb.sheetnames:
        ws = wb['Setting']
        for row in ws.iter_rows(min_row=2):
            if row[1].value == 'YES':  # Enabled column
                # Find StartDate and EndDate columns
                header_row = list(ws.iter_rows(min_row=1, max_row=1))[0]
                start_col = None
                end_col = None
                for idx, cell in enumerate(header_row):
                    if cell.value == 'StartDate':
                        start_col = idx + 1
                    elif cell.value == 'EndDate':
                        end_col = idx + 1
                
                if start_col and end_col:
                    ws.cell(row=row[0].row, column=start_col).value = start_date
                    ws.cell(row=row[0].row, column=end_col).value = end_date
    
    wb.save(excel_file)
    print(f"Updated dates in {excel_file}")

def create_test_variants(strategy_type):
    """Create 1-day and 30-day test variants for a strategy."""
    base_dir = f"test_data/{strategy_type}"
    
    # Define date ranges
    one_day_start = "03_01_2024"
    one_day_end = "03_01_2024"
    thirty_day_start = "01_01_2024"
    thirty_day_end = "31_01_2024"
    
    # Get list of files
    files = os.listdir(base_dir)
    
    for file in files:
        if file.endswith('.xlsx') and not any(x in file for x in ['1day', '30day']):
            base_file = os.path.join(base_dir, file)
            
            # Create 1-day variant
            if 'portfolio' in file:
                one_day_file = base_file.replace('.xlsx', '_1day.xlsx')
                shutil.copy2(base_file, one_day_file)
                update_portfolio_dates(one_day_file, one_day_start, one_day_end)
                
                # Create 30-day variant
                thirty_day_file = base_file.replace('.xlsx', '_30day.xlsx')
                shutil.copy2(base_file, thirty_day_file)
                update_portfolio_dates(thirty_day_file, thirty_day_start, thirty_day_end)

def prepare_all_test_data():
    """Prepare test data for all strategies."""
    strategies = ['tbs', 'orb', 'oi', 'indicator', 'tv']
    
    for strategy in strategies:
        print(f"\nPreparing test data for {strategy.upper()}...")
        create_test_variants(strategy)
    
    print("\nTest data preparation complete!")
    print("\nTest data structure:")
    os.system("tree test_data/")

def main():
    parser = argparse.ArgumentParser(description='Prepare test data for Phase 2.D testing')
    parser.add_argument('--strategy', choices=['tbs', 'orb', 'oi', 'indicator', 'tv', 'all'], 
                        default='all', help='Strategy type to prepare')
    args = parser.parse_args()
    
    if args.strategy == 'all':
        prepare_all_test_data()
    else:
        create_test_variants(args.strategy)

if __name__ == '__main__':
    main() 