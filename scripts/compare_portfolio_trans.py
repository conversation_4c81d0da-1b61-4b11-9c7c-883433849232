#!/usr/bin/env python3
"""
Compare PORTFOLIO Trans sheets between output and golden files
"""

import pandas as pd

output_file = "/srv/samba/shared/tbs_test_golden_dates.xlsx"
golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"

print("PORTFOLIO Trans comparison")
print("=" * 60)

try:
    # Read PORTFOLIO Trans sheets
    output_trans = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans', engine='openpyxl')
    golden_trans = pd.read_excel(golden_file, sheet_name='PORTFOLIO Trans', engine='openpyxl')
    
    print(f"\nOutput PORTFOLIO Trans shape: {output_trans.shape}")
    print(f"Golden PORTFOLIO Trans shape: {golden_trans.shape}")
    
    # Compare column names
    print(f"\nOutput columns: {list(output_trans.columns)}")
    print(f"\nGolden columns: {list(golden_trans.columns)}")
    
    # Show all output trades
    print(f"\n{'='*30} OUTPUT TRADES {'='*30}")
    for idx, row in output_trans.iterrows():
        print(f"\nTrade {idx + 1}:")
        print(f"  Entry: {row.get('entry_date', 'N/A')} {row.get('entry_time', 'N/A')}")
        print(f"  Exit: {row.get('exit_date', 'N/A')} {row.get('exit_time', 'N/A')}")
        print(f"  Strike: {row.get('strike', 'N/A')}, Type: {row.get('instrument_type', 'N/A')}, Trade: {row.get('side', 'N/A')}")
        print(f"  Entry Price: {row.get('entry_price', 'N/A')}, Exit Price: {row.get('exit_price', 'N/A')}")
        print(f"  PNL: {row.get('pnl', 'N/A')}, Net PNL: {row.get('netPnlAfterExpenses', 'N/A')}")
        print(f"  Exit Reason: {row.get('reason', 'N/A')}")
        
    # Show all golden trades
    print(f"\n{'='*30} GOLDEN TRADES {'='*30}")
    for idx, row in golden_trans.iterrows():
        print(f"\nTrade {idx + 1}:")
        print(f"  Entry: {row.get('Entry Date', 'N/A')} {row.get('Enter On', 'N/A')}")
        print(f"  Exit: {row.get('Exit Date', 'N/A')} {row.get('Exit at', 'N/A')}")
        print(f"  Strike: {row.get('Strike', 'N/A')}, Type: {row.get('CE/PE', 'N/A')}, Trade: {row.get('Trade', 'N/A')}")
        print(f"  Entry Price: {row.get('Entry at', 'N/A')}, Exit Price: {row.get('Exit at.1', 'N/A')}")
        print(f"  PNL: {row.get('PNL', 'N/A')}, Net PNL: {row.get('Net PNL', 'N/A')}")
        print(f"  Exit Reason: {row.get('Reason', 'N/A')}")
        
    # Summary comparison
    print(f"\n{'='*30} SUMMARY {'='*30}")
    print(f"Output Total PNL: {output_trans['pnl'].sum():.2f}")
    print(f"Golden Total PNL: {golden_trans['PNL'].sum():.2f}")
    print(f"Difference: {output_trans['pnl'].sum() - golden_trans['PNL'].sum():.2f}")
    
    # Check if dates match
    output_dates = set(output_trans['entry_date'].unique())
    golden_dates = set(golden_trans['Entry Date'].unique())
    print(f"\nOutput unique dates: {output_dates}")
    print(f"Golden unique dates: {golden_dates}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc() 