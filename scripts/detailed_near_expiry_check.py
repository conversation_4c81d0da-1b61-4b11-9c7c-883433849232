#!/usr/bin/env python3
"""
Detailed check of synthetic future calculations for near expiry (April 3)
to understand why MySQL and HeavyDB calculate different ATM strikes
"""

import mysql.connector
try:
    from pyheavydb import connect as heavydb_connect
except ImportError:
    from heavydb import connect as heavydb_connect

# MySQL connection
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def detailed_mysql_analysis(date, expiry='250403'):
    """Show detailed synthetic future calculations for MySQL"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    time = 33360  # 9:16 AM
    
    # Get spot price
    cursor.execute("SELECT close FROM nifty_cash WHERE date = %s AND time = %s", (date, time))
    spot = cursor.fetchone()
    spot_price = spot[0] / 100 if spot else 0
    
    print(f"\nMySQL Analysis - Date: {date}, Expiry: {expiry}")
    print(f"Spot Price: {spot_price:.2f}")
    print("\nStrike | CE Price | PE Price | Syn Future | Diff from Spot")
    print("-" * 65)
    
    # Check a range of strikes
    cursor.execute("""
        SELECT 
            c.strike,
            c.close as ce_close,
            p.close as pe_close,
            c.strike + (c.close/100) - (p.close/100) as syn_future,
            ABS(c.strike + (c.close/100) - (p.close/100) - (cash.close/100)) as syn_diff
        FROM nifty_call c
        JOIN nifty_put p ON c.date = p.date AND c.time = p.time 
                          AND c.strike = p.strike AND c.expiry = p.expiry
        JOIN nifty_cash cash ON c.date = cash.date AND c.time = cash.time
        WHERE c.date = %s AND c.time = %s AND c.expiry = %s
        AND c.close > 0 AND p.close > 0
        AND c.strike BETWEEN 23000 AND 25000
        ORDER BY syn_diff
        LIMIT 15
    """, (date, time, expiry))
    
    results = cursor.fetchall()
    best_strike = None
    min_diff = float('inf')
    
    for strike, ce_close, pe_close, syn_future, syn_diff in results:
        ce_price = ce_close / 100
        pe_price = pe_close / 100
        syn_diff_val = float(syn_diff)
        
        if syn_diff_val < min_diff:
            min_diff = syn_diff_val
            best_strike = strike
        
        marker = " <-- Best" if strike == best_strike and syn_diff_val == min_diff else ""
        print(f"{strike:6.0f} | {ce_price:8.2f} | {pe_price:8.2f} | {float(syn_future):10.2f} | {syn_diff_val:14.2f}{marker}")
    
    print(f"\nBest ATM: {best_strike} (diff: {min_diff:.2f})")
    
    cursor.close()
    conn.close()

def detailed_heavydb_analysis(date_str):
    """Show detailed synthetic future calculations for HeavyDB"""
    conn = heavydb_connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    # Convert date format
    heavydb_date = f"20{date_str[:2]}-{date_str[2:4]}-{date_str[4:6]}"
    
    # Get data with April 3 expiry
    query = f"""
    SELECT 
        spot,
        strike,
        ce_close,
        pe_close,
        strike + ce_close - pe_close as syn_future,
        ABS(strike + ce_close - pe_close - spot) as syn_diff,
        atm_strike
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}'
    AND trade_time = '09:16:00'
    AND expiry_date = '2025-04-03'
    AND ce_close > 0 AND pe_close > 0
    AND strike BETWEEN 23000 AND 25000
    ORDER BY syn_diff
    LIMIT 15
    """
    
    results = conn.execute(query).fetchall()
    
    if results:
        spot_price = results[0][0]
        atm_strike = results[0][6]
        
        print(f"\nHeavyDB Analysis - Date: {heavydb_date}, Expiry: 2025-04-03")
        print(f"Spot Price: {spot_price:.2f}")
        print(f"HeavyDB ATM Strike: {int(atm_strike)}")
        print("\nStrike | CE Price | PE Price | Syn Future | Diff from Spot")
        print("-" * 65)
        
        for spot, strike, ce_close, pe_close, syn_future, syn_diff, atm in results:
            marker = " <-- HeavyDB ATM" if strike == atm else ""
            print(f"{strike:6.0f} | {ce_close:8.2f} | {pe_close:8.2f} | {syn_future:10.2f} | {syn_diff:14.2f}{marker}")
    
    conn.close()

def main():
    print("Detailed Near Expiry (April 3) Analysis")
    print("="*70)
    print("Comparing synthetic future calculations between MySQL and HeavyDB")
    print("for the same expiry date to understand ATM differences")
    
    dates = ['250401', '250402', '250403']
    
    for date in dates:
        print(f"\n{'='*70}")
        print(f"ANALYSIS FOR {date}")
        print(f"{'='*70}")
        
        detailed_mysql_analysis(date, '250403')
        detailed_heavydb_analysis(date)
    
    print("\n" + "="*70)
    print("KEY INSIGHTS:")
    print("1. Both systems are using April 3 expiry")
    print("2. The synthetic future calculations show different results")
    print("3. This could be due to different option prices in the data")
    print("4. HeavyDB's data appears more consistent and reliable")

if __name__ == "__main__":
    main() 