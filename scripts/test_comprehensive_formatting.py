#!/usr/bin/env python3
"""Comprehensive test for all formatting fixes in HeavyDB backtester."""

import os
import sys
import pandas as pd
import subprocess
from datetime import datetime

# Add project paths
sys.path.append('/srv/samba/shared')

def run_heavydb_backtest():
    """Run the HeavyDB backtester with all formatting fixes."""
    print("\n" + "="*80)
    print("RUNNING HEAVYDB BACKTESTER WITH ALL FORMATTING FIXES")
    print("="*80)
    
    output_file = "heavydb_formatted_test_output.xlsx"
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "--output-path", output_file,
        "--start-date", "20250401",
        "--end-date", "20250401"
    ]
    
    env = os.environ.copy()
    env['PYTHONPATH'] = '/srv/samba/shared'
    
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    
    if result.returncode == 0:
        print(f"✓ HeavyDB backtest completed successfully")
        return output_file
    else:
        print(f"✗ HeavyDB backtester failed:")
        print(f"STDOUT:\n{result.stdout}")
        print(f"STDERR:\n{result.stderr}")
        return None

def verify_formatting_fixes(output_file):
    """Verify all formatting fixes are applied correctly."""
    print("\n" + "="*80)
    print("VERIFYING FORMATTING FIXES")
    print("="*80)
    
    if not os.path.exists(output_file):
        print(f"✗ Output file not found: {output_file}")
        return False
    
    xl = pd.ExcelFile(output_file)
    all_good = True
    
    # 1. Check PortfolioParameter format (should be 2x2)
    print("\n1. Checking PortfolioParameter format:")
    if "PortfolioParameter" in xl.sheet_names:
        df = pd.read_excel(output_file, sheet_name="PortfolioParameter")
        print(f"   Shape: {df.shape}")
        print(f"   Columns: {list(df.columns)}")
        if df.shape == (2, 2) and 'Particulars' in df.columns:
            print("   ✓ PortfolioParameter is in correct 2x2 format")
        else:
            print("   ✗ PortfolioParameter format is incorrect")
            all_good = False
    else:
        print("   ✗ PortfolioParameter sheet missing")
        all_good = False
    
    # 2. Check transaction sheets for formatting
    print("\n2. Checking transaction sheet formatting:")
    trans_sheets = [s for s in xl.sheet_names if 'Trans' in s]
    for sheet in trans_sheets:
        print(f"\n   Checking {sheet}:")
        df = pd.read_excel(output_file, sheet_name=sheet)
        
        # Check column names (should be Title Case)
        expected_cols = ['Portfolio Name', 'Strategy Name', 'Leg ID', 'Instrument Type', 
                        'Side', 'Strike', 'Entry Date', 'Entry Time', 'Exit Date', 
                        'Exit Time', 'Entry Price', 'Exit Price', 'PnL']
        missing_cols = [col for col in expected_cols if col not in df.columns]
        if missing_cols:
            print(f"   ⚠️  Missing expected columns: {missing_cols}")
        else:
            print(f"   ✓ Column names are in Title Case format")
        
        # Check case sensitivity (should be uppercase)
        if 'Instrument Type' in df.columns and not df.empty:
            instrument_types = df['Instrument Type'].dropna().unique()
            if all(val.isupper() for val in instrument_types):
                print(f"   ✓ Instrument types are uppercase: {instrument_types}")
            else:
                print(f"   ✗ Instrument types not uppercase: {instrument_types}")
                all_good = False
        
        if 'Side' in df.columns and not df.empty:
            sides = df['Side'].dropna().unique()
            if all(val.isupper() for val in sides):
                print(f"   ✓ Sides are uppercase: {sides}")
            else:
                print(f"   ✗ Sides not uppercase: {sides}")
                all_good = False
        
        # Check time format (should be HH:MM:SS)
        if 'Entry Time' in df.columns and not df.empty:
            sample_time = str(df['Entry Time'].iloc[0])
            if ':' in sample_time:
                print(f"   ✓ Time format is HH:MM:SS: {sample_time}")
            else:
                print(f"   ✗ Time format is incorrect: {sample_time}")
                all_good = False
    
    # 3. Check Metrics sheet (should not have duplicates)
    print("\n3. Checking Metrics sheet:")
    if "Metrics" in xl.sheet_names:
        df = pd.read_excel(output_file, sheet_name="Metrics")
        print(f"   Total rows: {len(df)}")
        if 'Strategy' in df.columns and 'Particulars' in df.columns:
            duplicates = df.duplicated(subset=['Strategy', 'Particulars'])
            if duplicates.any():
                print(f"   ✗ Found {duplicates.sum()} duplicate entries")
                all_good = False
            else:
                print(f"   ✓ No duplicate entries found")
    
    # 4. Check sheet name truncation
    print("\n4. Checking sheet name truncation:")
    long_sheets = [s for s in xl.sheet_names if 'ATM' in s or 'OTM' in s]
    for sheet in long_sheets:
        if len(sheet) > 31:
            print(f"   ⚠️  Sheet name longer than 31 chars: '{sheet}' ({len(sheet)} chars)")
        else:
            print(f"   ✓ Sheet name within limit: '{sheet}' ({len(sheet)} chars)")
    
    # 5. Check required sheets exist
    print("\n5. Checking required sheets:")
    required_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 
                      'Metrics', 'PORTFOLIO Trans', 'PORTFOLIO Results']
    for sheet in required_sheets:
        if sheet in xl.sheet_names:
            print(f"   ✓ {sheet} exists")
        else:
            print(f"   ✗ {sheet} missing")
            all_good = False
    
    return all_good

def compare_with_legacy(heavydb_file):
    """Compare formatted HeavyDB output with legacy output."""
    print("\n" + "="*80)
    print("COMPARING WITH LEGACY OUTPUT")
    print("="*80)
    
    legacy_file = "tests/outputs/gpu_parity_run/NIF0DTE_250401_cpu.xlsx"
    
    if not os.path.exists(legacy_file):
        print(f"Legacy file not found: {legacy_file}")
        return
    
    legacy_xl = pd.ExcelFile(legacy_file)
    heavydb_xl = pd.ExcelFile(heavydb_file)
    
    # Compare sheet counts
    print(f"\nSheet counts:")
    print(f"  Legacy: {len(legacy_xl.sheet_names)}")
    print(f"  HeavyDB: {len(heavydb_xl.sheet_names)}")
    
    # Compare specific sheets
    print("\nComparing key sheets:")
    
    # PortfolioParameter
    if "PortfolioParameter" in legacy_xl.sheet_names and "PortfolioParameter" in heavydb_xl.sheet_names:
        leg_pp = pd.read_excel(legacy_file, sheet_name="PortfolioParameter")
        hdb_pp = pd.read_excel(heavydb_file, sheet_name="PortfolioParameter")
        print(f"\nPortfolioParameter:")
        print(f"  Legacy shape: {leg_pp.shape}")
        print(f"  HeavyDB shape: {hdb_pp.shape}")
        if leg_pp.shape == hdb_pp.shape:
            print("  ✓ Shapes match!")
    
    # Metrics
    if "Metrics" in legacy_xl.sheet_names and "Metrics" in heavydb_xl.sheet_names:
        leg_metrics = pd.read_excel(legacy_file, sheet_name="Metrics")
        hdb_metrics = pd.read_excel(heavydb_file, sheet_name="Metrics")
        print(f"\nMetrics:")
        print(f"  Legacy rows: {len(leg_metrics)}")
        print(f"  HeavyDB rows: {len(hdb_metrics)}")
        
        # Check P&L values
        if 'Value' in leg_metrics.columns and 'Value' in hdb_metrics.columns:
            leg_pnl = leg_metrics[leg_metrics['Particulars'].str.contains('Total P', na=False)]['Value'].iloc[0] if len(leg_metrics[leg_metrics['Particulars'].str.contains('Total P', na=False)]) > 0 else None
            hdb_pnl = hdb_metrics[hdb_metrics['Particulars'].str.contains('Total P', na=False)]['Value'].iloc[0] if len(hdb_metrics[hdb_metrics['Particulars'].str.contains('Total P', na=False)]) > 0 else None
            
            if leg_pnl is not None and hdb_pnl is not None:
                print(f"  Legacy Total P&L: {leg_pnl}")
                print(f"  HeavyDB Total P&L: {hdb_pnl}")
                if abs(float(leg_pnl) - float(hdb_pnl)) < 0.01:
                    print("  ✓ P&L values match!")

def main():
    """Main execution."""
    print("="*80)
    print("COMPREHENSIVE FORMATTING TEST FOR HEAVYDB BACKTESTER")
    print(f"Date: {datetime.now()}")
    print("="*80)
    
    # Run HeavyDB backtest
    output_file = run_heavydb_backtest()
    
    if not output_file:
        print("\n✗ Failed to generate HeavyDB output")
        return
    
    # Verify formatting fixes
    print("\n" + "-"*80)
    all_fixes_good = verify_formatting_fixes(output_file)
    
    # Compare with legacy
    print("\n" + "-"*80)
    compare_with_legacy(output_file)
    
    # Final verdict
    print("\n" + "="*80)
    print("FINAL VERDICT")
    print("="*80)
    
    if all_fixes_good:
        print("\n✅ ALL FORMATTING FIXES ARE WORKING CORRECTLY!")
        print("\nThe HeavyDB backtester now produces output that matches the legacy format:")
        print("  ✓ PortfolioParameter in 2x2 format")
        print("  ✓ Transaction columns in Title Case")
        print("  ✓ Values in uppercase (CALL, PUT, BUY, SELL)")
        print("  ✓ Time format as HH:MM:SS")
        print("  ✓ No duplicate metrics")
        print("  ✓ All required sheets present")
    else:
        print("\n❌ Some formatting issues remain. Please review the output above.")
    
    print(f"\nOutput file: {output_file}")

if __name__ == "__main__":
    main() 