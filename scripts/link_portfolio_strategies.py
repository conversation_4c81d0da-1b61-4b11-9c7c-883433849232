#!/usr/bin/env python3
"""
Link portfolio Excel files to their appropriate strategy Excel files.
Updates the StrategySetting sheet to point to the correct strategy files.
"""

import os
import openpyxl

def update_strategy_reference(portfolio_file, strategy_type):
    """Update the StrategySetting sheet to reference the correct strategy file."""
    wb = openpyxl.load_workbook(portfolio_file)
    
    # Map strategy types to their files and evaluators
    strategy_map = {
        'tbs': {
            'file': 'input_tbs_multi_legs.xlsx',
            'evaluator': 'Tbs'
        },
        'orb': {
            'file': 'input_orb_strategy.xlsx',
            'evaluator': 'Orb'
        },
        'oi': {
            'file': 'input_oi_strategy.xlsx',
            'evaluator': 'Oi'
        },
        'indicator': {
            'file': 'input_indicator_strategy.xlsx',
            'evaluator': 'Indicator'
        }
    }
    
    if strategy_type not in strategy_map:
        print(f"Skipping {portfolio_file} - no strategy mapping for {strategy_type}")
        return
    
    # Create StrategySetting sheet if it doesn't exist
    if 'StrategySetting' not in wb.sheetnames:
        ws = wb.create_sheet('StrategySetting')
        # Add headers
        ws['A1'] = 'Enabled'
        ws['B1'] = 'PortfolioName'
        ws['C1'] = 'StrategyType'
        ws['D1'] = 'StrategyExcelFilePath'
        # Add a row
        ws['A2'] = 'YES'
        ws['B2'] = f'{strategy_type.upper()}0DTE'
        ws['C2'] = strategy_map[strategy_type]['evaluator']
        ws['D2'] = os.path.join(os.path.dirname(portfolio_file), strategy_map[strategy_type]['file'])
    else:
        ws = wb['StrategySetting']
        # Update existing rows
        for row in ws.iter_rows(min_row=2):
            if row[0].value == 'YES':  # Enabled
                # Update StrategyType
                row[2].value = strategy_map[strategy_type]['evaluator']
                # Update StrategyExcelFilePath
                row[3].value = os.path.join(os.path.dirname(portfolio_file), strategy_map[strategy_type]['file'])
    
    wb.save(portfolio_file)
    print(f"Updated strategy reference in {portfolio_file}")

def link_all_portfolios():
    """Link all portfolio files to their strategies."""
    strategies = ['tbs', 'orb', 'oi', 'indicator']
    
    for strategy in strategies:
        base_dir = f"test_data/{strategy}"
        files = os.listdir(base_dir)
        
        for file in files:
            if file.startswith('input_portfolio') and file.endswith('.xlsx'):
                portfolio_file = os.path.join(base_dir, file)
                update_strategy_reference(portfolio_file, strategy)
    
    print("\nPortfolio-strategy linking complete!")

if __name__ == '__main__':
    link_all_portfolios() 