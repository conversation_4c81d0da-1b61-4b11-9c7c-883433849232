#!/usr/bin/env python3
"""
Debug script to capture comprehensive execution trace from GPU backtester.

This script captures:
- HeavyDB query execution
- Strike selection logic
- Trade entry/exit decisions
- P&L calculations
- Excel output generation

Matches the format of legacy traces for easy comparison.
"""

import os
import sys
import json
import logging
import datetime
from pathlib import Path

# Add GPU code to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def setup_debug_logging():
    """Configure comprehensive debug logging."""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    logging.basicConfig(
        level=logging.DEBUG,
        format=log_format,
        handlers=[
            logging.FileHandler('gpu_debug_trace.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Create debug logger
    debug_logger = logging.getLogger('gpu_debug')
    return debug_logger

def capture_execution_trace(input_excel, output_file):
    """Run GPU backtester and capture execution trace."""
    logger = setup_debug_logging()
    
    logger.info(f"Starting GPU execution trace capture")
    logger.info(f"Input Excel: {input_excel}")
    logger.info(f"Output file: {output_file}")
    
    # Set environment for debug mode
    os.environ['DEBUG_MODE'] = 'true'
    
    # Import GPU modules
    try:
        from bt.backtester_stable.BTRUN import BTRunPortfolio_GPU
        from bt.backtester_stable.BTRUN import heavydb_trade_processing
        from bt.backtester_stable.BTRUN import models
        
        # Capture initial configuration
        trace_data = {
            'timestamp': datetime.datetime.now().isoformat(),
            'input_excel': input_excel,
            'config': {
                'heavydb_host': '127.0.0.1',
                'heavydb_port': 6274,
                'atm_method': 'synthetic_future (pre-computed in view)'
            },
            'execution_trace': []
        }
        
        logger.info("GPU modules imported successfully")
        logger.info(f"Config: {trace_data['config']}")
        
        # Add debug hooks to key functions
        original_evaluate_risk = heavydb_trade_processing.evaluate_risk_rule
        
        def debug_evaluate_risk(trade, candles, risk_rule, **kwargs):
            """Wrapper to capture risk evaluation."""
            logger.debug(f"Risk evaluation: trade={trade.trade_id}, rule={risk_rule.rule_type}")
            result = original_evaluate_risk(trade, candles, risk_rule, **kwargs)
            logger.debug(f"Risk result: exit={result.get('exit_triggered')}, reason={result.get('exit_reason')}")
            
            # Add to trace
            trace_data['execution_trace'].append({
                'type': 'risk_evaluation',
                'trade_id': trade.trade_id,
                'risk_rule': risk_rule.rule_type.value if hasattr(risk_rule.rule_type, 'value') else str(risk_rule.rule_type),
                'result': result
            })
            
            return result
        
        # Monkey-patch for debugging
        heavydb_trade_processing.evaluate_risk_rule = debug_evaluate_risk
        
        # Run the backtester with debug output
        logger.info("Would execute GPU backtester with debug hooks")
        
        # This would normally run:
        # python -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU \
        #   --legacy-excel \
        #   --portfolio-excel {input_excel} \
        #   --output-path debug_output.xlsx \
        #   --debug
        
    except ImportError as e:
        logger.error(f"Failed to import GPU modules: {e}")
        logger.error("Ensure you're running from the correct directory")
        return
    
    # Save trace data
    with open(output_file, 'w') as f:
        json.dump(trace_data, f, indent=2)
    
    logger.info(f"Execution trace saved to {output_file}")

def capture_query_trace():
    """Capture HeavyDB query execution trace."""
    logger = logging.getLogger('gpu_debug')
    
    # This would hook into HeavyDB query execution
    query_trace_example = {
        'type': 'heavydb_query',
        'timestamp': datetime.datetime.now().isoformat(),
        'query': """
            SELECT * FROM nifty_option_chain 
            WHERE trade_date = DATE '2024-01-03' 
            AND trade_time = TIME '09:16:00'
            AND strike = atm_strike
        """,
        'execution_time_ms': 15.3,
        'rows_returned': 2,
        'gpu_enabled': True
    }
    
    logger.debug(f"Query executed: {query_trace_example}")
    return query_trace_example

def capture_strike_selection_trace(spot, date, time):
    """Capture strike selection logic trace."""
    logger = logging.getLogger('gpu_debug')
    
    strike_trace = {
        'type': 'strike_selection',
        'timestamp': datetime.datetime.now().isoformat(),
        'inputs': {
            'spot': spot,
            'date': date,
            'time': time
        },
        'method': 'pre-computed ATM from nifty_option_chain',
        'query': f"SELECT DISTINCT atm_strike FROM nifty_option_chain WHERE trade_date = '{date}' AND trade_time = '{time}'",
        'result': {
            'atm_strike': 23000,  # Example
            'synthetic_future': 23005.5  # Example
        }
    }
    
    logger.debug(f"Strike selection: {strike_trace}")
    return strike_trace

def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Capture GPU backtester execution trace')
    parser.add_argument('--input-excel', required=True, help='Input Excel file path')
    parser.add_argument('--output-trace', default='gpu_trace.json', help='Output trace file')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    capture_execution_trace(args.input_excel, args.output_trace)

if __name__ == '__main__':
    main() 