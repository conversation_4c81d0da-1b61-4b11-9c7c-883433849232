#!/usr/bin/env python3
"""
Find a test date that has data in both MySQL and HeavyDB
Fixed version that avoids SQL syntax errors and uses available data
"""

import mysql.connector as mysql
from datetime import datetime
import sys
sys.path.insert(0, '.')

def check_mysql_dates():
    """Find dates with data in MySQL"""
    print("Checking MySQL for dates with complete data...")
    
    try:
        mydb = mysql.connect(
            host="************",
            user="mahesh", 
            password="mahesh_123",
            database="historicaldb"
        )
        cursor = mydb.cursor()
        
        # First, find what date ranges we have
        print("\nChecking available date ranges in MySQL...")
        
        # Check nifty_cash for available years
        query = """
            SELECT 
                MIN(date) as min_date,
                MAX(date) as max_date
            FROM nifty_cash
            WHERE date >= 240101  -- Start from 2024
        """
        cursor.execute(query)
        result = cursor.fetchone()
        
        if result:
            print(f"  Date range in nifty_cash: {result[0]} to {result[1]}")
        
        # Get recent dates with data (2024)
        query = """
            SELECT DISTINCT
                c.date as trade_date,
                COUNT(DISTINCT c.time) as cash_ticks
            FROM nifty_cash c
            WHERE c.date >= 240401 AND c.date < 250101  -- April 2024 to end of 2024
            GROUP BY c.date
            HAVING cash_ticks > 100
            ORDER BY c.date DESC
            LIMIT 20
        """
        
        cursor.execute(query)
        dates_with_cash = cursor.fetchall()
        
        print(f"\nFound {len(dates_with_cash)} dates with cash data in 2024")
        
        # For each date, check if we have option data
        mysql_good_dates = []
        
        for date_row in dates_with_cash[:10]:
            date_str = str(date_row[0])
            
            # Check if this date has option data
            query = f"""
                SELECT 
                    COUNT(DISTINCT strike) as call_strikes,
                    COUNT(*) as call_rows
                FROM nifty_call
                WHERE date = {date_str}
            """
            cursor.execute(query)
            call_result = cursor.fetchone()
            
            query = f"""
                SELECT 
                    COUNT(DISTINCT strike) as put_strikes,
                    COUNT(*) as put_rows
                FROM nifty_put
                WHERE date = {date_str}
            """
            cursor.execute(query)
            put_result = cursor.fetchone()
            
            if call_result[0] > 10 and put_result[0] > 10:
                # Convert YYMMDD to readable format
                year = f"20{date_str[:2]}"
                month = date_str[2:4]
                day = date_str[4:6]
                formatted_date = f"{year}-{month}-{day}"
                
                print(f"  {formatted_date}: {date_row[1]} cash ticks, {call_result[0]} call strikes, {put_result[0]} put strikes")
                mysql_good_dates.append((date_str, formatted_date))
        
        cursor.close()
        mydb.close()
        
        return mysql_good_dates
        
    except Exception as e:
        print(f"MySQL Error: {e}")
        import traceback
        traceback.print_exc()
        return []

def check_heavydb_dates(mysql_dates):
    """Check which MySQL dates also exist in HeavyDB"""
    print("\nChecking HeavyDB for matching dates...")
    
    try:
        from bt.backtester_stable.BTRUN.heavydb_connection import get_connection
        
        conn = get_connection()
        
        # Check what date range we have in HeavyDB
        query = """
            SELECT 
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date
            FROM nifty_option_chain
        """
        result = conn.execute(query).fetchone()
        
        if result:
            print(f"  HeavyDB date range: {result[0]} to {result[1]}")
        
        # Convert MySQL dates to HeavyDB format and check
        heavydb_dates = []
        
        for mysql_date, formatted_date in mysql_dates[:5]:
            query = f"""
                SELECT 
                    COUNT(*) as row_count,
                    MIN(trade_time) as first_time,
                    MAX(trade_time) as last_time
                FROM nifty_option_chain
                WHERE trade_date = DATE '{formatted_date}'
            """
            
            result = conn.execute(query).fetchone()
            
            if result and result[0] > 0:
                print(f"  {formatted_date}: {result[0]:,} rows ({result[1]} to {result[2]})")
                heavydb_dates.append(formatted_date)
        
        conn.close()
        
        return heavydb_dates
        
    except Exception as e:
        print(f"HeavyDB Error: {e}")
        import traceback
        traceback.print_exc()
        return []

def find_common_dates():
    """Find dates that exist in both databases"""
    print("="*60)
    print("Finding Common Test Dates Between MySQL and HeavyDB")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Get MySQL dates
    mysql_dates = check_mysql_dates()
    
    if not mysql_dates:
        print("\n✗ No suitable dates found in MySQL")
        return None
    
    # Check HeavyDB
    heavydb_dates = check_heavydb_dates(mysql_dates)
    
    if not heavydb_dates:
        print("\n✗ No matching dates found in HeavyDB")
        return None
    
    # Find first common date
    print("\n" + "="*60)
    print("Recommended Test Date:")
    print("="*60)
    
    for mysql_date, formatted_date in mysql_dates:
        if formatted_date in heavydb_dates:
            print(f"\n✓ Use date: {formatted_date}")
            print(f"  MySQL format: {mysql_date}")
            print(f"  HeavyDB format: {formatted_date}")
            print(f"  Input Excel format: {formatted_date[8:10]}_{formatted_date[5:7]}_{formatted_date[0:4]}")
            
            # Also show how to update the legacy patch
            print(f"\nFor legacy backtester MySQL patch:")
            print(f"  Update date conversion to use: '{formatted_date}'")
            
            return {
                'mysql': mysql_date,
                'heavydb': formatted_date,
                'excel': f"{formatted_date[8:10]}_{formatted_date[5:7]}_{formatted_date[0:4]}",
                'yyyymmdd': formatted_date.replace('-', '')
            }
    
    print("\n✗ No common dates found between MySQL and HeavyDB")
    print("\nTip: You may need to load more data into HeavyDB from the market_data folder")
    return None

if __name__ == "__main__":
    result = find_common_dates()
    
    if result:
        print("\nNext steps:")
        print(f"  1. Update input Excel with dates: {result['excel']} to {result['excel']}")
        print(f"  2. Update the legacy MySQL patch to use correct date format")
        print(f"  3. Run both backtesters and compare results") 