#!/usr/bin/env python3
"""
Run Legacy Backtester with Real MySQL Data
This script runs the actual legacy backtesting with MySQL data
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Add legacy path
legacy_path = os.path.abspath('bt/archive/backtester_stable')
sys.path.insert(0, legacy_path)

def setup_legacy_environment():
    """Setup the legacy environment to use MySQL"""
    print("="*60)
    print("Setting up Legacy Backtester with MySQL")
    print("="*60)
    
    # Change to legacy directory
    os.chdir(os.path.join(legacy_path, 'BTRUN'))
    
    # Import legacy modules
    try:
        # First, let's load the data
        sys.path.insert(0, os.path.join(legacy_path))
        from app.database import load
        
        print("Loading data from MySQL...")
        load()
        print("✓ Data loaded successfully from MySQL")
        
        # Now run the backtester
        btrun_path = os.path.join(legacy_path, 'BTRUN')
        if btrun_path not in sys.path:
            sys.path.insert(0, btrun_path)
            
        from Util import Util
        import config
        
        print("\nRunning legacy backtester...")
        print(f"Portfolio file: {config.PORTFOLIO_FILE_PATH}")
        print(f"Input folder: {config.INPUT_FILE_FOLDER}")
        
        # Get portfolio configuration
        portfolioForBt, portfolioSettingDf = Util.getPortfolioJson(
            excelFilePath=os.path.join(config.INPUT_FILE_FOLDER, config.PORTFOLIO_FILE_PATH)
        )
        
        if not portfolioForBt:
            print("✗ No portfolio configuration found")
            return None
            
        print(f"✓ Found {len(portfolioForBt)} portfolio(s)")
        
        # Run backtest for each portfolio
        results = []
        for pNo in portfolioForBt:
            print(f"\nProcessing portfolio: {portfolioForBt[pNo]['portfolio']['name']}")
            print(f"Start date: {portfolioForBt[pNo]['start_date']}")
            print(f"End date: {portfolioForBt[pNo]['end_date']}")
            
            # Run the backtest
            btResp = Util.getBacktestResults(btPara=portfolioForBt[pNo])
            
            if btResp and 'strategies' in btResp and 'orders' in btResp['strategies']:
                order_count = len(btResp['strategies']['orders'])
                print(f"✓ Generated {order_count} orders")
                
                # Parse the results
                parsedOrderDf, marginReqByEachStgy, maxProfitLossDf = Util.parseBacktestingResponse(
                    btResponse=btResp['strategies'], 
                    slippagePercent=portfolioForBt[pNo]['slippage_percent']
                )
                
                if not parsedOrderDf.empty:
                    total_pnl = parsedOrderDf['netPnlAfterExpenses'].sum()
                    print(f"✓ Total P&L: {total_pnl}")
                    results.append({
                        'portfolio': portfolioForBt[pNo]['portfolio']['name'],
                        'orders': order_count,
                        'total_pnl': total_pnl,
                        'trades': parsedOrderDf
                    })
                else:
                    print("✗ No trades generated")
            else:
                print("✗ No backtest results")
                
        return results
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_atm_calculation():
    """Check what ATM calculation method is being used"""
    print("\n" + "="*60)
    print("Checking ATM Calculation Method")
    print("="*60)
    
    try:
        from app.database import INDEX_DATA, CALLS_DATA, PUTS_DATA
        
        # Get some sample data
        if 'NIFTY' in INDEX_DATA and INDEX_DATA['NIFTY']:
            # Get first available date
            dates = sorted(INDEX_DATA['NIFTY'].keys())
            if dates:
                test_date = dates[0]
                times = sorted(INDEX_DATA['NIFTY'][test_date].keys())
                if times:
                    test_time = times[0]
                    spot_price = INDEX_DATA['NIFTY'][test_date][test_time].close
                    
                    # Simple ATM calculation
                    simple_atm = round(spot_price / 50) * 50
                    
                    print(f"Test date: {test_date}")
                    print(f"Test time: {test_time}")
                    print(f"Spot price: {spot_price}")
                    print(f"Simple ATM: {simple_atm}")
                    
                    # Check if we can calculate synthetic future ATM
                    if test_date in CALLS_DATA.get('NIFTY', {}) and test_date in PUTS_DATA.get('NIFTY', {}):
                        print("\nCalculating synthetic future ATM...")
                        # This would require implementing the synthetic future logic
                        print("(Synthetic future calculation would go here)")
                    
    except Exception as e:
        print(f"Error checking ATM calculation: {e}")

def main():
    """Main function"""
    print("Legacy Backtester with MySQL Data")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Check ATM calculation
    check_atm_calculation()
    
    # Run the backtester
    results = setup_legacy_environment()
    
    if results:
        print("\n" + "="*60)
        print("LEGACY BACKTEST RESULTS SUMMARY")
        print("="*60)
        
        for result in results:
            print(f"\nPortfolio: {result['portfolio']}")
            print(f"Orders: {result['orders']}")
            print(f"Total P&L: {result['total_pnl']}")
            
            # Show trade details
            if 'trades' in result and not result['trades'].empty:
                print("\nTrade Summary:")
                trades_df = result['trades']
                print(f"- Total trades: {len(trades_df)}")
                print(f"- Unique strikes: {trades_df['strike'].unique()}")
                print(f"- Entry times: {trades_df['entry_time'].unique()}")
                print(f"- Exit times: {trades_df['exit_time'].unique()}")
    else:
        print("\n✗ Legacy backtester failed to run")

if __name__ == "__main__":
    main() 