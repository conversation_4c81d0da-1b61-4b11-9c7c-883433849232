#!/usr/bin/env python3
"""
Disk Space Monitoring and Alert System for HeavyDB and ETL Pipeline
Monitors disk usage and sends alerts via multiple channels
"""

import os
import sys
import psutil
import json
import smtplib
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
import argparse
import yaml
import subprocess

class DiskSpaceMonitor:
    def __init__(self, config_file: str = "/srv/samba/shared/config/disk_monitor.yaml"):
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.state_file = Path(self.config.get('state_file', '/srv/samba/shared/logs/disk_monitor_state.json'))
        self.state = self.load_state()
        
    def load_config(self, config_file: str) -> Dict:
        """Load configuration from YAML file"""
        default_config = {
            'monitored_paths': {
                '/nvme0n1-disk': {
                    'name': 'HeavyDB NVMe Storage',
                    'critical_threshold': 90,
                    'warning_threshold': 80,
                    'info_threshold': 70
                },
                '/srv/samba/shared': {
                    'name': 'Shared Data Storage',
                    'critical_threshold': 85,
                    'warning_threshold': 75,
                    'info_threshold': 65
                },
                '/': {
                    'name': 'Root Filesystem',
                    'critical_threshold': 90,
                    'warning_threshold': 80,
                    'info_threshold': 70
                }
            },
            'alerts': {
                'telegram': {
                    'enabled': True,
                    'bot_token': os.environ.get('TELEGRAM_BOT_TOKEN', ''),
                    'chat_ids': []
                },
                'email': {
                    'enabled': False,
                    'smtp_server': 'smtp.gmail.com',
                    'smtp_port': 587,
                    'from_email': '',
                    'password': '',
                    'to_emails': []
                },
                'webhook': {
                    'enabled': False,
                    'url': '',
                    'headers': {}
                }
            },
            'monitoring': {
                'check_interval_minutes': 30,
                'alert_cooldown_hours': 4,
                'trend_analysis_days': 7,
                'predict_full_days': 3
            },
            'log_file': '/srv/samba/shared/logs/disk_monitor.log',
            'state_file': '/srv/samba/shared/logs/disk_monitor_state.json'
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                loaded_config = yaml.safe_load(f) or {}
                # Deep merge with defaults
                return self._deep_merge(default_config, loaded_config)
        else:
            # Save default config
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            with open(config_file, 'w') as f:
                yaml.dump(default_config, f, default_flow_style=False)
            return default_config
    
    def _deep_merge(self, base: Dict, update: Dict) -> Dict:
        """Deep merge two dictionaries"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                base[key] = self._deep_merge(base[key], value)
            else:
                base[key] = value
        return base
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_file = self.config.get('log_file', '/srv/samba/shared/logs/disk_monitor.log')
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_state(self) -> Dict:
        """Load previous monitoring state"""
        if self.state_file.exists():
            try:
                with open(self.state_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"Error loading state: {e}")
        
        return {
            'last_alerts': {},
            'usage_history': {},
            'predictions': {}
        }
    
    def save_state(self):
        """Save current monitoring state"""
        os.makedirs(self.state_file.parent, exist_ok=True)
        with open(self.state_file, 'w') as f:
            json.dump(self.state, f, indent=2)
    
    def get_disk_usage(self, path: str) -> Optional[Dict]:
        """Get disk usage for a specific path"""
        try:
            usage = psutil.disk_usage(path)
            
            # Get mount point and device
            mount_info = self._get_mount_info(path)
            
            return {
                'path': path,
                'total_gb': round(usage.total / (1024**3), 2),
                'used_gb': round(usage.used / (1024**3), 2),
                'free_gb': round(usage.free / (1024**3), 2),
                'percent': usage.percent,
                'mount_point': mount_info['mount_point'],
                'device': mount_info['device'],
                'filesystem': mount_info['filesystem']
            }
        except Exception as e:
            self.logger.error(f"Error getting disk usage for {path}: {e}")
            return None
    
    def _get_mount_info(self, path: str) -> Dict:
        """Get mount information for a path"""
        mount_point = path
        device = "unknown"
        filesystem = "unknown"
        
        # Find the mount point
        while not os.path.ismount(mount_point) and mount_point != '/':
            mount_point = os.path.dirname(mount_point)
        
        # Get device and filesystem
        for partition in psutil.disk_partitions():
            if partition.mountpoint == mount_point:
                device = partition.device
                filesystem = partition.fstype
                break
        
        return {
            'mount_point': mount_point,
            'device': device,
            'filesystem': filesystem
        }
    
    def analyze_usage_trend(self, path: str, usage: Dict) -> Dict:
        """Analyze disk usage trends and predict when disk will be full"""
        history_key = path
        
        # Initialize history if not exists
        if history_key not in self.state['usage_history']:
            self.state['usage_history'][history_key] = []
        
        # Add current usage
        self.state['usage_history'][history_key].append({
            'timestamp': datetime.now().isoformat(),
            'used_gb': usage['used_gb'],
            'percent': usage['percent']
        })
        
        # Keep only last N days of history
        cutoff_date = datetime.now() - timedelta(days=self.config['monitoring']['trend_analysis_days'])
        self.state['usage_history'][history_key] = [
            h for h in self.state['usage_history'][history_key]
            if datetime.fromisoformat(h['timestamp']) > cutoff_date
        ]
        
        history = self.state['usage_history'][history_key]
        
        if len(history) < 2:
            return {'trend': 'insufficient_data'}
        
        # Calculate growth rate
        first_entry = history[0]
        last_entry = history[-1]
        
        time_diff = datetime.fromisoformat(last_entry['timestamp']) - datetime.fromisoformat(first_entry['timestamp'])
        time_diff_days = time_diff.total_seconds() / (24 * 3600)
        
        if time_diff_days == 0:
            return {'trend': 'insufficient_data'}
        
        # GB per day growth rate
        gb_growth_rate = (last_entry['used_gb'] - first_entry['used_gb']) / time_diff_days
        
        # Predict when disk will be full
        days_until_full = None
        predicted_full_date = None
        
        if gb_growth_rate > 0:
            remaining_gb = usage['total_gb'] - usage['used_gb']
            days_until_full = remaining_gb / gb_growth_rate
            predicted_full_date = datetime.now() + timedelta(days=days_until_full)
        
        return {
            'trend': 'growing' if gb_growth_rate > 0 else 'shrinking' if gb_growth_rate < 0 else 'stable',
            'growth_rate_gb_per_day': round(gb_growth_rate, 2),
            'days_until_full': round(days_until_full, 1) if days_until_full else None,
            'predicted_full_date': predicted_full_date.isoformat() if predicted_full_date else None,
            'sample_days': round(time_diff_days, 1)
        }
    
    def check_alert_conditions(self, path: str, config: Dict, usage: Dict, trend: Dict) -> Optional[str]:
        """Check if alert should be sent based on thresholds and trends"""
        severity = None
        
        # Check percentage thresholds
        if usage['percent'] >= config['critical_threshold']:
            severity = 'CRITICAL'
        elif usage['percent'] >= config['warning_threshold']:
            severity = 'WARNING'
        elif usage['percent'] >= config['info_threshold']:
            severity = 'INFO'
        
        # Check predictive alerts
        if trend.get('days_until_full') is not None:
            if trend['days_until_full'] <= self.config['monitoring']['predict_full_days']:
                # Upgrade severity if disk will be full soon
                if severity is None or severity == 'INFO':
                    severity = 'WARNING'
                elif severity == 'WARNING':
                    severity = 'CRITICAL'
        
        # Check cooldown period
        if severity and path in self.state['last_alerts']:
            last_alert = self.state['last_alerts'][path]
            last_alert_time = datetime.fromisoformat(last_alert['timestamp'])
            cooldown_hours = self.config['monitoring']['alert_cooldown_hours']
            
            if datetime.now() - last_alert_time < timedelta(hours=cooldown_hours):
                # Don't send alert if in cooldown, unless severity increased
                if severity == last_alert.get('severity', ''):
                    return None
        
        return severity
    
    def format_alert_message(self, path: str, config: Dict, usage: Dict, trend: Dict, severity: str) -> Dict:
        """Format alert message for different channels"""
        emoji_map = {
            'CRITICAL': '🚨',
            'WARNING': '⚠️',
            'INFO': 'ℹ️'
        }
        
        title = f"{emoji_map.get(severity, '📊')} Disk Space Alert - {severity}"
        
        # Build message parts
        lines = [
            f"**Path**: {path}",
            f"**Name**: {config['name']}",
            f"**Device**: {usage['device']}",
            f"**Usage**: {usage['used_gb']} GB / {usage['total_gb']} GB ({usage['percent']:.1f}%)",
            f"**Free Space**: {usage['free_gb']} GB"
        ]
        
        # Add trend information
        if trend.get('trend') != 'insufficient_data':
            lines.append(f"**Growth Rate**: {trend['growth_rate_gb_per_day']} GB/day")
            if trend.get('days_until_full'):
                lines.append(f"**Predicted Full**: {trend['days_until_full']:.1f} days")
        
        # Add recommendations
        recommendations = self.get_recommendations(path, usage, severity)
        if recommendations:
            lines.append(f"**Recommended Actions**:")
            for rec in recommendations:
                lines.append(f"  • {rec}")
        
        return {
            'title': title,
            'message': '\n'.join(lines),
            'severity': severity,
            'path': path,
            'usage': usage,
            'trend': trend
        }
    
    def get_recommendations(self, path: str, usage: Dict, severity: str) -> List[str]:
        """Get recommendations based on path and severity"""
        recommendations = []
        
        if severity == 'CRITICAL':
            if 'heavyai' in path or 'heavydb' in path:
                recommendations.extend([
                    "Immediately clean up old data or archives",
                    "Consider migrating historical data to archive storage",
                    "Run: heavydb_cleanup_old_data.sh"
                ])
            elif 'shared' in path:
                recommendations.extend([
                    "Clean up old backtest results",
                    "Archive processed market data files",
                    "Remove temporary files in /tmp directories"
                ])
            recommendations.append("Consider adding additional storage immediately")
        
        elif severity == 'WARNING':
            if 'heavyai' in path or 'heavydb' in path:
                recommendations.extend([
                    "Plan for data archival strategy",
                    "Monitor ETL data growth patterns",
                    "Consider implementing data retention policies"
                ])
            recommendations.append("Schedule storage expansion within a week")
        
        elif severity == 'INFO':
            recommendations.append("Monitor usage trends")
            recommendations.append("Plan for future storage needs")
        
        return recommendations
    
    def send_telegram_alert(self, alert: Dict) -> bool:
        """Send alert via Telegram"""
        if not self.config['alerts']['telegram']['enabled']:
            return False
        
        bot_token = self.config['alerts']['telegram']['bot_token']
        chat_ids = self.config['alerts']['telegram']['chat_ids']
        
        if not bot_token or not chat_ids:
            self.logger.warning("Telegram not configured properly")
            return False
        
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        
        # Format message for Telegram (Markdown)
        message = f"*{alert['title']}*\n\n{alert['message']}"
        
        success = True
        for chat_id in chat_ids:
            try:
                response = requests.post(url, json={
                    'chat_id': chat_id,
                    'text': message,
                    'parse_mode': 'Markdown'
                })
                
                if response.status_code != 200:
                    self.logger.error(f"Telegram API error: {response.text}")
                    success = False
                    
            except Exception as e:
                self.logger.error(f"Error sending Telegram alert: {e}")
                success = False
        
        return success
    
    def send_email_alert(self, alert: Dict) -> bool:
        """Send alert via email"""
        if not self.config['alerts']['email']['enabled']:
            return False
        
        email_config = self.config['alerts']['email']
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = email_config['from_email']
            msg['To'] = ', '.join(email_config['to_emails'])
            msg['Subject'] = alert['title']
            
            # Create HTML body
            html_body = f"""
            <html>
            <body>
            <h2>{alert['title']}</h2>
            <pre>{alert['message']}</pre>
            <hr>
            <p>Generated by HeavyDB Disk Monitor at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </body>
            </html>
            """
            
            msg.attach(MIMEText(html_body, 'html'))
            
            # Send email
            with smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port']) as server:
                server.starttls()
                server.login(email_config['from_email'], email_config['password'])
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending email alert: {e}")
            return False
    
    def send_webhook_alert(self, alert: Dict) -> bool:
        """Send alert via webhook"""
        if not self.config['alerts']['webhook']['enabled']:
            return False
        
        webhook_config = self.config['alerts']['webhook']
        
        try:
            payload = {
                'timestamp': datetime.now().isoformat(),
                'alert': alert
            }
            
            response = requests.post(
                webhook_config['url'],
                json=payload,
                headers=webhook_config.get('headers', {}),
                timeout=30
            )
            
            return response.status_code in [200, 201, 202, 204]
            
        except Exception as e:
            self.logger.error(f"Error sending webhook alert: {e}")
            return False
    
    def send_alerts(self, alert: Dict):
        """Send alerts through all configured channels"""
        channels = []
        
        if self.send_telegram_alert(alert):
            channels.append("Telegram")
        
        if self.send_email_alert(alert):
            channels.append("Email")
        
        if self.send_webhook_alert(alert):
            channels.append("Webhook")
        
        if channels:
            self.logger.info(f"Alert sent via: {', '.join(channels)}")
            
            # Update last alert state
            path = alert['path']
            self.state['last_alerts'][path] = {
                'timestamp': datetime.now().isoformat(),
                'severity': alert['severity'],
                'usage_percent': alert['usage']['percent']
            }
        else:
            self.logger.warning("No alerts were sent successfully")
    
    def check_all_paths(self):
        """Check all monitored paths"""
        self.logger.info("Starting disk space check...")
        
        alerts_to_send = []
        status_summary = []
        
        for path, config in self.config['monitored_paths'].items():
            # Get disk usage
            usage = self.get_disk_usage(path)
            if not usage:
                continue
            
            # Analyze trends
            trend = self.analyze_usage_trend(path, usage)
            
            # Check alert conditions
            severity = self.check_alert_conditions(path, config, usage, trend)
            
            # Prepare status
            status = {
                'path': path,
                'name': config['name'],
                'usage': usage,
                'trend': trend,
                'severity': severity
            }
            status_summary.append(status)
            
            # Queue alert if needed
            if severity:
                alert = self.format_alert_message(path, config, usage, trend, severity)
                alerts_to_send.append(alert)
            
            # Log status
            self.logger.info(
                f"{config['name']} ({path}): "
                f"{usage['percent']:.1f}% used, "
                f"{usage['free_gb']:.1f} GB free"
            )
        
        # Send alerts
        for alert in alerts_to_send:
            self.send_alerts(alert)
        
        # Save state
        self.save_state()
        
        # Generate and save report
        self.generate_report(status_summary)
        
        return status_summary
    
    def generate_report(self, status_summary: List[Dict]):
        """Generate a comprehensive disk usage report"""
        report_file = Path("/srv/samba/shared/logs/disk_usage_report.json")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_paths': len(status_summary),
                'alerts': sum(1 for s in status_summary if s['severity']),
                'critical': sum(1 for s in status_summary if s['severity'] == 'CRITICAL'),
                'warning': sum(1 for s in status_summary if s['severity'] == 'WARNING')
            },
            'details': status_summary,
            'heavydb_specific': self.get_heavydb_metrics()
        }
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"Report saved to {report_file}")
    
    def get_heavydb_metrics(self) -> Dict:
        """Get HeavyDB-specific storage metrics"""
        metrics = {}
        
        try:
            # Check HeavyDB data directory size
            heavydb_data_dir = "/nvme0n1-disk/var/lib/heavyai/storage"
            if os.path.exists(heavydb_data_dir):
                # Get table sizes
                data_dir = Path(heavydb_data_dir) / "data"
                if data_dir.exists():
                    table_sizes = {}
                    for table_dir in data_dir.iterdir():
                        if table_dir.is_dir():
                            size = sum(f.stat().st_size for f in table_dir.rglob('*') if f.is_file())
                            table_sizes[table_dir.name] = size / (1024**3)  # GB
                    
                    # Sort by size
                    top_tables = sorted(table_sizes.items(), key=lambda x: x[1], reverse=True)[:10]
                    
                    metrics['top_tables_gb'] = dict(top_tables)
                    metrics['total_tables'] = len(table_sizes)
                    metrics['total_data_gb'] = sum(table_sizes.values())
            
            # Get log directory size
            log_dir = Path(heavydb_data_dir) / "log"
            if log_dir.exists():
                log_size = sum(f.stat().st_size for f in log_dir.rglob('*') if f.is_file())
                metrics['log_size_gb'] = log_size / (1024**3)
            
        except Exception as e:
            self.logger.error(f"Error getting HeavyDB metrics: {e}")
        
        return metrics
    
    def cleanup_suggestions(self) -> List[Dict]:
        """Generate cleanup suggestions based on current usage"""
        suggestions = []
        
        # Check for old log files
        log_dirs = [
            "/srv/samba/shared/logs",
            "/nvme0n1-disk/var/lib/heavyai/storage/log",
            "/srv/samba/shared/bt/backtester_stable/BTRUN/logs"
        ]
        
        for log_dir in log_dirs:
            if os.path.exists(log_dir):
                old_logs = []
                cutoff_date = datetime.now() - timedelta(days=30)
                
                for log_file in Path(log_dir).rglob("*.log"):
                    try:
                        if datetime.fromtimestamp(log_file.stat().st_mtime) < cutoff_date:
                            old_logs.append(log_file)
                    except:
                        pass
                
                if old_logs:
                    total_size = sum(f.stat().st_size for f in old_logs) / (1024**3)
                    suggestions.append({
                        'type': 'old_logs',
                        'path': log_dir,
                        'count': len(old_logs),
                        'size_gb': round(total_size, 2),
                        'action': f"rm {log_dir}/*.log -mtime +30"
                    })
        
        # Check for old backtest results
        output_dirs = [
            "/srv/samba/shared/Trades",
            "/srv/samba/shared/bt/backtester_stable/BTRUN/output"
        ]
        
        for output_dir in output_dirs:
            if os.path.exists(output_dir):
                old_files = []
                cutoff_date = datetime.now() - timedelta(days=7)
                
                for output_file in Path(output_dir).rglob("*"):
                    if output_file.is_file():
                        try:
                            if datetime.fromtimestamp(output_file.stat().st_mtime) < cutoff_date:
                                old_files.append(output_file)
                        except:
                            pass
                
                if old_files:
                    total_size = sum(f.stat().st_size for f in old_files) / (1024**3)
                    suggestions.append({
                        'type': 'old_outputs',
                        'path': output_dir,
                        'count': len(old_files),
                        'size_gb': round(total_size, 2),
                        'action': f"find {output_dir} -type f -mtime +7 -delete"
                    })
        
        return suggestions

def main():
    parser = argparse.ArgumentParser(description="Monitor disk space and send alerts")
    parser.add_argument(
        '--config',
        default='/srv/samba/shared/config/disk_monitor.yaml',
        help='Configuration file path'
    )
    parser.add_argument(
        '--daemon',
        action='store_true',
        help='Run as daemon (continuous monitoring)'
    )
    parser.add_argument(
        '--cleanup-report',
        action='store_true',
        help='Generate cleanup suggestions report'
    )
    
    args = parser.parse_args()
    
    # Create monitor
    monitor = DiskSpaceMonitor(args.config)
    
    if args.cleanup_report:
        # Generate cleanup suggestions
        suggestions = monitor.cleanup_suggestions()
        
        print("\nDisk Cleanup Suggestions")
        print("=" * 60)
        
        total_reclaimable = 0
        for suggestion in suggestions:
            print(f"\n{suggestion['type'].upper()}:")
            print(f"  Path: {suggestion['path']}")
            print(f"  Files: {suggestion['count']}")
            print(f"  Size: {suggestion['size_gb']} GB")
            print(f"  Command: {suggestion['action']}")
            total_reclaimable += suggestion['size_gb']
        
        print(f"\nTotal reclaimable space: {total_reclaimable:.2f} GB")
        
    elif args.daemon:
        # Run as daemon
        import time
        
        monitor.logger.info("Starting disk monitor daemon...")
        
        while True:
            try:
                monitor.check_all_paths()
                
                # Sleep for configured interval
                interval = monitor.config['monitoring']['check_interval_minutes']
                monitor.logger.info(f"Sleeping for {interval} minutes...")
                time.sleep(interval * 60)
                
            except KeyboardInterrupt:
                monitor.logger.info("Daemon stopped by user")
                break
            except Exception as e:
                monitor.logger.error(f"Error in daemon loop: {e}")
                time.sleep(60)  # Wait a minute before retrying
    
    else:
        # Run once
        monitor.check_all_paths()

if __name__ == "__main__":
    main()