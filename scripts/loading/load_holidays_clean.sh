#!/bin/bash
echo "Starting NSE holiday data load..."

DB_HOST="127.0.0.1"
DB_PORT="6274"
DB_USER="admin"
DB_PASS="HyperInteractive"
DB_NAME="heavyai"

# Base directory for SQL files relative to the script location
SQL_BASE_DIR="$(dirname "$0")/../../sql"

run_sql_file() {
    local file=$1
    local full_path="${SQL_BASE_DIR}/${file}"
    if [ ! -f "$full_path" ]; then
        echo "Error: SQL file not found at $full_path"
        exit 1
    fi
    echo "Running $full_path..."
    /opt/heavyai/bin/heavysql -s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASS -d $DB_NAME -q < "$full_path"
}

echo "Creating table..."
run_sql_file "schema/create_nse_holidays_table.sql"

echo "Loading 2019-2020 holidays..."
run_sql_file "data/holidays_2019_2020_clean.sql"

echo "Loading 2021-2022 holidays..."
run_sql_file "data/holidays_2021_2022_clean.sql"

echo "Loading 2023-2024 holidays..."
run_sql_file "data/holidays_2023_2024_clean.sql"

echo "Loading 2025-2026 holidays..."
run_sql_file "data/holidays_2025_2026_clean.sql"

echo "Verifying holiday count..."
VERIFY_SQL="${SQL_BASE_DIR}/tests/test_holiday_counts.sql"
if [ ! -f "$VERIFY_SQL" ]; then
    echo "Error: Verification SQL file not found at $VERIFY_SQL"
    # Fallback to inline query if test file moved/deleted
    echo "Running inline verification query..."
    /opt/heavyai/bin/heavysql -s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASS -d $DB_NAME --execute "SELECT EXTRACT(YEAR FROM holiday_date) AS holiday_year, COUNT(*) FROM nse_holidays GROUP BY EXTRACT(YEAR FROM holiday_date) ORDER BY EXTRACT(YEAR FROM holiday_date);"
else
    /opt/heavyai/bin/heavysql -s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASS -d $DB_NAME -q < "$VERIFY_SQL"
fi

echo "NSE holiday data load complete!" 