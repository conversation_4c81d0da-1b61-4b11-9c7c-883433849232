#!/usr/bin/env python3
"""
Test the fixed strike selection implementation by generating a configuration with both methods.
This script creates two strategies:
1. One using OTM2 strike selection
2. One using ATM with StrikeValue=2
And runs a backtest to verify they produce identical results.
"""

import os
import sys
import subprocess
from datetime import datetime
import pandas as pd
import shutil
from pathlib import Path
import json

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

def create_test_configurations():
    """Create test Excel files with both strike selection methods."""
    print("="*60)
    print("Creating Test Configurations")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Create a directory for test files
    test_dir = Path("test_strike_selection")
    test_dir.mkdir(exist_ok=True)
    
    # Strategy 1: Using OTM2
    strategy1_general_data = {
        'StrategyName': ['Strike_Test_OTM2'],
        'MoveSlToCost': ['no'],
        'Underlying': ['SPOT'],
        'Index': ['NIFTY'],
        'Weekdays': ['1,2,3,4,5'],
        'DTE': [0],
        'StartTime': [91600],
        'LastEntryTime': [120000],
        'EndTime': [120000],
        'Enabled': ['YES']
    }
    
    strategy1_leg_data = {
        'StrategyName': ['Strike_Test_OTM2', 'Strike_Test_OTM2'],
        'IsIdle': ['no', 'no'],
        'LegID': [1, 2],
        'Instrument': ['call', 'put'],
        'Transaction': ['buy', 'buy'],
        'Expiry': ['current', 'current'],
        'StrikeMethod': ['otm2', 'otm2'],
        'StrikeValue': [0, 0],
        'Lots': [1, 1],
        'SLType': ['percentage', 'percentage'],
        'SLValue': [50, 50],
        'TGTType': ['percentage', 'percentage'],
        'TGTValue': [100, 100]
    }
    
    # Convert to DataFrames
    strategy1_general_df = pd.DataFrame(strategy1_general_data)
    strategy1_leg_df = pd.DataFrame(strategy1_leg_data)
    
    strategy1_path = test_dir / "strategy_otm2.xlsx"
    try:
        with pd.ExcelWriter(strategy1_path, engine='openpyxl') as writer:
            strategy1_general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            strategy1_leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
    except Exception as e:
        print(f"Error creating OTM2 strategy file: {e}")
        
    print(f"✓ Created OTM2 strategy: {strategy1_path}")
    
    # Strategy 2: Using ATM with StrikeValue=2
    strategy2_general_data = {
        'StrategyName': ['Strike_Test_ATM_Value2'],
        'MoveSlToCost': ['no'],
        'Underlying': ['SPOT'],
        'Index': ['NIFTY'],
        'Weekdays': ['1,2,3,4,5'],
        'DTE': [0],
        'StartTime': [91600],
        'LastEntryTime': [120000],
        'EndTime': [120000],
        'Enabled': ['YES']
    }
    
    strategy2_leg_data = {
        'StrategyName': ['Strike_Test_ATM_Value2', 'Strike_Test_ATM_Value2'],
        'IsIdle': ['no', 'no'],
        'LegID': [1, 2],
        'Instrument': ['call', 'put'],
        'Transaction': ['buy', 'buy'],
        'Expiry': ['current', 'current'],
        'StrikeMethod': ['atm', 'atm'],
        'StrikeValue': [2, 2],
        'Lots': [1, 1],
        'SLType': ['percentage', 'percentage'],
        'SLValue': [50, 50],
        'TGTType': ['percentage', 'percentage'],
        'TGTValue': [100, 100]
    }
    
    # Convert to DataFrames
    strategy2_general_df = pd.DataFrame(strategy2_general_data)
    strategy2_leg_df = pd.DataFrame(strategy2_leg_data)
    
    strategy2_path = test_dir / "strategy_atm_value2.xlsx"
    try:
        with pd.ExcelWriter(strategy2_path, engine='openpyxl') as writer:
            strategy2_general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            strategy2_leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
    except Exception as e:
        print(f"Error creating ATM with value=2 strategy file: {e}")
        
    print(f"✓ Created ATM with value=2 strategy: {strategy2_path}")
    
    # Portfolio file pointing to both strategies
    portfolio_data = {
        'StartDate': ['01_05_2025'],
        'EndDate': ['01_05_2025'],
        'IsTickBT': ['no'],
        'Enabled': ['YES', 'YES'],
        'PortfolioName': ['OTM2_TEST', 'ATM_VALUE2_TEST'],
        'Multiplier': [1, 1],
        'SlippagePercent': [0.1, 0.1]
    }
    
    strategy_data = {
        'Enabled': ['YES', 'YES'],
        'PortfolioName': ['OTM2_TEST', 'ATM_VALUE2_TEST'],
        'StrategyType': ['Tbs', 'Tbs'],
        'StrategyExcelFilePath': [str(strategy1_path.absolute()), str(strategy2_path.absolute())]
    }
    
    # Convert to DataFrames
    portfolio_df = pd.DataFrame(portfolio_data)
    strategy_df = pd.DataFrame(strategy_data)
    
    portfolio_path = test_dir / "portfolio_strike_test.xlsx"
    try:
        with pd.ExcelWriter(portfolio_path, engine='openpyxl') as writer:
            portfolio_df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
            strategy_df.to_excel(writer, sheet_name='StrategySetting', index=False)
    except Exception as e:
        print(f"Error creating portfolio file: {e}")
        
    print(f"✓ Created portfolio file: {portfolio_path}")
    
    return portfolio_path

def run_backtest(portfolio_path):
    """Run the backtest with the test configurations."""
    print("\n" + "="*60)
    print("Running Backtest with Both Configurations")
    print("="*60)
    
    # Set up environment
    env = os.environ.copy()
    env['PYTHONPATH'] = os.path.abspath('.')
    
    # Add debug logging
    env['LOG_LEVEL'] = 'DEBUG'
    
    output_path = Path("test_strike_selection/test_output.xlsx")
    
    cmd = [
        sys.executable,
        "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", str(portfolio_path),
        "--output-path", str(output_path),
        "--start-date", "20250501",
        "--end-date", "20250501",
        "--verbose"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    print("\nRunning...")
    
    result = subprocess.run(
        cmd,
        env=env,
        capture_output=True,
        text=True,
        timeout=180
    )
    
    print(f"\nReturn code: {result.returncode}")
    
    if result.stdout:
        print("\nSTDOUT:")
        print("="*40)
        print(result.stdout[-2000:])  # Last 2000 chars
        
    if result.stderr:
        print("\nSTDERR:")
        print("="*40)
        print(result.stderr[-2000:])  # Last 2000 chars
    
    return output_path

def analyze_results(output_path):
    """Analyze the backtest results to verify both methods produce the same output."""
    print("\n" + "="*60)
    print("Analyzing Results")
    print("="*60)
    
    if not output_path.exists():
        print(f"❌ Output file not found: {output_path}")
        return
    
    try:
        # Read the output file
        xl = pd.ExcelFile(output_path)
        print(f"Output file sheets: {xl.sheet_names}")
        
        # Find result sheets for both strategies
        otm2_sheet = None
        atm_value2_sheet = None
        
        for sheet in xl.sheet_names:
            if 'OTM2_TEST' in sheet and 'Trans' in sheet:
                otm2_sheet = sheet
            elif 'ATM_VALUE2_TEST' in sheet and 'Trans' in sheet:
                atm_value2_sheet = sheet
        
        if not otm2_sheet:
            print("❌ OTM2 results sheet not found")
            return
            
        if not atm_value2_sheet:
            print("❌ ATM with value=2 results sheet not found")
            return
        
        # Read the transaction sheets
        otm2_df = pd.read_excel(xl, sheet_name=otm2_sheet)
        atm_value2_df = pd.read_excel(xl, sheet_name=atm_value2_sheet)
        
        print(f"\nOTM2 strategy results ({len(otm2_df)} trades):")
        print(otm2_df.head())
        
        print(f"\nATM with value=2 strategy results ({len(atm_value2_df)} trades):")
        print(atm_value2_df.head())
        
        # Compare the strike prices
        if 'Strike' in otm2_df.columns and 'Strike' in atm_value2_df.columns:
            otm2_strikes = set(otm2_df['Strike'].values)
            atm_value2_strikes = set(atm_value2_df['Strike'].values)
            
            print("\nStrike comparison:")
            print(f"OTM2 strikes: {sorted(otm2_strikes)}")
            print(f"ATM with value=2 strikes: {sorted(atm_value2_strikes)}")
            
            if otm2_strikes == atm_value2_strikes:
                print("✅ Both methods selected the same strikes!")
            else:
                print("❌ Strike selection differs between methods")
        
        # Compare P&L
        pnl_col = None
        for col in otm2_df.columns:
            if 'PNL' in col.upper() or 'P&L' in col.upper():
                pnl_col = col
                break
        
        if pnl_col:
            otm2_pnl = otm2_df[pnl_col].sum()
            atm_value2_pnl = atm_value2_df[pnl_col].sum()
            
            print("\nP&L comparison:")
            print(f"OTM2 total P&L: {otm2_pnl:.2f}")
            print(f"ATM with value=2 total P&L: {atm_value2_pnl:.2f}")
            
            if abs(otm2_pnl - atm_value2_pnl) < 0.01:
                print("✅ Both methods produced the same P&L!")
            else:
                print("❌ P&L differs between methods")
        
    except Exception as e:
        print(f"Error analyzing results: {e}")

def run_tests():
    """Run the complete test."""
    print("="*80)
    print("Strike Selection Fix Test")
    print(f"Time: {datetime.now()}")
    print("="*80)
    
    try:
        # Unit test first
        print("\nRunning unit tests...")
        env = os.environ.copy()
        env['PYTHONPATH'] = os.path.abspath('.')
        unit_test_cmd = [
            sys.executable,
            "-m", "unittest", "bt.backtester_stable.BTRUN.tests.test_strike_handling"
        ]
        result = subprocess.run(unit_test_cmd, capture_output=True, text=True, env=env)
        print(result.stdout)
        if result.returncode != 0:
            print(f"❌ Unit tests failed: {result.stderr}")
            return
        
        # Create test configurations
        portfolio_path = create_test_configurations()
        
        # Run backtest
        output_path = run_backtest(portfolio_path)
        
        # Analyze results
        analyze_results(output_path)
        
        print("\n" + "="*80)
        print("Test Complete")
        print("="*80)
        
    except Exception as e:
        print(f"Error running tests: {e}")

if __name__ == "__main__":
    run_tests() 