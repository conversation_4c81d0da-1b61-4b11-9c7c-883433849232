#!/usr/bin/env python3
"""Fix missing columns issue in legacy output generation."""

def fix_missing_columns():
    """Fix the column ordering issue in prepareOutputFile."""
    
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    
    # Read the file
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Find and replace the problematic line
    old_line = "transactionDf = transactionDf[colOrder].rename(columns=Util.COLUMN_RENAME_MAPPING)"
    
    # New code that only selects columns that exist
    new_code = """# Only select columns that exist in the dataframe
                colsToUse = [col for col in colOrder if col in transactionDf.columns]
                transactionDf = transactionDf[colsToUse].rename(columns=Util.COLUMN_RENAME_MAPPING)"""
    
    # Replace the line
    content = content.replace(old_line, new_code)
    
    # Write back
    with open(util_path, 'w') as f:
        f.write(content)
    
    print("✅ Fixed missing columns issue in Util.py")

if __name__ == "__main__":
    fix_missing_columns()
    print("\nYou can now run the legacy backtester again") 