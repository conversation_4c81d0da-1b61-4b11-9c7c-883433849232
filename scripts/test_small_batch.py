#!/usr/bin/env python3
"""
Test copying a small batch of NIFTY data
"""

import pandas as pd
from sqlalchemy import create_engine, text
import time

# Connection URLs
REMOTE_URL = 'mysql+pymysql://mahesh:mahesh_123@************:3306/historicaldb'
LOCAL_URL = 'mysql+pymysql://mahesh:mahesh_123@localhost:3306/historicaldb'

def test_small_copy():
    """Test with just 1000 rows"""
    print("Testing NIFTY data copy with 1000 rows...")
    
    try:
        # Create engines
        remote_engine = create_engine(REMOTE_URL)
        local_engine = create_engine(LOCAL_URL)
        
        # Test with nifty_call table
        table_name = 'nifty_call'
        
        # Get table structure
        with remote_engine.connect() as conn:
            result = conn.execute(text(f"SHOW CREATE TABLE {table_name}"))
            create_stmt = result.fetchone()[1]
        
        # Create table locally
        with local_engine.connect() as conn:
            conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
            conn.execute(text(create_stmt))
            conn.commit()
        print("✓ Table structure created")
        
        # Copy 1000 rows
        query = f"SELECT * FROM {table_name} WHERE date >= 220101 LIMIT 1000"
        print(f"Reading data...")
        df = pd.read_sql_query(text(query), remote_engine)
        print(f"✓ Read {len(df)} rows")
        
        # Show sample
        print("\nSample data:")
        print(df.head(3))
        
        # Write to local
        print("\nWriting to local database...")
        start_time = time.time()
        df.to_sql(table_name, local_engine, if_exists='append', index=False, method='multi', chunksize=100)
        print(f"✓ Written in {time.time() - start_time:.2f} seconds")
        
        # Verify
        with local_engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) as count FROM {table_name}"))
            count = result.scalar()
            
            result = conn.execute(text(f"SELECT MIN(date) as min_date, MAX(date) as max_date FROM {table_name}"))
            row = result.fetchone()
        
        print(f"\n✓ Verification successful!")
        print(f"  Rows copied: {count}")
        print(f"  Date range: {row.min_date} to {row.max_date}")
        
        # Dispose
        remote_engine.dispose()
        local_engine.dispose()
        
        print("\nTest completed successfully! Ready for full copy.")
        
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_small_copy() 