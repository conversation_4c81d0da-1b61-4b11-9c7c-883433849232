#!/usr/bin/env python3
"""Investigate duplicate strategy sheets in HeavyDB output."""

import os
import sys
import pandas as pd

# Add project paths
sys.path.append('/srv/samba/shared')

def investigate_sheets():
    """Investigate sheet naming in HeavyDB output."""
    
    output_file = "heavydb_formatted_test_output.xlsx"
    
    if not os.path.exists(output_file):
        print(f"File not found: {output_file}")
        return
    
    xl = pd.ExcelFile(output_file)
    
    print("="*80)
    print("SHEET INVESTIGATION")
    print("="*80)
    
    print(f"\nTotal sheets: {len(xl.sheet_names)}")
    print("\nAll sheet names:")
    for i, sheet in enumerate(xl.sheet_names):
        print(f"  {i+1}. '{sheet}' (length: {len(sheet)})")
    
    # Find strategy-related sheets
    print("\n\nStrategy-related sheets:")
    strategy_sheets = [s for s in xl.sheet_names if any(x in s for x in ['ATM', 'OTM', 'ITM'])]
    
    # Group by suffix
    trans_sheets = [s for s in strategy_sheets if 'Trans' in s]
    daywise_sheets = [s for s in strategy_sheets if 'DayWise' in s]
    monthwise_sheets = [s for s in strategy_sheets if 'MonthWise' in s]
    marginpct_sheets = [s for s in strategy_sheets if 'MarginPct' in s]
    
    print(f"\n  Transaction sheets: {trans_sheets}")
    print(f"  DayWise sheets: {daywise_sheets}")
    print(f"  MonthWise sheets: {monthwise_sheets}")
    print(f"  MarginPct sheets: {marginpct_sheets}")
    
    # Check if there are duplicate data
    if len(trans_sheets) > 1:
        print("\n\nComparing transaction sheets:")
        for sheet in trans_sheets:
            df = pd.read_excel(output_file, sheet_name=sheet)
            print(f"\n  {sheet}:")
            print(f"    Rows: {len(df)}")
            print(f"    Columns: {list(df.columns)[:5]}...")  # First 5 columns
            if not df.empty and 'Strategy Name' in df.columns:
                print(f"    Strategy values: {df['Strategy Name'].unique()}")
    
    # Check transaction_dfs keys
    print("\n\nDebugging transaction_dfs keys:")
    print("This issue might be caused by:")
    print("  1. Multiple strategy names in transaction_dfs dict")
    print("  2. Portfolio model having duplicate strategies")
    print("  3. Strategy name formatting differences")

def check_metrics_duplicates():
    """Check why metrics has 50 rows instead of 25."""
    
    output_file = "heavydb_formatted_test_output.xlsx"
    
    if not os.path.exists(output_file):
        return
    
    print("\n\n" + "="*80)
    print("METRICS INVESTIGATION")
    print("="*80)
    
    df = pd.read_excel(output_file, sheet_name="Metrics")
    
    print(f"\nTotal metrics rows: {len(df)}")
    
    if 'Strategy' in df.columns:
        print(f"Unique strategies: {df['Strategy'].unique()}")
        print("\nStrategy counts:")
        print(df['Strategy'].value_counts())
    
    if 'Particulars' in df.columns:
        print(f"\nUnique metrics: {len(df['Particulars'].unique())}")
        print("\nFirst few metrics:")
        print(df['Particulars'].unique()[:5])
    
    # Check for exact duplicates
    duplicates = df.duplicated(subset=['Strategy', 'Particulars'] if 'Strategy' in df.columns and 'Particulars' in df.columns else None)
    print(f"\nExact duplicate rows: {duplicates.sum()}")
    
    # Check if there are multiple entries per strategy
    if 'Strategy' in df.columns and 'Particulars' in df.columns:
        print("\n\nChecking for duplicate Strategy-Particulars combinations:")
        dup_df = df[df.duplicated(subset=['Strategy', 'Particulars'], keep=False)]
        if not dup_df.empty:
            print(f"Found {len(dup_df)} duplicate entries:")
            print(dup_df.head(10))

def main():
    """Main execution."""
    investigate_sheets()
    check_metrics_duplicates()
    
    print("\n\n" + "="*80)
    print("LIKELY ISSUES")
    print("="*80)
    print("\n1. Strategy sheets are being created from both:")
    print("   - transaction_dfs dictionary keys")
    print("   - portfolio_model.strategies list")
    print("   This might create duplicates if the names don't match exactly")
    print("\n2. Metrics has 50 rows because:")
    print("   - 25 metrics for 'Combined' strategy")
    print("   - 25 metrics for the actual strategy")
    print("   Legacy might only show portfolio-level metrics")

if __name__ == "__main__":
    main() 