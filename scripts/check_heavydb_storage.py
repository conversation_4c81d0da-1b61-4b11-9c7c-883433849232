#!/usr/bin/env python3
"""
HeavyDB Storage Checker
Verifies if HeavyDB is using optimal NVMe storage and provides recommendations
"""

import os
import subprocess
import psutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

class HeavyDBStorageChecker:
    def __init__(self):
        self.config_paths = [
            "/var/lib/heavyai/heavy.conf",
            "/var/lib/heavyai/heavy.conf.nvme",
            "/opt/heavyai/heavy.conf"
        ]
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "storage_optimal": False,
            "recommendations": [],
            "current_config": {},
            "disk_info": {}
        }
    
    def run_command(self, cmd: List[str]) -> Tuple[int, str, str]:
        """Execute shell command and return exit code, stdout, stderr"""
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate()
            return process.returncode, stdout, stderr
        except Exception as e:
            return -1, "", str(e)
    
    def check_nvme_drives(self) -> Dict[str, Dict]:
        """Identify all NVMe drives in the system"""
        nvme_drives = {}
        
        # Get disk information using lsblk
        code, stdout, _ = self.run_command(["lsblk", "-J", "-o", "NAME,TYPE,SIZE,ROTA,MOUNTPOINT,FSTYPE"])
        
        if code == 0:
            try:
                disk_data = json.loads(stdout)
                for device in disk_data.get("blockdevices", []):
                    if device["name"].startswith("nvme"):
                        nvme_drives[device["name"]] = {
                            "size": device["size"],
                            "mountpoint": device.get("mountpoint", ""),
                            "type": "NVMe SSD",
                            "rotational": False
                        }
                        # Check children (partitions)
                        for child in device.get("children", []):
                            if child.get("mountpoint"):
                                nvme_drives[child["name"]] = {
                                    "size": child["size"],
                                    "mountpoint": child["mountpoint"],
                                    "type": "NVMe SSD Partition",
                                    "rotational": False,
                                    "parent": device["name"]
                                }
            except json.JSONDecodeError:
                pass
        
        return nvme_drives
    
    def check_heavydb_config(self) -> Dict[str, str]:
        """Read HeavyDB configuration to find data directory"""
        config = {}
        
        for config_path in self.config_paths:
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#'):
                                if '=' in line:
                                    key, value = line.split('=', 1)
                                    key = key.strip()
                                    value = value.strip().strip('"').strip("'")
                                    config[key] = value
                    
                    config['config_file'] = config_path
                    break
                except Exception as e:
                    continue
        
        return config
    
    def get_mount_point(self, path: str) -> str:
        """Get the mount point for a given path"""
        path = Path(path).resolve()
        while not path.is_mount():
            path = path.parent
        return str(path)
    
    def check_disk_type(self, mount_point: str) -> Dict[str, any]:
        """Check if mount point is on NVMe or regular disk"""
        disk_info = {
            "mount_point": mount_point,
            "is_nvme": False,
            "device": "",
            "disk_type": "Unknown"
        }
        
        # Get device for mount point
        for partition in psutil.disk_partitions():
            if partition.mountpoint == mount_point:
                device = partition.device
                disk_info["device"] = device
                
                # Check if device is NVMe
                if "nvme" in device:
                    disk_info["is_nvme"] = True
                    disk_info["disk_type"] = "NVMe SSD"
                else:
                    # Check if rotational (HDD) or SSD
                    device_name = device.split('/')[-1].rstrip('0123456789')
                    rotational_file = f"/sys/block/{device_name}/queue/rotational"
                    
                    if os.path.exists(rotational_file):
                        with open(rotational_file, 'r') as f:
                            is_rotational = f.read().strip() == "1"
                            disk_info["disk_type"] = "HDD" if is_rotational else "SSD"
                
                break
        
        # Get disk usage
        usage = psutil.disk_usage(mount_point)
        disk_info["total_gb"] = round(usage.total / (1024**3), 2)
        disk_info["used_gb"] = round(usage.used / (1024**3), 2)
        disk_info["free_gb"] = round(usage.free / (1024**3), 2)
        disk_info["percent_used"] = usage.percent
        
        return disk_info
    
    def analyze_storage(self):
        """Main analysis function"""
        print("🔍 Checking HeavyDB Storage Configuration...")
        
        # 1. Find HeavyDB configuration
        config = self.check_heavydb_config()
        self.results["current_config"] = config
        
        if not config:
            self.results["recommendations"].append({
                "severity": "CRITICAL",
                "message": "HeavyDB configuration file not found",
                "action": "Verify HeavyDB installation"
            })
            return
        
        print(f"✓ Found HeavyDB config: {config.get('config_file', 'Unknown')}")
        
        # 2. Get data directory
        data_dir = config.get("data", "/var/lib/heavyai/storage")
        print(f"✓ Data directory: {data_dir}")
        
        # 3. Check if data directory exists
        if not os.path.exists(data_dir):
            self.results["recommendations"].append({
                "severity": "CRITICAL",
                "message": f"Data directory {data_dir} does not exist",
                "action": "Create directory or update configuration"
            })
            return
        
        # 4. Get mount point and disk info
        mount_point = self.get_mount_point(data_dir)
        disk_info = self.check_disk_type(mount_point)
        self.results["disk_info"] = disk_info
        
        print(f"✓ Mount point: {mount_point}")
        print(f"✓ Disk type: {disk_info['disk_type']}")
        print(f"✓ Storage: {disk_info['used_gb']} GB used of {disk_info['total_gb']} GB ({disk_info['percent_used']}%)")
        
        # 5. Check if on NVMe
        if disk_info["is_nvme"]:
            self.results["storage_optimal"] = True
            print("✅ HeavyDB is already on NVMe storage - OPTIMAL!")
        else:
            print("⚠️  HeavyDB is NOT on NVMe storage")
            
            # Find available NVMe drives
            nvme_drives = self.check_nvme_drives()
            
            if nvme_drives:
                print("\n📍 Available NVMe drives:")
                for drive, info in nvme_drives.items():
                    if info.get("mountpoint"):
                        print(f"   - {drive}: {info['size']} mounted at {info['mountpoint']}")
                
                # Recommend migration
                for drive, info in nvme_drives.items():
                    if info.get("mountpoint") and info["mountpoint"] != "/":
                        mount_usage = psutil.disk_usage(info["mountpoint"])
                        free_gb = mount_usage.free / (1024**3)
                        
                        if free_gb > disk_info["used_gb"] * 1.5:  # Need 1.5x space for safe migration
                            self.results["recommendations"].append({
                                "severity": "HIGH",
                                "message": f"Migrate HeavyDB to NVMe drive",
                                "action": f"Move data to {info['mountpoint']} (has {free_gb:.1f} GB free)",
                                "benefit": "5-10x performance improvement for ETL operations"
                            })
                            break
            else:
                self.results["recommendations"].append({
                    "severity": "MEDIUM",
                    "message": "No NVMe drives found",
                    "action": "Consider adding NVMe storage for optimal performance",
                    "benefit": "Significantly faster data loading and query performance"
                })
        
        # 6. Check disk space
        if disk_info["percent_used"] > 90:
            self.results["recommendations"].append({
                "severity": "CRITICAL",
                "message": f"Disk space critical: {disk_info['percent_used']}% used",
                "action": "Immediately free up space or add storage",
                "risk": "ETL operations will fail when disk is full"
            })
        elif disk_info["percent_used"] > 80:
            self.results["recommendations"].append({
                "severity": "HIGH",
                "message": f"Disk space warning: {disk_info['percent_used']}% used",
                "action": "Plan for additional storage soon",
                "risk": "May run out of space during large ETL operations"
            })
        elif disk_info["percent_used"] > 70:
            self.results["recommendations"].append({
                "severity": "MEDIUM",
                "message": f"Monitor disk space: {disk_info['percent_used']}% used",
                "action": "Set up automated monitoring and alerts",
                "preventive": "Avoid unexpected failures"
            })
        
        # 7. Check other optimizations
        self.check_other_optimizations(config)
    
    def check_other_optimizations(self, config: Dict[str, str]):
        """Check for other storage-related optimizations"""
        
        # Check allowed-import-paths
        import_paths = config.get("allowed-import-paths", "[]")
        if "/srv/samba/shared" not in import_paths:
            self.results["recommendations"].append({
                "severity": "MEDIUM",
                "message": "Import path not optimized",
                "action": "Add /srv/samba/shared to allowed-import-paths",
                "benefit": "Direct COPY FROM without file copying"
            })
        
        # Check fragment size
        # Note: This would require checking table definitions
        
    def generate_report(self) -> str:
        """Generate a detailed report"""
        report = []
        report.append("=" * 80)
        report.append("HeavyDB Storage Analysis Report")
        report.append("=" * 80)
        report.append(f"Generated: {self.results['timestamp']}")
        report.append("")
        
        # Current Configuration
        report.append("CURRENT CONFIGURATION:")
        report.append("-" * 40)
        config = self.results['current_config']
        report.append(f"Config File: {config.get('config_file', 'Not found')}")
        report.append(f"Data Directory: {config.get('data', 'Not configured')}")
        report.append("")
        
        # Disk Information
        report.append("STORAGE INFORMATION:")
        report.append("-" * 40)
        disk = self.results['disk_info']
        if disk:
            report.append(f"Mount Point: {disk['mount_point']}")
            report.append(f"Device: {disk['device']}")
            report.append(f"Type: {disk['disk_type']}")
            report.append(f"Total Size: {disk['total_gb']} GB")
            report.append(f"Used: {disk['used_gb']} GB ({disk['percent_used']}%)")
            report.append(f"Free: {disk['free_gb']} GB")
            report.append(f"On NVMe: {'YES ✅' if disk['is_nvme'] else 'NO ⚠️'}")
        report.append("")
        
        # Overall Status
        report.append("OVERALL STATUS:")
        report.append("-" * 40)
        if self.results['storage_optimal']:
            report.append("✅ Storage configuration is OPTIMAL")
        else:
            report.append("⚠️  Storage configuration needs OPTIMIZATION")
        report.append("")
        
        # Recommendations
        if self.results['recommendations']:
            report.append("RECOMMENDATIONS:")
            report.append("-" * 40)
            
            # Sort by severity
            severity_order = {"CRITICAL": 0, "HIGH": 1, "MEDIUM": 2, "LOW": 3}
            sorted_recs = sorted(
                self.results['recommendations'], 
                key=lambda x: severity_order.get(x['severity'], 999)
            )
            
            for i, rec in enumerate(sorted_recs, 1):
                report.append(f"{i}. [{rec['severity']}] {rec['message']}")
                report.append(f"   Action: {rec['action']}")
                if 'benefit' in rec:
                    report.append(f"   Benefit: {rec['benefit']}")
                if 'risk' in rec:
                    report.append(f"   Risk: {rec['risk']}")
                report.append("")
        
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def save_results(self, output_file: str = "/srv/samba/shared/logs/heavydb_storage_check.json"):
        """Save results to JSON file"""
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"\n📄 Results saved to: {output_file}")

def main():
    checker = HeavyDBStorageChecker()
    checker.analyze_storage()
    
    report = checker.generate_report()
    print("\n" + report)
    
    # Save results
    checker.save_results()
    
    # Return exit code based on severity
    if any(r['severity'] == 'CRITICAL' for r in checker.results['recommendations']):
        return 2
    elif any(r['severity'] == 'HIGH' for r in checker.results['recommendations']):
        return 1
    else:
        return 0

if __name__ == "__main__":
    exit(main())