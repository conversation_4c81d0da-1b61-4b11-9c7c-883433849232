#!/usr/bin/env python3
"""
Examine TBS input files to understand structure before running backtester
"""

import pandas as pd
import sys

# File paths
portfolio_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
tbs_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"
golden_output = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"

def examine_excel_file(filepath, name):
    print(f"\n{'='*60}")
    print(f"Examining {name}: {filepath}")
    print(f"{'='*60}")
    
    try:
        # Get all sheet names
        xl_file = pd.ExcelFile(filepath)
        print(f"\nSheets: {xl_file.sheet_names}")
        
        # Read each sheet
        for sheet_name in xl_file.sheet_names:
            print(f"\n--- Sheet: {sheet_name} ---")
            df = pd.read_excel(filepath, sheet_name=sheet_name)
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            # Show first few rows
            if not df.empty:
                print(f"\nFirst few rows:")
                print(df.head(3))
                
                # For specific sheets, show important configuration
                if sheet_name == "PortfolioSetting":
                    print(f"\nPortfolio Settings:")
                    for idx, row in df.iterrows():
                        if row.get('Enabled', 'NO').upper() == 'YES':
                            print(f"  - {row.get('PortfolioName', 'N/A')}: {row.get('StartDate', 'N/A')} to {row.get('EndDate', 'N/A')}")
                            
                elif sheet_name == "StrategySetting":
                    print(f"\nStrategy Settings:")
                    for idx, row in df.iterrows():
                        if row.get('Enabled', 'NO').upper() == 'YES':
                            print(f"  - Portfolio: {row.get('PortfolioName', 'N/A')}, Type: {row.get('StrategyType', 'N/A')}")
                            print(f"    Excel: {row.get('StrategyExcelFilePath', 'N/A')}")
                            
                elif sheet_name == "GeneralParameter":
                    print(f"\nActive Strategies:")
                    for idx, row in df.iterrows():
                        print(f"  - {row.get('StrategyName', 'N/A')}: DTE={row.get('DTE', 'N/A')}, StartTime={row.get('StartTime', 'N/A')}, EndTime={row.get('EndTime', 'N/A')}")
                        
    except Exception as e:
        print(f"Error reading {name}: {e}")

# Main execution
if __name__ == "__main__":
    # Examine portfolio file
    examine_excel_file(portfolio_file, "Portfolio File")
    
    # Examine TBS strategy file
    examine_excel_file(tbs_file, "TBS Strategy File")
    
    # Check if golden output exists
    print(f"\n{'='*60}")
    print(f"Checking Golden Output File")
    print(f"{'='*60}")
    
    try:
        # Just check if we can read it
        xl_file = pd.ExcelFile(golden_output)
        print(f"Golden output exists with sheets: {xl_file.sheet_names}")
        
        # Read TradeBook to understand expected output
        if 'TradeBook' in xl_file.sheet_names:
            df = pd.read_excel(golden_output, sheet_name='TradeBook')
            print(f"\nTradeBook shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            print(f"\nSample trades:")
            print(df[['entry_date', 'entry_time', 'exit_date', 'exit_time', 'symbol', 'strike', 'instrument_type', 'side', 'pnl']].head())
            
            # Summary statistics
            print(f"\nSummary:")
            print(f"Total trades: {len(df)}")
            print(f"Date range: {df['entry_date'].min()} to {df['entry_date'].max()}")
            print(f"Total P&L: {df['pnl'].sum()}")
            
    except Exception as e:
        print(f"Error reading golden output: {e}") 