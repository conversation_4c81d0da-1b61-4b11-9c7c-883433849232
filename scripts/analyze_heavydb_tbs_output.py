#!/usr/bin/env python3
"""
Analyze HeavyDB TBS output for April 3, 2025
Compare against expected results from previous tests
"""

import pandas as pd
import json
import os

def analyze_excel_output(file_path):
    """Analyze the Excel output from HeavyDB backtester"""
    print("="*70)
    print("HeavyDB TBS Output Analysis")
    print("="*70)
    print(f"File: {file_path}")
    
    # Load Excel file
    xl = pd.ExcelFile(file_path)
    print(f"\nSheets: {xl.sheet_names}")
    
    # Analyze PORTFOLIO Trans sheet
    if 'PORTFOLIO Trans' in xl.sheet_names:
        print("\n" + "="*50)
        print("PORTFOLIO Trans Sheet Analysis")
        print("="*50)
        
        df = pd.read_excel(file_path, sheet_name='PORTFOLIO Trans')
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        # Count trades
        trades = df[df['symbol'].notna()]
        print(f"\nTotal trades: {len(trades)}")
        
        # Show trade details
        print("\nTrade Details:")
        for idx, row in trades.iterrows():
            print(f"\nTrade {idx+1}:")
            print(f"  Symbol: {row['symbol']}")
            print(f"  Side: {row['Side']}")
            print(f"  Strike: {row['Strike']}")
            print(f"  Entry Time: {row['Entry Time']}")
            print(f"  Exit Time: {row['Exit Time']}")
            print(f"  Entry Price: {row['Entry Price']}")
            print(f"  Exit Price: {row['Exit Price']}")
            print(f"  P&L: {row['PnL']}")
        
        # Total P&L
        total_pnl = trades['PnL'].sum()
        print(f"\nTotal P&L: {total_pnl}")
    
    # Analyze metrics sheet
    if 'Metrics' in xl.sheet_names:
        print("\n" + "="*50)
        print("Metrics Sheet Analysis")
        print("="*50)
        
        df = pd.read_excel(file_path, sheet_name='Metrics')
        print(f"Shape: {df.shape}")
        
        # Show metrics
        print("\nMetrics Summary:")
        print(df.head(10))
    
    # Check if all expected sheets are present
    expected_sheets = [
        'PortfolioParameter', 'GeneralParameter', 'LegParameter',
        'PORTFOLIO Trans', 'PORTFOLIO Results',
        'Metrics', 'Max Profit and Loss'
    ]
    
    print("\n" + "="*50)
    print("Sheet Availability Check")
    print("="*50)
    
    for sheet in expected_sheets:
        status = "✓" if any(sheet in s for s in xl.sheet_names) else "✗"
        print(f"{status} {sheet}")

def analyze_json_output(file_path):
    """Analyze the JSON output from HeavyDB backtester"""
    print("\n" + "="*70)
    print("JSON Output Analysis")
    print("="*70)
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    # Show structure
    print("JSON Structure:")
    for key in data.keys():
        print(f"  - {key}")
    
    # Analyze trades
    if 'trades' in data:
        trades = data['trades']
        print(f"\nTotal trades in JSON: {len(trades)}")
        
        for i, trade in enumerate(trades[:4]):  # Show first 4
            print(f"\nTrade {i+1}:")
            print(f"  Symbol: {trade.get('symbol')}")
            print(f"  Entry: {trade.get('entry_datetime')}")
            print(f"  Exit: {trade.get('exit_datetime')}")
            print(f"  P&L: {trade.get('overall_pnl')}")

def verify_expected_results():
    """Verify against expected results"""
    print("\n" + "="*70)
    print("Verification Against Expected Results")
    print("="*70)
    
    print("\nExpected Results (from previous tests):")
    print("- Date: April 3, 2025 (DTE=0 expiry day)")
    print("- ATM Strike: 23200")
    print("- Trades: 4")
    print("  1. CE SELL @ 23200 (ATM)")
    print("  2. PE SELL @ 23200 (ATM)")
    print("  3. CE BUY @ 23300 (OTM2)")
    print("  4. PE BUY @ 23100 (OTM2)")
    print("- Total P&L: Should be calculated based on:")
    print("  - Entry at 09:16:00")
    print("  - Exit at 12:00:00")
    print("  - CE prices: 52.00 (23200), 14.85 (23300)")
    print("  - PE prices: 63.00 (23200), 33.79 (23100)")

def main():
    excel_file = 'output/tbs_apr3_heavydb.xlsx'
    json_file = 'output/tbs_apr3_heavydb.json'
    
    if os.path.exists(excel_file):
        analyze_excel_output(excel_file)
    else:
        print(f"Excel file not found: {excel_file}")
    
    if os.path.exists(json_file):
        analyze_json_output(json_file)
    else:
        print(f"JSON file not found: {json_file}")
    
    verify_expected_results()
    
    print("\n" + "="*70)
    print("Analysis Complete!")
    print("="*70)

if __name__ == "__main__":
    main() 