#!/usr/bin/env python3
"""Trace the backtest execution to find where trades are being lost."""

import sys
import json
import subprocess
from datetime import datetime

# Run the backtest with minimal configuration
print("=== Running Backtest with Tracing ===")

# Prepare command
cmd = [
    "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
    "--legacy-excel",
    "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
    "--output-path", "trace_output.xlsx",
    "--start-date", "20250401",
    "--end-date", "20250401"
]

# Set environment variables for debugging
env = {
    "PYTHONPATH": "/srv/samba/shared",
    "BACKTESTER_DEBUG": "1"  # Enable any debug flags if available
}

print(f"Command: {' '.join(cmd)}")
print(f"Start time: {datetime.now()}")

# Run the command and capture output
try:
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        cwd="/srv/samba/shared",
        env={**subprocess.os.environ, **env}
    )
    
    print("\n=== STDOUT ===")
    print(result.stdout)
    
    print("\n=== STDERR ===")
    print(result.stderr)
    
    print(f"\nReturn code: {result.returncode}")
    print(f"End time: {datetime.now()}")
    
    # Check if output file was created
    import os
    if os.path.exists("trace_output.xlsx"):
        print("\nOutput file created successfully")
        
        # Read and analyze the output
        import pandas as pd
        df = pd.read_excel("trace_output.xlsx")
        print(f"\nOutput rows: {len(df)}")
        print(f"Columns: {list(df.columns)[:10]}...")
        
        # Check for leg_id column
        if 'leg_id' in df.columns:
            print(f"Unique leg IDs in output: {df['leg_id'].unique()}")
        
        # Show first few rows
        print("\nFirst 5 rows:")
        print(df.head())
    else:
        print("\nERROR: Output file not created!")
        
except Exception as e:
    print(f"\nException running backtest: {e}")
    import traceback
    traceback.print_exc() 