#!/usr/bin/env python3
"""
TBS (Time-Based Strategy) parity testing script.

This script runs both legacy and GPU backtester on the same TBS input
and compares results to identify differences.
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
import subprocess
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tbs_parity_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('tbs_parity')

def setup_test_environment(test_date, output_dir):
    """Setup the test environment."""
    logger.info(f"Setting up test environment for date: {test_date}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Define test files
    legacy_output = os.path.join(output_dir, "legacy_tbs_output.xlsx")
    gpu_output = os.path.join(output_dir, "gpu_tbs_output.xlsx")
    comparison_report = os.path.join(output_dir, "tbs_comparison.html")
    
    # Define paths
    archive_path = "bt/archive/backtester_stable/BTRUN"
    gpu_path = "bt/backtester_stable/BTRUN"
    
    return {
        "legacy_output": legacy_output,
        "gpu_output": gpu_output,
        "comparison_report": comparison_report,
        "archive_path": archive_path,
        "gpu_path": gpu_path,
        "test_date": test_date
    }

def run_legacy_backtester(env, test_excel):
    """Run legacy TBS backtester."""
    logger.info("Running legacy TBS backtester...")
    
    # Get absolute path to archive directory
    archive_path = env["archive_path"]
    
    cmd = [
        f"cd {archive_path} &&",
        "export MYSQL_HOST=localhost &&",
        f"python3 BTRunPortfolio.py --excel {test_excel} --output {env['legacy_output']}"
    ]
    
    logger.info(f"Legacy command: {' '.join(cmd)}")
    
    # This is a shell command structure, should be run with shell=True
    cmd_str = " ".join(cmd)
    
    try:
        # Run legacy backtester
        subprocess.run(cmd_str, shell=True, check=True)
        logger.info(f"Legacy backtester completed. Output: {env['legacy_output']}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Legacy backtester failed: {e}")
        return False

def run_gpu_backtester(env, test_excel):
    """Run GPU TBS backtester."""
    logger.info("Running GPU TBS backtester...")
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", test_excel,
        "--output-path", env["gpu_output"]
    ]
    
    logger.info(f"GPU command: {' '.join(cmd)}")
    
    try:
        # Run GPU backtester
        subprocess.run(cmd, check=True)
        logger.info(f"GPU backtester completed. Output: {env['gpu_output']}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"GPU backtester failed: {e}")
        return False

def compare_results(env):
    """Compare legacy and GPU results."""
    logger.info("Comparing legacy and GPU results...")
    
    try:
        # Load both Excel files
        legacy_df = pd.read_excel(env["legacy_output"], sheet_name="PORTFOLIO Trans")
        gpu_df = pd.read_excel(env["gpu_output"], sheet_name="PORTFOLIO Trans")
        
        # Basic comparison
        logger.info(f"Legacy trades: {len(legacy_df)}")
        logger.info(f"GPU trades: {len(gpu_df)}")
        
        # Check if counts match
        if len(legacy_df) != len(gpu_df):
            logger.warning(f"Trade count mismatch: Legacy={len(legacy_df)}, GPU={len(gpu_df)}")
            
        # PnL comparison
        legacy_pnl = legacy_df["Net PNL"].sum() if "Net PNL" in legacy_df.columns else 0
        gpu_pnl = gpu_df["Net PNL"].sum() if "Net PNL" in gpu_df.columns else 0
        
        logger.info(f"Legacy net PnL: {legacy_pnl}")
        logger.info(f"GPU net PnL: {gpu_pnl}")
        logger.info(f"PnL difference: {gpu_pnl - legacy_pnl}")
        
        # Generate detailed comparison report
        generate_comparison_report(env, legacy_df, gpu_df)
        
        return {
            "legacy_trades": len(legacy_df),
            "gpu_trades": len(gpu_df),
            "legacy_pnl": legacy_pnl,
            "gpu_pnl": gpu_pnl,
            "pnl_diff": gpu_pnl - legacy_pnl
        }
    except Exception as e:
        logger.error(f"Comparison failed: {e}")
        return None

def generate_comparison_report(env, legacy_df, gpu_df):
    """Generate detailed HTML comparison report."""
    logger.info("Generating comparison report...")
    
    try:
        # Create HTML report
        with open(env["comparison_report"], "w") as f:
            f.write(f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>TBS Parity Test - {env['test_date']}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1, h2 {{ color: #333; }}
                    .summary {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
                    .mismatch {{ background-color: #ffe0e0; }}
                    table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                </style>
            </head>
            <body>
                <h1>TBS Parity Test Report</h1>
                
                <div class="summary">
                    <h2>Summary</h2>
                    <p><strong>Test Date:</strong> {env['test_date']}</p>
                    <p><strong>Legacy Trades:</strong> {len(legacy_df)}</p>
                    <p><strong>GPU Trades:</strong> {len(gpu_df)}</p>
                    <p><strong>Legacy Net PnL:</strong> {legacy_df["Net PNL"].sum() if "Net PNL" in legacy_df.columns else 0}</p>
                    <p><strong>GPU Net PnL:</strong> {gpu_df["Net PNL"].sum() if "Net PNL" in gpu_df.columns else 0}</p>
                    <p><strong>PnL Difference:</strong> {gpu_df["Net PNL"].sum() - legacy_df["Net PNL"].sum() if "Net PNL" in legacy_df.columns and "Net PNL" in gpu_df.columns else "N/A"}</p>
                </div>
            """)

            # Add sample trades
            max_sample = min(5, len(legacy_df), len(gpu_df))
            f.write(f"""
                <h2>Sample Trade Comparison (First {max_sample})</h2>
                <table>
                    <tr>
                        <th>Field</th>
                        <th>Legacy</th>
                        <th>GPU</th>
                    </tr>
            """)
            
            # Get common columns
            common_cols = set(legacy_df.columns).intersection(set(gpu_df.columns))
            
            for i in range(max_sample):
                legacy_row = legacy_df.iloc[i]
                gpu_row = gpu_df.iloc[i]
                
                for col in sorted(common_cols):
                    legacy_val = legacy_row[col]
                    gpu_val = gpu_row[col]
                    
                    # Check if values match
                    mismatch_class = ""
                    if legacy_val != gpu_val:
                        # For numeric values, allow small differences
                        if pd.api.types.is_numeric_dtype(legacy_df[col]) and pd.api.types.is_numeric_dtype(gpu_df[col]):
                            if abs(float(legacy_val) - float(gpu_val)) > 0.01:
                                mismatch_class = ' class="mismatch"'
                        else:
                            mismatch_class = ' class="mismatch"'
                    
                    f.write(f"""
                    <tr{mismatch_class}>
                        <td>{col}</td>
                        <td>{legacy_val}</td>
                        <td>{gpu_val}</td>
                    </tr>
                    """)
            
            f.write("""
                </table>
            </body>
            </html>
            """)
            
        logger.info(f"Comparison report generated: {env['comparison_report']}")
        return True
    except Exception as e:
        logger.error(f"Report generation failed: {e}")
        return False

def run_test(test_excel, test_date, output_dir):
    """Run the complete parity test."""
    logger.info(f"Starting TBS parity test with {test_excel}")
    
    # Setup environment
    env = setup_test_environment(test_date, output_dir)
    
    # Run legacy backtester
    legacy_success = run_legacy_backtester(env, test_excel)
    
    # Run GPU backtester
    gpu_success = run_gpu_backtester(env, test_excel)
    
    # Compare results if both runs succeeded
    if legacy_success and gpu_success:
        comparison = compare_results(env)
        return comparison
    else:
        logger.error("Test failed - one or both backtesters did not complete successfully")
        return None

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='TBS Parity Testing')
    parser.add_argument('--excel', required=True, help='Input Excel file path')
    parser.add_argument('--date', default=datetime.now().strftime('%Y%m%d'), help='Test date (YYYYMMDD)')
    parser.add_argument('--output-dir', default='debug_output/tbs', help='Output directory')
    
    args = parser.parse_args()
    
    # Run the test
    result = run_test(args.excel, args.date, args.output_dir)
    
    # Print summary
    if result:
        print("\nTBS PARITY TEST SUMMARY:")
        print("========================")
        print(f"Legacy trades: {result['legacy_trades']}")
        print(f"GPU trades: {result['gpu_trades']}")
        print(f"Legacy PnL: {result['legacy_pnl']}")
        print(f"GPU PnL: {result['gpu_pnl']}")
        print(f"PnL difference: {result['pnl_diff']}")

if __name__ == "__main__":
    main() 