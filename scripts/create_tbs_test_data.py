#!/usr/bin/env python3
"""
Generate a simple 1-day TBS test Excel file.

Creates a minimal TBS (Time-Based Strategy) test Excel file
with a single strategy and one or more legs for testing.
"""

import os
import pandas as pd
import argparse
from datetime import datetime

def create_portfolio_sheet():
    """Create the Portfolio sheet."""
    # Define portfolio data
    portfolio_data = {
        "Name": ["NIFTY_TBS_TEST"],
        "Lots": [1],
        "PortfolioStoploss": [500],
        "PortfolioTarget": [1000],
        "StartDate": [20240103],
        "EndDate": [20240103],
        "AllowedDays": ["12345"],  # Monday-Friday
        "PortfolioTrailingType": ["None"]
    }
    
    return pd.DataFrame(portfolio_data)

def create_strategy_sheet():
    """Create the Strategy sheet."""
    # Define strategy data
    strategy_data = {
        "PortfolioName": ["NIFTY_TBS_TEST"],
        "StrategyName": ["TBS_STRATEGY"],
        "StrategyType": ["TBS"],
        "Index": ["NIFTY"],
        "StrikeSelectionTime": ["0916"],
        "StartTime": ["0916"],
        "LastEntryTime": ["0916"],
        "EndTime": ["1200"],
        "StrategyProfit": [500],
        "StrategyLoss": [200],
        "StoplossCheckingInterval": [1],
        "TargetCheckingInterval": [1],
        "ReEntryCheckingInterval": [1],
        "StrategyTrailingType": ["None"],
        "MoveSlToCost": ["NO"],
        "ConsiderHedgePnLForStgyPnL": ["NO"],
        "TgtTrackingFrom": ["LTP"],
        "TgtRegisterPriceFrom": ["LTP"],
        "SlTrackingFrom": ["LTP"],
        "SlRegisterPriceFrom": ["LTP"]
    }
    
    return pd.DataFrame(strategy_data)

def create_leg_sheet():
    """Create the Leg sheet."""
    # Define leg data
    leg_data = {
        "StrategyName": ["TBS_STRATEGY", "TBS_STRATEGY"],
        "LegID": [1, 2],
        "IsIdle": ["NO", "NO"],
        "Instrument": ["CALL", "PUT"],
        "Transaction": ["BUY", "BUY"],
        "StrikeMethod": ["ATM", "ATM"],
        "StrikeValue": [0, 0],
        "PremiumRange": [0, 0],
        "Lots": [1, 1],
        "ExpiryType": ["WEEKLY", "WEEKLY"],
        "DTE": [0, 0],
        "SLType": ["PERCENTAGE", "PERCENTAGE"],
        "SLValue": [50, 50],
        "TGTType": ["PERCENTAGE", "PERCENTAGE"],
        "TGTValue": [100, 100],
        "OpenHedge": ["NO", "NO"],
        "TrailSLType": ["PERCENTAGE", "PERCENTAGE"],
        "SL_TrailAt": [0, 0],
        "SL_TrailBy": [0, 0],
        "SL_ReEntryType": ["ORIGINAL", "ORIGINAL"],
        "SL_ReEntryNo": [0, 0],
        "TGT_ReEntryType": ["ORIGINAL", "ORIGINAL"],
        "TGT_ReEntryNo": [0, 0]
    }
    
    return pd.DataFrame(leg_data)

def create_general_parameter_sheet():
    """Create the GeneralParameter sheet."""
    # Define general parameters
    general_param_data = {
        "Key": ["Slippage"],
        "Value": [0.1]  # 0.1% slippage
    }
    
    return pd.DataFrame(general_param_data)

def create_test_excel(output_file, num_legs=2):
    """Create a test Excel file with all required sheets."""
    print(f"Creating TBS test Excel file: {output_file}")
    
    # Create a writer to save the Excel file
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # Create and write sheets
        portfolio_df = create_portfolio_sheet()
        portfolio_df.to_excel(writer, sheet_name="Portfolio", index=False)
        
        strategy_df = create_strategy_sheet()
        strategy_df.to_excel(writer, sheet_name="Strategy", index=False)
        
        leg_df = create_leg_sheet()
        # Adjust number of legs if needed
        if num_legs != 2:
            if num_legs < 2:
                leg_df = leg_df.iloc[:num_legs]
            else:
                # Add more legs (duplicate the second leg)
                for i in range(2, num_legs):
                    new_leg = leg_df.iloc[1].copy()
                    new_leg["LegID"] = i + 1
                    leg_df = pd.concat([leg_df, pd.DataFrame([new_leg])], ignore_index=True)
        
        leg_df.to_excel(writer, sheet_name="Leg", index=False)
        
        general_param_df = create_general_parameter_sheet()
        general_param_df.to_excel(writer, sheet_name="GeneralParameter", index=False)
        
    print(f"Test Excel file created successfully: {output_file}")
    return output_file

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Create TBS Test Data')
    parser.add_argument('--output', default='test_data/tbs_test_1day.xlsx', 
                        help='Output Excel file path')
    parser.add_argument('--legs', type=int, default=2, 
                        help='Number of legs (default: 2)')
    
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # Create the test Excel file
    output_file = create_test_excel(args.output, args.legs)
    
    print(f"\nTest data generation complete. File: {output_file}")
    print("Use this file for TBS parity testing with scripts/test_tbs_parity.py")

if __name__ == "__main__":
    main() 