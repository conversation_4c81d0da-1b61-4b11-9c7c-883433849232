#!/usr/bin/env python3
"""
Validate GPU Backtester System
This script runs the GPU backtester and validates the output against expected results
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime

# Add project paths
sys.path.append('/srv/samba/shared')

def run_gpu_backtester():
    """Run the GPU backtester with known good configuration"""
    print("="*60)
    print("GPU Backtester Validation")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Use the working input files
    portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    output_file = "test_results/gpu_validation_output.xlsx"
    
    # Ensure output directory exists
    os.makedirs("test_results", exist_ok=True)
    
    # Run GPU backtester
    print("\nRunning GPU backtester...")
    cmd = [
        'python3', '-m', 'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
        '--legacy-excel',
        '--portfolio-excel', portfolio_file,
        '--output-path', output_file
    ]
    
    try:
        result = subprocess.run(
            cmd,
            cwd='/srv/samba/shared',
            capture_output=True,
            text=True,
            check=True
        )
        print("✓ GPU backtester completed successfully")
        
        # Validate output
        return validate_output(output_file)
        
    except subprocess.CalledProcessError as e:
        print(f"✗ GPU backtester failed: {e}")
        print(f"  stdout: {e.stdout}")
        print(f"  stderr: {e.stderr}")
        return False

def validate_output(output_file):
    """Validate the output file contains expected results"""
    print(f"\nValidating output: {output_file}")
    
    try:
        # Check file exists
        if not os.path.exists(output_file):
            print("✗ Output file not found")
            return False
        
        # Check sheets
        excel_file = pd.ExcelFile(output_file)
        sheets = excel_file.sheet_names
        
        expected_sheets = [
            'PortfolioParameter', 'GeneralParameter', 'LegParameter', 
            'Metrics', 'Max Profit and Loss', 'PORTFOLIO Trans'
        ]
        
        missing_sheets = [s for s in expected_sheets if s not in sheets]
        if missing_sheets:
            print(f"✗ Missing sheets: {missing_sheets}")
            return False
        
        print(f"✓ Found {len(sheets)} sheets")
        
        # Check transactions
        trans_df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
        trade_count = len(trans_df)
        
        if trade_count == 0:
            print("✗ No trades found")
            return False
        
        print(f"✓ Found {trade_count} trades")
        
        # Check P&L
        total_pnl = trans_df['PnL'].sum()
        print(f"✓ Total P&L: {total_pnl:.2f}")
        
        # Check trade details
        print("\nTrade Summary:")
        for idx, row in trans_df.iterrows():
            print(f"  Trade {idx+1}: {row['symbol']} {row['Strike']} {row['Instrument Type']} {row['Side']} - P&L: {row['PnL']:.2f}")
        
        # Check metrics
        metrics_df = pd.read_excel(output_file, sheet_name='Metrics')
        
        # Extract key metrics
        def get_metric(name):
            row = metrics_df[metrics_df['Particulars'] == name]
            return float(row['Value'].iloc[0]) if not row.empty else None
        
        margin_required = get_metric('Margin Required')
        total_pnl_metric = get_metric('Total PnL')
        
        print(f"\nKey Metrics:")
        print(f"  Margin Required: {margin_required:.2f}")
        print(f"  Total P&L (from metrics): {total_pnl_metric:.2f}")
        
        # Validate consistency
        pnl_diff = abs(total_pnl - total_pnl_metric) if total_pnl_metric else float('inf')
        if pnl_diff > 0.01:
            print(f"✗ P&L mismatch between transactions ({total_pnl:.2f}) and metrics ({total_pnl_metric:.2f})")
            return False
        
        print("✓ P&L consistent between transactions and metrics")
        
        # Expected results based on our previous testing
        expected_trade_count = 4
        expected_pnl_range = (-70, -55)  # Around -62.00
        
        if trade_count != expected_trade_count:
            print(f"✗ Expected {expected_trade_count} trades, got {trade_count}")
            return False
        
        if not (expected_pnl_range[0] <= total_pnl <= expected_pnl_range[1]):
            print(f"✗ P&L {total_pnl:.2f} outside expected range {expected_pnl_range}")
            return False
        
        print("✓ Results match expected values")
        
        return True
        
    except Exception as e:
        print(f"✗ Validation error: {e}")
        return False

def main():
    """Main validation function"""
    success = run_gpu_backtester()
    
    print("\n" + "="*60)
    if success:
        print("✓ GPU BACKTESTER VALIDATION PASSED")
        print("  The system is working correctly and producing expected results.")
    else:
        print("✗ GPU BACKTESTER VALIDATION FAILED")
        print("  The system needs investigation.")
    print("="*60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 