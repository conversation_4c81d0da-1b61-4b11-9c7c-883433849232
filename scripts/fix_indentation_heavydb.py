#!/usr/bin/env python3
"""Fix indentation issues in heavydb_trade_processing.py"""

# Read the file
with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

# Fix the indentation for specific lines
for i, line in enumerate(lines):
    # Line 263 (0-indexed: 262)
    if i == 262 and line.strip() == "risk_rule_dict['params'] = {}":
        lines[i] = '                                            ' + line.strip() + '\n'
    # Line 267 (0-indexed: 266)
    elif i == 266 and line.strip() == "risk_rule_dict['params']['start_time'] = strategy_entry_time_int":
        lines[i] = '                                            ' + line.strip() + '\n'

# Write the fixed file
with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'w') as f:
    f.writelines(lines)

print('Fixed indentation issues at lines 263 and 267') 