#!/usr/bin/env python3
"""Run legacy backtester with synthetic future ATM calculation to match new system."""

import sys
import os
import json
import requests
import pandas as pd
from datetime import datetime

# Legacy service endpoint
LEGACY_SERVICE_URL = "http://106.51.63.60:5000"

def test_connection():
    """Test connection to legacy service."""
    try:
        response = requests.get(f"{LEGACY_SERVICE_URL}/healthcheck", timeout=5)
        if response.status_code == 200:
            print("✓ Successfully connected to legacy service")
            return True
        else:
            print(f"✗ Legacy service returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Cannot connect to legacy service: {e}")
        print("Make sure port forwarding is enabled on Windows server")
        return False

def run_legacy_backtest_with_synthetic_atm():
    """Run legacy backtest with ATM calculation modified to use synthetic future method."""
    
    # Test connection first
    if not test_connection():
        return None
    
    # Prepare request with synthetic ATM flag
    request_data = {
        "portfolio_excel": "input_portfolio.xlsx",
        "start_date": "20250401",
        "end_date": "20250401",
        "use_synthetic_atm": True,  # This flag tells legacy to use synthetic future ATM
        "portfolio_name": "NIF0DTE"
    }
    
    print("\n=== Running Legacy Backtest with Synthetic ATM ===")
    print(f"Request: {json.dumps(request_data, indent=2)}")
    
    try:
        # Send request to legacy service
        response = requests.post(
            f"{LEGACY_SERVICE_URL}/run_backtest",
            json=request_data,
            timeout=300  # 5 minute timeout
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"\nLegacy backtest completed successfully")
            
            # Save result
            with open("legacy_synthetic_atm_result.json", "w") as f:
                json.dump(result, f, indent=2)
            print("Result saved to legacy_synthetic_atm_result.json")
            
            # Extract and display key metrics
            if "trades" in result:
                trades = result["trades"]
                print(f"\nTotal trades: {len(trades)}")
                
                # Show trades summary
                df = pd.DataFrame(trades)
                if not df.empty:
                    print("\nTrades Summary:")
                    print(df[['leg_id', 'option_type', 'strike', 'side', 'pnl']].to_string())
                    
                    # Calculate total P&L
                    total_pnl = df['pnl'].sum()
                    print(f"\nTotal P&L: {total_pnl}")
                    
                    # Show ATM strikes used
                    atm_strikes = df[df['strike_type'] == 'ATM']['strike'].unique() if 'strike_type' in df.columns else []
                    if len(atm_strikes) > 0:
                        print(f"ATM strikes used: {atm_strikes}")
                
            return result
        else:
            print(f"Legacy service returned error: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"Error running legacy backtest: {e}")
        return None

def compare_with_new_system():
    """Compare legacy synthetic ATM results with new system results."""
    
    # Load new system results if available
    new_result_file = "trace_output.xlsx"
    if os.path.exists(new_result_file):
        print("\n=== Comparing with New System ===")
        
        # Read transaction sheet
        df_new = pd.read_excel(new_result_file, sheet_name="PORTFOLIO Trans")
        print(f"New system trades: {len(df_new)}")
        
        if not df_new.empty:
            print("\nNew System Trades:")
            print(df_new[['leg_id', 'instrument_type', 'strike', 'side', 'pnl']].to_string())
            
            total_pnl_new = df_new['pnl'].sum()
            print(f"\nNew system total P&L: {total_pnl_new}")
    
    # Load legacy results
    if os.path.exists("legacy_synthetic_atm_result.json"):
        with open("legacy_synthetic_atm_result.json", "r") as f:
            legacy_result = json.load(f)
            
        if "trades" in legacy_result:
            df_legacy = pd.DataFrame(legacy_result["trades"])
            total_pnl_legacy = df_legacy['pnl'].sum() if not df_legacy.empty else 0
            
            print(f"\nLegacy system total P&L: {total_pnl_legacy}")
            
            # Compare
            if 'total_pnl_new' in locals():
                diff = abs(total_pnl_new - total_pnl_legacy)
                print(f"\nP&L Difference: {diff}")
                if diff < 0.01:  # Less than 1 paisa
                    print("✓ P&L matches!")
                else:
                    print("✗ P&L mismatch")

if __name__ == "__main__":
    # Run legacy with synthetic ATM
    legacy_result = run_legacy_backtest_with_synthetic_atm()
    
    # Compare results
    if legacy_result:
        compare_with_new_system()
    else:
        print("\nFailed to run legacy backtest")
        print("\nTroubleshooting steps:")
        print("1. Ensure Windows server is accessible")
        print("2. Run port forwarding on Windows: netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=***************")
        print("3. Check firewall settings")
        print("4. Verify legacy service is running") 