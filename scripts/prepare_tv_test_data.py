#!/usr/bin/env python3
"""
Prepare TV test data files with appropriate signal files and dates.
TV requires special handling as it uses external signals.
"""

import os
import shutil
import openpyxl
from datetime import datetime, timed<PERSON><PERSON>

def create_tv_signals(output_file, start_date, end_date, signal_count=10):
    """Create a signals Excel file with sample signals."""
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "List of trades"
    
    # Add headers
    headers = ['Trade #', 'Type', 'Date/Time', 'Contracts']
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col).value = header
    
    # Convert date strings to datetime
    start_dt = datetime.strptime(start_date, "%d_%m_%Y")
    end_dt = datetime.strptime(end_date, "%d_%m_%Y")
    
    # Generate sample signals
    current_row = 2
    trade_num = 1
    
    # For 1-day test, create 2-3 signals
    # For 30-day test, create signals throughout the period
    if start_dt == end_dt:
        # 1-day test - create morning and afternoon signals
        signals = [
            (start_dt.replace(hour=9, minute=30), 'Entry Long'),
            (start_dt.replace(hour=12, minute=0), 'Exit Long'),
            (start_dt.replace(hour=14, minute=0), 'Entry Short'),
            (start_dt.replace(hour=15, minute=20), 'Exit Short'),
        ]
    else:
        # 30-day test - create signals every few days
        signals = []
        current_date = start_dt
        while current_date <= end_dt:
            # Skip weekends
            if current_date.weekday() < 5:  # Monday = 0, Friday = 4
                signals.extend([
                    (current_date.replace(hour=9, minute=30), 'Entry Long'),
                    (current_date.replace(hour=15, minute=20), 'Exit Long'),
                ])
            current_date += timedelta(days=3)  # Signal every 3 days
    
    # Write signals to Excel
    for signal_dt, signal_type in signals:
        ws.cell(row=current_row, column=1).value = trade_num
        ws.cell(row=current_row, column=2).value = signal_type
        ws.cell(row=current_row, column=3).value = signal_dt.strftime("%Y%m%d %H%M%S")
        ws.cell(row=current_row, column=4).value = 1  # 1 lot
        
        if 'Exit' in signal_type:
            trade_num += 1
        current_row += 1
    
    wb.save(output_file)
    print(f"Created TV signals file: {output_file}")

def update_tv_settings(tv_file, signal_file, start_date, end_date):
    """Update TV settings file with dates and signal file reference."""
    wb = openpyxl.load_workbook(tv_file)
    
    # Update Setting sheet
    if 'Setting' in wb.sheetnames:
        ws = wb['Setting']
        
        # Find the enabled row
        for row in ws.iter_rows(min_row=2, max_row=50):  # Limit to first 50 rows
            if row[1].value == 'YES':  # Enabled column (B)
                # Find column indices
                header_row = list(ws.iter_rows(min_row=1, max_row=1))[0]
                cols = {}
                for idx, cell in enumerate(header_row):
                    if cell.value:
                        cols[cell.value] = idx + 1
                
                # Update values - use absolute path for signal file
                if 'SignalFilePath' in cols:
                    ws.cell(row=row[0].row, column=cols['SignalFilePath']).value = os.path.abspath(signal_file)
                if 'StartDate' in cols:
                    ws.cell(row=row[0].row, column=cols['StartDate']).value = start_date
                if 'EndDate' in cols:
                    ws.cell(row=row[0].row, column=cols['EndDate']).value = end_date
                if 'SignalDateFormat' in cols:
                    ws.cell(row=row[0].row, column=cols['SignalDateFormat']).value = "%Y%m%d %H%M%S"
                
                # Also need to set portfolio file paths
                # Check if these paths exist in the original file, otherwise use defaults
                long_path = ws.cell(row=row[0].row, column=cols['LongPortfolioFilePath']).value if 'LongPortfolioFilePath' in cols else None
                short_path = ws.cell(row=row[0].row, column=cols['ShortPortfolioFilePath']).value if 'ShortPortfolioFilePath' in cols else None
                manual_path = ws.cell(row=row[0].row, column=cols['ManualPortfolioFilePath']).value if 'ManualPortfolioFilePath' in cols else None
                
                # Update paths to use test_data directory versions if they exist
                if 'LongPortfolioFilePath' in cols and not long_path:
                    ws.cell(row=row[0].row, column=cols['LongPortfolioFilePath']).value = "test_data/tbs/input_portfolio_tbs.xlsx"
                if 'ShortPortfolioFilePath' in cols and not short_path:
                    ws.cell(row=row[0].row, column=cols['ShortPortfolioFilePath']).value = "test_data/tbs/input_portfolio_tbs.xlsx"
                if 'ManualPortfolioFilePath' in cols and not manual_path:
                    ws.cell(row=row[0].row, column=cols['ManualPortfolioFilePath']).value = "test_data/tbs/input_portfolio_tbs.xlsx"
    
    wb.save(tv_file)
    print(f"Updated TV settings in {tv_file}")

def prepare_tv_test_data():
    """Prepare TV test data files."""
    base_dir = "test_data/tv"
    base_tv_file = os.path.join(base_dir, "input_tv.xlsx")
    
    # Create 1-day variant
    tv_1day_file = os.path.join(base_dir, "input_tv_1day.xlsx")
    signals_1day_file = os.path.join(base_dir, "signals_1day.xlsx")
    
    shutil.copy2(base_tv_file, tv_1day_file)
    create_tv_signals(signals_1day_file, "03_01_2024", "03_01_2024")
    update_tv_settings(tv_1day_file, signals_1day_file, "03_01_2024", "03_01_2024")
    
    # Create 30-day variant
    tv_30day_file = os.path.join(base_dir, "input_tv_30day.xlsx")
    signals_30day_file = os.path.join(base_dir, "signals_30day.xlsx")
    
    shutil.copy2(base_tv_file, tv_30day_file)
    create_tv_signals(signals_30day_file, "01_01_2024", "31_01_2024", signal_count=20)
    update_tv_settings(tv_30day_file, signals_30day_file, "01_01_2024", "31_01_2024")
    
    print("\nTV test data preparation complete!")

if __name__ == '__main__':
    prepare_tv_test_data() 