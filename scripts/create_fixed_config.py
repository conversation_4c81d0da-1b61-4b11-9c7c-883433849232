#!/usr/bin/env python3
"""
Create a fixed version of the input configuration
"""

import pandas as pd
from datetime import datetime

def create_fixed_config():
    """Create a fixed version of the configuration"""
    print("="*60)
    print("Creating Fixed Configuration")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Read original files
    portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    strategy_file = "bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"
    
    # Read strategy sheets
    general_df = pd.read_excel(strategy_file, sheet_name='GeneralParameter')
    leg_df = pd.read_excel(strategy_file, sheet_name='LegParameter')
    
    print("\nOriginal Leg Configuration:")
    for i, row in leg_df.iterrows():
        print(f"  Leg {row['LegID']}: {row['Instrument']} {row['Transaction']} {row['StrikeMethod']} (value={row['StrikeValue']})")
    
    # Fix the configuration
    print("\nFixing configuration...")
    for i in range(len(leg_df)):
        if leg_df.loc[i, 'LegID'] == 3:
            leg_df.loc[i, 'StrikeMethod'] = 'otm2'
            leg_df.loc[i, 'StrikeValue'] = 0
        elif leg_df.loc[i, 'LegID'] == 4:
            leg_df.loc[i, 'StrikeMethod'] = 'otm2'
            leg_df.loc[i, 'StrikeValue'] = 0
    
    print("\nFixed Leg Configuration:")
    for i, row in leg_df.iterrows():
        print(f"  Leg {row['LegID']}: {row['Instrument']} {row['Transaction']} {row['StrikeMethod']} (value={row['StrikeValue']})")
    
    # Save to new file
    new_strategy_file = "bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs_fixed.xlsx"
    with pd.ExcelWriter(new_strategy_file, engine='openpyxl') as writer:
        general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
        leg_df.to_excel(writer, sheet_name='LegParameter', index=False)
    
    print(f"\n✓ Created fixed strategy file: {new_strategy_file}")
    
    # Update the portfolio to use the fixed strategy file
    portfolio_df = pd.read_excel(portfolio_file, sheet_name='PortfolioSetting')
    strategy_df = pd.read_excel(portfolio_file, sheet_name='StrategySetting')
    
    # Update the enabled strategy to use fixed file
    for i in range(len(strategy_df)):
        if strategy_df.loc[i, 'Enabled'] == 'YES':
            strategy_df.loc[i, 'StrategyExcelFilePath'] = new_strategy_file
    
    # Save new portfolio file
    new_portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx"
    with pd.ExcelWriter(new_portfolio_file, engine='openpyxl') as writer:
        portfolio_df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
        strategy_df.to_excel(writer, sheet_name='StrategySetting', index=False)
    
    print(f"✓ Created fixed portfolio file: {new_portfolio_file}")
    
    return new_portfolio_file, new_strategy_file

def test_atm_calculation():
    """Test ATM calculation in HeavyDB"""
    print("\n" + "="*60)
    print("Testing ATM Calculation in HeavyDB")
    print("="*60)
    
    import heavydb
    conn = heavydb.connect(host='127.0.0.1', port=6274, user='admin', password='HyperInteractive', dbname='heavyai')
    
    query = """
    SELECT 
        trade_time,
        spot,
        atm_strike,
        strike,
        ce_close,
        pe_close,
        strike + ce_close - pe_close AS synthetic_future,
        ABS((strike + ce_close - pe_close) - spot) AS abs_diff
    FROM nifty_option_chain
    WHERE trade_date = DATE '2024-12-31'
    AND trade_time = TIME '09:16:00'
    AND strike IN (23450, 23500, 23550, 23600, 23650)
    ORDER BY strike;
    """
    
    df = pd.read_sql(query, conn)
    print("\nATM Calculation Results:")
    print(df.to_string(index=False))
    
    # Find which strike was selected as ATM
    atm_from_query = df['atm_strike'].iloc[0]
    spot_price = df['spot'].iloc[0]
    legacy_atm = round(spot_price / 50) * 50
    
    print(f"\nSummary:")
    print(f"  Spot price: {spot_price}")
    print(f"  Legacy ATM (simple rounding): {legacy_atm}")
    print(f"  HeavyDB ATM (synthetic future): {atm_from_query}")
    print(f"  Difference: {legacy_atm - atm_from_query} points")
    
    conn.close()

if __name__ == "__main__":
    new_portfolio, new_strategy = create_fixed_config()
    test_atm_calculation()
    
    print("\n" + "="*60)
    print("Next Steps")
    print("="*60)
    print(f"\n1. Use the fixed portfolio file for testing:")
    print(f"   {new_portfolio}")
    print(f"\n2. Run the comparison with the fixed configuration")
    print(f"\n3. Consider aligning ATM calculation methods") 