#!/usr/bin/env python3
"""Analyze the structure around the risk rules block."""

with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

print("=== Analyzing Risk Rules Block Structure ===\n")

# Find key markers
markers = []
for i in range(140, 320):
    if i >= len(lines):
        break
    line = lines[i]
    indent = len(line) - len(line.lstrip())
    stripped = line.strip()
    
    # Track important lines
    if any(marker in line for marker in [
        'if leg.risk_rules and not entry_df.empty',
        'After risk rules block',
        'Build trade record for EVERY leg',
        'record = build_trade_record',
        'trade_records.append',
        'for leg in strategy.legs',
        'if entry_df.empty or exit_df.empty',
        'continue'
    ]):
        markers.append((i+1, indent, stripped[:80]))

# Print the structure
print("Line# | Indent | Content")
print("-" * 100)
prev_indent = None
for line_num, indent, content in markers:
    indent_change = ""
    if prev_indent is not None:
        if indent > prev_indent:
            indent_change = f" (→ +{indent - prev_indent})"
        elif indent < prev_indent:
            indent_change = f" (← -{prev_indent - indent})"
    print(f"{line_num:5d} | {indent:6d} | {'  ' * (indent//4)}{content}{indent_change}")
    prev_indent = indent

# Also check if the code after risk rules is at the correct indentation
print("\n=== Checking Indentation Consistency ===")
leg_loop_indent = None
risk_if_indent = None
after_risk_indent = None
build_trade_indent = None

for i in range(100, 320):
    if i >= len(lines):
        break
    line = lines[i]
    indent = len(line) - len(line.lstrip())
    
    if 'for leg in strategy.legs:' in line:
        leg_loop_indent = indent
    elif 'if leg.risk_rules and not entry_df.empty' in line:
        risk_if_indent = indent
    elif 'After risk rules block' in line:
        after_risk_indent = indent
    elif 'record = build_trade_record' in line:
        build_trade_indent = indent

print(f"Leg loop indent:        {leg_loop_indent}")
print(f"Risk if indent:         {risk_if_indent}")
print(f"After risk indent:      {after_risk_indent}")
print(f"Build trade indent:     {build_trade_indent}")

if after_risk_indent and leg_loop_indent:
    if after_risk_indent == leg_loop_indent + 4:
        print("\n✓ 'After risk rules' is correctly inside the leg loop")
    else:
        print(f"\n✗ 'After risk rules' indentation issue: expected {leg_loop_indent + 4}, got {after_risk_indent}") 