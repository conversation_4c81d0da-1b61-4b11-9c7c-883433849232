#!/usr/bin/env python3
"""
Check date range in golden file
"""

import pandas as pd

golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"

try:
    # Try reading TradeBook sheet directly
    df = pd.read_excel(golden_file, sheet_name='TradeBook', engine='openpyxl')
    
    print(f"Golden file date range information:")
    print(f"=" * 50)
    print(f"Total trades: {len(df)}")
    
    if 'entry_date' in df.columns:
        print(f"\nEntry dates:")
        print(f"  Min: {df['entry_date'].min()}")
        print(f"  Max: {df['entry_date'].max()}")
        print(f"  Unique dates: {df['entry_date'].nunique()}")
        print(f"\nDate list: {sorted(df['entry_date'].unique())}")
        
    if 'exit_date' in df.columns:
        print(f"\nExit dates:")
        print(f"  Min: {df['exit_date'].min()}")
        print(f"  Max: {df['exit_date'].max()}")
        
    # Show sample trades
    print(f"\nSample trades:")
    print(df[['entry_date', 'entry_time', 'exit_date', 'exit_time', 'strike', 'instrument_type', 'side', 'pnl']].head())
    
except Exception as e:
    print(f"Error reading golden file: {e}")
    
    # Try alternate approach
    try:
        print("\nTrying to read all sheets...")
        xl = pd.ExcelFile(golden_file, engine='openpyxl')
        print(f"Available sheets: {xl.sheet_names}")
        
        for sheet in xl.sheet_names:
            try:
                df = pd.read_excel(golden_file, sheet_name=sheet, engine='openpyxl')
                print(f"\n{sheet}: {df.shape}")
                if 'date' in str(df.columns).lower() or 'Date' in df.columns:
                    print(f"  Columns with 'date': {[c for c in df.columns if 'date' in str(c).lower()]}")
            except:
                print(f"  Could not read sheet {sheet}")
                
    except Exception as e2:
        print(f"Could not read file at all: {e2}") 