#!/usr/bin/env python3
"""Run complete comparison between new and legacy backtesters with all fixes applied."""

import sys
import os
import subprocess
import json
import pandas as pd
from datetime import datetime

sys.path.append('/srv/samba/shared')

def run_new_backtester():
    """Run the new HeavyDB-based backtester."""
    print("\n=== Running New Backtester ===")
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "--output-path", "new_system_output.xlsx",
        "--start-date", "20250401",
        "--end-date", "20250401"
    ]
    
    env = {
        "PYTHONPATH": "/srv/samba/shared",
        "USE_LEGACY_FORMAT": "1"  # Enable legacy format output
    }
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd="/srv/samba/shared",
            env={**os.environ, **env}
        )
        
        print("\n--- New System Output ---")
        print(result.stdout[-1000:] if len(result.stdout) > 1000 else result.stdout)  # Last 1000 chars
        
        if result.stderr:
            print("\n--- New System Errors ---")
            print(result.stderr[-1000:] if len(result.stderr) > 1000 else result.stderr)
        
        if result.returncode == 0:
            print("\n✓ New backtester completed successfully")
            return True
        else:
            print(f"\n✗ New backtester failed with code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n✗ Error running new backtester: {e}")
        return False

def analyze_new_results():
    """Analyze the new system results."""
    if not os.path.exists("new_system_output.xlsx"):
        print("✗ New system output file not found")
        return None
    
    print("\n=== Analyzing New System Results ===")
    
    # Read all sheets
    xl_file = pd.ExcelFile("new_system_output.xlsx")
    print(f"Sheets: {xl_file.sheet_names}")
    
    # Read transaction sheet
    if "PORTFOLIO Trans" in xl_file.sheet_names:
        df = pd.read_excel("new_system_output.xlsx", sheet_name="PORTFOLIO Trans")
        print(f"\nTotal trades: {len(df)}")
        
        if not df.empty:
            # Show trade details
            print("\nTrade Details:")
            cols_to_show = ['leg_id', 'instrument_type', 'strike', 'side', 'pnl']
            available_cols = [col for col in cols_to_show if col in df.columns]
            if available_cols:
                print(df[available_cols].to_string())
            
            # Calculate total P&L
            pnl_col = 'pnl' if 'pnl' in df.columns else 'netPnlAfterExpenses'
            if pnl_col in df.columns:
                total_pnl = df[pnl_col].sum()
                print(f"\nTotal P&L: {total_pnl}")
            
            # Check strike values
            if 'strike' in df.columns:
                unique_strikes = df['strike'].unique()
                print(f"\nUnique strikes: {unique_strikes}")
            
            # Check leg distribution
            if 'leg_id' in df.columns:
                leg_counts = df['leg_id'].value_counts()
                print(f"\nLeg distribution:")
                print(leg_counts)
                
                # Expected: 4 legs (1=SELL CALL ATM, 2=SELL PUT ATM, 3=BUY CALL OTM2, 4=BUY PUT OTM2)
                if len(leg_counts) < 4:
                    print(f"\n⚠️ Warning: Only {len(leg_counts)} legs found, expected 4")
        
        return df
    else:
        print("✗ No PORTFOLIO Trans sheet found")
        return None

def run_legacy_backtester():
    """Run the legacy backtester (if accessible)."""
    print("\n=== Running Legacy Backtester ===")
    
    # Check if we can access legacy service
    import requests
    
    LEGACY_SERVICE_URL = "http://************:5000"
    
    try:
        response = requests.get(f"{LEGACY_SERVICE_URL}/healthcheck", timeout=5)
        if response.status_code != 200:
            print("✗ Legacy service not accessible")
            return None
    except:
        print("✗ Cannot connect to legacy service")
        print("Skipping legacy comparison - service not available")
        return None
    
    # Run legacy backtest
    request_data = {
        "portfolio_excel": "input_portfolio.xlsx",
        "start_date": "20250401",
        "end_date": "20250401",
        "portfolio_name": "NIF0DTE"
    }
    
    try:
        response = requests.post(
            f"{LEGACY_SERVICE_URL}/run_backtest",
            json=request_data,
            timeout=300
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Legacy backtest completed successfully")
            
            # Save result
            with open("legacy_result.json", "w") as f:
                json.dump(result, f, indent=2)
            
            return result
        else:
            print(f"✗ Legacy service returned error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"✗ Error running legacy backtest: {e}")
        return None

def compare_results(new_df, legacy_result):
    """Compare results between new and legacy systems."""
    print("\n=== Comparing Results ===")
    
    if new_df is None or new_df.empty:
        print("✗ No new system results to compare")
        return
    
    # New system summary
    new_trade_count = len(new_df)
    new_pnl = new_df['pnl'].sum() if 'pnl' in new_df.columns else new_df.get('netPnlAfterExpenses', pd.Series()).sum()
    
    print(f"\nNew System:")
    print(f"  Trades: {new_trade_count}")
    print(f"  Total P&L: {new_pnl}")
    
    # Legacy system summary (if available)
    if legacy_result and "trades" in legacy_result:
        legacy_df = pd.DataFrame(legacy_result["trades"])
        legacy_trade_count = len(legacy_df)
        legacy_pnl = legacy_df['pnl'].sum() if not legacy_df.empty else 0
        
        print(f"\nLegacy System:")
        print(f"  Trades: {legacy_trade_count}")
        print(f"  Total P&L: {legacy_pnl}")
        
        # Compare
        print(f"\nDifferences:")
        print(f"  Trade count: {new_trade_count - legacy_trade_count}")
        print(f"  P&L: {new_pnl - legacy_pnl}")
        
        if new_trade_count == legacy_trade_count and abs(new_pnl - legacy_pnl) < 0.01:
            print("\n✅ Results match!")
        else:
            print("\n❌ Results do not match")
    
    # Check for missing trades issue
    if new_trade_count < 4:
        print(f"\n⚠️ Issue #1: Missing trades - only {new_trade_count} trades generated instead of 4")
        print("This needs to be investigated in the trade generation logic")

def main():
    """Run the complete comparison."""
    print("=== Backtester Output Sync Test ===")
    print(f"Date: {datetime.now()}")
    
    # Run new system
    new_success = run_new_backtester()
    
    # Analyze new results
    new_df = analyze_new_results() if new_success else None
    
    # Run legacy system (if available)
    legacy_result = run_legacy_backtester()
    
    # Compare results
    if new_df is not None:
        compare_results(new_df, legacy_result)
    
    # Dynamic status based on actual results
    trade_count = len(new_df) if new_df is not None else 0
    
    print("\n=== Summary of Issues ===")
    if trade_count == 4:
        print("1. Missing Trades: ✅ FIXED - All 4 trades are now generated correctly!")
        print("   Status: Fixed indentation issue in heavydb_trade_processing.py where build_trade_record was outside the leg loop")
    else:
        print(f"1. Missing Trades: ⚠️ Still an issue - only {trade_count} trades generated instead of 4")
        print("   Status: Needs further investigation")
    
    print("\n2. ATM Calculation: Different methods (simple vs synthetic future)")
    print("   Status: Script created for alignment, needs legacy service access to verify")
    
    print("\n3. Column Format: Different column names and formats")
    print("   Status: ✅ Column mapper created and integrated")
    
    print("\n=== Next Steps ===")
    if trade_count == 4:
        print("1. ✅ Trade generation fixed!")
        print("2. Compare ATM calculations with legacy service when available")
        print("3. Verify P&L calculations match between systems")
    else:
        print("1. Continue debugging trade generation")
        print("2. Verify strike selection for all legs")
        print("3. Run with legacy service to compare")

if __name__ == "__main__":
    main() 