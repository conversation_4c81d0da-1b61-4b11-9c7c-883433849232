#!/usr/bin/env python3
"""Check the current status of MySQL data copy."""

import mysql.connector
from datetime import datetime

def check_data_status():
    """Check the status of NIFTY data in local MySQL."""
    
    # Connect to local MySQL
    conn = mysql.connector.connect(
        host='localhost',
        user='mahesh',
        password='mahesh_123',
        database='historicaldb'
    )
    cursor = conn.cursor()
    
    print("=" * 60)
    print("MySQL Data Copy Status Check")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Check each table
    tables = ['nifty_call', 'nifty_put', 'nifty_cash', 'nifty_future']
    
    total_rows = 0
    for table in tables:
        # Get row count and date range
        query = f"""
        SELECT COUNT(*) as row_count,
               MIN(date) as min_date,
               MAX(date) as max_date,
               COUNT(DISTINCT date) as trading_days
        FROM {table}
        WHERE date LIKE '24%'
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        
        if result and result[0] > 0:
            row_count, min_date, max_date, trading_days = result
            total_rows += row_count
            
            print(f"\n{table}:")
            print(f"  Rows: {row_count:,}")
            print(f"  Date range: {min_date} to {max_date}")
            print(f"  Trading days: {trading_days}")
            
            # Check for gaps in nifty_put
            if table == 'nifty_put' and str(max_date) < '241231':
                print(f"  ⚠️  Data incomplete - missing dates after {max_date}")
                
                # Estimate remaining
                remaining_days = 248 - trading_days  # 248 trading days in 2024
                est_remaining = int(remaining_days * (row_count / trading_days))
                print(f"  Estimated remaining rows: ~{est_remaining:,}")
        else:
            print(f"\n{table}: NO DATA")
    
    # Check table sizes
    print("\n" + "-" * 60)
    print("Table Sizes:")
    
    query = """
    SELECT table_name, 
           ROUND(data_length/1024/1024, 2) as data_mb,
           ROUND((data_length + index_length)/1024/1024, 2) as total_mb
    FROM information_schema.tables 
    WHERE table_schema = 'historicaldb' 
      AND table_name LIKE 'nifty%'
    ORDER BY table_name
    """
    
    cursor.execute(query)
    for row in cursor.fetchall():
        print(f"  {row[0]}: {row[1]} MB data, {row[2]} MB total")
    
    print("\n" + "-" * 60)
    print(f"Total rows (2024 data): {total_rows:,}")
    
    # Check for running processes
    print("\nChecking for active copy processes...")
    import subprocess
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        copy_processes = [line for line in result.stdout.split('\n') 
                         if 'copy' in line and 'nifty' in line and 'python' in line]
        
        if copy_processes:
            print("Active copy processes found:")
            for proc in copy_processes:
                parts = proc.split()
                if len(parts) > 10:
                    pid = parts[1]
                    cmd = ' '.join(parts[10:])
                    print(f"  PID {pid}: {cmd[:60]}...")
        else:
            print("No active copy processes found")
    except:
        print("Could not check for active processes")
    
    cursor.close()
    conn.close()
    
    print("\n" + "=" * 60)
    
    # Recommendation
    if total_rows > 25000000:  # If we have most of the data
        print("\n✅ Sufficient data for testing. You can proceed with legacy vs GPU comparison.")
    else:
        print("\n⚠️  Data copy may still be in progress. Check active processes.")

if __name__ == "__main__":
    check_data_status() 