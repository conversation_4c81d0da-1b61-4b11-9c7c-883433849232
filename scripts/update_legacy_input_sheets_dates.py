#!/usr/bin/env python3
"""Update dates in legacy INPUT SHEETS files."""

import pandas as pd
import os

def update_portfolio_dates(excel_path, test_date="03_01_2024"):
    """Update dates in portfolio Excel file."""
    
    print(f"Updating dates in {excel_path} to {test_date}...")
    
    # Read Excel file
    xls = pd.ExcelFile(excel_path)
    
    # Create a writer
    with pd.ExcelWriter(excel_path, engine='openpyxl', mode='w') as writer:
        # Process each sheet
        for sheet_name in xls.sheet_names:
            df = pd.read_excel(xls, sheet_name=sheet_name)
            
            # Update dates based on sheet type
            if sheet_name == 'PortfolioSetting':
                if 'StartDate' in df.columns:
                    df['StartDate'] = test_date
                if 'EndDate' in df.columns:
                    df['EndDate'] = test_date
                print(f"  Updated {sheet_name}: StartDate and EndDate to {test_date}")
            
            # Write the sheet back
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"✅ Updated {excel_path}")

def main():
    """Update the INPUT SHEETS portfolio file."""
    
    # Path to the file in INPUT SHEETS directory
    portfolio_path = "bt/archive/backtester_stable/BTRUN/INPUT SHEETS/INPUT PORTFOLIO.xlsx"
    
    # Test date
    test_date = "03_01_2024"  # January 3, 2024
    
    print(f"Updating INPUT SHEETS files for test date: {test_date}")
    print("="*60)
    
    if os.path.exists(portfolio_path):
        update_portfolio_dates(portfolio_path, test_date)
    else:
        print(f"⚠️  File not found: {portfolio_path}")
    
    print("\n✅ Date updates complete!")
    print("\nYou can now run the legacy backtester")

if __name__ == "__main__":
    main() 