#!/usr/bin/env python3
"""Monitor GPU backtesting progress by checking output files"""
import os
import time
import glob
from datetime import datetime

def monitor_progress():
    """Monitor GPU backtest progress"""
    output_dir = "bt/backtester_stable/BTRUN/output"
    output_file = os.path.join(output_dir, "tbs_2024_full_year.xlsx")
    
    print("=== GPU Backtesting Progress Monitor ===")
    print(f"Monitoring output file: {output_file}")
    print("Press Ctrl+C to stop monitoring\n")
    
    start_time = datetime.now()
    
    while True:
        try:
            # Check if output file exists
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                mod_time = datetime.fromtimestamp(os.path.getmtime(output_file))
                elapsed = datetime.now() - start_time
                
                print(f"\r[{datetime.now().strftime('%H:%M:%S')}] "
                      f"Output file size: {file_size/1024:.1f} KB | "
                      f"Last modified: {mod_time.strftime('%H:%M:%S')} | "
                      f"Elapsed: {elapsed}", end='', flush=True)
                
                # Check if file hasn't been modified for 30 seconds (likely done)
                if (datetime.now() - mod_time).seconds > 30:
                    print("\n\nBacktest appears to be complete!")
                    print(f"Total time: {elapsed}")
                    break
            else:
                elapsed = datetime.now() - start_time
                print(f"\r[{datetime.now().strftime('%H:%M:%S')}] "
                      f"Waiting for output file... | "
                      f"Elapsed: {elapsed}", end='', flush=True)
            
            time.sleep(5)  # Check every 5 seconds
            
        except KeyboardInterrupt:
            print("\n\nMonitoring stopped by user.")
            break
        except Exception as e:
            print(f"\n\nError: {e}")
            break

if __name__ == "__main__":
    monitor_progress() 