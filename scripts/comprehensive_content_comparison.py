#!/usr/bin/env python3
"""Comprehensive comparison of content between legacy and HeavyDB outputs."""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import subprocess

# Add project paths
sys.path.append('/srv/samba/shared')

def run_heavydb_backtest():
    """Run the HeavyDB backtester and return output path."""
    print("\n" + "="*80)
    print("RUNNING HEAVYDB BACKTESTER")
    print("="*80)
    
    output_file = "heavydb_comprehensive_output.xlsx"
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "--output-path", output_file,
        "--start-date", "20250401",
        "--end-date", "20250401"
    ]
    
    env = os.environ.copy()
    env['PYTHONPATH'] = '/srv/samba/shared'
    
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    
    if result.returncode == 0:
        print(f"✓ HeavyDB backtest completed successfully")
        return output_file
    else:
        print(f"✗ HeavyDB backtester failed")
        return None

def compare_dataframes(df1, df2, sheet_name, tolerance=0.01):
    """Compare two dataframes and report differences."""
    print(f"\n  Comparing {sheet_name}:")
    
    # Check shape
    if df1.shape != df2.shape:
        print(f"    ✗ Shape mismatch: Legacy {df1.shape} vs HeavyDB {df2.shape}")
        return False
    
    # Check columns
    if list(df1.columns) != list(df2.columns):
        print(f"    ✗ Column mismatch:")
        print(f"      Legacy: {list(df1.columns)}")
        print(f"      HeavyDB: {list(df2.columns)}")
        missing_in_heavydb = set(df1.columns) - set(df2.columns)
        missing_in_legacy = set(df2.columns) - set(df1.columns)
        if missing_in_heavydb:
            print(f"      Missing in HeavyDB: {missing_in_heavydb}")
        if missing_in_legacy:
            print(f"      Missing in Legacy: {missing_in_legacy}")
        return False
    
    # Compare values
    all_match = True
    for col in df1.columns:
        if df1[col].dtype in ['float64', 'float32']:
            # Numeric comparison with tolerance
            if not np.allclose(df1[col].fillna(0), df2[col].fillna(0), rtol=tolerance, atol=tolerance):
                diff_mask = ~np.isclose(df1[col].fillna(0), df2[col].fillna(0), rtol=tolerance, atol=tolerance)
                diff_count = diff_mask.sum()
                if diff_count > 0:
                    print(f"    ⚠️  Numeric differences in column '{col}': {diff_count} values differ")
                    # Show first few differences
                    diff_indices = diff_mask[diff_mask].index[:3]
                    for idx in diff_indices:
                        print(f"       Row {idx}: Legacy={df1.loc[idx, col]}, HeavyDB={df2.loc[idx, col]}")
                    all_match = False
        else:
            # String/categorical comparison
            if not df1[col].equals(df2[col]):
                diff_mask = df1[col] != df2[col]
                diff_count = diff_mask.sum()
                print(f"    ⚠️  Text differences in column '{col}': {diff_count} values differ")
                # Show first few differences
                diff_indices = diff_mask[diff_mask].index[:3]
                for idx in diff_indices:
                    print(f"       Row {idx}: Legacy='{df1.loc[idx, col]}', HeavyDB='{df2.loc[idx, col]}'")
                all_match = False
    
    if all_match:
        print(f"    ✓ Content matches perfectly!")
    
    return all_match

def analyze_sheet_mapping():
    """Analyze potential sheet name mappings between systems."""
    mappings = {
        # Direct mappings
        'PortfolioParameter': 'PortfolioSetting',  # Parameter sheets from input
        'GeneralParameter': 'GeneralParameter',
        'LegParameter': 'LegParameter',
        'Metrics': 'Metrics',
        'Max Profit and Loss': 'Max Profit and Loss',
        'PORTFOLIO Trans': 'PORTFOLIO Trans',
        'PORTFOLIO Results': 'PORTFOLIO Results',
        
        # Strategy-specific mappings (truncation differences)
        'RS,916-1200,ATM-Sell,OTM2-Buy w_Trans': 'RS,916-1200,ATM-SELL,OTM2-BU_Trans',
        'RS,916-1200,ATM-Sell,OTM2-Buy w_DayWise': 'RS,916-1200,ATM-SELL,OTM2-BU_DayWise',
        'RS,916-1200,ATM-Sell,OTM2-Buy w_MonthWise': 'RS,916-1200,ATM-SELL,OTM2-BU_MonthWise',
        'RS,916-1200,ATM-Sell,OTM2-Buy w_MarginPct': 'RS,916-1200,ATM-SELL,OTM2-BU_MarginPct',
    }
    return mappings

def comprehensive_comparison(heavydb_file, legacy_file):
    """Perform comprehensive comparison of all sheets and content."""
    print("\n" + "="*80)
    print("COMPREHENSIVE CONTENT COMPARISON")
    print("="*80)
    
    # Load both files
    heavydb_xl = pd.ExcelFile(heavydb_file)
    legacy_xl = pd.ExcelFile(legacy_file)
    
    heavydb_sheets = set(heavydb_xl.sheet_names)
    legacy_sheets = set(legacy_xl.sheet_names)
    
    print(f"\nSheet counts:")
    print(f"  Legacy: {len(legacy_sheets)} sheets")
    print(f"  HeavyDB: {len(heavydb_sheets)} sheets")
    
    # Get mappings
    sheet_mappings = analyze_sheet_mapping()
    
    # Track comparison results
    results = {
        'matched': [],
        'content_differs': [],
        'missing_in_heavydb': [],
        'extra_in_heavydb': []
    }
    
    # Compare mapped sheets
    print("\n" + "-"*80)
    print("COMPARING SHEET CONTENTS")
    print("-"*80)
    
    for heavydb_sheet in heavydb_sheets:
        # Find corresponding legacy sheet
        legacy_sheet = None
        
        # Try direct match first
        if heavydb_sheet in legacy_sheets:
            legacy_sheet = heavydb_sheet
        # Try mapping
        elif heavydb_sheet in sheet_mappings and sheet_mappings[heavydb_sheet] in legacy_sheets:
            legacy_sheet = sheet_mappings[heavydb_sheet]
        # Try case-insensitive match
        else:
            for ls in legacy_sheets:
                if heavydb_sheet.upper() == ls.upper():
                    legacy_sheet = ls
                    break
        
        if legacy_sheet:
            # Load and compare data
            try:
                heavydb_df = pd.read_excel(heavydb_file, sheet_name=heavydb_sheet)
                legacy_df = pd.read_excel(legacy_file, sheet_name=legacy_sheet)
                
                if compare_dataframes(legacy_df, heavydb_df, f"{legacy_sheet} → {heavydb_sheet}"):
                    results['matched'].append(heavydb_sheet)
                else:
                    results['content_differs'].append(heavydb_sheet)
            except Exception as e:
                print(f"  ✗ Error comparing {heavydb_sheet}: {e}")
                results['content_differs'].append(heavydb_sheet)
        else:
            results['extra_in_heavydb'].append(heavydb_sheet)
    
    # Check for missing sheets
    for legacy_sheet in legacy_sheets:
        found = False
        # Check direct match
        if legacy_sheet in heavydb_sheets:
            found = True
        # Check reverse mapping
        else:
            for hdb_sheet, leg_sheet in sheet_mappings.items():
                if leg_sheet == legacy_sheet and hdb_sheet in heavydb_sheets:
                    found = True
                    break
        # Check case-insensitive
        if not found:
            for hdb_sheet in heavydb_sheets:
                if legacy_sheet.upper() == hdb_sheet.upper():
                    found = True
                    break
        
        if not found:
            results['missing_in_heavydb'].append(legacy_sheet)
    
    # Summary report
    print("\n" + "="*80)
    print("COMPARISON SUMMARY")
    print("="*80)
    
    print(f"\n✓ Sheets with matching content: {len(results['matched'])}")
    for sheet in results['matched']:
        print(f"  - {sheet}")
    
    if results['content_differs']:
        print(f"\n⚠️  Sheets with content differences: {len(results['content_differs'])}")
        for sheet in results['content_differs']:
            print(f"  - {sheet}")
    
    if results['missing_in_heavydb']:
        print(f"\n✗ Sheets missing in HeavyDB: {len(results['missing_in_heavydb'])}")
        for sheet in results['missing_in_heavydb']:
            print(f"  - {sheet}")
            # Analyze what these sheets contain
            try:
                df = pd.read_excel(legacy_file, sheet_name=sheet)
                print(f"    (Contains {len(df)} rows, {len(df.columns)} columns)")
            except:
                pass
    
    if results['extra_in_heavydb']:
        print(f"\n➕ Extra sheets in HeavyDB: {len(results['extra_in_heavydb'])}")
        for sheet in results['extra_in_heavydb']:
            print(f"  - {sheet}")
    
    # Overall verdict
    print("\n" + "="*80)
    print("OVERALL VERDICT")
    print("="*80)
    
    if not results['missing_in_heavydb'] and not results['content_differs']:
        print("\n✅ PERFECT MATCH! All sheets and content match between systems.")
    elif not results['missing_in_heavydb']:
        print("\n⚠️  All sheets present but some content differs.")
    else:
        print("\n❌ Some sheets are missing and/or content differs.")
    
    return results

def show_specific_differences(heavydb_file, legacy_file):
    """Show specific differences in key sheets."""
    print("\n" + "="*80)
    print("DETAILED ANALYSIS OF KEY DIFFERENCES")
    print("="*80)
    
    # Check transaction data specifically
    print("\n1. Transaction Data Comparison:")
    try:
        # Find transaction sheets
        heavydb_xl = pd.ExcelFile(heavydb_file)
        legacy_xl = pd.ExcelFile(legacy_file)
        
        hdb_trans_sheets = [s for s in heavydb_xl.sheet_names if 'Trans' in s and 'PORTFOLIO' not in s]
        leg_trans_sheets = [s for s in legacy_xl.sheet_names if 'Trans' in s and 'PORTFOLIO' not in s]
        
        if hdb_trans_sheets and leg_trans_sheets:
            hdb_df = pd.read_excel(heavydb_file, sheet_name=hdb_trans_sheets[0])
            leg_df = pd.read_excel(legacy_file, sheet_name=leg_trans_sheets[0])
            
            print(f"\n  HeavyDB trades: {len(hdb_df)}")
            print(f"  Legacy trades: {len(leg_df)}")
            
            # Compare key columns if they exist
            key_cols = ['leg_id', 'strike', 'instrument_type', 'side', 'entry_time', 'exit_time', 'pnl']
            for col in key_cols:
                if col in hdb_df.columns and col in leg_df.columns:
                    hdb_unique = set(hdb_df[col].dropna())
                    leg_unique = set(leg_df[col].dropna())
                    if hdb_unique != leg_unique:
                        print(f"\n  Differences in '{col}':")
                        print(f"    HeavyDB unique: {hdb_unique - leg_unique}")
                        print(f"    Legacy unique: {leg_unique - hdb_unique}")
    except Exception as e:
        print(f"  Error analyzing transactions: {e}")
    
    # Check metrics specifically
    print("\n2. Metrics Comparison:")
    try:
        hdb_metrics = pd.read_excel(heavydb_file, sheet_name='Metrics')
        leg_metrics = pd.read_excel(legacy_file, sheet_name='Metrics')
        
        # Compare total P&L
        hdb_pnl = hdb_metrics[hdb_metrics['Particulars'].str.contains('Total P', na=False)]
        leg_pnl = leg_metrics[leg_metrics['Particulars'].str.contains('Total P', na=False)]
        
        if not hdb_pnl.empty and not leg_pnl.empty:
            print(f"\n  HeavyDB Total P&L: {hdb_pnl['Value'].iloc[0]}")
            print(f"  Legacy Total P&L: {leg_pnl['Value'].iloc[0]}")
    except Exception as e:
        print(f"  Error analyzing metrics: {e}")

def main():
    """Main execution."""
    print("="*80)
    print("COMPREHENSIVE HEAVYDB vs LEGACY CONTENT COMPARISON")
    print(f"Date: {datetime.now()}")
    print("="*80)
    
    # Run HeavyDB backtest
    heavydb_file = run_heavydb_backtest()
    
    if not heavydb_file:
        print("\n✗ Failed to generate HeavyDB output")
        return
    
    # Legacy file path
    legacy_file = "tests/outputs/gpu_parity_run/NIF0DTE_250401_cpu.xlsx"
    
    if not os.path.exists(legacy_file):
        print(f"\n✗ Legacy file not found: {legacy_file}")
        return
    
    # Perform comprehensive comparison
    results = comprehensive_comparison(heavydb_file, legacy_file)
    
    # Show specific differences
    show_specific_differences(heavydb_file, legacy_file)
    
    print("\n" + "="*80)
    print("COMPARISON COMPLETE")
    print("="*80)

if __name__ == "__main__":
    main() 