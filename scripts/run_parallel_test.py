#!/usr/bin/env python3
"""
Run legacy and new backtesters in parallel and compare results
"""

import subprocess
import os
import sys
import shutil
import pandas as pd
import json
from datetime import datetime
import time
import requests

# Configuration
LEGACY_BASE = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN"
NEW_BASE = "/srv/samba/shared/bt/backtester_stable/BTRUN"

# Common input files
PORTFOLIO_FILE = "input_portfolio.xlsx"
STRATEGY_FILE = "input_tbs_multi_legs.xlsx"

# Date range for testing (limit to specific dates to speed up testing)
START_DATE = "01_04_2025"
END_DATE = "11_04_2025"

def check_legacy_http_service():
    """Check if the legacy HTTP service is running"""
    print("Checking legacy HTTP service availability...")
    
    # These are the endpoints from config.py
    endpoints = [
        "http://192.168.173.120:5000/backtest/start",
        "http://192.168.173.180:5000/backtest/start"
    ]
    
    for endpoint in endpoints:
        try:
            # Try to connect with a short timeout
            response = requests.get(endpoint.replace("/backtest/start", "/"), timeout=2)
            if response.status_code == 200:
                print(f"  ✓ Service available at: {endpoint}")
                return True
        except:
            pass
    
    print("  ❌ Legacy HTTP service not available")
    print("  The legacy backtester requires an HTTP service to be running")
    print("  Endpoints checked:")
    for endpoint in endpoints:
        print(f"    - {endpoint}")
    
    return False

def setup_legacy_input():
    """Set up input files for legacy backtester"""
    print("Setting up legacy input files...")
    
    # Create INPUT SHEETS folder if it doesn't exist
    legacy_input_dir = os.path.join(LEGACY_BASE, "INPUT SHEETS")
    os.makedirs(legacy_input_dir, exist_ok=True)
    
    # Copy portfolio file
    src_portfolio = os.path.join(NEW_BASE, "input_sheets", PORTFOLIO_FILE)
    dst_portfolio = os.path.join(legacy_input_dir, "INPUT PORTFOLIO.xlsx")
    shutil.copy2(src_portfolio, dst_portfolio)
    print(f"  Copied portfolio file to: {dst_portfolio}")
    
    # Copy strategy file
    src_strategy = os.path.join(NEW_BASE, "input_sheets", STRATEGY_FILE)
    dst_strategy = os.path.join(legacy_input_dir, STRATEGY_FILE)
    shutil.copy2(src_strategy, dst_strategy)
    print(f"  Copied strategy file to: {dst_strategy}")
    
    # Update the portfolio file to point to the correct strategy path
    # This might need adjustment based on the actual path structure
    # For now, we'll assume the legacy code can find it
    
    return True

def test_legacy_components():
    """Test individual legacy components that don't require HTTP service"""
    print("\n" + "="*60)
    print("Testing Legacy Components (without HTTP service)")
    print("="*60)
    
    # Change to legacy directory
    original_dir = os.getcwd()
    os.chdir(LEGACY_BASE)
    
    try:
        # Test portfolio parsing
        print("\n1. Testing portfolio parsing...")
        test_code = """
import sys
sys.path.insert(0, '.')
import pandas as pd
from Util import Util

# Test loading portfolio
portfolioForBt, portfolioSettingDf = Util.getPortfolioJson(
    excelFilePath='INPUT SHEETS/INPUT PORTFOLIO.xlsx'
)

print(f"  Loaded {len(portfolioForBt)} portfolio(s)")
for pNo in portfolioForBt:
    portfolio = portfolioForBt[pNo]
    print(f"  Portfolio: {portfolio['portfolio']['name']}")
    print(f"  Strategies: {len(portfolio['strategies'])}")
    for strategy in portfolio['strategies']:
        print(f"    - {strategy.get('strategy_name', 'N/A')}")
"""
        
        # Write test script
        with open("test_legacy_parsing.py", "w") as f:
            f.write(test_code)
        
        # Run test
        env = os.environ.copy()
        env['PYTHONPATH'] = f"{LEGACY_BASE}:{os.path.dirname(LEGACY_BASE)}"
        
        result = subprocess.run(
            [sys.executable, "test_legacy_parsing.py"], 
            capture_output=True, 
            text=True, 
            env=env
        )
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"  ❌ Failed: {result.stderr}")
        
        # Clean up
        if os.path.exists("test_legacy_parsing.py"):
            os.remove("test_legacy_parsing.py")
        
    finally:
        os.chdir(original_dir)

def run_legacy_backtester():
    """Run the legacy backtester"""
    print("\n" + "="*60)
    print("Running LEGACY backtester...")
    print("="*60)
    
    # First check if HTTP service is available
    if not check_legacy_http_service():
        print("\n  ⚠️  Cannot run full legacy backtester without HTTP service")
        print("  Running component tests instead...")
        test_legacy_components()
        return None
    
    start_time = time.time()
    
    # Change to legacy directory
    original_dir = os.getcwd()
    os.chdir(LEGACY_BASE)
    
    try:
        # Run the legacy backtester
        # Need to ensure Python path includes necessary modules
        env = os.environ.copy()
        env['PYTHONPATH'] = f"{LEGACY_BASE}:{os.path.dirname(LEGACY_BASE)}"
        
        cmd = [sys.executable, "BTRunPortfolio.py"]
        print(f"  Command: {' '.join(cmd)}")
        print(f"  Working directory: {os.getcwd()}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, env=env)
        
        if result.returncode != 0:
            print(f"  ❌ Legacy backtester failed with return code: {result.returncode}")
            print(f"  stdout: {result.stdout}")
            print(f"  stderr: {result.stderr}")
            return None
        
        print(f"  ✓ Legacy backtester completed successfully")
        
        # Find the output file in Trades folder
        trades_dir = os.path.join(LEGACY_BASE, "Trades")
        if os.path.exists(trades_dir):
            files = sorted([f for f in os.listdir(trades_dir) if f.endswith('.xlsx')], 
                          key=lambda x: os.path.getmtime(os.path.join(trades_dir, x)))
            if files:
                legacy_output = os.path.join(trades_dir, files[-1])
                print(f"  Output file: {legacy_output}")
                
                # Copy to a standard location
                legacy_result = f"/srv/samba/shared/legacy_test_output.xlsx"
                shutil.copy2(legacy_output, legacy_result)
                print(f"  Copied to: {legacy_result}")
                
                elapsed = time.time() - start_time
                print(f"  Execution time: {elapsed:.2f} seconds")
                
                return legacy_result
            
    finally:
        os.chdir(original_dir)
    
    return None

def run_new_backtester():
    """Run the new HeavyDB backtester"""
    print("\n" + "="*60)
    print("Running NEW (HeavyDB) backtester...")
    print("="*60)
    
    start_time = time.time()
    
    # Build command
    portfolio_path = os.path.join(NEW_BASE, "input_sheets", PORTFOLIO_FILE)
    output_path = "/srv/samba/shared/new_test_output.xlsx"
    
    cmd = [
        sys.executable, "-m", "backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", portfolio_path,
        "--output-path", output_path,
        "--start-date", START_DATE.replace("_", ""),  # Convert to YYYYMMDD format
        "--end-date", END_DATE.replace("_", "")
    ]
    
    print(f"  Command: {' '.join(cmd)}")
    
    # Set up environment
    env = os.environ.copy()
    env['PYTHONPATH'] = "/srv/samba/shared/bt"
    
    result = subprocess.run(cmd, capture_output=True, text=True, cwd="/srv/samba/shared/bt", env=env)
    
    if result.returncode != 0:
        print(f"  ❌ New backtester failed with return code: {result.returncode}")
        print(f"  stdout: {result.stdout}")
        print(f"  stderr: {result.stderr}")
        return None
    
    print(f"  ✓ New backtester completed successfully")
    
    elapsed = time.time() - start_time
    print(f"  Execution time: {elapsed:.2f} seconds")
    
    return output_path

def compare_with_golden():
    """Compare new backtester output with golden file"""
    print("\n" + "="*60)
    print("Comparing with Golden File")
    print("="*60)
    
    new_file = "/srv/samba/shared/new_test_output.xlsx"
    golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"
    
    if not os.path.exists(new_file):
        print("  ❌ New output file not found")
        return
    
    if not os.path.exists(golden_file):
        print("  ❌ Golden file not found")
        return
    
    try:
        # Read both files
        new_xl = pd.ExcelFile(new_file, engine='openpyxl')
        golden_xl = pd.ExcelFile(golden_file, engine='openpyxl')
        
        # Compare PORTFOLIO Trans sheet
        if 'PORTFOLIO Trans' in new_xl.sheet_names and 'PORTFOLIO Trans' in golden_xl.sheet_names:
            new_trans = pd.read_excel(new_file, sheet_name='PORTFOLIO Trans')
            golden_trans = pd.read_excel(golden_file, sheet_name='PORTFOLIO Trans')
            
            print(f"\n  Trade count comparison:")
            print(f"    New: {len(new_trans)} trades")
            print(f"    Golden: {len(golden_trans)} trades")
            
            # Compare P&L
            if 'pnl' in new_trans.columns:
                new_pnl = new_trans['pnl'].sum()
                golden_pnl = golden_trans['PNL'].sum()
                
                print(f"\n  Total P&L comparison:")
                print(f"    New: {new_pnl:.2f}")
                print(f"    Golden: {golden_pnl:.2f}")
                print(f"    Difference: {abs(new_pnl - golden_pnl):.2f}")
                
                if abs(new_pnl - golden_pnl) < 100:  # Within 100 rupees
                    print(f"    ✓ P&L values are close!")
                else:
                    print(f"    ⚠️  Significant P&L difference")
        
    except Exception as e:
        print(f"  ❌ Error comparing with golden file: {e}")
        import traceback
        traceback.print_exc()

def compare_results(legacy_file, new_file):
    """Compare results from both backtesters"""
    print("\n" + "="*60)
    print("Comparing Results")
    print("="*60)
    
    if not legacy_file:
        print("  ⚠️  Legacy output not available - comparing with golden file instead")
        compare_with_golden()
        return
    
    if not new_file:
        print("  ❌ New output not available")
        return
    
    try:
        # Read Excel files
        legacy_xl = pd.ExcelFile(legacy_file, engine='openpyxl')
        new_xl = pd.ExcelFile(new_file, engine='openpyxl')
        
        print(f"\n  Legacy sheets: {legacy_xl.sheet_names}")
        print(f"  New sheets: {new_xl.sheet_names}")
        
        # Compare key sheets
        key_sheets = ['PORTFOLIO Trans', 'Metrics']
        
        for sheet in key_sheets:
            print(f"\n  Comparing {sheet}:")
            
            # Check if sheet exists in both
            if sheet not in legacy_xl.sheet_names and sheet not in new_xl.sheet_names:
                print(f"    ⚠️  Sheet missing in both outputs")
                continue
            elif sheet not in legacy_xl.sheet_names:
                print(f"    ⚠️  Sheet missing in legacy output")
                continue
            elif sheet not in new_xl.sheet_names:
                print(f"    ⚠️  Sheet missing in new output")
                continue
            
            legacy_df = pd.read_excel(legacy_file, sheet_name=sheet)
            new_df = pd.read_excel(new_file, sheet_name=sheet)
            
            print(f"    Legacy shape: {legacy_df.shape}")
            print(f"    New shape: {new_df.shape}")
            
            if sheet == 'PORTFOLIO Trans':
                # Compare trade counts
                print(f"    Legacy trades: {len(legacy_df)}")
                print(f"    New trades: {len(new_df)}")
                
                # Compare P&L
                if 'PNL' in legacy_df.columns or 'pnl' in legacy_df.columns:
                    legacy_pnl_col = 'PNL' if 'PNL' in legacy_df.columns else 'pnl'
                    legacy_total_pnl = legacy_df[legacy_pnl_col].sum()
                    print(f"    Legacy total P&L: {legacy_total_pnl:.2f}")
                
                if 'pnl' in new_df.columns:
                    new_total_pnl = new_df['pnl'].sum()
                    print(f"    New total P&L: {new_total_pnl:.2f}")
                    
                    if 'PNL' in legacy_df.columns or 'pnl' in legacy_df.columns:
                        diff = abs(new_total_pnl - legacy_total_pnl)
                        pct_diff = (diff / abs(legacy_total_pnl) * 100) if legacy_total_pnl != 0 else 0
                        print(f"    Difference: {diff:.2f} ({pct_diff:.2f}%)")
                        
                        if pct_diff < 0.1:  # Less than 0.1% difference
                            print(f"    ✓ P&L values match closely!")
                        elif pct_diff < 1:  # Less than 1% difference
                            print(f"    ⚠️  Small P&L difference detected")
                        else:
                            print(f"    ❌ Significant P&L difference!")
                
        print("\n" + "="*60)
        print("Summary:")
        print("  - Legacy output: " + ("✓ Generated" if legacy_file else "❌ Failed"))
        print("  - New output: " + ("✓ Generated" if new_file else "❌ Failed"))
        
    except Exception as e:
        print(f"  ❌ Error comparing results: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function to orchestrate parallel testing"""
    print("Parallel Backtester Testing")
    print("="*60)
    print(f"Start Date: {START_DATE}")
    print(f"End Date: {END_DATE}")
    
    # Setup legacy input
    if not setup_legacy_input():
        print("❌ Failed to setup legacy input files")
        return
    
    # Run both backtesters
    legacy_output = run_legacy_backtester()
    new_output = run_new_backtester()
    
    # Compare results
    compare_results(legacy_output, new_output)

if __name__ == "__main__":
    main() 