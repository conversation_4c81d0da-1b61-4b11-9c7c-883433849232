#!/usr/bin/env python3
"""
Check specific strike 23450 which HeavyDB identifies as ATM
"""

import mysql.connector

# MySQL connection
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def check_strike_23450():
    """Check strike 23450 specifically"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    date = '250401'
    time = 33360  # 9:16 AM
    expiry = '250409'  # April 9 expiry
    
    print("Checking Strike 23450 (HeavyDB's ATM)")
    print("="*60)
    
    # Get spot price
    cursor.execute("SELECT close FROM nifty_cash WHERE date = %s AND time = %s", (date, time))
    spot = cursor.fetchone()
    spot_price = spot[0] / 100 if spot else 0
    print(f"Spot Price: {spot_price:.2f}")
    
    # Check if 23450 exists with April 9 expiry
    print(f"\nChecking strike 23450 with expiry {expiry}:")
    
    cursor.execute("""
        SELECT strike, close, 'CE' as type 
        FROM nifty_call 
        WHERE date = %s AND time = %s AND strike = 23450 AND expiry = %s
        UNION ALL
        SELECT strike, close, 'PE' as type 
        FROM nifty_put 
        WHERE date = %s AND time = %s AND strike = 23450 AND expiry = %s
    """, (date, time, expiry, date, time, expiry))
    
    results = cursor.fetchall()
    ce_price = None
    pe_price = None
    
    for strike, close, opt_type in results:
        price = close / 100
        print(f"  {opt_type}: {price:.2f}")
        if opt_type == 'CE':
            ce_price = price
        else:
            pe_price = price
    
    if ce_price and pe_price:
        synthetic_future = 23450 + ce_price - pe_price
        diff = abs(synthetic_future - spot_price)
        print(f"\nSynthetic Future Calculation:")
        print(f"  Strike + CE - PE = {23450} + {ce_price:.2f} - {pe_price:.2f} = {synthetic_future:.2f}")
        print(f"  Difference from spot: {diff:.2f}")
    
    # Now check all strikes and their synthetic future values
    print("\n\nAll Strikes with Synthetic Future Values:")
    print("Strike | CE Price | PE Price | Syn Future | Diff from Spot")
    print("-" * 60)
    
    cursor.execute("""
        SELECT 
            c.strike,
            c.close as ce_close,
            p.close as pe_close
        FROM nifty_call c
        JOIN nifty_put p ON c.date = p.date AND c.time = p.time 
                          AND c.strike = p.strike AND c.expiry = p.expiry
        WHERE c.date = %s AND c.time = %s AND c.expiry = %s
        AND c.close > 0 AND p.close > 0
        AND c.strike BETWEEN 23000 AND 24000
        ORDER BY c.strike
    """, (date, time, expiry))
    
    min_diff = float('inf')
    best_strike = None
    
    for strike, ce_close, pe_close in cursor.fetchall():
        ce_price = ce_close / 100
        pe_price = pe_close / 100
        syn_future = strike + ce_price - pe_price
        diff = abs(syn_future - spot_price)
        
        if diff < min_diff:
            min_diff = diff
            best_strike = strike
        
        # Highlight strike 23450
        marker = " <-- HeavyDB's ATM" if strike == 23450 else ""
        print(f"{strike:6.0f} | {ce_price:8.2f} | {pe_price:8.2f} | {syn_future:10.2f} | {diff:14.2f}{marker}")
    
    print(f"\nBest ATM by synthetic future: {best_strike} (diff: {min_diff:.2f})")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    check_strike_23450() 