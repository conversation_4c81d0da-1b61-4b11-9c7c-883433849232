#!/usr/bin/env python3
"""
Expiry-focused data integrity check
Compares only CW (Current Week), NW (Next Week), CM (Current Month), NM (Next Month) expiries
"""

import mysql.connector
import pandas as pd
from datetime import datetime, timedelta
import calendar

# Database configurations
MYSQL_CONFIG = {
    "host": "************",
    "user": "mahesh",
    "password": "mahesh_123",
    "database": "historicaldb"
}

HEAVYDB_CONFIG = {
    "host": "127.0.0.1",
    "port": 6274,
    "user": "admin",
    "password": "HyperInteractive",
    "dbname": "heavyai"
}

def get_expiry_dates(test_date):
    """Calculate CW, NW, CM, NM expiry dates from a given date"""
    # Convert string to datetime if needed
    if isinstance(test_date, str):
        test_date = datetime.strptime(test_date, '%Y-%m-%d')
    
    expiries = {}
    
    # Current Week (Thursday)
    days_ahead = 3 - test_date.weekday()  # Thursday is 3
    if days_ahead <= 0:  # Target day already happened this week
        days_ahead += 7
    expiries['CW'] = test_date + timedelta(days=days_ahead)
    
    # Next Week
    expiries['NW'] = expiries['CW'] + timedelta(days=7)
    
    # Current Month (last Thursday)
    year, month = test_date.year, test_date.month
    last_day = calendar.monthrange(year, month)[1]
    last_date = datetime(year, month, last_day)
    
    # Find last Thursday
    while last_date.weekday() != 3:  # Thursday
        last_date -= timedelta(days=1)
    
    if last_date.date() >= test_date.date():
        expiries['CM'] = last_date
    else:
        # Move to next month
        if month == 12:
            year += 1
            month = 1
        else:
            month += 1
        last_day = calendar.monthrange(year, month)[1]
        last_date = datetime(year, month, last_day)
        while last_date.weekday() != 3:
            last_date -= timedelta(days=1)
        expiries['CM'] = last_date
    
    # Next Month
    cm_year, cm_month = expiries['CM'].year, expiries['CM'].month
    if cm_month == 12:
        nm_year, nm_month = cm_year + 1, 1
    else:
        nm_year, nm_month = cm_year, cm_month + 1
    
    last_day = calendar.monthrange(nm_year, nm_month)[1]
    last_date = datetime(nm_year, nm_month, last_day)
    while last_date.weekday() != 3:
        last_date -= timedelta(days=1)
    expiries['NM'] = last_date
    
    return expiries

def check_mysql_expiry_data(cursor, test_date, expiry_dates):
    """Check MySQL data for specific expiries"""
    print(f"\nMySQL Data for {test_date}:")
    print("-" * 60)
    
    for expiry_type, expiry_date in expiry_dates.items():
        expiry_str = expiry_date.strftime('%Y-%m-%d')
        
        # Check call options
        query = f"""
        SELECT 
            COUNT(*) as records,
            COUNT(DISTINCT strike) as strikes,
            MIN(close/100.0) as min_price,
            MAX(close/100.0) as max_price
        FROM nifty_call
        WHERE DATE(date) = '{test_date}'
        AND expiry = '{expiry_str}'
        """
        
        try:
            cursor.execute(query)
            result = cursor.fetchone()
            
            if result and result[0] > 0:
                print(f"\n{expiry_type} Expiry ({expiry_str}):")
                print(f"  Call Options: {result[0]:,} records, {result[1]} strikes")
                print(f"  Price range: {result[2]:.2f} to {result[3]:.2f}")
                
                # Check put options
                query = query.replace('nifty_call', 'nifty_put')
                cursor.execute(query)
                result = cursor.fetchone()
                if result and result[0] > 0:
                    print(f"  Put Options: {result[0]:,} records")
            else:
                print(f"\n{expiry_type} Expiry ({expiry_str}): No data")
                
        except Exception as e:
            print(f"\n{expiry_type} Expiry ({expiry_str}): Error - {e}")

def check_heavydb_expiry_data(cursor, test_date, expiry_dates):
    """Check HeavyDB data for specific expiries"""
    print(f"\nHeavyDB Data for {test_date}:")
    print("-" * 60)
    
    # First check if we have any data for the test date
    try:
        cursor.execute(f"""
        SELECT COUNT(*) 
        FROM nifty_option_chain 
        WHERE trade_date = DATE '{test_date}'
        """)
        total_count = cursor.fetchone()[0]
        
        if total_count == 0:
            print(f"  ⚠ No data found for {test_date}")
            return
            
    except Exception as e:
        print(f"  Error checking data: {e}")
        return
    
    for expiry_type, expiry_date in expiry_dates.items():
        expiry_str = expiry_date.strftime('%Y-%m-%d')
        
        query = f"""
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN ce_symbol IS NOT NULL THEN 1 END) as call_records,
            COUNT(CASE WHEN pe_symbol IS NOT NULL THEN 1 END) as put_records,
            COUNT(DISTINCT strike) as strikes,
            MIN(CASE WHEN ce_close > 0 THEN ce_close END) as min_call_price,
            MAX(ce_close) as max_call_price,
            COUNT(DISTINCT atm_strike) as atm_count
        FROM nifty_option_chain
        WHERE trade_date = DATE '{test_date}'
        AND expiry_date = DATE '{expiry_str}'
        """
        
        try:
            cursor.execute(query)
            result = cursor.fetchone()
            
            if result and result[0] > 0:
                print(f"\n{expiry_type} Expiry ({expiry_str}):")
                print(f"  Total records: {result[0]:,}")
                print(f"  Call Options: {result[1]:,} records")
                print(f"  Put Options: {result[2]:,} records")
                print(f"  Unique strikes: {result[3]}")
                if result[4] and result[5]:
                    print(f"  Call price range: {result[4]:.2f} to {result[5]:.2f}")
                print(f"  ATM strikes identified: {result[6]}")
                
                # Check expiry bucket classification
                cursor.execute(f"""
                SELECT DISTINCT expiry_bucket
                FROM nifty_option_chain
                WHERE trade_date = DATE '{test_date}'
                AND expiry_date = DATE '{expiry_str}'
                """)
                buckets = [row[0] for row in cursor.fetchall()]
                if buckets:
                    print(f"  Expiry buckets: {', '.join(buckets)}")
            else:
                print(f"\n{expiry_type} Expiry ({expiry_str}): No data")
                
        except Exception as e:
            print(f"\n{expiry_type} Expiry ({expiry_str}): Error - {e}")

def compare_specific_strike(mysql_cursor, heavydb_cursor, test_date, expiry_date, strike):
    """Compare specific strike data between databases"""
    print(f"\nStrike Comparison - {strike} @ {test_date} (Expiry: {expiry_date}):")
    print("-" * 60)
    
    # MySQL data
    mysql_query = f"""
    SELECT 
        MIN(time) as first_time,
        MAX(time) as last_time,
        COUNT(*) as records,
        MIN(close/100.0) as min_price,
        MAX(close/100.0) as max_price
    FROM nifty_call
    WHERE DATE(date) = '{test_date}'
    AND expiry = '{expiry_date}'
    AND strike = {strike}
    """
    
    try:
        mysql_cursor.execute(mysql_query)
        mysql_result = mysql_cursor.fetchone()
        
        if mysql_result and mysql_result[2] > 0:
            print("MySQL:")
            print(f"  Records: {mysql_result[2]}")
            print(f"  Time range: {mysql_result[0]} to {mysql_result[1]} seconds")
            print(f"  Price range: {mysql_result[3]:.2f} to {mysql_result[4]:.2f}")
        else:
            print("MySQL: No data")
    except Exception as e:
        print(f"MySQL: Error - {e}")
    
    # HeavyDB data
    heavydb_query = f"""
    SELECT 
        MIN(trade_time) as first_time,
        MAX(trade_time) as last_time,
        COUNT(*) as records,
        MIN(ce_close) as min_price,
        MAX(ce_close) as max_price,
        atm_strike,
        call_strike_type
    FROM nifty_option_chain
    WHERE trade_date = DATE '{test_date}'
    AND expiry_date = DATE '{expiry_date}'
    AND strike = {strike}
    AND ce_symbol IS NOT NULL
    GROUP BY atm_strike, call_strike_type
    """
    
    try:
        heavydb_cursor.execute(heavydb_query)
        heavydb_results = heavydb_cursor.fetchall()
        
        if heavydb_results:
            print("\nHeavyDB:")
            for result in heavydb_results:
                print(f"  Records: {result[2]}")
                print(f"  Time range: {result[0]} to {result[1]}")
                print(f"  Price range: {result[3]:.2f} to {result[4]:.2f}")
                print(f"  ATM Strike: {result[5]}, Strike Type: {result[6]}")
        else:
            print("\nHeavyDB: No data")
    except Exception as e:
        print(f"\nHeavyDB: Error - {e}")

def main():
    """Main execution"""
    print("="*60)
    print("Expiry-Focused Data Integrity Check")
    print("Comparing CW, NW, CM, NM expiries only")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Test dates
    test_dates = ['2025-04-01', '2025-04-03', '2025-04-09']
    
    # Connect to databases
    try:
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        mysql_cursor = mysql_conn.cursor()
        print("✓ Connected to MySQL")
    except Exception as e:
        print(f"✗ MySQL connection failed: {e}")
        return
    
    try:
        from heavydb import connect
    except ImportError:
        import pymapd
        connect = pymapd.connect
    
    try:
        heavydb_conn = connect(**HEAVYDB_CONFIG)
        heavydb_cursor = heavydb_conn.cursor()
        print("✓ Connected to HeavyDB")
    except Exception as e:
        print(f"✗ HeavyDB connection failed: {e}")
        mysql_cursor.close()
        mysql_conn.close()
        return
    
    # Check each test date
    for test_date in test_dates:
        print("\n" + "="*60)
        print(f"Checking data for: {test_date}")
        print("="*60)
        
        # Calculate expiry dates
        expiry_dates = get_expiry_dates(test_date)
        print("\nExpiry Dates:")
        for exp_type, exp_date in expiry_dates.items():
            print(f"  {exp_type}: {exp_date.strftime('%Y-%m-%d (%A)')}")
        
        # Check data in both databases
        check_mysql_expiry_data(mysql_cursor, test_date, expiry_dates)
        check_heavydb_expiry_data(heavydb_cursor, test_date, expiry_dates)
        
        # Compare specific strike for CW expiry
        if 'CW' in expiry_dates:
            compare_specific_strike(
                mysql_cursor, 
                heavydb_cursor, 
                test_date, 
                expiry_dates['CW'].strftime('%Y-%m-%d'),
                23000  # Example strike
            )
    
    # Cleanup
    mysql_cursor.close()
    mysql_conn.close()
    heavydb_cursor.close()
    heavydb_conn.close()
    
    print("\n" + "="*60)
    print("Expiry-Focused Check Complete")
    print("="*60)

if __name__ == "__main__":
    main() 