"""
Create UAT Test Users Script
Creates test user accounts for User Acceptance Testing
"""

import mysql.connector
import hashlib
import secrets
import json
from datetime import datetime, timedelta
from typing import List, Dict

class UATUserCreator:
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'your_password',  # Update with actual password
            'database': 'gpu_backtester'
        }
        self.test_users = []
        
    def connect_db(self):
        """Connect to MySQL database"""
        return mysql.connector.connect(**self.db_config)
    
    def generate_api_key(self) -> str:
        """Generate secure API key for user"""
        return secrets.token_urlsafe(32)
    
    def hash_password(self, password: str) -> str:
        """Hash password for storage"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def create_test_users(self) -> List[Dict]:
        """Create different types of test users for UAT"""
        
        users = [
            # Power Users (experienced)
            {
                'phone': '9876543210',
                'name': 'Power User 1',
                'email': '<EMAIL>',
                'role': 'power_user',
                'password': 'PowerTest@123',
                'description': 'Experienced trader with backtesting knowledge'
            },
            {
                'phone': '9876543211',
                'name': 'Power User 2',
                'email': '<EMAIL>',
                'role': 'power_user',
                'password': 'PowerTest@123',
                'description': 'Strategy expert with technical knowledge'
            },
            {
                'phone': '9876543212',
                'name': 'Power User 3',
                'email': '<EMAIL>',
                'role': 'power_user',
                'password': 'PowerTest@123',
                'description': 'Quantitative analyst'
            },
            
            # Regular Users (standard)
            {
                'phone': '9876543220',
                'name': 'Regular User 1',
                'email': '<EMAIL>',
                'role': 'regular_user',
                'password': 'RegularTest@123',
                'description': 'Standard trader with basic knowledge'
            },
            {
                'phone': '9876543221',
                'name': 'Regular User 2',
                'email': '<EMAIL>',
                'role': 'regular_user',
                'password': 'RegularTest@123',
                'description': 'Options trader'
            },
            {
                'phone': '9876543222',
                'name': 'Regular User 3',
                'email': '<EMAIL>',
                'role': 'regular_user',
                'password': 'RegularTest@123',
                'description': 'Intraday trader'
            },
            {
                'phone': '9876543223',
                'name': 'Regular User 4',
                'email': '<EMAIL>',
                'role': 'regular_user',
                'password': 'RegularTest@123',
                'description': 'Swing trader'
            },
            {
                'phone': '9876543224',
                'name': 'Regular User 5',
                'email': '<EMAIL>',
                'role': 'regular_user',
                'password': 'RegularTest@123',
                'description': 'Portfolio manager'
            },
            
            # New Users (beginners)
            {
                'phone': '9876543230',
                'name': 'New User 1',
                'email': '<EMAIL>',
                'role': 'new_user',
                'password': 'NewTest@123',
                'description': 'Beginner with limited backtesting experience'
            },
            {
                'phone': '9876543231',
                'name': 'New User 2',
                'email': '<EMAIL>',
                'role': 'new_user',
                'password': 'NewTest@123',
                'description': 'First-time backtesting user'
            },
            {
                'phone': '9876543232',
                'name': 'New User 3',
                'email': '<EMAIL>',
                'role': 'new_user',
                'password': 'NewTest@123',
                'description': 'Learning algorithmic trading'
            },
            
            # Admin User for UAT support
            {
                'phone': '9876543200',
                'name': 'UAT Admin',
                'email': '<EMAIL>',
                'role': 'admin',
                'password': 'AdminTest@123',
                'description': 'UAT administrator with full access'
            }
        ]
        
        return users
    
    def create_user_in_db(self, user: Dict) -> Dict:
        """Create user in database"""
        conn = self.connect_db()
        cursor = conn.cursor()
        
        try:
            # Check if user already exists
            cursor.execute("SELECT id FROM users WHERE phone = %s", (user['phone'],))
            existing = cursor.fetchone()
            
            if existing:
                print(f"User {user['phone']} already exists, skipping...")
                return None
            
            # Create user
            api_key = self.generate_api_key()
            insert_query = """
                INSERT INTO users (phone, name, email, password_hash, api_key, 
                                 role, is_active, created_at, last_login, 
                                 uat_user, description)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            cursor.execute(insert_query, (
                user['phone'],
                user['name'],
                user['email'],
                self.hash_password(user['password']),
                api_key,
                user['role'],
                True,
                datetime.now(),
                None,
                True,  # Mark as UAT user
                user['description']
            ))
            
            user_id = cursor.lastrowid
            
            # Create user preferences
            pref_query = """
                INSERT INTO user_preferences (user_id, preferred_indices, 
                                            default_strategy, theme, 
                                            notifications_enabled)
                VALUES (%s, %s, %s, %s, %s)
            """
            
            # Set different preferences based on user type
            if user['role'] == 'power_user':
                indices = json.dumps(['NIFTY', 'BANKNIFTY', 'FINNIFTY'])
                strategy = 'TBS'
            elif user['role'] == 'regular_user':
                indices = json.dumps(['NIFTY', 'BANKNIFTY'])
                strategy = 'TV'
            else:
                indices = json.dumps(['NIFTY'])
                strategy = 'ORB'
            
            cursor.execute(pref_query, (
                user_id,
                indices,
                strategy,
                'light',
                True
            ))
            
            # Create usage quota
            quota_query = """
                INSERT INTO user_quotas (user_id, daily_backtest_limit, 
                                       monthly_backtest_limit, storage_limit_mb,
                                       concurrent_backtest_limit)
                VALUES (%s, %s, %s, %s, %s)
            """
            
            # Different quotas based on role
            if user['role'] == 'admin':
                daily = 1000
                monthly = 30000
                storage = 10240  # 10GB
                concurrent = 50
            elif user['role'] == 'power_user':
                daily = 100
                monthly = 3000
                storage = 5120  # 5GB
                concurrent = 10
            elif user['role'] == 'regular_user':
                daily = 50
                monthly = 1500
                storage = 2048  # 2GB
                concurrent = 5
            else:  # new_user
                daily = 20
                monthly = 600
                storage = 1024  # 1GB
                concurrent = 2
            
            cursor.execute(quota_query, (
                user_id,
                daily,
                monthly,
                storage,
                concurrent
            ))
            
            conn.commit()
            
            # Return created user info
            return {
                'user_id': user_id,
                'phone': user['phone'],
                'name': user['name'],
                'password': user['password'],
                'api_key': api_key,
                'role': user['role']
            }
            
        except Exception as e:
            conn.rollback()
            print(f"Error creating user {user['phone']}: {e}")
            return None
        finally:
            cursor.close()
            conn.close()
    
    def create_sample_portfolios(self, user_id: int, role: str):
        """Create sample portfolios for testing"""
        conn = self.connect_db()
        cursor = conn.cursor()
        
        portfolios = []
        
        if role in ['power_user', 'admin']:
            portfolios = [
                {
                    'name': 'Conservative Iron Condor',
                    'strategy_type': 'TBS',
                    'capital': 1000000,
                    'description': 'Low-risk iron condor strategy'
                },
                {
                    'name': 'Aggressive Straddle',
                    'strategy_type': 'TBS',
                    'capital': 500000,
                    'description': 'High volatility capture strategy'
                },
                {
                    'name': 'TV Signals Portfolio',
                    'strategy_type': 'TV',
                    'capital': 750000,
                    'description': 'TradingView signal-based trading'
                }
            ]
        elif role == 'regular_user':
            portfolios = [
                {
                    'name': 'Basic Options Portfolio',
                    'strategy_type': 'TBS',
                    'capital': 500000,
                    'description': 'Simple options strategies'
                },
                {
                    'name': 'ORB Intraday',
                    'strategy_type': 'ORB',
                    'capital': 250000,
                    'description': 'Opening range breakout'
                }
            ]
        else:  # new_user
            portfolios = [
                {
                    'name': 'Starter Portfolio',
                    'strategy_type': 'ORB',
                    'capital': 100000,
                    'description': 'Beginner-friendly strategy'
                }
            ]
        
        try:
            for portfolio in portfolios:
                insert_query = """
                    INSERT INTO user_portfolios (user_id, name, strategy_type,
                                               capital, description, is_active,
                                               created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                
                cursor.execute(insert_query, (
                    user_id,
                    portfolio['name'],
                    portfolio['strategy_type'],
                    portfolio['capital'],
                    portfolio['description'],
                    True,
                    datetime.now()
                ))
            
            conn.commit()
            print(f"Created {len(portfolios)} sample portfolios for user {user_id}")
            
        except Exception as e:
            conn.rollback()
            print(f"Error creating portfolios: {e}")
        finally:
            cursor.close()
            conn.close()
    
    def setup_uat_environment(self):
        """Complete UAT environment setup"""
        print("=== Setting up UAT Environment ===\n")
        
        # Create test users
        users = self.create_test_users()
        created_users = []
        
        print("Creating test users...")
        for user in users:
            result = self.create_user_in_db(user)
            if result:
                created_users.append(result)
                print(f"✓ Created: {result['name']} ({result['phone']})")
                
                # Create sample portfolios
                self.create_sample_portfolios(result['user_id'], result['role'])
        
        # Save credentials to file
        self.save_credentials(created_users)
        
        print(f"\n✅ Created {len(created_users)} test users successfully!")
        print("📄 Credentials saved to: uat_test_credentials.json")
        
        return created_users
    
    def save_credentials(self, users: List[Dict]):
        """Save user credentials to file"""
        credentials = {
            'uat_environment': {
                'url': 'http://173.208.247.17:8000',
                'api_base': 'http://173.208.247.17:8000/api/v2',
                'created_at': datetime.now().isoformat()
            },
            'test_users': users,
            'user_groups': {
                'power_users': [u for u in users if u['role'] == 'power_user'],
                'regular_users': [u for u in users if u['role'] == 'regular_user'],
                'new_users': [u for u in users if u['role'] == 'new_user'],
                'admin': [u for u in users if u['role'] == 'admin']
            },
            'test_instructions': {
                'login': 'Use phone number and password to login',
                'otp': 'For testing, OTP is always 123456',
                'api_access': 'Use api_key in Authorization header as: Bearer {api_key}'
            }
        }
        
        with open('uat_test_credentials.json', 'w') as f:
            json.dump(credentials, f, indent=2)
    
    def create_database_tables(self):
        """Create necessary database tables if they don't exist"""
        conn = self.connect_db()
        cursor = conn.cursor()
        
        # Add UAT-specific columns to users table if needed
        try:
            cursor.execute("""
                ALTER TABLE users 
                ADD COLUMN IF NOT EXISTS uat_user BOOLEAN DEFAULT FALSE,
                ADD COLUMN IF NOT EXISTS description TEXT
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_preferences (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    preferred_indices JSON,
                    default_strategy VARCHAR(10),
                    theme VARCHAR(20) DEFAULT 'light',
                    notifications_enabled BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_quotas (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    daily_backtest_limit INT DEFAULT 50,
                    monthly_backtest_limit INT DEFAULT 1500,
                    storage_limit_mb INT DEFAULT 2048,
                    concurrent_backtest_limit INT DEFAULT 5,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_portfolios (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    name VARCHAR(100) NOT NULL,
                    strategy_type VARCHAR(10),
                    capital DECIMAL(15,2),
                    description TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """)
            
            conn.commit()
            print("✓ Database tables ready")
            
        except Exception as e:
            print(f"Error creating tables: {e}")
            conn.rollback()
        finally:
            cursor.close()
            conn.close()


def main():
    """Main function to create UAT users"""
    creator = UATUserCreator()
    
    # Create necessary tables
    creator.create_database_tables()
    
    # Set up UAT environment
    created_users = creator.setup_uat_environment()
    
    # Print summary
    print("\n=== UAT User Creation Summary ===")
    print(f"Total users created: {len(created_users)}")
    print("\nUser Types:")
    print(f"  - Power Users: {len([u for u in created_users if u['role'] == 'power_user'])}")
    print(f"  - Regular Users: {len([u for u in created_users if u['role'] == 'regular_user'])}")
    print(f"  - New Users: {len([u for u in created_users if u['role'] == 'new_user'])}")
    print(f"  - Admin: {len([u for u in created_users if u['role'] == 'admin'])}")
    
    print("\n📱 Test Login Instructions:")
    print("1. Navigate to http://173.208.247.17:8000")
    print("2. Use phone number from uat_test_credentials.json")
    print("3. Use password from credentials file")
    print("4. OTP for testing is: 123456")


if __name__ == "__main__":
    main()