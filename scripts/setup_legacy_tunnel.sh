#!/bin/bash

echo "========================================="
echo "Setting up tunnel to Legacy Service"
echo "========================================="

# Windows Server Details (from your RDP config)
WINDOWS_HOST="************"
WINDOWS_USER="MahaInvest"
WINDOWS_PASS="Maruth@123"
SSH_PORT="22"  # Default SSH port on Windows

# Legacy service details
LEGACY_INTERNAL_IP="***************"
LEGACY_PORT="5000"
LOCAL_PORT="5001"

echo "
To access the legacy service from Linux, you have several options:

1. SSH Tunnel (if SSH is enabled on Windows):
   ssh -L ${LOCAL_PORT}:${LEGACY_INTERNAL_IP}:${LEGACY_PORT} ${WINDOWS_USER}@${WINDOWS_HOST} -p ${SSH_PORT}
   
   Note: You may need to enable SSH on the Windows server first.

2. Use RDP + Port Forwarding:
   - RDP to ${WINDOWS_HOST}:33898
   - Run: netsh interface portproxy add v4tov4 listenport=5000 listenaddress=${WINDOWS_HOST} connectport=5000 connectaddress=${LEGACY_INTERNAL_IP}
   
3. Direct Execution via RDP:
   - Copy test scripts to Windows server
   - Run legacy backtester directly on Windows
   - Copy results back

4. Install SSH Server on Windows:
   - Enable OpenSSH Server on Windows
   - Then use SSH tunnel option #1
"

# Alternative: Use plink (PuTTY) for Windows SSH
echo "
Alternative using plink:
plink -L ${LOCAL_PORT}:${LEGACY_INTERNAL_IP}:${LEGACY_PORT} ${WINDOWS_USER}@${WINDOWS_HOST} -pw ${WINDOWS_PASS}
" 