#!/usr/bin/env python3
"""Final monitoring script for the full year 2024 TBS backtest"""
import os
import time
import psutil
from datetime import datetime

def monitor_final_backtest():
    """Monitor the final full year backtest"""
    output_file = "bt/backtester_stable/BTRUN/output/tbs_2024_full_year_final.xlsx"
    
    print("🚀 GPU-Optimized TBS Backtesting System - Full Year 2024")
    print("=" * 60)
    print(f"📊 Strategy: Time-Based Strategy (TBS)")
    print(f"📅 Period: January 1, 2024 - December 31, 2024 (365 days)")
    print(f"⚡ GPU Optimization: Enabled with 4 workers")
    print(f"📁 Output: {output_file}")
    print("=" * 60)
    print()
    
    start_time = datetime.now()
    last_size = 0
    
    while True:
        try:
            # Check for running processes
            gpu_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or [])
                    if 'BTRunPortfolio_GPU' in cmdline and 'tbs_2024_full_year_final' in cmdline:
                        gpu_processes.append(proc.info['pid'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Check output file
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                mod_time = datetime.fromtimestamp(os.path.getmtime(output_file))
                elapsed = datetime.now() - start_time
                
                size_change = file_size - last_size
                time_since_mod = (datetime.now() - mod_time).seconds
                
                # Progress estimation
                estimated_size = 20 * 1024 * 1024  # 20MB estimate
                progress = min(100, (file_size / estimated_size) * 100)
                
                print(f"\r⏱️  [{datetime.now().strftime('%H:%M:%S')}] "
                      f"📈 Size: {file_size/1024/1024:.2f}MB "
                      f"(+{size_change/1024:.1f}KB) | "
                      f"🔄 Progress: ~{progress:.1f}% | "
                      f"🖥️  Processes: {len(gpu_processes)} | "
                      f"⌛ Elapsed: {elapsed} | "
                      f"🔄 Updated: {time_since_mod}s ago", end='', flush=True)
                
                last_size = file_size
                
                # Check completion
                if time_since_mod > 120 and len(gpu_processes) == 0:
                    print(f"\n\n🎉 BACKTEST COMPLETED SUCCESSFULLY!")
                    print(f"📊 Final output size: {file_size/1024/1024:.2f}MB")
                    print(f"⏱️  Total execution time: {elapsed}")
                    print(f"📈 Processing rate: {365 / max(1, elapsed.total_seconds() / 3600):.1f} days/hour")
                    print(f"💾 Output saved to: {output_file}")
                    print("\n✅ The TBS system has successfully processed the entire year 2024!")
                    break
                    
            else:
                elapsed = datetime.now() - start_time
                print(f"\r⏳ [{datetime.now().strftime('%H:%M:%S')}] "
                      f"Initializing backtest... | "
                      f"🖥️  Processes: {len(gpu_processes)} | "
                      f"⌛ Elapsed: {elapsed}", end='', flush=True)
            
            time.sleep(15)  # Check every 15 seconds
            
        except KeyboardInterrupt:
            print(f"\n\n⏹️  Monitoring stopped by user")
            print(f"⏱️  Monitoring duration: {datetime.now() - start_time}")
            break
        except Exception as e:
            print(f"\n\n❌ Monitoring error: {e}")
            break

if __name__ == "__main__":
    monitor_final_backtest() 