#!/usr/bin/env python3
"""
Check MySQL data for April 1-3 with near expiry (April 3, 2025)
to understand discrepancies when using the nearest expiry
"""

import mysql.connector

# MySQL connection
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def check_near_expiry_data(date, expiry='250403'):
    """Check data availability for near expiry"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    time = 33360  # 9:16 AM
    
    print(f"\n{'='*70}")
    print(f"Date: {date} | Near Expiry: {expiry}")
    print(f"{'='*70}")
    
    # Get spot price
    cursor.execute("SELECT close FROM nifty_cash WHERE date = %s AND time = %s", (date, time))
    spot = cursor.fetchone()
    spot_price = spot[0] / 100 if spot else 0
    print(f"Spot Price: {spot_price:.2f}")
    
    # Check available expiries for this date
    print("\nAvailable Expiries:")
    cursor.execute("""
        SELECT DISTINCT expiry, COUNT(*) as count
        FROM nifty_call
        WHERE date = %s AND time = %s
        GROUP BY expiry
        ORDER BY expiry
    """, (date, time))
    
    for exp, count in cursor.fetchall():
        marker = " <-- Near Expiry" if exp == expiry else ""
        print(f"  {exp}: {count} call options{marker}")
    
    # Check strikes with both CE and PE for near expiry
    print(f"\nOption Chain for Near Expiry ({expiry}):")
    print("Strike | CE Price | PE Price | Syn Future | Diff | Complete")
    print("-" * 65)
    
    cursor.execute("""
        SELECT 
            c.strike,
            c.close as ce_close,
            p.close as pe_close
        FROM nifty_call c
        LEFT JOIN nifty_put p ON c.date = p.date AND c.time = p.time 
                                AND c.strike = p.strike AND c.expiry = p.expiry
        WHERE c.date = %s AND c.time = %s AND c.expiry = %s
        AND c.strike BETWEEN 22000 AND 25000
        ORDER BY c.strike
    """, (date, time, expiry))
    
    complete_pairs = 0
    missing_pe = 0
    best_atm = None
    min_diff = float('inf')
    
    for strike, ce_close, pe_close in cursor.fetchall():
        if ce_close and pe_close:
            complete_pairs += 1
            ce_price = ce_close / 100
            pe_price = pe_close / 100
            syn_future = strike + ce_price - pe_price
            diff = abs(syn_future - spot_price)
            
            if diff < min_diff:
                min_diff = diff
                best_atm = strike
            
            # Show strikes near spot
            if abs(strike - spot_price) <= 500:
                print(f"{strike:6.0f} | {ce_price:8.2f} | {pe_price:8.2f} | {syn_future:9.2f} | {diff:4.2f} | YES")
        else:
            missing_pe += 1
            ce_price = ce_close / 100 if ce_close else 0
            # Show strikes with missing data near spot
            if abs(strike - spot_price) <= 500:
                ce_str = f"{ce_price:8.2f}" if ce_close else "     N/A"
                pe_str = f"{pe_close/100:8.2f}" if pe_close else "     N/A"
                print(f"{strike:6.0f} | {ce_str} | {pe_str} |    N/A   |  N/A | NO")
    
    print(f"\nSummary:")
    print(f"  Complete option pairs: {complete_pairs}")
    print(f"  Missing PE options: {missing_pe}")
    print(f"  Best ATM (if any): {best_atm if best_atm else 'Cannot calculate - insufficient data'}")
    
    # Now check with April 9 expiry for comparison
    print(f"\nComparison with April 9 Expiry (250409):")
    cursor.execute("""
        SELECT COUNT(*) as complete_pairs
        FROM nifty_call c
        JOIN nifty_put p ON c.date = p.date AND c.time = p.time 
                          AND c.strike = p.strike AND c.expiry = p.expiry
        WHERE c.date = %s AND c.time = %s AND c.expiry = '250409'
        AND c.close > 0 AND p.close > 0
    """, (date, time))
    
    apr9_count = cursor.fetchone()[0]
    print(f"  Complete option pairs with April 9 expiry: {apr9_count}")
    
    cursor.close()
    conn.close()

def main():
    print("MySQL Near Expiry Analysis (April 3, 2025 Expiry)")
    print("="*70)
    print("Checking if using near expiry instead of weekly expiry")
    print("resolves the data quality issues...")
    
    # Check April 1-3 with April 3 expiry
    dates = ['250401', '250402', '250403']
    
    for date in dates:
        check_near_expiry_data(date, '250403')
    
    print("\n" + "="*70)
    print("FINDINGS:")
    print("1. Check if near expiry (April 3) has complete data")
    print("2. Compare with April 9 expiry data availability")
    print("3. Determine if expiry selection is causing the issues")
    print("="*70)

if __name__ == "__main__":
    main() 