#!/usr/bin/env python3
"""Comprehensive comparison of legacy and new backtester outputs including all sheets."""

import os
import sys
import pandas as pd
import numpy as np
import subprocess
import shutil
from datetime import datetime
from pathlib import Path
import openpyxl
import time

# Add project paths
sys.path.append('/srv/samba/shared')

def setup_test_environment():
    """Set up test environment with proper dates."""
    print("\n" + "="*80)
    print("SETTING UP TEST ENVIRONMENT")
    print("="*80)
    
    # Update portfolio dates to April 1, 2025
    portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    
    print(f"\n1. Updating dates in {portfolio_file}")
    wb = openpyxl.load_workbook(portfolio_file)
    ws = wb['PortfolioSetting']
    
    # Find StartDate and EndDate columns
    header_row = 1
    start_col = end_col = None
    
    for col in range(1, ws.max_column + 1):
        cell_value = ws.cell(row=header_row, column=col).value
        if cell_value == 'StartDate':
            start_col = col
        elif cell_value == 'EndDate':
            end_col = col
    
    # Update dates to 01_04_2025
    for row in range(2, ws.max_row + 1):
        if ws.cell(row=row, column=1).value:
            ws.cell(row=row, column=start_col).value = "01_04_2025"
            ws.cell(row=row, column=end_col).value = "01_04_2025"
    
    wb.save(portfolio_file)
    print("   ✓ Updated dates to 01_04_2025")
    
    # Set up legacy environment
    print("\n2. Setting up legacy environment")
    legacy_dir = Path("bt/archive/backtester_stable/BTRUN")
    input_sheets_dir = legacy_dir / "INPUT SHEETS"
    input_sheets_dir.mkdir(exist_ok=True)
    
    # Copy files with expected names
    shutil.copy2(portfolio_file, input_sheets_dir / "INPUT PORTFOLIO.xlsx")
    shutil.copy2("bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx", 
                 input_sheets_dir / "INPUT TBS MULTI LEGS.xlsx")
    
    # Create Trades directory
    trades_dir = legacy_dir / "Trades"
    trades_dir.mkdir(exist_ok=True)
    
    print("   ✓ Legacy environment ready")

def run_legacy_backtest():
    """Run the legacy backtester."""
    print("\n" + "="*80)
    print("RUNNING LEGACY BACKTESTER")
    print("="*80)
    
    original_dir = os.getcwd()
    legacy_dir = Path("bt/archive/backtester_stable/BTRUN")
    
    try:
        os.chdir(legacy_dir)
        
        # Run legacy backtester
        result = subprocess.run(
            [sys.executable, "BTRunPortfolio.py"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✓ Legacy backtester completed")
            
            # Find output file
            trades_dir = Path("Trades")
            output_files = list(trades_dir.glob("NIF0DTE*.xlsx"))
            
            if output_files:
                # Get the most recent file
                latest_file = max(output_files, key=lambda f: f.stat().st_mtime)
                
                # Copy to main directory with clear name
                dst = Path(original_dir) / "legacy_output_april1.xlsx"
                shutil.copy2(latest_file, dst)
                print(f"✓ Legacy output saved to: {dst}")
                return str(dst)
            else:
                print("✗ No output files found in Trades directory")
        else:
            print(f"✗ Legacy backtester failed: {result.stderr[:500]}")
            
    except Exception as e:
        print(f"✗ Error running legacy backtester: {e}")
        
    finally:
        os.chdir(original_dir)
    
    return None

def run_heavydb_backtest():
    """Run the HeavyDB backtester."""
    print("\n" + "="*80)
    print("RUNNING HEAVYDB BACKTESTER")
    print("="*80)
    
    output_file = "heavydb_output_april1.xlsx"
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "--output-path", output_file,
        "--start-date", "20250401",
        "--end-date", "20250401"
    ]
    
    env = os.environ.copy()
    env['PYTHONPATH'] = '/srv/samba/shared'
    
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    
    if result.returncode == 0:
        print(f"✓ HeavyDB output saved to: {output_file}")
        return output_file
    else:
        print(f"✗ HeavyDB backtester failed: {result.stderr[:500]}")
        return None

def analyze_excel_sheets(file_path, label):
    """Analyze all sheets in an Excel file."""
    print(f"\n{label}: {file_path}")
    print("-" * 60)
    
    if not os.path.exists(file_path):
        print("  File not found!")
        return {}
    
    xl = pd.ExcelFile(file_path)
    sheet_info = {}
    
    for sheet_name in xl.sheet_names:
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        sheet_info[sheet_name] = {
            'shape': df.shape,
            'columns': list(df.columns),
            'data': df
        }
        print(f"  Sheet: {sheet_name}")
        print(f"    Shape: {df.shape}")
        print(f"    Columns: {list(df.columns)[:5]}{'...' if len(df.columns) > 5 else ''}")
    
    return sheet_info

def compare_transaction_data(legacy_df, heavydb_df):
    """Compare transaction data between legacy and HeavyDB."""
    print("\n  Transaction Comparison:")
    print("  " + "-"*50)
    
    # Map column names
    col_mapping = {
        # Legacy -> HeavyDB
        'ID': 'leg_id',
        'Strike': 'strike',
        'CE/PE': 'instrument_type',
        'Trade': 'side',
        'Entry at': 'entry_price',
        'Exit at': 'exit_price',
        'PNL': 'pnl',
        'Net PNL': 'netPnlAfterExpenses'
    }
    
    # Compare number of trades
    print(f"    Legacy trades: {len(legacy_df)}")
    print(f"    HeavyDB trades: {len(heavydb_df)}")
    
    # Compare each trade
    for i in range(min(len(legacy_df), len(heavydb_df))):
        print(f"\n    Trade {i+1}:")
        
        # Legacy trade
        l_trade = legacy_df.iloc[i]
        l_id = l_trade.get('ID', 'N/A')
        l_strike = l_trade.get('Strike', 'N/A')
        l_type = l_trade.get('CE/PE', 'N/A')
        l_side = l_trade.get('Trade', 'N/A')
        l_entry = l_trade.get('Entry at', 0)
        l_exit = l_trade.get('Exit at', 0)
        l_pnl = l_trade.get('PNL', 0)
        
        # HeavyDB trade
        h_trade = heavydb_df.iloc[i]
        h_id = h_trade.get('leg_id', 'N/A')
        h_strike = h_trade.get('strike', 'N/A')
        h_type = h_trade.get('instrument_type', 'N/A')
        h_side = h_trade.get('side', 'N/A')
        h_entry = h_trade.get('entry_price', 0)
        h_exit = h_trade.get('exit_price', 0)
        h_pnl = h_trade.get('pnl', 0)
        
        print(f"      Legacy:  {l_id} | {l_strike} | {l_type} {l_side} | Entry: {l_entry:.2f} | Exit: {l_exit:.2f} | P&L: {l_pnl:.2f}")
        print(f"      HeavyDB: {h_id} | {h_strike} | {h_type} {h_side} | Entry: {h_entry:.2f} | Exit: {h_exit:.2f} | P&L: {h_pnl:.2f}")
        
        # Check for differences
        if abs(l_pnl - h_pnl) > 0.01:
            print(f"      ⚠️  P&L difference: {abs(l_pnl - h_pnl):.2f}")

def compare_outputs(legacy_file, heavydb_file):
    """Compare outputs from both systems."""
    print("\n" + "="*80)
    print("DETAILED SHEET COMPARISON")
    print("="*80)
    
    # Analyze both files
    legacy_sheets = analyze_excel_sheets(legacy_file, "LEGACY OUTPUT")
    heavydb_sheets = analyze_excel_sheets(heavydb_file, "HEAVYDB OUTPUT")
    
    # Compare sheet names
    print("\n" + "="*80)
    print("SHEET NAME COMPARISON")
    print("="*80)
    
    legacy_names = set(legacy_sheets.keys())
    heavydb_names = set(heavydb_sheets.keys())
    
    print("\nSheets in Legacy but NOT in HeavyDB:")
    missing_sheets = legacy_names - heavydb_names
    for sheet in sorted(missing_sheets):
        print(f"  ❌ {sheet}")
    
    print("\nSheets in HeavyDB but NOT in Legacy:")
    extra_sheets = heavydb_names - legacy_names
    for sheet in sorted(extra_sheets):
        print(f"  ➕ {sheet}")
    
    print("\nCommon sheets:")
    common_sheets = legacy_names & heavydb_names
    for sheet in sorted(common_sheets):
        print(f"  ✓ {sheet}")
    
    # Compare transaction data
    print("\n" + "="*80)
    print("TRANSACTION DATA COMPARISON")
    print("="*80)
    
    # Find transaction sheets
    legacy_trans_sheet = None
    heavydb_trans_sheet = None
    
    for sheet in legacy_sheets:
        if 'Trans' in sheet and 'PORTFOLIO' not in sheet:
            legacy_trans_sheet = sheet
            break
    
    for sheet in heavydb_sheets:
        if 'Trans' in sheet and 'PORTFOLIO' not in sheet and 'RS,916' in sheet:
            heavydb_trans_sheet = sheet
            break
    
    if legacy_trans_sheet and heavydb_trans_sheet:
        legacy_trans = legacy_sheets[legacy_trans_sheet]['data']
        heavydb_trans = heavydb_sheets[heavydb_trans_sheet]['data']
        compare_transaction_data(legacy_trans, heavydb_trans)
    
    # Summary
    print("\n" + "="*80)
    print("SUMMARY")
    print("="*80)
    
    print("\nMissing sheets in HeavyDB output:")
    if 'PortfolioParameter' in missing_sheets:
        print("  ❌ PortfolioParameter - Input configuration sheet")
    if 'GeneralParameter' in missing_sheets:
        print("  ❌ GeneralParameter - Strategy parameters sheet")
    if 'LegParameter' in missing_sheets:
        print("  ❌ LegParameter - Leg configuration sheet")
    
    print("\nThese parameter sheets should be copied from the input Excel file.")
    print("They contain the configuration used for the backtest.")

def main():
    """Main execution."""
    print("="*80)
    print("COMPREHENSIVE BACKTEST OUTPUT COMPARISON")
    print(f"Date: {datetime.now()}")
    print("="*80)
    
    # Step 1: Set up environment
    setup_test_environment()
    
    # Step 2: Run legacy backtest
    print("\n" + "="*80)
    print("PHASE 1: LEGACY SYSTEM")
    print("="*80)
    time.sleep(1)
    
    legacy_output = run_legacy_backtest()
    
    # Step 3: Run HeavyDB backtest
    print("\n" + "="*80)
    print("PHASE 2: HEAVYDB SYSTEM")
    print("="*80)
    time.sleep(1)
    
    heavydb_output = run_heavydb_backtest()
    
    # Step 4: Compare outputs
    if legacy_output and heavydb_output:
        print("\n" + "="*80)
        print("PHASE 3: COMPARISON")
        print("="*80)
        time.sleep(1)
        
        compare_outputs(legacy_output, heavydb_output)
    else:
        print("\n✗ Could not compare - one or both outputs missing")
    
    print("\n" + "="*80)
    print("COMPARISON COMPLETE")
    print("="*80)

if __name__ == "__main__":
    main() 