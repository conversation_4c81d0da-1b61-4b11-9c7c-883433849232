#!/usr/bin/env python3
"""
Remote legacy backtester execution and comparison script.
Since we can't directly access the internal service, this script provides alternatives.
"""

import os
import sys
import json
import pandas as pd
import subprocess
import mysql.connector
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Windows server connection details
WINDOWS_HOST = "************"
WINDOWS_USER = "MahaInvest"
WINDOWS_PASS = "Maruth@123"
RDP_PORT = "33898"

# MySQL connection (from combined_operations.py)
MYSQL_CONFIG = {
    "host": "************",
    "user": "mahesh",
    "password": "mahesh_123",
    "database": "historicaldb"
}

def option1_port_forward_setup():
    """Instructions for setting up port forwarding on Windows"""
    print("""
    OPTION 1: Port Forwarding Setup
    ===============================
    
    1. RDP to the Windows server:
       xfreerdp /u:MahaInvest /p:Maruth@123 /v:************:33898
    
    2. Open Command Prompt as Administrator on Windows
    
    3. Add port forwarding rule:
       netsh interface portproxy add v4tov4 listenport=5000 listenaddress=************ connectport=5000 connectaddress=***************
    
    4. Open Windows Firewall port:
       netsh advfirewall firewall add rule name="Legacy Backtest Service" dir=in action=allow protocol=TCP localport=5000
    
    5. Verify the rule:
       netsh interface portproxy show all
    
    6. Then from Linux, access the service at:
       http://************:5000
    """)

def option2_direct_execution():
    """Run legacy backtester directly on Windows via shared results"""
    print("""
    OPTION 2: Direct Execution via Shared Database
    ==============================================
    
    1. Create a results table in MySQL to share outputs:
    """)
    
    # SQL to create shared results table
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS backtest_results (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_id VARCHAR(50),
        run_date DATETIME,
        system_type VARCHAR(20),  -- 'legacy' or 'new'
        portfolio_name VARCHAR(100),
        start_date DATE,
        end_date DATE,
        total_pnl DECIMAL(15,2),
        max_drawdown DECIMAL(15,2),
        sharpe_ratio DECIMAL(10,4),
        result_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    print(f"SQL to execute on MySQL:\n{create_table_sql}")
    
    print("""
    2. On Windows server (via RDP):
       - Modify legacy backtester to save results to MySQL
       - Run: python BTRunPortfolio.py --output-mysql
    
    3. On Linux server:
       - Run new backtester and save to same MySQL table
       - Compare results from both systems
    """)

def option3_file_transfer():
    """Transfer files between servers for comparison"""
    print("""
    OPTION 3: File Transfer Approach
    ================================
    
    1. Use shared network folder or SCP to transfer files
    
    2. From Windows (after running legacy backtest):
       - Save output to: C:\\backtest_output\\legacy_result.xlsx
    
    3. Transfer to Linux using one of:
       a) SMB/CIFS mount:
          mount -t cifs //************/backtest_output /mnt/windows -o user=MahaInvest,password=Maruth@123
       
       b) Use WinSCP or FileZilla to transfer files
       
       c) Use MySQL to store file content:
          - Convert Excel to CSV/JSON
          - Store in MySQL BLOB/TEXT field
          - Retrieve on Linux side
    """)

def create_comparison_script():
    """Create a practical comparison script"""
    comparison_script = """#!/usr/bin/env python3
import pandas as pd
import mysql.connector
from datetime import datetime

# Connect to MySQL to fetch results
def get_results_from_mysql(test_id, system_type):
    conn = mysql.connector.connect(
        host="************",
        user="mahesh",
        password="mahesh_123",
        database="historicaldb"
    )
    cursor = conn.cursor()
    
    query = '''
    SELECT result_data 
    FROM backtest_results 
    WHERE test_id = %s AND system_type = %s
    ORDER BY created_at DESC
    LIMIT 1
    '''
    
    cursor.execute(query, (test_id, system_type))
    result = cursor.fetchone()
    
    cursor.close()
    conn.close()
    
    return json.loads(result[0]) if result else None

# Main comparison
test_id = f"comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

print("Fetching legacy results...")
legacy_results = get_results_from_mysql(test_id, 'legacy')

print("Fetching new results...")
new_results = get_results_from_mysql(test_id, 'new')

if legacy_results and new_results:
    # Compare key metrics
    print("\\nComparison Results:")
    print(f"Total P&L - Legacy: {legacy_results.get('total_pnl')}, New: {new_results.get('total_pnl')}")
    print(f"Max Drawdown - Legacy: {legacy_results.get('max_drawdown')}, New: {new_results.get('max_drawdown')}")
else:
    print("Missing results from one or both systems")
"""
    
    with open('scripts/mysql_comparison.py', 'w') as f:
        f.write(comparison_script)
    
    print("\nCreated mysql_comparison.py for result comparison")

def main():
    print("=" * 80)
    print("Legacy Backtester Remote Access Solutions")
    print("=" * 80)
    
    print("\nSince the legacy service (***************:5000) is on an internal network,")
    print("and we're accessing from outside, here are practical solutions:\n")
    
    print("1. PORT FORWARDING (Recommended)")
    print("-" * 40)
    option1_port_forward_setup()
    
    print("\n2. SHARED DATABASE APPROACH")
    print("-" * 40)
    option2_direct_execution()
    
    print("\n3. FILE TRANSFER APPROACH")
    print("-" * 40)
    option3_file_transfer()
    
    print("\n4. CREATING HELPER SCRIPTS")
    print("-" * 40)
    create_comparison_script()
    
    print("\n" + "=" * 80)
    print("RECOMMENDED APPROACH:")
    print("1. Try port forwarding first (Option 1)")
    print("2. If that fails, use the shared MySQL database approach (Option 2)")
    print("3. This allows both systems to write results to a common location")
    print("=" * 80)

if __name__ == "__main__":
    main() 