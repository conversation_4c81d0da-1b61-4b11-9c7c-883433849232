#!/usr/bin/env python3
"""Update dates in legacy Excel files to use 2024 dates for testing."""

import pandas as pd
from openpyxl import load_workbook
import os
import shutil

def update_excel_dates(excel_path, start_date="03_01_2024", end_date="03_01_2024"):
    """Update dates in Excel file to use 2024 dates."""
    
    print(f"Updating dates in: {excel_path}")
    
    # Create backup
    backup_path = excel_path + ".backup"
    shutil.copy2(excel_path, backup_path)
    
    # Load workbook
    wb = load_workbook(excel_path)
    
    # Update PortfolioSetting sheet if it exists
    if 'PortfolioSetting' in wb.sheetnames:
        ws = wb['PortfolioSetting']
        
        # Find and update dates
        for row in ws.iter_rows():
            for cell in row:
                if cell.value == 'StartDate':
                    # Update the cell to the right
                    date_cell = ws.cell(row=cell.row, column=cell.column + 1)
                    date_cell.value = start_date
                    print(f"  Updated StartDate to {start_date}")
                elif cell.value == 'EndDate':
                    # Update the cell to the right
                    date_cell = ws.cell(row=cell.row, column=cell.column + 1)
                    date_cell.value = end_date
                    print(f"  Updated EndDate to {end_date}")
    
    # Update Portfolio sheet if it exists (alternative format)
    if 'Portfolio' in wb.sheetnames:
        ws = wb['Portfolio']
        
        # Look for date columns
        for row in ws.iter_rows(min_row=1, max_row=10):
            for cell in row:
                if str(cell.value).lower() in ['startdate', 'start date']:
                    # Update the cell below or to the right
                    if ws.cell(row=cell.row + 1, column=cell.column).value:
                        date_cell = ws.cell(row=cell.row + 1, column=cell.column)
                        date_cell.value = start_date
                        print(f"  Updated StartDate to {start_date}")
                    elif ws.cell(row=cell.row, column=cell.column + 1).value:
                        date_cell = ws.cell(row=cell.row, column=cell.column + 1)
                        date_cell.value = start_date
                        print(f"  Updated StartDate to {start_date}")
                        
                elif str(cell.value).lower() in ['enddate', 'end date']:
                    # Update the cell below or to the right
                    if ws.cell(row=cell.row + 1, column=cell.column).value:
                        date_cell = ws.cell(row=cell.row + 1, column=cell.column)
                        date_cell.value = end_date
                        print(f"  Updated EndDate to {end_date}")
                    elif ws.cell(row=cell.row, column=cell.column + 1).value:
                        date_cell = ws.cell(row=cell.row, column=cell.column + 1)
                        date_cell.value = end_date
                        print(f"  Updated EndDate to {end_date}")
    
    # Save the workbook
    wb.save(excel_path)
    print(f"  Saved updated file")
    
    return True

def main():
    """Update dates in legacy Excel files."""
    
    print("Updating Excel dates for legacy backtester...")
    print("=" * 60)
    
    # Change to legacy directory
    legacy_dir = "bt/archive/backtester_stable/BTRUN"
    os.chdir(legacy_dir)
    
    # Update portfolio file
    portfolio_file = "INPUT SHEETS/INPUT PORTFOLIO.xlsx"
    if os.path.exists(portfolio_file):
        update_excel_dates(portfolio_file, "03_01_2024", "03_01_2024")
    else:
        print(f"Portfolio file not found: {portfolio_file}")
    
    # Update strategy file
    strategy_file = "INPUT SHEETS/input_tbs_multi_legs.xlsx"
    if os.path.exists(strategy_file):
        # Strategy files usually don't have dates, but check anyway
        update_excel_dates(strategy_file, "03_01_2024", "03_01_2024")
    else:
        print(f"Strategy file not found: {strategy_file}")
    
    print("\nDate update complete!")
    
    # Now try running the backtester again
    print("\nRunning legacy backtester with updated dates...")
    os.system("export MYSQL_HOST=localhost && export DEBUG_MODE=true && python3 BTRunPortfolio.py")

if __name__ == "__main__":
    main() 