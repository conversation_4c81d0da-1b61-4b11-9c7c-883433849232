#!/usr/bin/env python3
"""
Verify nifty_spot table and demonstrate usage
"""

from heavydb import connect
import sys

# HeavyDB connection details
HOST = "127.0.0.1"
PORT = 6274
USER = "admin"
PASSWORD = "HyperInteractive"
DATABASE = "heavyai"

def main():
    # Connect to HeavyDB
    print("Connecting to HeavyDB...")
    try:
        conn = connect(
            host=HOST,
            port=PORT,
            user=USER,
            password=PASSWORD,
            dbname=DATABASE
        )
        cursor = conn.cursor()
    except Exception as e:
        print(f"Error connecting to HeavyDB: {e}")
        sys.exit(1)
    
    # Check table exists
    print("\n1. Checking table structure...")
    try:
        cursor.execute("SHOW TABLES")
        tables = [row[0] for row in cursor.fetchall()]
        if 'nifty_spot' in tables:
            print("✓ nifty_spot table exists")
        else:
            print("✗ nifty_spot table not found")
            sys.exit(1)
    except Exception as e:
        print(f"Error checking tables: {e}")
    
    # Get table schema
    print("\n2. Table schema:")
    try:
        cursor.execute("SHOW CREATE TABLE nifty_spot")
        result = cursor.fetchone()
        print(result[0])
    except Exception as e:
        print(f"Error getting schema: {e}")
    
    # Get row count
    print("\n3. Data statistics:")
    try:
        cursor.execute("SELECT COUNT(*) FROM nifty_spot")
        count = cursor.fetchone()[0]
        print(f"Total rows: {count:,}")
        
        cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM nifty_spot")
        min_date, max_date = cursor.fetchone()
        print(f"Date range: {min_date} to {max_date}")
        
        cursor.execute("SELECT COUNT(DISTINCT trade_date) FROM nifty_spot")
        distinct_dates = cursor.fetchone()[0]
        print(f"Distinct trading days: {distinct_dates}")
        
        cursor.execute("SELECT COUNT(DISTINCT symbol) FROM nifty_spot")
        distinct_symbols = cursor.fetchone()[0]
        print(f"Distinct symbols: {distinct_symbols}")
    except Exception as e:
        print(f"Error getting statistics: {e}")
    
    # Sample data
    print("\n4. Sample data (first 5 rows):")
    try:
        cursor.execute("SELECT * FROM nifty_spot ORDER BY trade_date, trade_time LIMIT 5")
        rows = cursor.fetchall()
        for row in rows:
            print(f"  {row}")
    except Exception as e:
        print(f"Error getting sample data: {e}")
    
    # Example queries for backtesting
    print("\n5. Example queries for backtesting:")
    
    # Query 1: Get OHLC for a specific date
    print("\n   Query 1: Get OHLC for a specific date")
    query1 = """
    SELECT trade_time, "open", high, low, "close" 
    FROM nifty_spot 
    WHERE trade_date = DATE '2022-01-03'
    ORDER BY trade_time
    LIMIT 5
    """
    print(f"   SQL: {query1.strip()}")
    try:
        cursor.execute(query1)
        results = cursor.fetchall()
        for row in results:
            print(f"     {row}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Query 2: Join with option chain
    print("\n   Query 2: Join spot data with option chain")
    query2 = """
    SELECT 
        s.trade_date,
        s.trade_time,
        s."close" as spot_close,
        oc.strike,
        oc.ce_close,
        oc.pe_close
    FROM nifty_spot s
    JOIN nifty_option_chain oc 
        ON s.trade_date = oc.trade_date 
        AND s.trade_time = oc.trade_time
    WHERE s.trade_date = DATE '2022-01-03'
        AND oc.strike = oc.atm_strike
    LIMIT 5
    """
    print(f"   SQL: {query2.strip()}")
    try:
        cursor.execute(query2)
        results = cursor.fetchall()
        for row in results:
            print(f"     {row}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Query 3: Calculate intraday range
    print("\n   Query 3: Calculate intraday range")
    query3 = """
    SELECT 
        trade_date,
        MIN(low) as day_low,
        MAX(high) as day_high,
        MAX(high) - MIN(low) as day_range,
        FIRST_VALUE("open") OVER (PARTITION BY trade_date ORDER BY trade_time) as day_open,
        LAST_VALUE("close") OVER (PARTITION BY trade_date ORDER BY trade_time 
            ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) as day_close
    FROM nifty_spot
    WHERE trade_date BETWEEN DATE '2022-01-03' AND DATE '2022-01-07'
    GROUP BY trade_date, "open", "close", trade_time
    QUALIFY ROW_NUMBER() OVER (PARTITION BY trade_date ORDER BY trade_time DESC) = 1
    ORDER BY trade_date
    """
    print(f"   SQL: {query3.strip()}")
    try:
        cursor.execute(query3)
        results = cursor.fetchall()
        for row in results:
            print(f"     Date: {row[0]}, Low: {row[1]:.2f}, High: {row[2]:.2f}, Range: {row[3]:.2f}, Open: {row[4]:.2f}, Close: {row[5]:.2f}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Close connection
    cursor.close()
    conn.close()
    print("\nDone!")

if __name__ == "__main__":
    main() 