#!/usr/bin/env python3
"""Fix the lot size loading issue in legacy Util.py."""

def fix_lotsize_issue():
    """Fix the populateLotSize function in Util.py."""
    
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    
    # Read the file
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Find and replace the problematic line
    # Original: lotsizedf = lotsizedf[~pd.isnull(lotsizedf['underlyingname'])]
    # Fix: Add error handling
    
    old_line = "lotsizedf = lotsizedf[~pd.isnull(lotsizedf['underlyingname'])]"
    new_code = """# Fixed: Add error handling for lot size loading
        if 'underlyingname' in lotsizedf.columns:
            lotsizedf = lotsizedf[~pd.isnull(lotsizedf['underlyingname'])]
        else:
            # Try with different column names
            if lotsizedf.columns[0] != 'underlyingname':
                lotsizedf.columns = ['underlyingname', 'lotsize']
            lotsizedf = lotsizedf[~pd.isnull(lotsizedf['underlyingname'])]"""
    
    # Replace the line
    content = content.replace(old_line, new_code)
    
    # Write back
    with open(util_path, 'w') as f:
        f.write(content)
    
    print("✅ Fixed lot size loading issue in Util.py")

if __name__ == "__main__":
    fix_lotsize_issue()
    print("\nYou can now run the legacy backtester again") 