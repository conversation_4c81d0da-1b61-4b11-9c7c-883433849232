#!/usr/bin/env python3
"""
Debug script to capture comprehensive execution trace from legacy backtester.

This script instruments the legacy code to capture:
- Strike selection decisions with reasoning
- Trade entry/exit times and conditions
- P&L calculations at each step
- Risk rule evaluations
- Excel output generation
"""

import os
import sys
import json
import logging
import datetime
from pathlib import Path

# Add legacy code to path
sys.path.insert(0, str(Path(__file__).parent.parent / "bt/archive/backtester_stable/BTRUN"))

def setup_debug_logging():
    """Configure comprehensive debug logging."""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    logging.basicConfig(
        level=logging.DEBUG,
        format=log_format,
        handlers=[
            logging.FileHandler('legacy_debug_trace.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Create debug logger
    debug_logger = logging.getLogger('legacy_debug')
    return debug_logger

def instrument_legacy_code():
    """Add debug instrumentation to legacy code."""
    # This would modify the legacy code to add debug logging
    # For now, we'll document the approach
    
    instrumentation_code = """
# Add to Util.py at the top
import json
import logging
DEBUG_MODE = os.environ.get('DEBUG_MODE', 'false').lower() == 'true'
if DEBUG_MODE:
    debug_logger = logging.getLogger('legacy_debug')
    
# In strike selection functions
def get_atm_strike(spot, date, time):
    if DEBUG_MODE:
        debug_logger.debug(f"ATM calculation: spot={spot}, date={date}, time={time}")
    
    # ... existing logic ...
    
    if DEBUG_MODE:
        debug_logger.debug(f"ATM selected: {atm_strike}, method=synthetic_future")
    return atm_strike

# In trade entry/exit
def enter_trade(params):
    if DEBUG_MODE:
        debug_logger.debug(f"Trade entry: {json.dumps(params, indent=2)}")
    # ... existing logic ...

# In P&L calculation
def calculate_pnl(trade):
    if DEBUG_MODE:
        debug_logger.debug(f"P&L calculation start: entry={trade['entry_price']}, exit={trade['exit_price']}")
    # ... step by step with debug logs ...
    if DEBUG_MODE:
        debug_logger.debug(f"P&L result: gross={gross_pnl}, net={net_pnl}")
"""
    
    print("Instrumentation approach documented.")
    print("To instrument legacy code, add the following to Util.py:")
    print(instrumentation_code)

def capture_execution_trace(input_excel, output_file):
    """Run legacy backtester and capture execution trace."""
    logger = setup_debug_logging()
    
    logger.info(f"Starting legacy execution trace capture")
    logger.info(f"Input Excel: {input_excel}")
    logger.info(f"Output file: {output_file}")
    
    # Set environment for debug mode
    os.environ['DEBUG_MODE'] = 'true'
    os.environ['MYSQL_HOST'] = 'localhost'
    
    # Import legacy modules
    try:
        import BTRunPortfolio
        import Util
        
        # Capture initial configuration
        trace_data = {
            'timestamp': datetime.datetime.now().isoformat(),
            'input_excel': input_excel,
            'config': {
                'host': Util.config.host if hasattr(Util, 'config') else 'unknown',
                'use_synthetic_atm': getattr(Util.config, 'USE_SYNTHETIC_FUTURE_ATM', False)
            },
            'execution_trace': []
        }
        
        logger.info("Legacy modules imported successfully")
        logger.info(f"Config: {trace_data['config']}")
        
        # Run the backtester
        # This would normally call BTRunPortfolio.main() or similar
        # For now, we document the approach
        
        logger.info("Would execute: python BTRunPortfolio.py")
        
    except ImportError as e:
        logger.error(f"Failed to import legacy modules: {e}")
        logger.error("Ensure you're running from the correct directory")
        return
    
    # Save trace data
    with open(output_file, 'w') as f:
        json.dump(trace_data, f, indent=2)
    
    logger.info(f"Execution trace saved to {output_file}")

def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Capture legacy backtester execution trace')
    parser.add_argument('--input-excel', help='Input Excel file path')
    parser.add_argument('--output-trace', default='legacy_trace.json', help='Output trace file')
    parser.add_argument('--instrument-only', action='store_true', help='Only show instrumentation code')
    
    args = parser.parse_args()
    
    if args.instrument_only:
        instrument_legacy_code()
    else:
        if not args.input_excel:
            parser.error("--input-excel is required when not using --instrument-only")
        capture_execution_trace(args.input_excel, args.output_trace)

if __name__ == '__main__':
    main() 