#!/usr/bin/env python3
"""
Check the April 1-3 data issue in HeavyDB - why trades aren't generated
"""

try:
    from pyheavydb import connect as heavydb_connect
except ImportError:
    from heavydb import connect as heavydb_connect

def check_data_availability(date_str):
    """Check what data is available for the test strategy"""
    conn = heavydb_connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    # Convert date format
    heavydb_date = f"20{date_str[:2]}-{date_str[2:4]}-{date_str[4:6]}"
    
    print(f"\n{'='*70}")
    print(f"Date: {heavydb_date}")
    print(f"{'='*70}")
    
    # Get ATM strike and check required trades
    query = f"""
    SELECT 
        spot,
        atm_strike,
        expiry_date,
        expiry_bucket
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}'
    AND trade_time = '09:16:00'
    AND expiry_date = '2025-04-03'
    LIMIT 1
    """
    
    result = conn.execute(query).fetchone()
    if not result:
        print("No data found for April 3 expiry!")
        conn.close()
        return
    
    spot, atm_strike, expiry_date, expiry_bucket = result
    atm_strike = int(atm_strike)
    
    print(f"Spot: {spot:.2f}")
    print(f"ATM Strike: {atm_strike}")
    print(f"Expiry: {expiry_date} ({expiry_bucket})")
    
    # Check each required trade
    trades = [
        (atm_strike, 'CE', 'ATM CE SELL'),
        (atm_strike, 'PE', 'ATM PE SELL'),
        (atm_strike + 100, 'CE', 'OTM2 CE BUY'),  # OTM2 for CE
        (atm_strike - 100, 'PE', 'OTM2 PE BUY')   # OTM2 for PE
    ]
    
    print("\nRequired Trades:")
    print("Strike | Type | Description | Price | Available")
    print("-" * 60)
    
    available_count = 0
    for strike, opt_type, desc in trades:
        if opt_type == 'CE':
            query = f"""
            SELECT ce_close 
            FROM nifty_option_chain
            WHERE trade_date = '{heavydb_date}'
            AND trade_time = '09:16:00'
            AND strike = {strike}
            AND expiry_date = '2025-04-03'
            AND ce_close > 0
            """
        else:
            query = f"""
            SELECT pe_close 
            FROM nifty_option_chain
            WHERE trade_date = '{heavydb_date}'
            AND trade_time = '09:16:00'
            AND strike = {strike}
            AND expiry_date = '2025-04-03'
            AND pe_close > 0
            """
        
        result = conn.execute(query).fetchone()
        if result and result[0] > 0:
            price = result[0]
            available = "YES"
            available_count += 1
        else:
            price = 0
            available = "NO"
        
        price_str = f"{price:7.2f}" if price > 0 else "   N/A"
        print(f"{strike:6d} | {opt_type:4s} | {desc:12s} | {price_str} | {available}")
    
    print(f"\nAvailable trades: {available_count}/4")
    
    # Check what expiries have complete data
    print("\nChecking other expiries for comparison:")
    query2 = f"""
    SELECT 
        expiry_date,
        COUNT(DISTINCT strike) as strikes,
        COUNT(CASE WHEN ce_close > 0 THEN 1 END) as ce_count,
        COUNT(CASE WHEN pe_close > 0 THEN 1 END) as pe_count
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}'
    AND trade_time = '09:16:00'
    GROUP BY expiry_date
    ORDER BY expiry_date
    """
    
    print("\nExpiry Date | Strikes | CE Count | PE Count")
    print("-" * 45)
    
    results = conn.execute(query2).fetchall()
    for expiry, strikes, ce_cnt, pe_cnt in results:
        print(f"{expiry} | {strikes:7d} | {ce_cnt:8d} | {pe_cnt:8d}")
    
    conn.close()

def main():
    print("April 1-3 Data Issue Analysis")
    print("="*70)
    print("Checking why trades can't be generated for April 1-3")
    print("with the test strategy (ATM SELL, OTM2 BUY)")
    
    dates = ['250401', '250402', '250403']
    
    for date in dates:
        check_data_availability(date)
    
    print("\n" + "="*70)
    print("SUMMARY:")
    print("The issue appears to be that HeavyDB has pre-computed ATM values")
    print("but the actual option data for April 3 expiry is incomplete.")
    print("This is why trades can't be generated even though ATM is calculated.")
    print("\nPOSSIBLE SOLUTIONS:")
    print("1. Use April 9 expiry instead of April 3 (more complete data)")
    print("2. Load complete option chain data for April 3 expiry")
    print("3. Adjust the test to use a different date range with better data")

if __name__ == "__main__":
    main() 