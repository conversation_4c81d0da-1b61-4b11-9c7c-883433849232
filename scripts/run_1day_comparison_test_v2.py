#!/usr/bin/env python3
"""Run 1-day comparison test between legacy and GPU backtesters - version 2."""

import argparse
import subprocess
import os
import sys
import glob
from datetime import datetime
import mysql.connector
import shutil

def timestamp_print(msg):
    """Print message with timestamp."""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {msg}")

def check_mysql_data(date_str):
    """Check if MySQL has data for the given date."""
    # Convert date format
    date_yymmdd = date_str[-2:]  # 20240103 -> 03
    date_pattern = f"24%{date_yymmdd}"  # For 2024 data
    
    conn = mysql.connector.connect(
        host='localhost',
        user='mahesh',
        password='mahesh_123',
        database='historicaldb'
    )
    cursor = conn.cursor()
    
    # Check for call and put data
    cursor.execute(f"SELECT COUNT(*) FROM nifty_call WHERE date LIKE '{date_pattern}'")
    call_count = cursor.fetchone()[0]
    
    cursor.execute(f"SELECT COUNT(*) FROM nifty_put WHERE date LIKE '{date_pattern}'")
    put_count = cursor.fetchone()[0]
    
    conn.close()
    
    return call_count > 0 and put_count > 0, call_count, put_count

def find_latest_output_file(trades_dir, portfolio_name="NIF0DTE"):
    """Find the latest output file in the Trades directory."""
    pattern = os.path.join(trades_dir, f"{portfolio_name} *.xlsx")
    files = glob.glob(pattern)
    if files:
        # Sort by modification time and get the latest
        latest_file = max(files, key=os.path.getmtime)
        return latest_file
    return None

def run_legacy_backtester():
    """Run the legacy backtester."""
    # Store current directory
    current_dir = os.getcwd()
    
    # Change to legacy BTRUN directory
    legacy_dir = "bt/archive/backtester_stable/BTRUN"
    os.chdir(legacy_dir)
    
    # Set environment variables
    os.environ['MYSQL_HOST'] = 'localhost'
    
    # Run legacy backtester
    cmd = ["python3", "BTRunPortfolio.py"]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    # Change back to original directory
    os.chdir(current_dir)
    
    return result.returncode == 0, result.stdout, result.stderr

def run_gpu_backtester(output_path):
    """Run the GPU backtester."""
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "--output-path", output_path,
        "--debug"
    ]
    result = subprocess.run(cmd, capture_output=True, text=True)
    return result.returncode == 0, result.stdout, result.stderr

def main():
    parser = argparse.ArgumentParser(description='Run 1-day comparison test')
    parser.add_argument('--date', required=True, help='Date in YYYYMMDD format')
    args = parser.parse_args()
    
    timestamp_print("="*60)
    timestamp_print(f"Starting 1-day comparison test for {args.date}")
    timestamp_print("="*60)
    
    # Create results directory
    results_dir = f"comparison_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(results_dir, exist_ok=True)
    
    # Check prerequisites
    timestamp_print("Checking prerequisites...")
    
    # Check MySQL data
    has_data, call_count, put_count = check_mysql_data(args.date)
    if not has_data:
        timestamp_print(f"❌ No MySQL data found for {args.date}")
        return 1
    timestamp_print(f"✅ MySQL data available for {args.date}: {call_count} calls, {put_count} puts")
    
    # Check legacy config
    config_path = "bt/archive/backtester_stable/BTRUN/config.py"
    with open(config_path, 'r') as f:
        config_content = f.read()
    
    if 'USE_SYNTHETIC_FUTURE_ATM = True' in config_content and "host = 'localhost'" in config_content:
        timestamp_print("✅ Legacy config prepared correctly")
    else:
        timestamp_print("❌ Legacy config not properly set")
        return 1
    
    # Check input files
    legacy_input = "bt/archive/backtester_stable/BTRUN/INPUT SHEETS/INPUT PORTFOLIO.xlsx"
    gpu_input = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    
    if os.path.exists(legacy_input) and os.path.exists(gpu_input):
        timestamp_print("✅ Input files found")
    else:
        timestamp_print("❌ Input files missing")
        return 1
    
    # Update input files for the test date
    timestamp_print("Updating input files for date {}...".format(args.date))
    timestamp_print("⚠️  Please ensure input Excel files have dates set to 03_01_2024")
    
    # Run legacy backtester
    timestamp_print("Running legacy backtester...")
    legacy_trades_dir = "bt/archive/backtester_stable/BTRUN/Trades"
    
    # Clear old files from Trades directory
    if os.path.exists(legacy_trades_dir):
        for f in glob.glob(os.path.join(legacy_trades_dir, "*.xlsx")):
            os.remove(f)
    
    timestamp_print(f"Command: cd bt/archive/backtester_stable/BTRUN && python3 BTRunPortfolio.py")
    legacy_success, legacy_stdout, legacy_stderr = run_legacy_backtester()
    
    # Save legacy logs
    with open(os.path.join(results_dir, "legacy_stdout.log"), "w") as f:
        f.write(legacy_stdout)
    with open(os.path.join(results_dir, "legacy_stderr.log"), "w") as f:
        f.write(legacy_stderr)
    
    if legacy_success:
        timestamp_print("✅ Legacy backtester completed successfully")
        
        # Find the output file
        legacy_output = find_latest_output_file(legacy_trades_dir)
        if legacy_output:
            timestamp_print(f"✅ Legacy output file found: {os.path.basename(legacy_output)}")
            # Copy to results directory
            shutil.copy2(legacy_output, os.path.join(results_dir, "legacy_output.xlsx"))
        else:
            timestamp_print("❌ Legacy output file not found")
            timestamp_print("⚠️  Legacy backtest failed, but continuing with GPU test")
    else:
        timestamp_print("❌ Legacy backtester failed")
        timestamp_print("⚠️  Continuing with GPU test anyway")
    
    # Run GPU backtester
    timestamp_print("Running GPU backtester...")
    gpu_output = os.path.join(results_dir, "gpu_output.xlsx")
    timestamp_print(f"Command: python3 -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU --legacy-excel --portfolio-excel bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx --output-path {gpu_output} --debug")
    
    gpu_success, gpu_stdout, gpu_stderr = run_gpu_backtester(gpu_output)
    
    # Save GPU logs
    with open(os.path.join(results_dir, "gpu_stdout.log"), "w") as f:
        f.write(gpu_stdout)
    with open(os.path.join(results_dir, "gpu_stderr.log"), "w") as f:
        f.write(gpu_stderr)
    
    if gpu_success and os.path.exists(gpu_output):
        timestamp_print("✅ GPU backtester completed successfully")
    else:
        timestamp_print("❌ GPU backtester failed")
    
    # Log results
    with open(os.path.join(results_dir, "comparison.log"), "w") as f:
        f.write(f"Comparison test for date: {args.date}\n")
        f.write(f"Legacy success: {legacy_success}\n")
        f.write(f"GPU success: {gpu_success}\n")
        f.write(f"Legacy output: {legacy_output if 'legacy_output' in locals() else 'Not found'}\n")
        f.write(f"GPU output: {gpu_output if os.path.exists(gpu_output) else 'Not found'}\n")
    
    timestamp_print("")
    timestamp_print("="*60)
    timestamp_print("Comparison test complete")
    timestamp_print(f"Results saved to: {results_dir}/")
    timestamp_print("="*60)
    
    # If both succeeded, we can now compare the outputs
    if legacy_success and gpu_success and os.path.exists(gpu_output) and 'legacy_output' in locals():
        timestamp_print("")
        timestamp_print("Both backtesters completed successfully!")
        timestamp_print("You can now compare:")
        timestamp_print(f"  - Legacy: {results_dir}/legacy_output.xlsx")
        timestamp_print(f"  - GPU: {results_dir}/gpu_output.xlsx")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 