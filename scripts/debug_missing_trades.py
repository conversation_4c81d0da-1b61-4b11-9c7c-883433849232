#!/usr/bin/env python3
"""Debug script to trace missing trades in the new backtester."""

import sys
import json
sys.path.append('/srv/samba/shared')

from bt.backtester_stable.BTRUN.excel_parser.portfolio_parser import parse_portfolio_excel
from bt.backtester_stable.BTRUN.models.portfolio import PortfolioModel
import pandas as pd

def debug_missing_trades():
    """Debug why only 1 trade appears instead of 4."""
    
    # Path to test files
    portfolio_excel = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    
    print("=== STEP 1: Excel Parsing ===")
    # Parse the Excel file - returns dict of portfolios
    portfolios_dict = parse_portfolio_excel(portfolio_excel)
    
    # Check portfolio settings
    print(f"Portfolio count: {len(portfolios_dict)}")
    
    # Get the first portfolio for analysis
    portfolio_name = list(portfolios_dict.keys())[0] if portfolios_dict else None
    portfolio_model = portfolios_dict.get(portfolio_name) if portfolio_name else None
    
    if not portfolio_model:
        print("No portfolios found!")
        return None, None
    
    print(f"\nAnalyzing portfolio: {portfolio_name}")
    print(f"  Start date: {portfolio_model.start_date}")
    print(f"  End date: {portfolio_model.end_date}")
    print(f"  Strategy count: {len(portfolio_model.strategies)}")
    
    # Check strategies
    print(f"\n=== STEP 2: Strategy Analysis ===")
    for i, strategy in enumerate(portfolio_model.strategies, 1):
        print(f"\nStrategy {i}:")
        print(f"  Name: {strategy.strategy_name}")
        print(f"  Type: {strategy.evaluator}")
        print(f"  Excel path: {strategy.strategy_excel_path}")
        print(f"  Entry start: {strategy.entry_start}")
        print(f"  Entry end: {strategy.entry_end}")
        print(f"  DTE filter: {strategy.extra_params.get('DTE', 'N/A')}")
        
        # Check leg parameters - THIS IS KEY
        print(f"  Leg count: {len(strategy.legs)}")
        for j, leg in enumerate(strategy.legs, 1):
            print(f"\n    Leg {j}:")
            print(f"      LegID: {leg.leg_id}")
            print(f"      Instrument: {leg.option_type}")
            print(f"      Transaction: {leg.transaction}")
            print(f"      Strike: {leg.strike_rule}")
            print(f"      Strike Value: {leg.strike_value if hasattr(leg, 'strike_value') else 'N/A'}")
            print(f"      Fixed Strike: {leg.fixed_strike if hasattr(leg, 'fixed_strike') else 'N/A'}")
            print(f"      Lots: {leg.lots}")
            print(f"      Is Idle: {leg.extra_params.get('IsIdle', 'N/A')}")
    
    strategy_model = portfolio_model.strategies[0] if portfolio_model.strategies else None
    
    print("\n=== STEP 3: Trade Generation ===")
    # Now let's see what trades are generated
    # We need to simulate the actual backtest flow
    
    # Create a minimal backtest configuration
    config = {
        'start_date': '2025-04-01',
        'end_date': '2025-04-01',
        'portfolio_model': portfolio_model,
        'output_path': 'debug_trades.xlsx'
    }
    
    # Try to trace through the trade generation
    print("\nAttempting to trace trade generation...")
    
    # Check if we can access the backtest engine
    print("Skipping backtest engine import for now - will check models instead")
    
    return portfolio_model, strategy_model if 'strategy_model' in locals() else None

def check_output_generation():
    """Check how trades are written to output."""
    print("\n=== STEP 4: Output Generation ===")
    
    # Check if there's an existing output file we can analyze
    import os
    output_files = [
        "bt/backtester_stable/BTRUN/output/one_day_test_output.xlsx",
        "debug_trades.xlsx"
    ]
    
    for output_file in output_files:
        if os.path.exists(output_file):
            print(f"\nAnalyzing output file: {output_file}")
            df = pd.read_excel(output_file)
            print(f"Total rows in output: {len(df)}")
            print(f"Columns: {list(df.columns)[:10]}...")  # First 10 columns
            
            # Check unique strategies
            if 'strategy_name' in df.columns:
                print(f"Unique strategies: {df['strategy_name'].unique()}")
            
            # Check unique leg IDs
            if 'leg_id' in df.columns:
                print(f"Unique leg IDs: {df['leg_id'].unique()}")
            
            # Show first few rows
            print("\nFirst few rows:")
            print(df.head())

if __name__ == "__main__":
    portfolio_model, strategy_model = debug_missing_trades()
    check_output_generation()
    
    # Save models for inspection
    if portfolio_model:
        with open('debug_portfolio_model.json', 'w') as f:
            json.dump(portfolio_model.model_dump(), f, indent=2, default=str)
        print("\nPortfolio model saved to debug_portfolio_model.json")
    
    if strategy_model:
        with open('debug_strategy_model.json', 'w') as f:
            json.dump(strategy_model.model_dump(), f, indent=2, default=str)
        print("Strategy model saved to debug_strategy_model.json") 