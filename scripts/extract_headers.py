import pandas as pd
import os
import csv

# Define paths
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
INPUT_DIR = os.path.join(BASE_DIR, 'bt', 'backtester_stable', 'BTRUN', 'input_sheets')
OUTPUT_DIR = os.path.join(BASE_DIR, 'tests', 'fixtures')

PORTFOLIO_XLSX = os.path.join(INPUT_DIR, 'input_portfolio.xlsx')
STRATEGY_XLSX = os.path.join(INPUT_DIR, 'input_tbs_multi_legs.xlsx')

PORTFOLIO_HEADERS_CSV = os.path.join(OUTPUT_DIR, 'portfolio_headers.csv')
GENERAL_HEADERS_CSV = os.path.join(OUTPUT_DIR, 'general_headers.csv')
LEG_HEADERS_CSV = os.path.join(OUTPUT_DIR, 'leg_headers.csv')

# Sheet names
PORTFOLIO_SETTING_SHEET = 'PortfolioSetting'
STRATEGY_SETTING_SHEET = 'StrategySetting'
GENERAL_PARAM_SHEET = 'GeneralParameter'
LEG_PARAM_SHEET = 'LegParameter'

def get_headers_from_sheet(excel_path, sheet_name):
    """Extracts headers from a given sheet in an Excel file."""
    try:
        df = pd.read_excel(excel_path, sheet_name=sheet_name, nrows=0)
        return list(df.columns)
    except FileNotFoundError:
        print(f"Error: Excel file not found at {excel_path}")
        return []
    except ValueError as e:
        if 'Worksheet named' in str(e) and 'not found' in str(e):
            print(f"Error: Sheet '{sheet_name}' not found in {excel_path}")
        else:
            print(f"Error reading sheet '{sheet_name}' from {excel_path}: {e}")
        return []
    except Exception as e:
        print(f"An unexpected error occurred while reading {excel_path}, sheet '{sheet_name}': {e}")
        return []

def write_headers_to_csv(csv_path, headers):
    """Writes a list of headers to a CSV file."""
    if not headers:
        print(f"No headers to write for {os.path.basename(csv_path)}.")
        return
    try:
        with open(csv_path, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(headers)
        print(f"Successfully wrote headers to {csv_path}")
    except IOError as e:
        print(f"Error writing to CSV file {csv_path}: {e}")
    except Exception as e:
        print(f"An unexpected error occurred while writing to {csv_path}: {e}")

def main():
    # Ensure output directory exists
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        print(f"Created directory: {OUTPUT_DIR}")

    # 1. PortfolioSetting and StrategySetting headers
    portfolio_headers = get_headers_from_sheet(PORTFOLIO_XLSX, PORTFOLIO_SETTING_SHEET)
    strategy_headers = get_headers_from_sheet(PORTFOLIO_XLSX, STRATEGY_SETTING_SHEET)
    
    combined_portfolio_strategy_headers = []
    if portfolio_headers or strategy_headers:
        combined_portfolio_strategy_headers = sorted(list(set(portfolio_headers + strategy_headers)))
    
    write_headers_to_csv(PORTFOLIO_HEADERS_CSV, combined_portfolio_strategy_headers)

    # 2. GeneralParameter headers
    general_headers = get_headers_from_sheet(STRATEGY_XLSX, GENERAL_PARAM_SHEET)
    write_headers_to_csv(GENERAL_HEADERS_CSV, general_headers)

    # 3. LegParameter headers
    leg_headers = get_headers_from_sheet(STRATEGY_XLSX, LEG_PARAM_SHEET)
    write_headers_to_csv(LEG_HEADERS_CSV, leg_headers)

if __name__ == '__main__':
    # Ensure scripts directory is created if run directly and it does not exist
    # (though this script itself implies its existence)
    scripts_dir = os.path.dirname(os.path.abspath(__file__))
    if not os.path.exists(scripts_dir):
        os.makedirs(scripts_dir)
        print(f"Created directory: {scripts_dir}")
    main() 