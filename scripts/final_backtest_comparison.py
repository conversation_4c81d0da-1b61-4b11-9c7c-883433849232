#!/usr/bin/env python3
"""
Final comparison of legacy MySQL results and new HeavyDB backtester
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import json

def run_legacy_mysql():
    """Run the legacy MySQL direct query script"""
    print("\n" + "="*60)
    print("Running Legacy Backtester (MySQL Direct)")
    print("="*60)
    
    result = subprocess.run(
        ["python3", "scripts/fix_legacy_simple.py"],
        capture_output=True,
        text=True
    )
    
    if result.returncode == 0:
        print("  ✓ Legacy backtester completed")
        return True
    else:
        print("  ✗ Legacy backtester failed")
        if result.stderr:
            print(f"  Error: {result.stderr[:500]}")
        return False

def run_new_backtester():
    """Run the new HeavyDB backtester"""
    print("\n" + "="*60)
    print("Running New GPU Backtester (HeavyDB)")
    print("="*60)
    
    # Set up environment
    env = os.environ.copy()
    env['PYTHONPATH'] = os.path.abspath('.')
    
    cmd = [
        sys.executable,
        "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "--output-path", "comparison_outputs/new_gpu_output.xlsx",
        "--start-date", "20241231",
        "--end-date", "20241231"
    ]
    
    print(f"  Running command...")
    
    result = subprocess.run(
        cmd,
        env=env,
        capture_output=True,
        text=True,
        timeout=180
    )
    
    if result.returncode == 0 and os.path.exists("comparison_outputs/new_gpu_output.xlsx"):
        print("  ✓ New backtester completed")
        return True
    else:
        print("  ✗ New backtester failed")
        if result.stderr:
            print(f"  Error: {result.stderr[:500]}")
        return False

def compare_results():
    """Compare the results from both backtesters"""
    print("\n" + "="*60)
    print("Comparing Results")
    print("="*60)
    
    legacy_file = "comparison_outputs/legacy_output_direct_mysql.xlsx"
    new_file = "comparison_outputs/new_gpu_output.xlsx"
    
    if not os.path.exists(legacy_file):
        print("  ✗ Legacy output file not found")
        return
    
    if not os.path.exists(new_file):
        print("  ✗ New output file not found")
        return
    
    # Load legacy results
    print("\n1. Loading Legacy Results:")
    legacy_df = pd.read_excel(legacy_file, sheet_name='PORTFOLIO Trans')
    print(f"  Trades: {len(legacy_df)}")
    print(f"  Total P&L: {legacy_df['pnl'].sum():.2f}")
    
    # Show trades
    print("\n  Legacy Trades:")
    for _, row in legacy_df.iterrows():
        print(f"    {row['side']} {row['strike']} {row['option_type']} @ {row['entry_price']:.2f} -> {row['exit_price']:.2f}, P&L: {row['pnl']:.2f}")
    
    # Load new results
    print("\n2. Loading New Results:")
    try:
        # Try different sheet names
        sheet_names = pd.ExcelFile(new_file).sheet_names
        print(f"  Available sheets: {sheet_names}")
        
        # Find the transaction sheet
        trans_sheet = None
        for sheet in sheet_names:
            if 'Trans' in sheet or 'trans' in sheet:
                trans_sheet = sheet
                break
        
        if trans_sheet:
            new_df = pd.read_excel(new_file, sheet_name=trans_sheet)
            print(f"  Trades: {len(new_df)}")
            
            # Map column names if needed
            if 'PNL' in new_df.columns:
                pnl_col = 'PNL'
            elif 'pnl' in new_df.columns:
                pnl_col = 'pnl'
            elif 'Net PNL' in new_df.columns:
                pnl_col = 'Net PNL'
            else:
                pnl_col = new_df.columns[new_df.columns.str.contains('pnl', case=False)][0] if any(new_df.columns.str.contains('pnl', case=False)) else None
            
            if pnl_col:
                print(f"  Total P&L: {new_df[pnl_col].sum():.2f}")
            
            # Show trades
            print("\n  New Trades:")
            for _, row in new_df.iterrows():
                # Map column names
                side_col = 'Trade' if 'Trade' in row else 'side' if 'side' in row else None
                strike_col = 'Strike' if 'Strike' in row else 'strike' if 'strike' in row else None
                option_col = 'CE/PE' if 'CE/PE' in row else 'option_type' if 'option_type' in row else None
                entry_col = 'Entry at' if 'Entry at' in row else 'entry_price' if 'entry_price' in row else None
                exit_col = 'Exit at' if 'Exit at' in row else 'exit_price' if 'exit_price' in row else None
                
                if all([side_col, strike_col, option_col, entry_col, exit_col, pnl_col]):
                    print(f"    {row[side_col]} {row[strike_col]} {row[option_col]} @ {row[entry_col]:.2f} -> {row[exit_col]:.2f}, P&L: {row[pnl_col]:.2f}")
        
    except Exception as e:
        print(f"  Error loading new results: {e}")
        
    # Load JSON for more detailed comparison
    print("\n3. Detailed Comparison:")
    try:
        with open("comparison_outputs/legacy_output_direct_mysql.json", "r") as f:
            legacy_json = json.load(f)
        
        print(f"  Legacy Total P&L: {legacy_json['total_pnl']:.2f}")
        print(f"  Legacy Trades: {legacy_json['metrics']['total_trades']}")
        print(f"  Legacy Winning: {legacy_json['metrics']['winning_trades']}")
        print(f"  Legacy Losing: {legacy_json['metrics']['losing_trades']}")
        
    except Exception as e:
        print(f"  Could not load legacy JSON: {e}")

def main():
    print("="*80)
    print("Final Backtester Comparison")
    print(f"Test Date: 2024-12-31")
    print(f"Time: {datetime.now()}")
    print("="*80)
    
    # Run legacy backtester
    legacy_success = run_legacy_mysql()
    
    # Run new backtester
    new_success = run_new_backtester()
    
    # Compare results
    if legacy_success or new_success:
        compare_results()
    
    # Summary
    print("\n" + "="*80)
    print("Summary")
    print("="*80)
    print(f"Legacy Backtester: {'✓ Success' if legacy_success else '✗ Failed'}")
    print(f"New Backtester: {'✓ Success' if new_success else '✗ Failed'}")
    
    if legacy_success and new_success:
        print("\n✓ Both backtesters ran successfully!")
        print("  - Legacy used direct MySQL queries")
        print("  - New used HeavyDB with GPU acceleration")
        print("  - Both used REAL data (no mocks)")
        print("\nCheck the comparison above to verify results match")
    print("="*80)

if __name__ == "__main__":
    main() 