#!/usr/bin/env python3
"""
Debug TBS strike selection differences.

This script helps debug strike selection discrepancies between
legacy and GPU TBS implementations.
"""

import os
import sys
import logging
import argparse
import pandas as pd
import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tbs_strike_debug.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('tbs_strike_debug')

def add_archive_path():
    """Add archive code to Python path."""
    archive_path = project_root / "bt/archive/backtester_stable/BTRUN"
    sys.path.insert(0, str(archive_path))
    return archive_path

def load_excel_config(excel_file):
    """Load configuration from Excel file."""
    logger.info(f"Loading Excel config: {excel_file}")
    
    # Load the Excel sheets
    portfolio_df = pd.read_excel(excel_file, sheet_name="PortfolioSetting")
    strategy_df = pd.read_excel(excel_file, sheet_name="StrategySetting")
    
    # Filter for enabled strategies only
    enabled_strategies = strategy_df[strategy_df["Enabled"] == "YES"]
    if enabled_strategies.empty:
        raise ValueError("No enabled strategies found in StrategySetting sheet")
    
    # Use the first enabled strategy
    strategy_row = enabled_strategies.iloc[0]
    strategy_excel_path = strategy_row["StrategyExcelFilePath"]
    
    logger.info(f"Using strategy file: {strategy_excel_path}")
    
    # Load strategy file
    strategy_file_df = pd.read_excel(strategy_excel_path, sheet_name="GeneralParameter")
    leg_df = pd.read_excel(strategy_excel_path, sheet_name="LegParameter")
    
    # Extract relevant information
    portfolio_name = portfolio_df["PortfolioName"].iloc[0]
    strategy_name = strategy_file_df["StrategyName"].iloc[0]
    
    # Filter strategy and legs
    strategy = strategy_file_df[strategy_file_df["StrategyName"] == strategy_name].iloc[0]
    legs = leg_df[leg_df["StrategyName"] == strategy_name]
    
    # Get date range
    start_date = portfolio_df["StartDate"].iloc[0]
    end_date = portfolio_df["EndDate"].iloc[0]
    
    # Get times
    strike_selection_time = strategy["StrikeSelectionTime"]
    
    return {
        "portfolio_name": portfolio_name,
        "strategy_name": strategy_name,
        "strategy": strategy,
        "legs": legs,
        "start_date": start_date,
        "end_date": end_date,
        "strike_selection_time": strike_selection_time
    }

def debug_legacy_strike_selection(config, test_date):
    """Debug legacy strike selection."""
    logger.info("Debugging legacy strike selection...")
    
    try:
        # Import legacy modules (after adding path)
        add_archive_path()
        
        # Format date and time for legacy
        date_str = str(test_date)
        time_str = str(config["strike_selection_time"]).zfill(6)  # Ensure 6 digits for HHMMSS
        
        logger.info(f"Legacy system: Date={date_str}, Time={time_str}")
        
        # Get the legs
        legs = config["legs"]
        
        # For each leg, check strike selection
        for _, leg in legs.iterrows():
            leg_id = leg["LegID"]
            strike_method = leg["StrikeMethod"]
            instrument = leg["Instrument"]
            
            logger.info(f"Legacy Leg {leg_id}: {instrument}, Method={strike_method}")
            
            # Document the legacy approach
            legacy_approach = f"""
            Legacy Strike Selection Logic for Leg {leg_id}:
            1. Get spot price at {time_str}
            2. Apply strike method '{strike_method}' for {instrument}
            3. If ATM:
               - Check config.USE_SYNTHETIC_FUTURE_ATM setting
               - Either use synthetic future or round to nearest strike
            4. If ITM/OTM:
               - Calculate offset from ATM based on strike increments
            """
            
            logger.info(legacy_approach)
            
        return True
    except Exception as e:
        logger.error(f"Legacy strike selection debug failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def query_heavydb_strike_data(test_date, time_str):
    """Query HeavyDB for strike data using project utilities."""
    try:
        logger.info("Connecting to HeavyDB using project utilities...")
        
        # Try to use project connection utilities
        try:
            from bt.backtester_stable.BTRUN.dal.heavydb_connection import get_connection
            logger.info("Using project's HeavyDB connection utilities")
            conn = get_connection()
        except ImportError as e:
            logger.warning(f"Could not import project utilities: {e}")
            logger.info("Falling back to direct connection...")
            
            # Fallback to direct connection
            try:
                from heavydb import connect
                conn = connect(
                    host='127.0.0.1',
                    port=6274,
                    user='admin',
                    password='HyperInteractive',
                    dbname='heavyai'
                )
                logger.info("Connected using heavydb library")
            except ImportError:
                try:
                    import pymapd
                    conn = pymapd.connect(
                        host='127.0.0.1',
                        port=6274,
                        user='admin',
                        password='HyperInteractive',
                        db_name='heavyai'
                    )
                    logger.info("Connected using pymapd library")
                except ImportError:
                    logger.error("No HeavyDB library available. Install with:")
                    logger.error("  pip install heavydb importlib_metadata")
                    logger.error("  OR")
                    logger.error("  pip install pymapd")
                    return None
        
        # Test connection
        try:
            test_result = conn.execute("SELECT 1 as test")
            logger.info("HeavyDB connection test successful")
        except Exception as e:
            logger.error(f"HeavyDB connection test failed: {e}")
            return None
        
        # Convert date to datetime object
        if isinstance(test_date, int):
            date_str = str(test_date)
            test_date = datetime.datetime.strptime(date_str, "%Y%m%d").date()
        elif isinstance(test_date, str):
            test_date = datetime.datetime.strptime(test_date, "%Y%m%d").date()
        
        # Format time
        if len(str(time_str)) == 4:
            time_obj = datetime.datetime.strptime(str(time_str), "%H%M").time()
        elif len(str(time_str)) == 6:
            time_obj = datetime.datetime.strptime(str(time_str), "%H%M%S").time()
        else:
            # Assume it's already in HHMMSS format, pad if needed
            time_str_padded = str(time_str).zfill(6)
            time_obj = datetime.datetime.strptime(time_str_padded, "%H%M%S").time()
        
        # Format date and time for SQL
        sql_date = test_date.strftime("%Y-%m-%d")
        sql_time = time_obj.strftime("%H:%M:%S")
        
        # Query for available strikes with ATM info
        query = f"""
        SELECT DISTINCT strike, atm_strike, spot, call_strike_type, put_strike_type
        FROM nifty_option_chain
        WHERE trade_date = DATE '{sql_date}'
          AND trade_time = TIME '{sql_time}'
        ORDER BY strike
        """
        
        logger.info(f"Executing HeavyDB query for {sql_date} {sql_time}")
        logger.debug(f"Query: {query}")
        
        # Execute query
        result = conn.execute(query)
        rows = result.fetchall()
        
        if not rows:
            logger.warning(f"No data found for {sql_date} {sql_time}")
            
            # Check what data is available
            check_query = f"""
            SELECT COUNT(*) as count
            FROM nifty_option_chain
            WHERE trade_date = DATE '{sql_date}'
            """
            check_result = conn.execute(check_query)
            count = check_result.fetchone()[0]
            logger.info(f"Total records for date {sql_date}: {count}")
            
            if count == 0:
                # Check available dates
                date_query = """
                SELECT DISTINCT trade_date
                FROM nifty_option_chain
                ORDER BY trade_date DESC
                LIMIT 5
                """
                date_result = conn.execute(date_query)
                dates = [row[0] for row in date_result.fetchall()]
                logger.info(f"Available recent dates: {dates}")
            
            return None
        
        # Process results
        strikes = []
        atm_strike = None
        spot_price = None
        
        for row in rows:
            strike, atm, price, call_type, put_type = row
            strikes.append({
                'strike': strike,
                'call_type': call_type,
                'put_type': put_type
            })
            if atm_strike is None:
                atm_strike = atm
            if spot_price is None:
                spot_price = price
        
        strike_data = {
            "strikes": strikes,
            "atm_strike": atm_strike,
            "spot_price": spot_price,
            "date": sql_date,
            "time": sql_time
        }
        
        logger.info(f"HeavyDB strike data: ATM={atm_strike}, Spot={spot_price}, Strikes={len(strikes)}")
        return strike_data
    
    except Exception as e:
        logger.error(f"Failed to query HeavyDB: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None

def debug_gpu_strike_selection(config, test_date):
    """Debug GPU strike selection."""
    logger.info("Debugging GPU strike selection...")
    
    try:
        # Format date and time for GPU
        if isinstance(test_date, str):
            # Parse string date in YYYYMMDD format
            test_date = datetime.datetime.strptime(test_date, "%Y%m%d").date()
        elif isinstance(test_date, int):
            # Parse integer date in YYYYMMDD format
            date_str = str(test_date)
            test_date = datetime.datetime.strptime(date_str, "%Y%m%d").date()
        
        time_str = str(config["strike_selection_time"]).zfill(6)  # Ensure 6 digits
        
        logger.info(f"GPU system: Date={test_date}, Time={time_str}")
        
        # Query HeavyDB for strike data
        strike_data = query_heavydb_strike_data(test_date, time_str)
        
        if strike_data:
            # Get the legs
            legs = config["legs"]
            
            # For each leg, check strike selection
            for _, leg in legs.iterrows():
                leg_id = leg["LegID"]
                strike_method = leg["StrikeMethod"]
                instrument = leg["Instrument"]
                
                logger.info(f"GPU Leg {leg_id}: {instrument}, Method={strike_method}")
                
                # Apply strike selection logic
                if strike_method.upper() == "ATM":
                    selected_strike = strike_data["atm_strike"]
                    logger.info(f"Selected strike (ATM): {selected_strike}")
                    
                    # Show how it's calculated
                    gpu_approach = f"""
                    GPU Strike Selection for Leg {leg_id} (ATM):
                    - Pre-computed ATM strike from nifty_option_chain: {selected_strike}
                    - Underlying price: {strike_data['spot_price']}
                    - Calculated using synthetic future method
                    """
                    logger.info(gpu_approach)
                    
                elif strike_method.upper().startswith("ITM") or strike_method.upper().startswith("OTM"):
                    # Find the strike based on type
                    target_type = strike_method.upper()
                    if instrument.upper() in ["CALL", "CE"]:
                        type_field = "call_type"
                    else:
                        type_field = "put_type"
                    
                    selected_strike = None
                    for strike_info in strike_data["strikes"]:
                        if strike_info[type_field] == target_type:
                            selected_strike = strike_info["strike"]
                            break
                    
                    if selected_strike:
                        logger.info(f"Selected strike ({target_type}): {selected_strike}")
                    else:
                        logger.warning(f"No strike found for type {target_type}")
                        
                else:
                    logger.info(f"Strike method '{strike_method}' not implemented in debug script")
        
        return True
    except Exception as e:
        logger.error(f"GPU strike selection debug failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def compare_strike_selection(legacy_strikes, gpu_strikes):
    """Compare strike selection between legacy and GPU."""
    logger.info("Comparing strike selection...")
    
    # For now, this is a placeholder
    # We would compare actual strike selections here
    logger.info("Strike selection comparison not fully implemented")
    
    return True

def debug_strikes(excel_file, test_date):
    """Debug strike selection for TBS."""
    logger.info(f"Starting TBS strike selection debug for date: {test_date}")
    
    # Load configuration
    config = load_excel_config(excel_file)
    
    # Debug legacy strike selection
    legacy_result = debug_legacy_strike_selection(config, test_date)
    
    # Debug GPU strike selection
    gpu_result = debug_gpu_strike_selection(config, test_date)
    
    # Compare results
    if legacy_result and gpu_result:
        compare_strike_selection(None, None)
    
    return legacy_result and gpu_result

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Debug TBS Strike Selection')
    parser.add_argument('--excel', required=True, help='Input Excel file path')
    parser.add_argument('--date', default='20240103', 
                        help='Test date (YYYYMMDD)')
    
    args = parser.parse_args()
    
    # Run the debug
    result = debug_strikes(args.excel, args.date)
    
    # Print summary
    if result:
        print("\nTBS STRIKE SELECTION DEBUG COMPLETED")
        print("Check tbs_strike_debug.log for details")
    else:
        print("\nTBS STRIKE SELECTION DEBUG FAILED")
        print("Check tbs_strike_debug.log for errors")

if __name__ == "__main__":
    main() 