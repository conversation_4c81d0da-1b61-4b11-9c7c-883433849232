#!/usr/bin/env python3
"""
Check max PnL and min PnL in the output Excel file
"""

import pandas as pd
import json

def check_max_min_pnl():
    """Check the max/min PnL values in the output"""
    excel_file = 'output/tbs_apr3_heavydb.xlsx'
    
    print("Checking Max/Min PnL in TBS Output")
    print("="*70)
    
    # Load Excel file
    xl = pd.ExcelFile(excel_file)
    
    # Check Max Profit and Loss sheet
    if 'Max Profit and Loss' in xl.sheet_names:
        print("\nMax Profit and Loss Sheet:")
        print("-"*50)
        df = pd.read_excel(excel_file, sheet_name='Max Profit and Loss')
        print(df)
    
    # Check PORTFOLIO Trans sheet for actual trades
    if 'PORTFOLIO Trans' in xl.sheet_names:
        print("\n\nActual Trades and P&L:")
        print("-"*50)
        df = pd.read_excel(excel_file, sheet_name='PORTFOLIO Trans')
        
        # Get trade details
        trades = df[df['symbol'].notna()]
        
        print("\nIndividual Trade P&L:")
        for idx, row in trades.iterrows():
            print(f"  Trade {idx+1}: Strike {row['Strike']}, P&L: {row['PnL']}")
        
        # Calculate cumulative P&L
        trades_sorted = trades.sort_values('entry_datetime')
        trades_sorted['cumulative_pnl'] = trades_sorted['PnL'].cumsum()
        
        print("\nCumulative P&L Analysis:")
        print(f"  Starting P&L: 0.00")
        cumulative = 0
        max_pnl = 0
        min_pnl = 0
        
        for idx, row in trades_sorted.iterrows():
            cumulative += row['PnL']
            max_pnl = max(max_pnl, cumulative)
            min_pnl = min(min_pnl, cumulative)
            print(f"  After Trade {idx+1}: {cumulative:.2f}")
        
        print(f"\nCalculated Max P&L: {max_pnl:.2f}")
        print(f"Calculated Min P&L: {min_pnl:.2f}")
        print(f"Final P&L: {cumulative:.2f}")
    
    # Check if max_profit and max_loss columns exist in trades
    if 'PORTFOLIO Trans' in xl.sheet_names:
        df = pd.read_excel(excel_file, sheet_name='PORTFOLIO Trans')
        if 'max_profit' in df.columns and 'max_loss' in df.columns:
            print("\n\nmax_profit/max_loss columns in PORTFOLIO Trans:")
            print("-"*50)
            for idx, row in df.iterrows():
                if pd.notna(row['symbol']):
                    print(f"Trade {idx+1}: max_profit={row['max_profit']}, max_loss={row['max_loss']}")
    
    # Check JSON output
    json_file = 'output/tbs_apr3_heavydb.json'
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        print("\n\nJSON Metrics:")
        print("-"*50)
        if 'metrics' in data:
            metrics = data['metrics']
            for metric in metrics:
                if 'Max' in metric.get('Particulars', '') or 'Min' in metric.get('Particulars', ''):
                    print(f"{metric.get('Particulars')}: {metric.get('Value')}")
    except:
        pass

def main():
    check_max_min_pnl()
    
    print("\n" + "="*70)
    print("ISSUE EXPLANATION:")
    print("If max/min PnL shows 0.00, it could be because:")
    print("1. The calculation logic is not tracking intraday P&L movements")
    print("2. All trades exit at the same time (12:00:00)")
    print("3. The max_profit/max_loss columns are not being populated correctly")

if __name__ == "__main__":
    main() 