#!/usr/bin/env python3
"""
HeavyDB Migration Script - Safely migrate HeavyDB data to NVMe storage
"""

import os
import sys
import subprocess
import shutil
import time
import psutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import json
import argparse

class HeavyDBMigrator:
    def __init__(self, target_nvme_path: str, dry_run: bool = True):
        self.target_path = Path(target_nvme_path)
        self.dry_run = dry_run
        self.heavydb_service = "heavydb"
        self.config_file = "/var/lib/heavyai/heavy.conf.nvme"
        self.backup_suffix = f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.log_file = f"/srv/samba/shared/logs/heavydb_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        self.steps_completed = []
        
    def log(self, message: str, level: str = "INFO"):
        """Log messages to both console and file"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"
        print(log_message)
        
        # Also write to log file
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        with open(self.log_file, 'a') as f:
            f.write(log_message + "\n")
    
    def run_command(self, cmd: List[str], check: bool = True) -> Tuple[int, str, str]:
        """Execute shell command and return exit code, stdout, stderr"""
        self.log(f"Running: {' '.join(cmd)}")
        
        if self.dry_run and any(risky in cmd[0] for risky in ['systemctl', 'cp', 'mv', 'rsync']):
            self.log("DRY RUN - Command not executed", "WARNING")
            return 0, "DRY RUN", ""
        
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            stdout, stderr = process.communicate()
            
            if check and process.returncode != 0:
                raise Exception(f"Command failed: {stderr}")
            
            return process.returncode, stdout, stderr
        except Exception as e:
            self.log(f"Command error: {str(e)}", "ERROR")
            if check:
                raise
            return -1, "", str(e)
    
    def check_prerequisites(self) -> bool:
        """Check if migration can proceed"""
        self.log("Checking prerequisites...")
        
        # 1. Check if running as root
        if os.geteuid() != 0:
            self.log("This script must be run as root", "ERROR")
            return False
        
        # 2. Check if target path exists
        if not self.target_path.exists():
            self.log(f"Target path {self.target_path} does not exist", "ERROR")
            return False
        
        # 3. Check if target is on NVMe
        target_mount = self._get_mount_point(str(self.target_path))
        if not self._is_nvme(target_mount):
            self.log(f"Target path {self.target_path} is not on NVMe storage", "WARNING")
            response = input("Continue anyway? (y/N): ")
            if response.lower() != 'y':
                return False
        
        # 4. Check HeavyDB service status
        code, stdout, _ = self.run_command(["systemctl", "is-active", self.heavydb_service], check=False)
        if stdout.strip() != "active":
            self.log("HeavyDB service is not running", "WARNING")
        
        # 5. Get current data directory
        current_data_dir = self._get_current_data_dir()
        if not current_data_dir:
            self.log("Could not determine current data directory", "ERROR")
            return False
        
        self.current_data_dir = Path(current_data_dir)
        self.log(f"Current data directory: {self.current_data_dir}")
        
        # 6. Check space requirements
        current_usage = self._get_directory_size(self.current_data_dir)
        target_free = psutil.disk_usage(str(self.target_path)).free
        
        required_space = current_usage * 1.2  # 20% buffer
        
        self.log(f"Current data size: {current_usage / (1024**3):.2f} GB")
        self.log(f"Target free space: {target_free / (1024**3):.2f} GB")
        self.log(f"Required space (with buffer): {required_space / (1024**3):.2f} GB")
        
        if target_free < required_space:
            self.log("Insufficient space on target drive", "ERROR")
            return False
        
        # 7. Check for existing data in target
        new_data_dir = self.target_path / "heavydb_data"
        if new_data_dir.exists() and any(new_data_dir.iterdir()):
            self.log(f"Target directory {new_data_dir} already exists and is not empty", "WARNING")
            response = input("Overwrite existing data? (y/N): ")
            if response.lower() != 'y':
                return False
        
        self.new_data_dir = new_data_dir
        
        self.log("✅ All prerequisites checked", "SUCCESS")
        return True
    
    def _get_mount_point(self, path: str) -> str:
        """Get the mount point for a given path"""
        path = Path(path).resolve()
        while not path.is_mount():
            path = path.parent
        return str(path)
    
    def _is_nvme(self, mount_point: str) -> bool:
        """Check if mount point is on NVMe"""
        for partition in psutil.disk_partitions():
            if partition.mountpoint == mount_point:
                return "nvme" in partition.device
        return False
    
    def _get_current_data_dir(self) -> Optional[str]:
        """Get current HeavyDB data directory from config"""
        if not os.path.exists(self.config_file):
            self.log(f"Config file {self.config_file} not found", "ERROR")
            return None
        
        with open(self.config_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith("data") and "=" in line:
                    _, value = line.split("=", 1)
                    return value.strip().strip('"').strip("'")
        return None
    
    def _get_directory_size(self, path: Path) -> int:
        """Get total size of directory in bytes"""
        total = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total += os.path.getsize(filepath)
        return total
    
    def stop_heavydb(self) -> bool:
        """Stop HeavyDB service"""
        self.log("Stopping HeavyDB service...")
        
        code, _, stderr = self.run_command(["systemctl", "stop", self.heavydb_service])
        if code != 0:
            self.log(f"Failed to stop HeavyDB: {stderr}", "ERROR")
            return False
        
        # Wait for service to fully stop
        time.sleep(5)
        
        # Verify it's stopped
        code, stdout, _ = self.run_command(["systemctl", "is-active", self.heavydb_service], check=False)
        if stdout.strip() == "active":
            self.log("HeavyDB service is still running", "ERROR")
            return False
        
        self.log("✅ HeavyDB service stopped", "SUCCESS")
        self.steps_completed.append("stop_service")
        return True
    
    def backup_config(self) -> bool:
        """Backup current configuration"""
        self.log("Backing up configuration...")
        
        backup_path = self.config_file + self.backup_suffix
        
        if not self.dry_run:
            shutil.copy2(self.config_file, backup_path)
            self.log(f"✅ Config backed up to {backup_path}", "SUCCESS")
        else:
            self.log(f"DRY RUN - Would backup config to {backup_path}", "WARNING")
        
        self.steps_completed.append("backup_config")
        return True
    
    def migrate_data(self) -> bool:
        """Migrate data to new location using rsync"""
        self.log("Starting data migration...")
        self.log(f"Source: {self.current_data_dir}")
        self.log(f"Destination: {self.new_data_dir}")
        
        # Create target directory
        if not self.dry_run:
            self.new_data_dir.mkdir(parents=True, exist_ok=True)
        
        # Use rsync for reliable copying with progress
        rsync_cmd = [
            "rsync",
            "-avP",  # archive, verbose, progress
            "--delete",  # Remove files that don't exist in source
            f"{self.current_data_dir}/",  # Note trailing slash
            f"{self.new_data_dir}/"
        ]
        
        start_time = time.time()
        
        if not self.dry_run:
            # Run rsync
            process = subprocess.Popen(
                rsync_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Print progress
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(f"  {line.strip()}")
            
            process.wait()
            
            if process.returncode != 0:
                stderr = process.stderr.read()
                self.log(f"Rsync failed: {stderr}", "ERROR")
                return False
            
            elapsed = time.time() - start_time
            self.log(f"✅ Data migration completed in {elapsed:.1f} seconds", "SUCCESS")
            
            # Verify file count
            src_count = sum(1 for _ in Path(self.current_data_dir).rglob("*") if _.is_file())
            dst_count = sum(1 for _ in Path(self.new_data_dir).rglob("*") if _.is_file())
            
            self.log(f"Source files: {src_count}, Destination files: {dst_count}")
            
            if src_count != dst_count:
                self.log("File count mismatch!", "WARNING")
                response = input("Continue anyway? (y/N): ")
                if response.lower() != 'y':
                    return False
        else:
            self.log("DRY RUN - Would run rsync to copy data", "WARNING")
        
        self.steps_completed.append("migrate_data")
        return True
    
    def update_config(self) -> bool:
        """Update configuration to point to new data directory"""
        self.log("Updating configuration...")
        
        if not self.dry_run:
            # Read current config
            with open(self.config_file, 'r') as f:
                lines = f.readlines()
            
            # Update data directory line
            updated = False
            for i, line in enumerate(lines):
                if line.strip().startswith("data") and "=" in line:
                    lines[i] = f'data = "{self.new_data_dir}"\n'
                    updated = True
                    break
            
            if not updated:
                # Add data directory if not found
                lines.append(f'data = "{self.new_data_dir}"\n')
            
            # Write updated config
            with open(self.config_file, 'w') as f:
                f.writelines(lines)
            
            self.log(f"✅ Configuration updated to use {self.new_data_dir}", "SUCCESS")
        else:
            self.log(f"DRY RUN - Would update config to use {self.new_data_dir}", "WARNING")
        
        self.steps_completed.append("update_config")
        return True
    
    def update_permissions(self) -> bool:
        """Set correct permissions on new data directory"""
        self.log("Setting permissions...")
        
        if not self.dry_run:
            # Set ownership to heavyai user
            self.run_command(["chown", "-R", "heavyai:heavyai", str(self.new_data_dir)])
            
            # Set directory permissions
            self.run_command(["chmod", "755", str(self.new_data_dir)])
            
            self.log("✅ Permissions updated", "SUCCESS")
        else:
            self.log("DRY RUN - Would update permissions", "WARNING")
        
        self.steps_completed.append("update_permissions")
        return True
    
    def start_heavydb(self) -> bool:
        """Start HeavyDB service"""
        self.log("Starting HeavyDB service...")
        
        code, _, stderr = self.run_command(["systemctl", "start", self.heavydb_service])
        if code != 0:
            self.log(f"Failed to start HeavyDB: {stderr}", "ERROR")
            return False
        
        # Wait for service to start
        time.sleep(10)
        
        # Verify it's running
        code, stdout, _ = self.run_command(["systemctl", "is-active", self.heavydb_service], check=False)
        if stdout.strip() != "active":
            self.log("HeavyDB service failed to start", "ERROR")
            return False
        
        self.log("✅ HeavyDB service started", "SUCCESS")
        self.steps_completed.append("start_service")
        return True
    
    def verify_migration(self) -> bool:
        """Verify the migration was successful"""
        self.log("Verifying migration...")
        
        if self.dry_run:
            self.log("DRY RUN - Skipping verification", "WARNING")
            return True
        
        # Test database connection
        try:
            from heavydb import connect
            conn = connect(
                host='localhost',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM nifty_option_chain LIMIT 1")
            result = cursor.fetchone()
            cursor.close()
            conn.close()
            
            self.log(f"✅ Database connection successful, test query returned: {result}", "SUCCESS")
            self.steps_completed.append("verify")
            return True
            
        except Exception as e:
            self.log(f"Database verification failed: {str(e)}", "ERROR")
            return False
    
    def cleanup_old_data(self) -> bool:
        """Remove old data directory after successful migration"""
        self.log("Cleanup old data...")
        
        if self.dry_run:
            self.log(f"DRY RUN - Would remove {self.current_data_dir}", "WARNING")
            return True
        
        self.log(f"Old data directory: {self.current_data_dir}")
        self.log(f"Size: {self._get_directory_size(self.current_data_dir) / (1024**3):.2f} GB")
        
        response = input("Remove old data directory? (y/N): ")
        if response.lower() == 'y':
            shutil.rmtree(self.current_data_dir)
            self.log("✅ Old data directory removed", "SUCCESS")
        else:
            self.log("Old data directory retained", "INFO")
        
        self.steps_completed.append("cleanup")
        return True
    
    def rollback(self):
        """Rollback migration in case of failure"""
        self.log("Starting rollback...", "WARNING")
        
        if "start_service" in self.steps_completed:
            self.log("Stopping HeavyDB service...")
            self.run_command(["systemctl", "stop", self.heavydb_service], check=False)
        
        if "update_config" in self.steps_completed:
            self.log("Restoring configuration...")
            backup_path = self.config_file + self.backup_suffix
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, self.config_file)
        
        if "stop_service" in self.steps_completed:
            self.log("Restarting HeavyDB service...")
            self.run_command(["systemctl", "start", self.heavydb_service], check=False)
        
        self.log("Rollback completed", "WARNING")
    
    def run_migration(self) -> bool:
        """Run the complete migration process"""
        self.log("=" * 80)
        self.log("HeavyDB to NVMe Migration Process")
        self.log("=" * 80)
        self.log(f"Target: {self.target_path}")
        self.log(f"Mode: {'DRY RUN' if self.dry_run else 'LIVE'}")
        self.log("")
        
        try:
            # Check prerequisites
            if not self.check_prerequisites():
                return False
            
            if not self.dry_run:
                response = input("\n⚠️  Ready to start migration. Continue? (y/N): ")
                if response.lower() != 'y':
                    self.log("Migration cancelled by user")
                    return False
            
            # Execute migration steps
            steps = [
                ("Stop HeavyDB", self.stop_heavydb),
                ("Backup configuration", self.backup_config),
                ("Migrate data", self.migrate_data),
                ("Update configuration", self.update_config),
                ("Update permissions", self.update_permissions),
                ("Start HeavyDB", self.start_heavydb),
                ("Verify migration", self.verify_migration),
                ("Cleanup old data", self.cleanup_old_data)
            ]
            
            for step_name, step_func in steps:
                self.log(f"\n{'='*40}")
                self.log(f"Step: {step_name}")
                self.log(f"{'='*40}")
                
                if not step_func():
                    self.log(f"Step '{step_name}' failed!", "ERROR")
                    if not self.dry_run:
                        self.rollback()
                    return False
            
            self.log("\n" + "="*80)
            self.log("✅ Migration completed successfully!", "SUCCESS")
            self.log("="*80)
            
            # Generate summary
            self.generate_summary()
            
            return True
            
        except Exception as e:
            self.log(f"Migration failed with error: {str(e)}", "ERROR")
            if not self.dry_run:
                self.rollback()
            return False
    
    def generate_summary(self):
        """Generate migration summary"""
        summary = {
            "timestamp": datetime.now().isoformat(),
            "source": str(self.current_data_dir),
            "destination": str(self.new_data_dir),
            "steps_completed": self.steps_completed,
            "dry_run": self.dry_run,
            "log_file": self.log_file
        }
        
        summary_file = f"/srv/samba/shared/logs/migration_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        self.log(f"\nSummary saved to: {summary_file}")

def main():
    parser = argparse.ArgumentParser(description="Migrate HeavyDB to NVMe storage")
    parser.add_argument(
        "target_path",
        help="Target NVMe mount point (e.g., /nvme0n1-disk)"
    )
    parser.add_argument(
        "--live",
        action="store_true",
        help="Run in live mode (default is dry-run)"
    )
    
    args = parser.parse_args()
    
    # Create migrator
    migrator = HeavyDBMigrator(
        target_nvme_path=args.target_path,
        dry_run=not args.live
    )
    
    # Run migration
    success = migrator.run_migration()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())