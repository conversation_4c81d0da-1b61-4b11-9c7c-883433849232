#!/usr/bin/env python3
"""
Test ATM Synthetic Future Calculation
Compares legacy simple rounding vs synthetic future method
"""

import mysql.connector
import sys
from datetime import datetime

# Test configuration
TEST_DATES = [
    ('240103', '91600'),  # Jan 3, 2024, 9:16 AM
    ('240104', '91600'),  # Jan 4, 2024, 9:16 AM
    ('240105', '91600'),  # Jan 5, 2024, 9:16 AM
]

def get_simple_atm(spot):
    """Legacy simple rounding method"""
    return round(spot / 50) * 50

def get_synthetic_atm(spot, date, time, conn):
    """Synthetic future method matching HeavyDB"""
    cursor = conn.cursor()
    
    # Get spot price first
    spot_query = """
    SELECT DISTINCT spot 
    FROM nifty_cash 
    WHERE date = %s AND time = %s 
    LIMIT 1
    """
    cursor.execute(spot_query, (date, time))
    result = cursor.fetchone()
    if result:
        spot = float(result[0])
    
    # Query both call and put data
    query = """
    SELECT 
        nc.strike,
        nc.close as ce_close,
        np.close as pe_close
    FROM nifty_call nc
    INNER JOIN nifty_put np 
        ON nc.strike = np.strike 
        AND nc.date = np.date 
        AND nc.time = np.time
    WHERE nc.date = %s AND nc.time = %s
        AND nc.close IS NOT NULL 
        AND np.close IS NOT NULL
    ORDER BY nc.strike
    """
    
    cursor.execute(query, (date, time))
    strikes_data = cursor.fetchall()
    
    if not strikes_data:
        print(f"  WARNING: No paired strikes found for {date} {time}")
        return round(spot / 50) * 50
    
    # Calculate synthetic future for each strike
    min_diff = float('inf')
    atm_strike = None
    best_synthetic = None
    
    print(f"\n  Synthetic Future Calculations for spot={spot}:")
    print(f"  {'Strike':<10} {'CE_Close':<10} {'PE_Close':<10} {'Synthetic':<12} {'Diff':<10}")
    print(f"  {'-'*60}")
    
    for strike, ce_close, pe_close in strikes_data:
        synthetic_future = strike + ce_close - pe_close
        diff = abs(synthetic_future - spot)
        
        # Print top 5 closest strikes
        if diff < min_diff * 1.2:  # Within 20% of best
            print(f"  {strike:<10.0f} {ce_close:<10.2f} {pe_close:<10.2f} {synthetic_future:<12.2f} {diff:<10.2f}")
        
        if diff < min_diff:
            min_diff = diff
            atm_strike = strike
            best_synthetic = synthetic_future
    
    print(f"\n  Selected ATM: {atm_strike} (Synthetic: {best_synthetic:.2f}, Diff: {min_diff:.2f})")
    
    cursor.close()
    return atm_strike if atm_strike else round(spot / 50) * 50

def compare_atm_methods():
    """Compare ATM calculation methods"""
    # Connect to MySQL
    try:
        conn = mysql.connector.connect(
            host='localhost',
            user='mahesh',
            password='mahesh_123',
            database='historicaldb'
        )
        cursor = conn.cursor()
        
        print("="*80)
        print("ATM Strike Calculation Comparison")
        print("="*80)
        
        for date, time in TEST_DATES:
            print(f"\nDate: {date}, Time: {time}")
            print("-"*40)
            
            # Get spot price
            cursor.execute(
                "SELECT spot FROM nifty_cash WHERE date = %s AND time = %s LIMIT 1",
                (date, time)
            )
            result = cursor.fetchone()
            
            if not result:
                print(f"  No data found for {date} {time}")
                continue
            
            spot = float(result[0])
            print(f"  Spot Price: {spot}")
            
            # Calculate ATM using both methods
            simple_atm = get_simple_atm(spot)
            synthetic_atm = get_synthetic_atm(spot, date, time, conn)
            
            print(f"\n  Results:")
            print(f"  Simple Rounding ATM: {simple_atm}")
            print(f"  Synthetic Future ATM: {synthetic_atm}")
            print(f"  Difference: {abs(simple_atm - synthetic_atm)}")
            print(f"  Match: {'YES' if simple_atm == synthetic_atm else 'NO'}")
        
        # Summary statistics
        print("\n" + "="*80)
        print("SUMMARY")
        print("="*80)
        print("\nTo ensure legacy matches HeavyDB:")
        print("1. Modify Util.py to use synthetic future calculation")
        print("2. Add the get_synthetic_atm function")
        print("3. Replace simple rounding logic with synthetic future method")
        print("4. Test with multiple dates to verify parity")
        
        cursor.close()
        conn.close()
        
    except mysql.connector.Error as err:
        print(f"MySQL Error: {err}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def test_legacy_modification():
    """Test if legacy code has been modified to use synthetic future"""
    print("\n" + "="*80)
    print("Legacy Code Modification Check")
    print("="*80)
    
    # Check if config has the flag
    sys.path.insert(0, 'bt/archive/backtester_stable/BTRUN')
    try:
        import config
        if hasattr(config, 'USE_SYNTHETIC_FUTURE_ATM'):
            print(f"✓ Config flag found: USE_SYNTHETIC_FUTURE_ATM = {config.USE_SYNTHETIC_FUTURE_ATM}")
        else:
            print("✗ Config flag USE_SYNTHETIC_FUTURE_ATM not found in config.py")
            print("  Add: USE_SYNTHETIC_FUTURE_ATM = True")
    except ImportError:
        print("✗ Could not import config.py")

if __name__ == '__main__':
    compare_atm_methods()
    test_legacy_modification() 