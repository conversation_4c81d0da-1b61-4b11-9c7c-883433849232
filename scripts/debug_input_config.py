#!/usr/bin/env python3
"""
Debug the input Excel configuration to understand trade generation issues
"""

import pandas as pd
import os
from datetime import datetime

def examine_input_config():
    """Examine the input Excel configuration"""
    print("="*60)
    print("Debugging Input Configuration")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Check portfolio Excel
    portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    
    print(f"\n1. Examining Portfolio Excel: {portfolio_file}")
    xl = pd.ExcelFile(portfolio_file)
    print(f"   Sheets: {xl.sheet_names}")
    
    # Read PortfolioSetting
    portfolio_df = pd.read_excel(xl, sheet_name='PortfolioSetting')
    print("\n   PortfolioSetting:")
    print(portfolio_df[['PortfolioName', 'StartDate', 'EndDate', 'Enabled']])
    
    # Read StrategySetting
    strategy_df = pd.read_excel(xl, sheet_name='StrategySetting')
    print("\n   StrategySetting:")
    print(strategy_df[['PortfolioName', 'StrategyType', 'StrategyExcelFilePath', 'Enabled']])
    
    # Get strategy Excel path
    enabled_strategy = strategy_df[strategy_df['Enabled'] == 'YES']
    if not enabled_strategy.empty:
        strategy_path = enabled_strategy.iloc[0]['StrategyExcelFilePath']
        if not os.path.isabs(strategy_path):
            strategy_path = os.path.join('bt/backtester_stable/BTRUN/input_sheets', os.path.basename(strategy_path))
        
        print(f"\n2. Examining Strategy Excel: {strategy_path}")
        
        if os.path.exists(strategy_path):
            strat_xl = pd.ExcelFile(strategy_path)
            print(f"   Sheets: {strat_xl.sheet_names}")
            
            # Read GeneralParameter
            general_df = pd.read_excel(strat_xl, sheet_name='GeneralParameter')
            print("\n   GeneralParameter:")
            print(f"   Strategy: {general_df['StrategyName'].iloc[0]}")
            print(f"   StartTime: {general_df['StartTime'].iloc[0]}")
            print(f"   EndTime: {general_df['EndTime'].iloc[0]}")
            print(f"   DTE: {general_df['DTE'].iloc[0]}")
            
            # Read LegParameter
            leg_df = pd.read_excel(strat_xl, sheet_name='LegParameter')
            print("\n   LegParameter:")
            print("   Number of legs:", len(leg_df))
            
            # Show all legs
            for i, row in leg_df.iterrows():
                print(f"\n   Leg {i+1}:")
                print(f"     LegID: {row['LegID']}")
                print(f"     IsIdle: {row['IsIdle']}")
                print(f"     Instrument: {row['Instrument']}")
                print(f"     Transaction: {row['Transaction']}")
                print(f"     StrikeMethod: {row['StrikeMethod']}")
                print(f"     StrikeValue: {row.get('StrikeValue', 0)}")
                print(f"     Lots: {row['Lots']}")
            
            # Check for active legs
            active_legs = leg_df[leg_df['IsIdle'] != 'yes']
            print(f"\n   Active legs: {len(active_legs)}")
            
def check_date_format():
    """Check date format consistency"""
    print("\n" + "="*60)
    print("Checking Date Format")
    print("="*60)
    
    # Current date in Excel
    portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    portfolio_df = pd.read_excel(portfolio_file, sheet_name='PortfolioSetting')
    
    print(f"\nPortfolio dates:")
    print(f"  StartDate: {portfolio_df['StartDate'].iloc[0]}")
    print(f"  EndDate: {portfolio_df['EndDate'].iloc[0]}")
    
    # Check if dates are updated for test
    if '2024' in str(portfolio_df['StartDate'].iloc[0]):
        print("  ✓ Dates appear to be updated for 2024 test")
    else:
        print("  ✗ Dates may need to be updated for 2024-12-31 test")

def check_atm_calculation():
    """Check ATM calculation differences"""
    print("\n" + "="*60)
    print("ATM Calculation Differences")
    print("="*60)
    
    spot_price = 23559.15
    
    # Legacy method
    legacy_atm = round(spot_price / 50) * 50
    print(f"\nLegacy ATM calculation:")
    print(f"  Spot: {spot_price}")
    print(f"  ATM: {legacy_atm} (simple rounding)")
    
    # New method (example from output)
    print(f"\nNew backtester ATM:")
    print(f"  Uses synthetic future calculation")
    print(f"  ATM selected: 23450 (from output)")
    print(f"  Difference: {legacy_atm - 23450} points")
    
def suggest_fixes():
    """Suggest fixes for the issues"""
    print("\n" + "="*60)
    print("Suggested Fixes")
    print("="*60)
    
    print("\n1. Strike Selection Issue:")
    print("   - New backtester uses synthetic future for ATM")
    print("   - Legacy uses simple rounding")
    print("   - Need to align the ATM calculation logic")
    
    print("\n2. Missing Trades Issue:")
    print("   - Check if legs are marked as 'IsIdle'")
    print("   - Verify date format compatibility")
    print("   - Check if parser correctly reads all legs")
    
    print("\n3. Output Format Issue:")
    print("   - Ensure column names match")
    print("   - Verify all required fields are present")

if __name__ == "__main__":
    examine_input_config()
    check_date_format()
    check_atm_calculation()
    suggest_fixes() 