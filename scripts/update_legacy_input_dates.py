#!/usr/bin/env python3
"""Update dates in legacy input Excel files for testing."""

import pandas as pd
import os
from datetime import datetime

def update_portfolio_dates(excel_path, test_date="03_01_2024"):
    """Update dates in portfolio Excel file."""
    
    print(f"Updating dates in {excel_path} to {test_date}...")
    
    # Read Excel file
    xls = pd.ExcelFile(excel_path)
    
    # Create a writer
    with pd.ExcelWriter(excel_path, engine='openpyxl', mode='w') as writer:
        # Process each sheet
        for sheet_name in xls.sheet_names:
            df = pd.read_excel(xls, sheet_name=sheet_name)
            
            # Update dates based on sheet type
            if sheet_name == 'PortfolioSetting':
                if 'StartDate' in df.columns:
                    df['StartDate'] = test_date
                if 'EndDate' in df.columns:
                    df['EndDate'] = test_date
                print(f"  Updated {sheet_name}: StartDate and EndDate to {test_date}")
            
            elif sheet_name == 'GeneralParameter':
                # No date fields to update in GeneralParameter typically
                pass
            
            # Write the sheet back
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"✅ Updated {excel_path}")

def main():
    """Update all necessary Excel files."""
    
    # Paths to Excel files
    portfolio_path = "bt/archive/backtester_stable/BTRUN/input_portfolio.xlsx"
    input_sheets_path = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    
    # Test date
    test_date = "03_01_2024"  # January 3, 2024
    
    print(f"Updating input files for test date: {test_date}")
    print("="*60)
    
    # Update legacy portfolio file
    if os.path.exists(portfolio_path):
        update_portfolio_dates(portfolio_path, test_date)
    else:
        print(f"⚠️  File not found: {portfolio_path}")
    
    # Update main input sheets portfolio file
    if os.path.exists(input_sheets_path):
        update_portfolio_dates(input_sheets_path, test_date)
    else:
        print(f"⚠️  File not found: {input_sheets_path}")
    
    print("\n✅ Date updates complete!")
    print("\nYou can now run the comparison test:")
    print("  python3 scripts/run_1day_comparison_test.py --date 20240103")

if __name__ == "__main__":
    main() 