#!/usr/bin/env python3
"""
Copy all NIFTY data from 2022 onwards from remote MySQL to local MySQL
"""

import pymysql
import pandas as pd
from datetime import datetime
import sys
import time

# Remote MySQL connection details
REMOTE_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

# Local MySQL connection details
LOCAL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

# NIFTY-related tables to copy
NIFTY_TABLES = [
    'nifty_call',
    'nifty_put',
    'nifty_cash',
    'nifty_spot',
    'nifty_future',
    'holidays',
    'lot_size'
]

def get_table_schema(conn, table_name):
    """Get CREATE TABLE statement for a table"""
    cursor = conn.cursor()
    cursor.execute(f"SHOW CREATE TABLE {table_name}")
    result = cursor.fetchone()
    cursor.close()
    return result[1] if result else None

def copy_table_with_date_filter(remote_conn, local_conn, table_name, date_column='date', start_date='220101', batch_size=50000):
    """Copy a table from remote to local with date filtering (2022 onwards)"""
    print(f"\nCopying table: {table_name} (from 2022 onwards)")
    print("-" * 50)
    
    try:
        # Get and create table schema
        create_stmt = get_table_schema(remote_conn, table_name)
        if not create_stmt:
            print(f"Could not get schema for {table_name}")
            return False
        
        local_cursor = local_conn.cursor()
        
        # Drop table if exists and recreate
        local_cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
        local_cursor.execute(create_stmt)
        local_conn.commit()
        print("Table structure created")
        
        # Check if table has date column
        remote_cursor = remote_conn.cursor()
        remote_cursor.execute(f"DESCRIBE {table_name}")
        columns = [col[0] for col in remote_cursor.fetchall()]
        
        if date_column not in columns:
            print(f"Table {table_name} doesn't have '{date_column}' column, copying all data")
            # For tables without date column (like holidays, lot_size), copy all data
            remote_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_rows = remote_cursor.fetchone()[0]
        else:
            # Get row count for data from 2022 onwards
            remote_cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {date_column} >= {start_date}")
            total_rows = remote_cursor.fetchone()[0]
        
        print(f"Total rows to copy: {total_rows:,}")
        
        if total_rows == 0:
            print("No data to copy")
            return True
        
        # Copy data in batches
        offset = 0
        start_time = time.time()
        
        while offset < total_rows:
            # Read batch from remote with date filter
            if date_column in columns:
                query = f"""
                SELECT * FROM {table_name} 
                WHERE {date_column} >= {start_date}
                ORDER BY {date_column}, time
                LIMIT {batch_size} OFFSET {offset}
                """
            else:
                query = f"SELECT * FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
            
            df = pd.read_sql(query, remote_conn)
            
            if df.empty:
                break
            
            # Write to local
            df.to_sql(table_name, local_conn, if_exists='append', index=False, method='multi')
            
            offset += batch_size
            progress = min(100, (offset / total_rows) * 100)
            elapsed = time.time() - start_time
            eta = (elapsed / offset) * (total_rows - offset) if offset > 0 else 0
            
            print(f"\rProgress: {progress:.1f}% ({offset:,}/{total_rows:,} rows) - ETA: {eta:.0f}s", end='')
            sys.stdout.flush()
        
        print(f"\nCompleted in {time.time() - start_time:.1f} seconds")
        
        # Verify copy
        local_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        local_rows = local_cursor.fetchone()[0]
        
        if date_column in columns:
            # Check date range
            local_cursor.execute(f"SELECT MIN({date_column}), MAX({date_column}) FROM {table_name}")
            min_date, max_date = local_cursor.fetchone()
            print(f"Date range: {min_date} to {max_date}")
        
        print(f"✓ Verification: {local_rows:,} rows copied")
        
        remote_cursor.close()
        local_cursor.close()
        
        return True
            
    except Exception as e:
        print(f"Error copying {table_name}: {e}")
        return False

def main():
    """Main function"""
    print("NIFTY Data Copy Tool (2022 onwards)")
    print("=" * 70)
    print(f"Source: {REMOTE_CONFIG['host']}:{REMOTE_CONFIG['port']}/{REMOTE_CONFIG['database']}")
    print(f"Destination: {LOCAL_CONFIG['host']}:{LOCAL_CONFIG['port']}/{LOCAL_CONFIG['database']}")
    print("=" * 70)
    
    try:
        # Connect to databases
        print("\nConnecting to databases...")
        remote_conn = pymysql.connect(**REMOTE_CONFIG)
        local_conn = pymysql.connect(**LOCAL_CONFIG)
        print("✓ Connected successfully")
        
        # Check which NIFTY tables exist
        remote_cursor = remote_conn.cursor()
        remote_cursor.execute("SHOW TABLES")
        all_tables = [row[0] for row in remote_cursor.fetchall()]
        remote_cursor.close()
        
        # Filter for existing NIFTY tables
        tables_to_copy = [t for t in NIFTY_TABLES if t in all_tables]
        missing_tables = [t for t in NIFTY_TABLES if t not in all_tables]
        
        if missing_tables:
            print(f"\nWarning: These tables don't exist: {', '.join(missing_tables)}")
        
        print(f"\nTables to copy: {', '.join(tables_to_copy)}")
        
        # Confirm before proceeding
        response = input("\nProceed with copying NIFTY data from 2022 onwards? (yes/no): ")
        if response.lower() != 'yes':
            print("Aborted.")
            return 0
        
        # Copy each table
        success_count = 0
        failed_tables = []
        
        for table in tables_to_copy:
            if copy_table_with_date_filter(remote_conn, local_conn, table):
                success_count += 1
            else:
                failed_tables.append(table)
        
        # Summary
        print(f"\n{'='*70}")
        print(f"Summary:")
        print(f"  Successfully copied: {success_count}/{len(tables_to_copy)} tables")
        if failed_tables:
            print(f"  Failed tables: {', '.join(failed_tables)}")
        
        # Show data statistics
        print(f"\nData Statistics:")
        local_cursor = local_conn.cursor()
        
        for table in tables_to_copy:
            if table in failed_tables:
                continue
                
            local_cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = local_cursor.fetchone()[0]
            
            # Get date range if applicable
            local_cursor.execute(f"DESCRIBE {table}")
            columns = [col[0] for col in local_cursor.fetchall()]
            
            if 'date' in columns and count > 0:
                local_cursor.execute(f"SELECT MIN(date), MAX(date) FROM {table}")
                min_date, max_date = local_cursor.fetchone()
                print(f"  {table}: {count:,} rows ({min_date} to {max_date})")
            else:
                print(f"  {table}: {count:,} rows")
        
        local_cursor.close()
        
        # Close connections
        remote_conn.close()
        local_conn.close()
        
        print("\n✓ NIFTY data copy completed!")
        print("\nYou can now run legacy vs HeavyDB comparisons using:")
        print("  python3 scripts/run_legacy_vs_heavydb_comparison.py")
        
    except Exception as e:
        print(f"\nError: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 