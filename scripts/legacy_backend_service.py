#!/usr/bin/env python3
"""
Legacy Backend Service - Real Implementation
This service runs the actual legacy backtesting logic with MySQL data
"""

import os
import sys
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify
import threading
import time

# Add legacy paths
legacy_path = os.path.abspath('bt/archive/backtester_stable')
sys.path.insert(0, legacy_path)

# Import legacy modules
try:
    from app.pm import run_portfolio
    from app.parser.utils import parse_portfolio_json
    from app.commons.enums import FEEDSOURCE, EXCHANGE, PRODUCTTYPE, ORDERTYPE, BROKER
    from app.config import Config
    print("✓ Legacy modules imported successfully")
except ImportError as e:
    print(f"✗ Failed to import legacy modules: {e}")
    print("Make sure you're running from the correct directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

@app.route('/healthcheck', methods=['GET'])
def healthcheck():
    """Health check endpoint"""
    return jsonify({
        "status": "ok", 
        "service": "legacy_backend",
        "mysql_connected": check_mysql_connection(),
        "timestamp": datetime.now().isoformat()
    })

def check_mysql_connection():
    """Check if MySQL connection is working"""
    try:
        # Import MySQL connection utilities
        from app.database.utils import get_available_trading_dates
        from app.commons.enums import NSEINDEX
        
        # Try to get some data
        dates = get_available_trading_dates(NSEINDEX.NIFTY)
        return len(dates) > 0
    except Exception as e:
        logger.error(f"MySQL connection check failed: {e}")
        return False

@app.route('/backtest/start', methods=['POST'])
def start_backtest():
    """
    Start backtest using actual legacy logic with MySQL data
    """
    try:
        logger.info("Received backtest request")
        
        # Extract request data
        data = request.json
        portfolio_json = data.get("portfolio")
        start_date = int(data.get("start_date"))
        end_date = int(data.get("end_date"))
        fix_vix = float(data.get("fix_vix", 0))
        broker_details = data.get("broker_details", {"broker": "BROKER.ZERODHA", "client_id": "test"})
        exchange = data.get("exchange", "EXCHANGE.NSE")
        product_type = data.get("product_type", "PRODUCTTYPE.MIS")
        order_type = data.get("order_type", "ORDERTYPE.MARKET")
        check_interval = int(data.get("check_interval", 300))  # 5 minutes
        feed_source = data.get("feed_source", "FEEDSOURCE.HISTORICAL")
        
        logger.info(f"Backtest parameters: {start_date} to {end_date}")
        
        # Parse portfolio
        portfolio = parse_portfolio_json(portfolio_json)
        logger.info(f"Parsed portfolio with {len(portfolio.strategies)} strategies")
        
        # Convert enum strings to actual enums
        exchange_enum = eval(exchange)
        product_type_enum = eval(product_type)
        order_type_enum = eval(order_type)
        feed_source_enum = eval(feed_source)
        
        # Run the actual legacy backtesting logic
        logger.info("Starting legacy backtest execution...")
        start_time = time.time()
        
        result = run_portfolio(
            portfolio=portfolio,
            start_date=start_date,
            end_date=end_date,
            fix_vix=fix_vix,
            broker_details=broker_details,
            exchange=exchange_enum,
            product_type=product_type_enum,
            order_type=order_type_enum,
            check_interval=check_interval,
            feed_source=feed_source_enum,
            is_live=False
        )
        
        execution_time = time.time() - start_time
        logger.info(f"Backtest completed in {execution_time:.2f} seconds")
        
        # Log result summary
        if 'strategies' in result:
            strategies = result['strategies']
            if 'orders' in strategies:
                order_count = len(strategies['orders'])
                logger.info(f"Generated {order_count} orders")
                
                # Calculate total P&L
                total_pnl = sum(order.get('pnl', 0) for order in strategies['orders'])
                logger.info(f"Total P&L: {total_pnl}")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        logger.exception(e)
        return jsonify({"error": str(e)}), 500

def run_service(port=5000):
    """Run the Flask service"""
    logger.info(f"Starting Legacy Backend Service on port {port}")
    logger.info("This service runs ACTUAL legacy backtesting logic with MySQL data")
    
    # Check MySQL connection on startup
    if check_mysql_connection():
        logger.info("✓ MySQL connection verified")
    else:
        logger.warning("✗ MySQL connection failed - service may not work correctly")
    
    app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)

def main():
    """Main entry point"""
    print("="*60)
    print("Legacy Backend Service - Real Implementation")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Install required packages if missing
    try:
        import mysql.connector
        print("✓ mysql-connector-python available")
    except ImportError:
        print("Installing mysql-connector-python...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "mysql-connector-python"])
        print("✓ mysql-connector-python installed")
    
    # Start services on both ports (tick and minute)
    print("Starting backend services...")
    
    # Start tick data service on port 5000
    tick_thread = threading.Thread(target=run_service, args=(5000,))
    tick_thread.daemon = True
    tick_thread.start()
    print("✓ Tick data service started on http://127.0.0.1:5000")
    
    # Start minute data service on port 5001
    minute_thread = threading.Thread(target=run_service, args=(5001,))
    minute_thread.daemon = True
    minute_thread.start()
    print("✓ Minute data service started on http://127.0.0.1:5001")
    
    print("\nLegacy Backend Services are running!")
    print("These services use REAL MySQL data and legacy backtesting logic")
    print("Press Ctrl+C to stop")
    
    try:
        # Keep the main thread alive
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nShutting down legacy backend services...")

if __name__ == "__main__":
    main() 