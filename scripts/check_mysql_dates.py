#!/usr/bin/env python3
import mysql.connector

conn = mysql.connector.connect(
    host='************',
    user='mahesh',
    password='mahesh_123',
    database='historicaldb'
)

cursor = conn.cursor()

# Check date range
cursor.execute("SELECT MIN(date), MAX(date) FROM nifty_cash")
min_date, max_date = cursor.fetchone()
print(f"NIFTY cash data range: {min_date} to {max_date}")

# Check recent dates
cursor.execute("SELECT DISTINCT date FROM nifty_cash ORDER BY date DESC LIMIT 10")
recent_dates = cursor.fetchall()
print("\nRecent dates in database:")
for date in recent_dates:
    print(f"  {date[0]}")

# Check data format
cursor.execute("SELECT date, time, close FROM nifty_cash LIMIT 5")
print("\nSample data:")
for row in cursor.fetchall():
    print(f"  Date: {row[0]}, Time: {row[1]}, Close: {row[2]}")

cursor.close()
conn.close() 