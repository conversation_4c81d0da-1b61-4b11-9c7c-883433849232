#!/usr/bin/env python3
"""Find loop structure to understand where trades should be appended."""

with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

# Track indentation levels
print("=== Loop Structure Analysis ===")
current_indent = 0
indent_stack = []

for i in range(85, 320):  # Focus on the relevant section
    if i >= len(lines):
        break
        
    line = lines[i]
    indent = len(line) - len(line.lstrip())
    stripped = line.strip()
    
    # Track significant lines
    if 'for current_trade_date_obj in trade_dates:' in line:
        print(f'\nLine {i+1} ({indent} spaces): DATE LOOP START')
        indent_stack.append(('date_loop', indent))
    elif 'for strategy in pf.strategies:' in line:
        print(f'Line {i+1} ({indent} spaces): STRATEGY LOOP START')
        indent_stack.append(('strategy_loop', indent))
    elif 'for leg in strategy.legs:' in line:
        print(f'Line {i+1} ({indent} spaces): LEG LOOP START')
        indent_stack.append(('leg_loop', indent))
    elif 'build_trade_record' in line:
        print(f'Line {i+1} ({indent} spaces): build_trade_record called')
    elif 'trade_records.append' in line:
        print(f'Line {i+1} ({indent} spaces): >>> trade_records.append <<<')
        # Check which loops we're inside
        print(f'  Currently inside these loops:')
        for loop_name, loop_indent in indent_stack:
            if indent > loop_indent:
                print(f'    - {loop_name} (indent {loop_indent})')
            else:
                print(f'    - NOT inside {loop_name} (indent {loop_indent})')
    
    # Track when we exit loops (dedent)
    if stripped and indent < current_indent and indent_stack:
        # We've dedented, check if we've exited any loops
        while indent_stack and indent <= indent_stack[-1][1]:
            exited_loop, _ = indent_stack.pop()
            print(f'Line {i+1} ({indent} spaces): Exited {exited_loop}')
    
    if stripped:  # Update current indent for non-empty lines
        current_indent = indent 