#!/usr/bin/env python3
"""Fix formatting and presentation issues to match legacy output exactly."""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime

# Add project paths
sys.path.append('/srv/samba/shared')

def analyze_formatting_differences():
    """Analyze specific formatting differences between outputs."""
    
    print("="*80)
    print("ANALYZING FORMATTING DIFFERENCES")
    print("="*80)
    
    legacy_file = "tests/outputs/gpu_parity_run/NIF0DTE_250401_cpu.xlsx"
    heavydb_file = "heavydb_comprehensive_output.xlsx"
    
    if not os.path.exists(legacy_file) or not os.path.exists(heavydb_file):
        print("Files not found")
        return
    
    # 1. Case Sensitivity in Transactions
    print("\n1. CASE SENSITIVITY ANALYSIS:")
    print("-"*40)
    
    # Check legacy transaction sheet
    legacy_xl = pd.ExcelFile(legacy_file)
    trans_sheets = [s for s in legacy_xl.sheet_names if 'Trans' in s and 'PORTFOLIO' not in s]
    if trans_sheets:
        leg_trans = pd.read_excel(legacy_file, sheet_name=trans_sheets[0])
        print(f"Legacy transaction columns: {list(leg_trans.columns)}")
        if 'Instrument Type' in leg_trans.columns:
            print(f"Legacy instrument types: {leg_trans['Instrument Type'].unique()}")
        if 'Side' in leg_trans.columns:
            print(f"Legacy sides: {leg_trans['Side'].unique()}")
    
    # Check HeavyDB transaction sheet
    hdb_xl = pd.ExcelFile(heavydb_file)
    hdb_trans_sheets = [s for s in hdb_xl.sheet_names if 'Trans' in s and 'PORTFOLIO' not in s]
    if hdb_trans_sheets:
        hdb_trans = pd.read_excel(heavydb_file, sheet_name=hdb_trans_sheets[0])
        print(f"\nHeavyDB transaction columns: {list(hdb_trans.columns)}")
        if 'instrument_type' in hdb_trans.columns:
            print(f"HeavyDB instrument types: {hdb_trans['instrument_type'].unique()}")
        if 'side' in hdb_trans.columns:
            print(f"HeavyDB sides: {hdb_trans['side'].unique()}")
    
    # 2. Time Format
    print("\n\n2. TIME FORMAT ANALYSIS:")
    print("-"*40)
    
    if trans_sheets and 'Entry Time' in leg_trans.columns:
        print(f"Legacy Entry Time format: {leg_trans['Entry Time'].iloc[0]} (type: {type(leg_trans['Entry Time'].iloc[0])})")
    
    if hdb_trans_sheets and 'entry_time' in hdb_trans.columns:
        print(f"HeavyDB entry_time format: {hdb_trans['entry_time'].iloc[0]} (type: {type(hdb_trans['entry_time'].iloc[0])})")
    
    # 3. PortfolioParameter Format
    print("\n\n3. PORTFOLIO PARAMETER FORMAT:")
    print("-"*40)
    
    leg_pp = pd.read_excel(legacy_file, sheet_name="PortfolioParameter")
    hdb_pp = pd.read_excel(heavydb_file, sheet_name="PortfolioParameter")
    
    print(f"Legacy shape: {leg_pp.shape}")
    print(f"Legacy columns: {list(leg_pp.columns)}")
    print(f"Legacy first 2 rows:\n{leg_pp.head(2)}")
    
    print(f"\nHeavyDB shape: {hdb_pp.shape}")
    print(f"HeavyDB columns: {list(hdb_pp.columns)}")
    
    # 4. Metrics Duplicate Analysis
    print("\n\n4. METRICS DUPLICATE ANALYSIS:")
    print("-"*40)
    
    leg_metrics = pd.read_excel(legacy_file, sheet_name="Metrics")
    hdb_metrics = pd.read_excel(heavydb_file, sheet_name="Metrics")
    
    print(f"Legacy metrics shape: {leg_metrics.shape}")
    print(f"HeavyDB metrics shape: {hdb_metrics.shape}")
    
    if 'Strategy' in hdb_metrics.columns:
        print(f"\nHeavyDB unique strategies in metrics: {hdb_metrics['Strategy'].unique()}")
        print(f"Value counts:\n{hdb_metrics['Strategy'].value_counts()}")

def create_formatting_fixes():
    """Create fixes for all formatting issues."""
    
    print("\n\n" + "="*80)
    print("CREATING FORMATTING FIXES")
    print("="*80)
    
    # Fix 1: Case Sensitivity Mapping
    case_mappings = {
        # Instrument types
        'call': 'CALL',
        'put': 'PUT',
        
        # Sides
        'buy': 'BUY',
        'sell': 'SELL',
        
        # Strike types
        'atm': 'ATM',
        'itm': 'ITM',
        'otm': 'OTM',
        
        # Other common values
        'yes': 'YES',
        'no': 'NO'
    }
    
    print("\n1. Case Mapping Dictionary Created:")
    for k, v in case_mappings.items():
        print(f"   {k} → {v}")
    
    # Fix 2: Time Format Function
    def format_time_hhmmss(time_val):
        """Convert time to HHMMSS format."""
        if isinstance(time_val, str) and ':' in time_val:
            # Already in HH:MM:SS format
            return time_val
        elif isinstance(time_val, (int, float)):
            # Convert HHMMSS integer to HH:MM:SS string
            time_str = str(int(time_val)).zfill(6)
            return f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:]}"
        else:
            return str(time_val)
    
    print("\n2. Time Format Function Created")
    print("   Example: 91600 → 09:16:00")
    
    # Fix 3: PortfolioParameter Transform
    def transform_portfolio_parameter(df):
        """Transform PortfolioParameter to legacy 2x2 format."""
        if df.empty:
            return df
        
        # Legacy format has 2 rows: Headers and Values
        # with columns: Particulars, PORTFOLIO (portfolio name)
        
        # Get portfolio name from first row
        portfolio_name = df['PortfolioName'].iloc[0] if 'PortfolioName' in df.columns else 'PORTFOLIO'
        
        # Create new format
        result = pd.DataFrame({
            'Particulars': ['BacktestStartDate', 'BacktestEndDate'],
            portfolio_name: [
                df['StartDate'].iloc[0] if 'StartDate' in df.columns else '',
                df['EndDate'].iloc[0] if 'EndDate' in df.columns else ''
            ]
        })
        
        return result
    
    print("\n3. PortfolioParameter Transform Function Created")
    print("   Converts 1x21 format to 2x2 legacy format")
    
    # Fix 4: Remove Duplicate Metrics
    def deduplicate_metrics(df):
        """Remove duplicate metrics entries."""
        if 'Strategy' in df.columns and 'Particulars' in df.columns:
            # Keep only unique combinations of Strategy and Particulars
            df = df.drop_duplicates(subset=['Strategy', 'Particulars'], keep='first')
        return df
    
    print("\n4. Metrics Deduplication Function Created")
    
    return case_mappings, format_time_hhmmss, transform_portfolio_parameter, deduplicate_metrics

def apply_formatting_fixes(input_file, output_file):
    """Apply all formatting fixes to create legacy-compatible output."""
    
    print("\n\n" + "="*80)
    print("APPLYING FORMATTING FIXES")
    print("="*80)
    
    # Get fix functions
    case_mappings, format_time_hhmmss, transform_portfolio_parameter, deduplicate_metrics = create_formatting_fixes()
    
    # Load the Excel file
    xl = pd.ExcelFile(input_file)
    
    # Create a new Excel writer
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        
        for sheet_name in xl.sheet_names:
            df = pd.read_excel(input_file, sheet_name=sheet_name)
            
            print(f"\nProcessing sheet: {sheet_name}")
            
            # Apply specific fixes based on sheet type
            if sheet_name == "PortfolioParameter":
                # Transform to 2x2 format
                df = transform_portfolio_parameter(df)
                print(f"  - Transformed to {df.shape} format")
            
            elif sheet_name == "Metrics":
                # Remove duplicates
                original_len = len(df)
                df = deduplicate_metrics(df)
                print(f"  - Removed {original_len - len(df)} duplicate rows")
            
            elif "Trans" in sheet_name:
                # Fix case sensitivity
                if 'instrument_type' in df.columns:
                    df['instrument_type'] = df['instrument_type'].str.upper()
                    print(f"  - Fixed instrument_type case")
                
                if 'side' in df.columns:
                    df['side'] = df['side'].str.upper()
                    print(f"  - Fixed side case")
                
                # Fix time format
                for col in ['entry_time', 'exit_time']:
                    if col in df.columns:
                        df[col] = df[col].apply(format_time_hhmmss)
                        print(f"  - Fixed {col} format")
                
                # Convert column names to Title Case for legacy compatibility
                column_mapping = {
                    'portfolio_name': 'Portfolio Name',
                    'strategy_name': 'Strategy Name',
                    'leg_id': 'Leg ID',
                    'instrument_type': 'Instrument Type',
                    'side': 'Side',
                    'strike': 'Strike',
                    'expiry': 'Expiry',
                    'entry_date': 'Entry Date',
                    'entry_time': 'Entry Time',
                    'entry_price': 'Entry Price',
                    'exit_date': 'Exit Date',
                    'exit_time': 'Exit Time',
                    'exit_price': 'Exit Price',
                    'quantity': 'Quantity',
                    'pnl': 'PnL'
                }
                
                # Apply column renaming
                df = df.rename(columns=column_mapping)
                print(f"  - Renamed columns to legacy format")
            
            # Write the processed dataframe
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"\n✅ Fixed output written to: {output_file}")

def main():
    """Main execution."""
    # First analyze the differences
    analyze_formatting_differences()
    
    # Create and show the fixes
    create_formatting_fixes()
    
    # Apply fixes to create legacy-compatible output
    input_file = "heavydb_comprehensive_output.xlsx"
    output_file = "heavydb_legacy_formatted_output.xlsx"
    
    if os.path.exists(input_file):
        apply_formatting_fixes(input_file, output_file)
        
        print("\n\n" + "="*80)
        print("FORMATTING FIXES COMPLETE")
        print("="*80)
        print(f"\nOriginal HeavyDB output: {input_file}")
        print(f"Legacy-formatted output: {output_file}")
        print("\nAll formatting issues have been addressed:")
        print("  ✅ Case sensitivity fixed (CALL, PUT, BUY, SELL)")
        print("  ✅ Time format converted (HH:MM:SS)")
        print("  ✅ PortfolioParameter transformed to 2x2 format")
        print("  ✅ Duplicate metrics removed")
        print("  ✅ Column names converted to Title Case")
    else:
        print(f"\nInput file not found: {input_file}")
        print("Please run the HeavyDB backtester first.")

if __name__ == "__main__":
    main() 