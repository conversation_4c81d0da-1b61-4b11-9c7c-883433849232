#!/usr/bin/env python3
"""
Find a good test date that has data in both MySQL and HeavyDB
"""

import mysql.connector as mysql
from datetime import datetime
import sys
sys.path.insert(0, '.')

def check_mysql_dates():
    """Find dates with data in MySQL"""
    print("Checking MySQL for dates with complete data...")
    
    try:
        mydb = mysql.connect(
            host="************",
            user="mahesh", 
            password="mahesh_123",
            database="historicaldb"
        )
        cursor = mydb.cursor()
        
        # Get recent dates with data
        # MySQL uses YYMMDD format
        query = """
            SELECT 
                c.date as trade_date,
                COUNT(DISTINCT c.time) as cash_ticks,
                COUNT(DISTINCT ca.strike) as call_strikes,
                COUNT(DISTINCT p.strike) as put_strikes
            FROM nifty_cash c
            JOIN nifty_call ca ON c.date = ca.date
            JOIN nifty_put p ON c.date = p.date
            WHERE c.date >= 240401  -- April 2024
            GROUP BY c.date
            HAVING cash_ticks > 100 AND call_strikes > 10 AND put_strikes > 10
            ORDER BY c.date DESC
            LIMIT 20
        """
        
        cursor.execute(query)
        mysql_dates = cursor.fetchall()
        
        print(f"\nFound {len(mysql_dates)} dates with complete data in MySQL:")
        mysql_good_dates = []
        for row in mysql_dates[:10]:
            date_str = str(row[0])
            # Convert YYMMDD to readable format
            year = f"20{date_str[:2]}"
            month = date_str[2:4]
            day = date_str[4:6]
            formatted_date = f"{year}-{month}-{day}"
            print(f"  {formatted_date}: {row[1]} cash ticks, {row[2]} call strikes, {row[3]} put strikes")
            mysql_good_dates.append((date_str, formatted_date))
        
        cursor.close()
        mydb.close()
        
        return mysql_good_dates
        
    except Exception as e:
        print(f"MySQL Error: {e}")
        return []

def check_heavydb_dates(mysql_dates):
    """Check which MySQL dates also exist in HeavyDB"""
    print("\nChecking HeavyDB for matching dates...")
    
    try:
        from bt.backtester_stable.BTRUN.heavydb_connection import get_connection
        
        conn = get_connection()
        
        # Convert MySQL dates to HeavyDB format
        date_conditions = []
        for mysql_date, formatted_date in mysql_dates:
            date_conditions.append(f"trade_date = DATE '{formatted_date}'")
        
        if not date_conditions:
            return []
        
        query = f"""
            SELECT 
                trade_date,
                COUNT(*) as row_count,
                MIN(trade_time) as first_time,
                MAX(trade_time) as last_time
            FROM nifty_option_chain
            WHERE {' OR '.join(date_conditions[:5])}
            GROUP BY trade_date
            ORDER BY trade_date DESC
        """
        
        result = conn.execute(query).fetchall()
        
        print(f"\nFound {len(result)} matching dates in HeavyDB:")
        heavydb_dates = []
        for row in result:
            print(f"  {row[0]}: {row[1]:,} rows ({row[2]} to {row[3]})")
            heavydb_dates.append(str(row[0]))
        
        conn.close()
        
        return heavydb_dates
        
    except Exception as e:
        print(f"HeavyDB Error: {e}")
        return []

def find_common_dates():
    """Find dates that exist in both databases"""
    print("="*60)
    print("Finding Common Test Dates")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Get MySQL dates
    mysql_dates = check_mysql_dates()
    
    if not mysql_dates:
        print("\n✗ No suitable dates found in MySQL")
        return None
    
    # Check HeavyDB
    heavydb_dates = check_heavydb_dates(mysql_dates)
    
    if not heavydb_dates:
        print("\n✗ No matching dates found in HeavyDB")
        return None
    
    # Find common dates
    print("\n" + "="*60)
    print("Recommended Test Date:")
    print("="*60)
    
    for mysql_date, formatted_date in mysql_dates:
        if formatted_date in heavydb_dates:
            print(f"\n✓ Use date: {formatted_date}")
            print(f"  MySQL format: {mysql_date}")
            print(f"  HeavyDB format: {formatted_date}")
            print(f"  Input Excel format: {formatted_date[8:10]}_{formatted_date[5:7]}_{formatted_date[0:4]}")
            
            return {
                'mysql': mysql_date,
                'heavydb': formatted_date,
                'excel': f"{formatted_date[8:10]}_{formatted_date[5:7]}_{formatted_date[0:4]}",
                'yyyymmdd': formatted_date.replace('-', '')
            }
    
    print("\n✗ No common dates found between MySQL and HeavyDB")
    return None

if __name__ == "__main__":
    result = find_common_dates()
    
    if result:
        print("\nTo run the comparison test with this date:")
        print(f"  1. Update input Excel with dates: {result['excel']} to {result['excel']}")
        print(f"  2. Run: python3 scripts/run_comparison_with_real_data.py --date {result['yyyymmdd']}") 