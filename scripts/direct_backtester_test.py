#!/usr/bin/env python3
"""
Direct test of the new backtester with minimal configuration
"""

import os
import sys
import subprocess
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def run_direct_test():
    """Run a direct test with explicit paths"""
    print("="*60)
    print("Direct Backtester Test")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Create output directory if needed
    output_dir = "bt/backtester_stable/BTRUN/output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"\n✓ Created output directory: {output_dir}")
    
    # Configuration
    portfolio_excel = os.path.abspath("bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx")
    output_path = os.path.abspath("bt/backtester_stable/BTRUN/output/direct_test_output.xlsx")
    
    print(f"\nInput file: {portfolio_excel}")
    print(f"Output path: {output_path}")
    
    # Check input
    if not os.path.exists(portfolio_excel):
        print("\n✗ Input file not found!")
        return False
    
    print("\n✓ Input file exists")
    
    # Run backtester directly
    print("\nRunning backtester...")
    
    cmd = [
        sys.executable,
        "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", portfolio_excel,
        "--output-path", output_path,
        "--start-date", "20250401",
        "--end-date", "20250401"  # Just one day
    ]
    
    # Also try with current directory approach
    alt_output = "direct_test_output.xlsx"
    
    print(f"Command: python -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU ...")
    
    try:
        # Set environment
        env = os.environ.copy()
        env['PYTHONPATH'] = os.path.abspath('.')
        
        # Run command
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        print(f"\nReturn code: {result.returncode}")
        
        # Show output
        if result.stdout:
            print("\nSTDOUT (last 1000 chars):")
            print("-" * 40)
            print(result.stdout[-1000:])
            
        if result.stderr:
            print("\nSTDERR (last 500 chars):")
            print("-" * 40)
            print(result.stderr[-500:])
        
        # Check for output files
        print("\nChecking for output files...")
        
        possible_outputs = [
            output_path,
            alt_output,
            f"{output_dir}/direct_test_output.xlsx",
            "./direct_test_output.xlsx",
            "bt/backtester_stable/BTRUN/direct_test_output.xlsx"
        ]
        
        found = False
        for path in possible_outputs:
            if os.path.exists(path):
                file_size = os.path.getsize(path)
                print(f"  ✓ Found output at: {path} ({file_size:,} bytes)")
                found = True
                
                # Also check JSON output
                json_path = path.replace('.xlsx', '.json')
                if os.path.exists(json_path):
                    json_size = os.path.getsize(json_path)
                    print(f"  ✓ Found JSON at: {json_path} ({json_size:,} bytes)")
                break
        
        if not found:
            print("  ✗ No output file found")
            
            # List files in output directory
            print(f"\nFiles in {output_dir}:")
            if os.path.exists(output_dir):
                for f in os.listdir(output_dir):
                    print(f"  - {f}")
            
        return found
        
    except Exception as e:
        print(f"\n✗ Error: {e}")
        return False

def check_module_import():
    """Check if the module can be imported directly"""
    print("\nChecking direct import...")
    
    try:
        import bt.backtester_stable.BTRUN.BTRunPortfolio_GPU as gpu_bt
        print("  ✓ Module imported successfully")
        print(f"  Module path: {gpu_bt.__file__}")
        return True
    except ImportError as e:
        print(f"  ✗ Import error: {e}")
        return False

if __name__ == "__main__":
    # Check import first
    check_module_import()
    
    # Run test
    success = run_direct_test()
    
    print("\n" + "="*60)
    if success:
        print("✓ Direct test PASSED")
    else:
        print("✗ Direct test FAILED")
    print("="*60) 