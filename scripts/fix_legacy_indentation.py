#!/usr/bin/env python3
"""Fix indentation issue in patched Util.py."""

import re

def fix_util_indentation():
    """Fix the indentation in Util.py."""
    
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    
    # Read the file
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Create the correctly indented getBacktestResults implementation
    # Note: Using raw string to preserve exact formatting
    new_implementation = '''    @staticmethod
    def getBacktestResults(btPara: dict) -> dict:
        """Modified to use LocalBacktestEngine instead of Flask services."""
        
        startTime = datetime.now()
        logging.info(f"{startTime}, Backtesting Portfolio: {btPara['portfolio']['id']} using LocalBacktestEngine")
        
        try:
            # Import LocalBacktestEngine
            from LocalBacktestEngine import LocalBacktestEngine
            
            # MySQL configuration
            mysql_config = {
                'host': 'localhost',
                'user': 'mahesh',
                'password': 'mahesh_123',
                'database': 'historicaldb'
            }
            
            # Initialize local engine
            engine = LocalBacktestEngine(mysql_config)
            
            # Execute backtest
            respp = engine.execute_trade(btPara)
            
            # Close connection
            engine.close()
            
            # Add portfolio name to orders
            if 'strategies' in respp and 'orders' in respp['strategies']:
                orderss = pd.DataFrame(respp['strategies']['orders'])
                if not orderss.empty:
                    orderss['portfolio_name'] = btPara['portfolio']['name']
                    respp['strategies']['orders'] = orderss.to_dict("records")
            
            logging.info(f"LocalBacktestEngine completed successfully")
            
        except Exception as e:
            logging.error(f"Error in LocalBacktestEngine: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            respp = {}
        
        endTime = datetime.now()
        durationn = round((endTime-startTime).total_seconds(), 2)
        logging.info(f"{endTime}, Completed backtesting portfolio: {btPara['portfolio']['id']}, Time taken: {durationn} \\n")
        
        return respp'''
    
    # Find the position to insert
    # Look for the getMonthWiseStats method which comes before getBacktestResults
    month_stats_pos = content.find("def getMonthWiseStats(tradesDf: pd.DataFrame) -> pd.DataFrame:")
    
    if month_stats_pos == -1:
        print("❌ Could not find getMonthWiseStats method")
        return False
    
    # Find the end of getMonthWiseStats method
    # Look for the next method or class definition
    next_method_pattern = r'\n\s{4}@staticmethod\s*\n\s{4}def'
    match = re.search(next_method_pattern, content[month_stats_pos:])
    
    if match:
        insert_pos = month_stats_pos + match.start()
    else:
        # If no next method found, insert at end
        insert_pos = len(content)
    
    # Remove any existing getBacktestResults method
    # Find and remove the broken version
    pattern = r'@staticmethod\s*\n\s*def getBacktestResults\(btPara: dict\) -> dict:.*?(?=\n\s{0,4}@staticmethod|\n\s{0,4}class|\Z)'
    content_cleaned = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # Insert the new method at the correct position
    before = content_cleaned[:insert_pos]
    after = content_cleaned[insert_pos:]
    
    # Make sure we have proper spacing
    new_content = before.rstrip() + '\n\n' + new_implementation + '\n\n    ' + after.lstrip()
    
    # Write the fixed content
    with open(util_path, 'w') as f:
        f.write(new_content)
    
    print("✅ Fixed indentation in Util.py")
    return True

if __name__ == "__main__":
    if fix_util_indentation():
        print("\nYou can now run the legacy backtester:")
        print("  cd bt/archive/backtester_stable/BTRUN")
        print("  python3 BTRunPortfolio.py") 