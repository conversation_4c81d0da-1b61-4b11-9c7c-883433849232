#!/usr/bin/env python3
"""
Copy data from remote MySQL to local MySQL database
"""

import pymysql
import pandas as pd
from datetime import datetime
import sys
import time

# Remote MySQL connection details
REMOTE_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

# Local MySQL connection details
LOCAL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def get_table_schema(conn, table_name):
    """Get CREATE TABLE statement for a table"""
    cursor = conn.cursor()
    cursor.execute(f"SHOW CREATE TABLE {table_name}")
    result = cursor.fetchone()
    cursor.close()
    return result[1] if result else None

def copy_table(remote_conn, local_conn, table_name, batch_size=10000):
    """Copy a table from remote to local with batching"""
    print(f"\nCopying table: {table_name}")
    print("-" * 50)
    
    try:
        # Get row count
        remote_cursor = remote_conn.cursor()
        remote_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        total_rows = remote_cursor.fetchone()[0]
        print(f"Total rows to copy: {total_rows:,}")
        
        if total_rows == 0:
            print("No data to copy")
            return True
        
        # Get and create table schema
        create_stmt = get_table_schema(remote_conn, table_name)
        if not create_stmt:
            print(f"Could not get schema for {table_name}")
            return False
        
        local_cursor = local_conn.cursor()
        
        # Drop table if exists and recreate
        local_cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
        local_cursor.execute(create_stmt)
        local_conn.commit()
        print("Table structure created")
        
        # Copy data in batches
        offset = 0
        start_time = time.time()
        
        while offset < total_rows:
            # Read batch from remote
            query = f"SELECT * FROM {table_name} LIMIT {batch_size} OFFSET {offset}"
            df = pd.read_sql(query, remote_conn)
            
            if df.empty:
                break
            
            # Write to local
            df.to_sql(table_name, local_conn, if_exists='append', index=False, method='multi')
            
            offset += batch_size
            progress = min(100, (offset / total_rows) * 100)
            elapsed = time.time() - start_time
            eta = (elapsed / offset) * (total_rows - offset) if offset > 0 else 0
            
            print(f"\rProgress: {progress:.1f}% ({offset:,}/{total_rows:,} rows) - ETA: {eta:.0f}s", end='')
            sys.stdout.flush()
        
        print(f"\nCompleted in {time.time() - start_time:.1f} seconds")
        
        # Verify copy
        local_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        local_rows = local_cursor.fetchone()[0]
        
        if local_rows == total_rows:
            print(f"✓ Verification passed: {local_rows:,} rows copied")
            return True
        else:
            print(f"✗ Verification failed: Expected {total_rows:,} rows, got {local_rows:,}")
            return False
            
    except Exception as e:
        print(f"Error copying {table_name}: {e}")
        return False
    finally:
        if 'remote_cursor' in locals():
            remote_cursor.close()
        if 'local_cursor' in locals():
            local_cursor.close()

def main():
    """Main function"""
    print("MySQL Data Copy Tool")
    print("=" * 70)
    print(f"Source: {REMOTE_CONFIG['host']}:{REMOTE_CONFIG['port']}/{REMOTE_CONFIG['database']}")
    print(f"Destination: {LOCAL_CONFIG['host']}:{LOCAL_CONFIG['port']}/{LOCAL_CONFIG['database']}")
    print("=" * 70)
    
    # Priority tables for backtesting
    priority_tables = [
        'nifty_spot',
        'nifty_cash', 
        'nifty_call',
        'nifty_put',
        'nifty_future',
        'holidays',
        'lot_size'
    ]
    
    try:
        # Connect to databases
        print("\nConnecting to databases...")
        remote_conn = pymysql.connect(**REMOTE_CONFIG)
        local_conn = pymysql.connect(**LOCAL_CONFIG)
        print("✓ Connected successfully")
        
        # Get list of tables
        remote_cursor = remote_conn.cursor()
        remote_cursor.execute("SHOW TABLES")
        all_tables = [row[0] for row in remote_cursor.fetchall()]
        remote_cursor.close()
        
        # Filter for existing priority tables
        tables_to_copy = [t for t in priority_tables if t in all_tables]
        other_tables = [t for t in all_tables if t not in priority_tables]
        
        print(f"\nFound {len(all_tables)} tables total")
        print(f"Priority tables to copy: {len(tables_to_copy)}")
        
        # Ask user what to copy
        print("\nOptions:")
        print("1. Copy priority tables only (recommended for testing)")
        print("2. Copy all tables")
        print("3. Copy specific date range for priority tables")
        print("4. Exit")
        
        choice = input("\nEnter choice (1-4): ")
        
        if choice == '1':
            # Copy priority tables
            success_count = 0
            for table in tables_to_copy:
                if copy_table(remote_conn, local_conn, table):
                    success_count += 1
            
            print(f"\n{'='*70}")
            print(f"Copied {success_count}/{len(tables_to_copy)} priority tables successfully")
            
        elif choice == '2':
            # Copy all tables
            all_tables_to_copy = tables_to_copy + other_tables
            success_count = 0
            
            for i, table in enumerate(all_tables_to_copy):
                print(f"\n[{i+1}/{len(all_tables_to_copy)}]", end='')
                if copy_table(remote_conn, local_conn, table):
                    success_count += 1
            
            print(f"\n{'='*70}")
            print(f"Copied {success_count}/{len(all_tables_to_copy)} tables successfully")
            
        elif choice == '3':
            # Copy with date range
            start_date = input("Enter start date (YYYY-MM-DD): ")
            end_date = input("Enter end date (YYYY-MM-DD): ")
            
            print(f"\nCopying data from {start_date} to {end_date}")
            
            # Custom copy logic for date range
            for table in tables_to_copy:
                print(f"\nProcessing {table} with date filter...")
                # This would need custom logic per table based on date columns
                # For now, just copy full tables
                copy_table(remote_conn, local_conn, table)
        
        # Close connections
        remote_conn.close()
        local_conn.close()
        
        print("\n✓ Data copy completed!")
        
    except Exception as e:
        print(f"\nError: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 