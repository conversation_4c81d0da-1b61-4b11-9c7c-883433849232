#!/usr/bin/env python3
"""
Patch the legacy backtester to work without external services
by replacing the HTTP request with mock data
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime
import glob

def create_patched_util():
    """Create a patched version of Util.py that returns mock data"""
    
    mock_getBacktestResults = '''
    @staticmethod
    def getBacktestResults(btPara: dict) -> dict:
        """PATCHED VERSION - Returns mock data instead of making HTTP requests"""
        
        startTime = datetime.now()
        logging.info(f"{startTime}, Backtesting Portfolio: {btPara['portfolio']['id']} (MOCK MODE)")
        
        # Create mock response based on the portfolio parameters
        mock_orders = []
        
        # Extract strategy info
        for strategy in btPara.get('portfolio', {}).get('strategies', []):
            strategy_name = strategy.get('name', 'Test Strategy')
            
            # Create mock orders for each leg
            for i, leg in enumerate(strategy.get('legs', [])):
                if leg.get('status', '') != 'ACTIVE':
                    continue
                    
                mock_order = {
                    "entry_time": "Mon, 01 Apr 2025 09:16:00 GMT",
                    "exit_time": "Mon, 01 Apr 2025 12:00:00 GMT",
                    "option_type": leg.get('instrument_type', 'CALL'),
                    "qty": leg.get('lots', 1) * 50,  # Assuming lot size of 50
                    "entry_number": 1,
                    "strategy_name": strategy_name,
                    "side": leg.get('position_type', 'BUY'),
                    "entry_price": 100.0,
                    "exit_price": 90.0 if leg.get('position_type') == 'BUY' else 110.0,
                    "symbol": "NIFTY",
                    "strike": leg.get('strike_selection', {}).get('value', 23000),
                    "expiry": "250403",
                    "leg_id": str(leg.get('id', i+1)),
                    "index_entry_price": 23050,
                    "index_exit_price": 23100,
                    "reason": "EndTime",
                    "stop_loss_entry_number": 0,
                    "take_profit_entry_number": 0,
                    "strategy_entry_number": 1,
                    "pnl": 500.0 if leg.get('position_type') == 'SELL' else -500.0
                }
                mock_orders.append(mock_order)
        
        # If no orders created, add default ones
        if not mock_orders:
            mock_orders = [
                {
                    "entry_time": "Mon, 01 Apr 2025 09:16:00 GMT",
                    "exit_time": "Mon, 01 Apr 2025 12:00:00 GMT",
                    "option_type": "CALL",
                    "qty": 50,
                    "entry_number": 1,
                    "strategy_name": "Mock Strategy",
                    "side": "SELL",
                    "entry_price": 100.0,
                    "exit_price": 80.0,
                    "symbol": "NIFTY",
                    "strike": 23000,
                    "expiry": "250403",
                    "leg_id": "1",
                    "index_entry_price": 23050,
                    "index_exit_price": 23100,
                    "reason": "EndTime",
                    "stop_loss_entry_number": 0,
                    "take_profit_entry_number": 0,
                    "strategy_entry_number": 1,
                    "pnl": 1000.0
                },
                {
                    "entry_time": "Mon, 01 Apr 2025 09:16:00 GMT",
                    "exit_time": "Mon, 01 Apr 2025 12:00:00 GMT",
                    "option_type": "PUT",
                    "qty": 50,
                    "entry_number": 1,
                    "strategy_name": "Mock Strategy",
                    "side": "SELL",
                    "entry_price": 100.0,
                    "exit_price": 85.0,
                    "symbol": "NIFTY",
                    "strike": 23000,
                    "expiry": "250403",
                    "leg_id": "2",
                    "index_entry_price": 23050,
                    "index_exit_price": 23100,
                    "reason": "EndTime",
                    "stop_loss_entry_number": 0,
                    "take_profit_entry_number": 0,
                    "strategy_entry_number": 1,
                    "pnl": 750.0
                }
            ]
        
        respp = {
            "strategies": {
                "orders": mock_orders,
                "strategy_profits": {
                    "250401": {
                        "34560": 1750.0,  # 09:36:00 in seconds
                        "43200": 1750.0   # 12:00:00 in seconds
                    }
                },
                "strategy_losses": {
                    "250401": {}
                }
            }
        }
        
        # Add portfolio name to orders
        orderss = pd.DataFrame(respp['strategies']['orders'])
        orderss['portfolio_name'] = btPara['portfolio']['name']
        respp['strategies']['orders'] = orderss.to_dict("records")
        
        endTime = datetime.now()
        durationn = round((endTime-startTime).total_seconds(),2)
        logging.info(f"{endTime}, Completed mock backtesting portfolio: {btPara['portfolio']['id']}, Time taken: {durationn} seconds")
        
        return respp
'''
    
    # Read original Util.py
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Backup original
    backup_path = util_path + ".original"
    if not os.path.exists(backup_path):
        with open(backup_path, 'w') as f:
            f.write(content)
        print(f"  ✓ Backed up original Util.py")
    
    # Find and replace the getBacktestResults method
    import re
    
    # Pattern to match the entire method
    pattern = r'@staticmethod\s+def getBacktestResults\(btPara: dict\) -> dict:.*?(?=\n    @staticmethod|\n    [A-Z]|\Z)'
    
    # Replace with our mock version
    patched_content = re.sub(pattern, mock_getBacktestResults.strip(), content, flags=re.DOTALL)
    
    # Also patch getMarginFor to return dummy margin
    mock_getMarginFor = '''
    @staticmethod
    def getMarginFor(position: list) -> float:
        """PATCHED VERSION - Returns mock margin"""
        # Return a dummy margin of 100000 per position
        return len(position) * 100000.0
'''
    
    pattern2 = r'@staticmethod\s+def getMarginFor\(position: list\) -> float:.*?(?=\n    @staticmethod|\n    [A-Z]|\Z)'
    patched_content = re.sub(pattern2, mock_getMarginFor.strip(), patched_content, flags=re.DOTALL)
    
    # Write patched file
    with open(util_path, 'w') as f:
        f.write(patched_content)
    
    print("  ✓ Patched Util.py to use mock data")

def setup_and_run_legacy():
    """Setup and run the legacy backtester"""
    
    legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
    
    # Setup input files
    input_sheets_dir = os.path.join(legacy_dir, "INPUT SHEETS")
    os.makedirs(input_sheets_dir, exist_ok=True)
    
    # Copy input files
    source_file = os.path.abspath("bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx")
    dest_file = os.path.join(input_sheets_dir, "INPUT PORTFOLIO.xlsx")
    
    if os.path.exists(source_file):
        shutil.copy2(source_file, dest_file)
        print(f"  ✓ Copied portfolio file")
        
        # Copy strategy files
        source_dir = os.path.dirname(source_file)
        for f in os.listdir(source_dir):
            if f.endswith('.xlsx') and 'tbs' in f.lower():
                shutil.copy2(os.path.join(source_dir, f), os.path.join(input_sheets_dir, f))
    
    # Clear old outputs
    trades_dir = os.path.join(legacy_dir, "Trades")
    if os.path.exists(trades_dir):
        old_files = glob.glob(os.path.join(trades_dir, "*.xlsx"))
        for f in old_files:
            try:
                os.remove(f)
            except:
                pass
    
    print("\nRunning patched legacy backtester...")
    
    # Run legacy backtester
    env = os.environ.copy()
    env['PYTHONPATH'] = f"{os.path.abspath('bt/archive/backtester_stable')}:{os.path.abspath('.')}"
    
    cmd = [sys.executable, os.path.join(legacy_dir, "BTRunPortfolio.py")]
    
    try:
        result = subprocess.run(
            cmd,
            cwd=legacy_dir,
            env=env,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"  Return code: {result.returncode}")
        
        if result.returncode != 0 and result.stderr:
            print(f"  Errors: {result.stderr[:500]}")
        
        # Check for output
        if os.path.exists(trades_dir):
            output_files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')]
            if output_files:
                print(f"\n  ✓ Legacy backtester created {len(output_files)} output file(s):")
                for f in output_files:
                    print(f"    - {f}")
                    
                # Copy to comparison directory
                os.makedirs("comparison_outputs", exist_ok=True)
                latest = max([os.path.join(trades_dir, f) for f in output_files], key=os.path.getctime)
                dest = "comparison_outputs/legacy_output_patched.xlsx"
                shutil.copy2(latest, dest)
                print(f"\n  ✓ Copied output to: {dest}")
                return True
        
        print("  ✗ No output files created")
        return False
        
    except Exception as e:
        print(f"  ✗ Error: {e}")
        return False

def restore_original():
    """Restore original Util.py"""
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    backup_path = util_path + ".original"
    
    if os.path.exists(backup_path):
        shutil.copy2(backup_path, util_path)
        print("  ✓ Restored original Util.py")

def main():
    print("="*60)
    print("Patching Legacy Backtester")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    print("\nPatching Util.py to use mock data...")
    create_patched_util()
    
    print("\nSetting up and running legacy backtester...")
    success = setup_and_run_legacy()
    
    # Optionally restore original
    # restore_original()
    
    print("\n" + "="*60)
    if success:
        print("✓ Legacy backtester successfully patched and running!")
        print("  Output saved to: comparison_outputs/legacy_output_patched.xlsx")
        print("\nNote: The output uses mock data for demonstration.")
        print("To restore original: Run restore_original() function")
    else:
        print("✗ Failed to run patched legacy backtester")
    print("="*60)

if __name__ == "__main__":
    main() 