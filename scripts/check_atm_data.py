#!/usr/bin/env python3
"""Check ATM strike data from both systems."""

import mysql.connector

def check_mysql_data():
    """Check MySQL data availability."""
    
    conn = mysql.connector.connect(
        host='localhost',
        user='mahesh',
        password='mahesh_123',
        database='historicaldb'
    )
    cursor = conn.cursor()
    
    # Check cash data for 09:16
    cursor.execute("""
        SELECT date, time, close 
        FROM nifty_cash 
        WHERE date = '240103' AND time >= '091600' AND time < '091700'
        ORDER BY time
    """)
    
    print("Cash data for Jan 3, 2024 at 09:16:")
    print("Date    | Time   | Close")
    print("-" * 30)
    cash_data = cursor.fetchall()
    for date, time, close in cash_data:
        print(f"{date} | {time} | {close}")
    
    if cash_data:
        spot = cash_data[0][2]
        print(f"\nSpot price: {spot}")
        print(f"Simple ATM (round/50): {round(spot/50)*50}")
    
    # Check option data
    cursor.execute("""
        SELECT nc.strike
        FROM nifty_call nc
        INNER JOIN nifty_put np ON nc.strike = np.strike 
            AND nc.date = np.date AND nc.time = np.time
        WHERE nc.date = '240103' AND nc.time >= '091600' AND nc.time < '091700'
            AND nc.close IS NOT NULL AND np.close IS NOT NULL
        GROUP BY nc.strike
        ORDER BY nc.strike
    """)
    
    strikes = [row[0] for row in cursor.fetchall()]
    print(f"\nAvailable strikes with data: {strikes[:10]}...")
    
    conn.close()

if __name__ == "__main__":
    check_mysql_data() 