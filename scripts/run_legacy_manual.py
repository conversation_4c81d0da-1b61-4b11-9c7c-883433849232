#!/usr/bin/env python3
"""Run legacy backtester manually with proper configuration"""

import os
import shutil
import subprocess
from openpyxl import load_workbook

def run_legacy_test():
    # Step 1: Copy input files to legacy directory
    legacy_dir = "bt/archive/backtester_stable/BTRUN"
    
    # Copy TBS test files
    shutil.copy("test_data/tbs/input_portfolio_tbs_1day.xlsx", 
                f"{legacy_dir}/input_portfolio.xlsx")
    shutil.copy("test_data/tbs/input_tbs_multi_legs.xlsx", 
                f"{legacy_dir}/input_tbs_multi_legs.xlsx")
    
    print("Copied test files to legacy directory")
    
    # Step 2: Set environment variables
    os.environ['MYSQL_HOST'] = 'localhost'
    os.environ['DEBUG_MODE'] = 'true'
    
    # Step 3: Run legacy backtester
    os.chdir(legacy_dir)
    print(f"Changed to directory: {os.getcwd()}")
    print("Running legacy backtester...")
    
    result = subprocess.run(
        ["python3", "BTRunPortfolio.py"],
        capture_output=True,
        text=True
    )
    
    print("Legacy backtester stdout:")
    print(result.stdout)
    
    if result.stderr:
        print("Legacy backtester stderr:")
        print(result.stderr)
    
    # Check if output was created
    if os.path.exists("output.xlsx"):
        print("Success! Legacy output created: output.xlsx")
        # Copy to main directory for comparison
        shutil.copy("output.xlsx", "/srv/samba/shared/legacy_output.xlsx")
    else:
        print("No output.xlsx created by legacy backtester")
    
    return result.returncode == 0

if __name__ == "__main__":
    success = run_legacy_test()
    exit(0 if success else 1) 