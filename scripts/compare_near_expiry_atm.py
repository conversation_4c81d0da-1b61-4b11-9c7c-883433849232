#!/usr/bin/env python3
"""
Compare ATM calculations between MySQL and HeavyDB using near expiry (April 3)
"""

import mysql.connector
try:
    from pyheavydb import connect as heavydb_connect
except ImportError:
    from heavydb import connect as heavydb_connect

# Connection configs
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def get_mysql_atm_near_expiry(date, expiry='250403'):
    """Calculate ATM using MySQL with near expiry"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    time = 33360  # 9:16 AM
    
    # Get best ATM using synthetic future method
    query = """
    SELECT 
        c.strike,
        c.close as ce_close,
        p.close as pe_close,
        cash.close as spot_price,
        c.strike + (c.close/100) - (p.close/100) as syn_future,
        ABS(c.strike + (c.close/100) - (p.close/100) - (cash.close/100)) as syn_diff
    FROM nifty_call c
    JOIN nifty_put p ON c.date = p.date AND c.time = p.time 
                      AND c.strike = p.strike AND c.expiry = p.expiry
    JOIN nifty_cash cash ON c.date = cash.date AND c.time = cash.time
    WHERE c.date = %s AND c.time = %s AND c.expiry = %s
    AND c.close > 0 AND p.close > 0
    ORDER BY syn_diff
    LIMIT 1
    """
    
    cursor.execute(query, (date, time, expiry))
    result = cursor.fetchone()
    
    if result:
        strike, ce_close, pe_close, spot, syn_future, syn_diff = result
        spot_price = spot / 100
        
        cursor.close()
        conn.close()
        
        return {
            'atm_strike': strike,
            'spot_price': spot_price,
            'ce_price': ce_close / 100,
            'pe_price': pe_close / 100,
            'syn_future': float(syn_future),
            'syn_diff': float(syn_diff)
        }
    
    cursor.close()
    conn.close()
    return None

def get_heavydb_atm_and_expiry(date_str):
    """Get ATM from HeavyDB and check which expiry it's using"""
    conn = heavydb_connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    # Convert date format
    heavydb_date = f"20{date_str[:2]}-{date_str[2:4]}-{date_str[4:6]}"
    
    # Get ATM and expiry info
    query = f"""
    SELECT 
        spot,
        atm_strike,
        ce_close,
        pe_close,
        expiry_date,
        expiry_bucket
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}' 
    AND trade_time = '09:16:00'
    AND strike = atm_strike
    ORDER BY expiry_date
    LIMIT 1
    """
    
    result = conn.execute(query).fetchone()
    conn.close()
    
    if result:
        return {
            'spot_price': result[0],
            'atm_strike': result[1],
            'ce_price': result[2],
            'pe_price': result[3],
            'expiry_date': result[4],
            'expiry_bucket': result[5]
        }
    return None

def test_trades_with_atm(atm_strike, date, expiry):
    """Test if all 4 trades can execute with given ATM"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    entry_time = 33360  # 9:16 AM
    trades = [
        {'strike': atm_strike, 'option': 'CE'},
        {'strike': atm_strike, 'option': 'PE'},
        {'strike': atm_strike + 100, 'option': 'CE'},  # OTM2
        {'strike': atm_strike - 100, 'option': 'PE'}   # OTM2
    ]
    
    available_trades = 0
    missing_trades = []
    
    for trade in trades:
        if trade['option'] == 'CE':
            table = 'nifty_call'
        else:
            table = 'nifty_put'
        
        cursor.execute(f"""
            SELECT COUNT(*) FROM {table}
            WHERE date = %s AND time = %s 
            AND strike = %s AND expiry = %s
        """, (date, entry_time, trade['strike'], expiry))
        
        count = cursor.fetchone()[0]
        if count > 0:
            available_trades += 1
        else:
            missing_trades.append(f"{trade['strike']} {trade['option']}")
    
    cursor.close()
    conn.close()
    
    return available_trades, missing_trades

def main():
    print("MySQL vs HeavyDB ATM Comparison with Near Expiry")
    print("="*70)
    
    dates = [
        ('250401', '250403'),  # April 1 with April 3 expiry
        ('250402', '250403'),  # April 2 with April 3 expiry
        ('250403', '250403'),  # April 3 with April 3 expiry (expiry day)
    ]
    
    for mysql_date, near_expiry in dates:
        print(f"\n{'='*70}")
        print(f"Date: {mysql_date}")
        print(f"{'='*70}")
        
        # Get MySQL ATM with near expiry
        mysql_result = get_mysql_atm_near_expiry(mysql_date, near_expiry)
        
        # Get HeavyDB ATM
        heavydb_result = get_heavydb_atm_and_expiry(mysql_date)
        
        print("\nMySQL Results (Near Expiry: April 3):")
        if mysql_result:
            print(f"  Spot Price: {mysql_result['spot_price']:.2f}")
            print(f"  ATM Strike: {mysql_result['atm_strike']}")
            print(f"  CE Price: {mysql_result['ce_price']:.2f}, PE Price: {mysql_result['pe_price']:.2f}")
            print(f"  Synthetic Future: {mysql_result['syn_future']:.2f}")
            print(f"  Difference from Spot: {mysql_result['syn_diff']:.2f}")
            
            # Test trade availability
            trades_avail, missing = test_trades_with_atm(mysql_result['atm_strike'], mysql_date, near_expiry)
            print(f"  Trade Availability: {trades_avail}/4 trades")
            if missing:
                print(f"  Missing: {', '.join(missing)}")
        else:
            print("  No ATM found - insufficient data")
        
        print("\nHeavyDB Results:")
        if heavydb_result:
            print(f"  Spot Price: {heavydb_result['spot_price']:.2f}")
            print(f"  ATM Strike: {int(heavydb_result['atm_strike'])}")
            print(f"  CE Price: {heavydb_result['ce_price']:.2f}, PE Price: {heavydb_result['pe_price']:.2f}")
            print(f"  Expiry Date: {heavydb_result['expiry_date']}")
            print(f"  Expiry Bucket: {heavydb_result['expiry_bucket']}")
        
        if mysql_result and heavydb_result:
            print(f"\nDifference:")
            print(f"  ATM Strike Difference: {abs(mysql_result['atm_strike'] - int(heavydb_result['atm_strike']))} points")
            print(f"  MySQL using near expiry (April 3)")
            print(f"  HeavyDB using {heavydb_result['expiry_bucket']} expiry ({heavydb_result['expiry_date']})")
    
    print("\n" + "="*70)
    print("CONCLUSION:")
    print("1. MySQL has better data availability with near expiry (April 3)")
    print("2. HeavyDB appears to be using weekly expiry (April 9)")
    print("3. This explains the ATM calculation differences")
    print("4. The issue is expiry selection logic, not data quality alone")

if __name__ == "__main__":
    main() 