#!/usr/bin/env python3
"""Properly fix the input file dates to 2024 full year"""

import pandas as pd
import os
from openpyxl import load_workbook

def fix_input_dates_properly():
    """Fix the input file dates to proper 2024 range"""
    
    input_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_full_year.xlsx"
    
    print("🔧 Fixing Input File Dates to 2024")
    print("=" * 50)
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False
    
    try:
        # Load workbook with openpyxl to preserve formatting
        wb = load_workbook(input_file)
        
        # Check PortfolioSetting sheet
        if 'PortfolioSetting' in wb.sheetnames:
            ws = wb['PortfolioSetting']
            
            print("📅 Current PortfolioSetting content:")
            
            # Find and update date rows
            for row in ws.iter_rows():
                if len(row) >= 2 and row[0].value:
                    cell_value = str(row[0].value).strip()
                    
                    if 'StartDate' in cell_value:
                        old_value = row[1].value
                        row[1].value = '01_01_2024'  # January 1, 2024
                        print(f"   ✅ Updated StartDate: {old_value} → 01_01_2024")
                        
                    elif 'EndDate' in cell_value:
                        old_value = row[1].value
                        row[1].value = '31_12_2024'  # December 31, 2024
                        print(f"   ✅ Updated EndDate: {old_value} → 31_12_2024")
                        
                    elif 'Date' in cell_value:
                        print(f"   📋 {cell_value}: {row[1].value}")
            
            # Save the workbook
            wb.save(input_file)
            print(f"\n✅ Successfully updated {input_file}")
            
            # Verify the changes
            print("\n🔍 Verifying changes:")
            with pd.ExcelFile(input_file) as xls:
                portfolio_df = pd.read_excel(xls, sheet_name='PortfolioSetting')
                for idx, row in portfolio_df.iterrows():
                    if 'Date' in str(row.iloc[0]):
                        print(f"   {row.iloc[0]}: {row.iloc[1]}")
            
            return True
            
        else:
            print("❌ PortfolioSetting sheet not found")
            return False
            
    except Exception as e:
        print(f"❌ Error fixing input file: {e}")
        return False

def create_clean_2024_input():
    """Create a completely clean 2024 input file"""
    
    source_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_120000.xlsx"
    target_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_clean.xlsx"
    
    print(f"\n🔧 Creating Clean 2024 Input File")
    print("=" * 50)
    
    if not os.path.exists(source_file):
        print(f"❌ Source file not found: {source_file}")
        return None
    
    try:
        # Load source workbook
        wb = load_workbook(source_file)
        
        # Update PortfolioSetting sheet
        if 'PortfolioSetting' in wb.sheetnames:
            ws = wb['PortfolioSetting']
            
            # Find and update date rows
            for row in ws.iter_rows():
                if len(row) >= 2 and row[0].value:
                    cell_value = str(row[0].value).strip()
                    
                    if 'StartDate' in cell_value:
                        row[1].value = '01_01_2024'  # January 1, 2024
                        print(f"   ✅ Set StartDate: 01_01_2024")
                        
                    elif 'EndDate' in cell_value:
                        row[1].value = '31_12_2024'  # December 31, 2024
                        print(f"   ✅ Set EndDate: 31_12_2024")
            
            # Save as new file
            wb.save(target_file)
            print(f"\n✅ Created clean input file: {target_file}")
            return target_file
            
        else:
            print("❌ PortfolioSetting sheet not found")
            return None
            
    except Exception as e:
        print(f"❌ Error creating clean input file: {e}")
        return None

if __name__ == "__main__":
    # First try to fix existing file
    if fix_input_dates_properly():
        print("\n🎯 Input file fixed successfully!")
    else:
        print("\n⚠️ Failed to fix existing file, creating new one...")
        clean_file = create_clean_2024_input()
        if clean_file:
            print(f"\n🎯 Use this clean file: {clean_file}")
        else:
            print("\n❌ Failed to create clean file")
    
    print(f"\n🚀 Run command with corrected file:")
    print(f"python3 -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU \\")
    print(f"  --legacy-excel \\")
    print(f"  --portfolio-excel bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_clean.xlsx \\")
    print(f"  --output-path bt/backtester_stable/BTRUN/output/tbs_2024_full_year_final.xlsx \\")
    print(f"  --start-date 20240101 --end-date 20241231 \\")
    print(f"  --workers auto") 