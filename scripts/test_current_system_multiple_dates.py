#!/usr/bin/env python3
"""Test current system across multiple dates with performance monitoring."""

import sys
import os
import subprocess
import pandas as pd
from datetime import datetime, timedelta
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

sys.path.append('/srv/samba/shared')

def run_backtest_for_date(args):
    """Run backtest for a single date (used by process pool)."""
    portfolio_excel, start_date, end_date = args
    
    output_file = f"test_{start_date.strftime('%Y%m%d')}.xlsx"
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", portfolio_excel,
        "--output-path", output_file,
        "--start-date", start_date.strftime("%Y%m%d"),
        "--end-date", end_date.strftime("%Y%m%d")
    ]
    
    env = {
        "PYTHONPATH": "/srv/samba/shared",
        "USE_LEGACY_FORMAT": "1"
    }
    
    try:
        start_time = time.time()
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd="/srv/samba/shared",
            env={**os.environ, **env},
            timeout=300  # 5 minute timeout
        )
        execution_time = time.time() - start_time
        
        if result.returncode == 0 and os.path.exists(output_file):
            # Read the output
            df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
            
            # Clean up
            os.remove(output_file)
            
            return {
                'date': start_date.strftime('%Y-%m-%d'),
                'success': True,
                'trades': len(df),
                'total_pnl': df['pnl'].sum() if 'pnl' in df.columns else 0,
                'execution_time': execution_time,
                'legs': df['leg_id'].tolist() if 'leg_id' in df.columns else [],
                'strikes': df['strike'].unique().tolist() if 'strike' in df.columns else []
            }
        else:
            return {
                'date': start_date.strftime('%Y-%m-%d'),
                'success': False,
                'error': result.stderr[-500:] if result.stderr else 'Unknown error',
                'execution_time': execution_time
            }
            
    except subprocess.TimeoutExpired:
        return {
            'date': start_date.strftime('%Y-%m-%d'),
            'success': False,
            'error': 'Timeout after 5 minutes',
            'execution_time': 300
        }
    except Exception as e:
        return {
            'date': start_date.strftime('%Y-%m-%d'),
            'success': False,
            'error': str(e),
            'execution_time': 0
        }

def main():
    """Run tests across multiple dates."""
    print("=== CURRENT SYSTEM MULTI-DATE TEST ===")
    print(f"Start Time: {datetime.now()}")
    
    portfolio_excel = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    
    # Generate test dates - focus on actual trading days
    test_dates = []
    
    # Test specific dates in April 2025
    # Note: Avoid weekends and holidays
    trading_days = [
        datetime(2025, 4, 1),   # Tuesday
        datetime(2025, 4, 2),   # Wednesday
        datetime(2025, 4, 3),   # Thursday
        datetime(2025, 4, 4),   # Friday
        datetime(2025, 4, 7),   # Monday
        datetime(2025, 4, 8),   # Tuesday
        datetime(2025, 4, 9),   # Wednesday
        datetime(2025, 4, 10),  # Thursday
        datetime(2025, 4, 11),  # Friday
    ]
    
    # Create test arguments
    test_args = [(portfolio_excel, date, date) for date in trading_days]
    
    # Run tests in parallel
    max_workers = min(4, multiprocessing.cpu_count())
    print(f"\nRunning {len(test_args)} tests with {max_workers} workers...\n")
    
    results = []
    start_time = time.time()
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_date = {
            executor.submit(run_backtest_for_date, args): args[1]
            for args in test_args
        }
        
        # Process results as they complete
        for future in as_completed(future_to_date):
            date = future_to_date[future]
            try:
                result = future.result()
                results.append(result)
                
                if result['success']:
                    print(f"✓ {result['date']}: {result['trades']} trades, P&L: {result['total_pnl']:.2f}, Time: {result['execution_time']:.1f}s")
                else:
                    print(f"✗ {result['date']}: Failed - {result['error'][:100]}")
                    
            except Exception as e:
                print(f"✗ {date.strftime('%Y-%m-%d')}: Exception - {str(e)}")
    
    total_time = time.time() - start_time
    
    # Generate summary
    print("\n" + "="*60)
    print("SUMMARY REPORT")
    print("="*60)
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"\nTotal Tests: {len(results)}")
    print(f"Successful: {len(successful)}")
    print(f"Failed: {len(failed)}")
    print(f"Success Rate: {len(successful)/len(results)*100:.1f}%")
    
    if successful:
        print(f"\nPerformance Statistics:")
        print(f"  Average execution time: {sum(r['execution_time'] for r in successful)/len(successful):.2f}s")
        print(f"  Min execution time: {min(r['execution_time'] for r in successful):.2f}s")
        print(f"  Max execution time: {max(r['execution_time'] for r in successful):.2f}s")
        
        # Check consistency
        print(f"\nConsistency Check:")
        trade_counts = [r['trades'] for r in successful]
        if len(set(trade_counts)) == 1:
            print(f"  ✓ All tests generated {trade_counts[0]} trades consistently")
        else:
            print(f"  ⚠️ Trade count varies: {set(trade_counts)}")
        
        # Check P&L distribution
        pnls = [r['total_pnl'] for r in successful]
        print(f"\nP&L Distribution:")
        print(f"  Min P&L: {min(pnls):.2f}")
        print(f"  Max P&L: {max(pnls):.2f}")
        print(f"  Average P&L: {sum(pnls)/len(pnls):.2f}")
        
        # Check leg consistency
        all_legs = [tuple(r['legs']) for r in successful]
        if len(set(all_legs)) == 1:
            print(f"\n✓ All tests have consistent leg structure:")
            print(f"  Legs: {successful[0]['legs']}")
        else:
            print(f"\n⚠️ Leg structure varies across tests")
    
    print(f"\nTotal execution time: {total_time:.2f}s")
    print(f"Average time per test: {total_time/len(results):.2f}s")
    
    # Save detailed results
    results_df = pd.DataFrame(results)
    results_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    results_df.to_csv(results_file, index=False)
    print(f"\nDetailed results saved to: {results_file}")

if __name__ == "__main__":
    main() 