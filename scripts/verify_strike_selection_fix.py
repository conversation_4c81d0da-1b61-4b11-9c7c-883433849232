#!/usr/bin/env python3
"""
Verify that the strike selection fix works for both otm2 and atm with value=2 by 
generating and printing the SQL for both cases.
"""

import os
import sys
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

# Import required modules
from bt.backtester_stable.BTRUN.models.leg import LegModel
from bt.backtester_stable.BTRUN.models.common import OptionType, StrikeRule, ExpiryRule, TransactionType
from bt.backtester_stable.BTRUN.query_builder.entry_exit_sql import _build_strike_condition, build_entry_sql


def verify_strike_selection():
    """Verify that strike selection works for both methods."""
    print("="*80)
    print("Strike Selection Fix Verification")
    print(f"Time: {datetime.now()}")
    print("="*80)
    
    trade_date = "2025-04-01"
    alias = "oc"
    
    # Base leg model for tests
    base_leg = LegModel(
        leg_id="1",
        index="NIFTY",
        option_type=OptionType.CALL,
        transaction=TransactionType.BUY,
        lots=1,
        strike_rule=StrikeRule.ATM,
        expiry_rule=ExpiryRule.CURRENT_WEEK,
        fixed_strike=None,
        entry_time="091600",
        exit_time="153000",
        extra_params={}
    )
    
    print("\n1. OTM2 Strike Selection:")
    print("-" * 40)
    
    # Create a leg with OTM strike rule and distance=2
    otm_leg = base_leg.model_copy(deep=True)
    otm_leg.strike_rule = StrikeRule.OTM
    otm_leg.strike_distance = 2
    
    # Get the strike condition for OTM2
    otm_condition = _build_strike_condition(otm_leg, alias)
    print(f"OTM2 Strike Condition: {otm_condition}")
    
    # Get the entry SQL for OTM2
    otm_sql = build_entry_sql(otm_leg, trade_date, "09:16:00", alias)
    print("\nOTM2 Entry SQL:")
    print(otm_sql)
    
    print("\n2. ATM with value=2 Strike Selection:")
    print("-" * 40)
    
    # Create a leg with ATM strike rule and value=2
    atm_leg = base_leg.model_copy(deep=True)
    atm_leg.strike_rule = StrikeRule.ATM
    atm_leg.strike_value = 2
    
    # Get the strike condition for ATM with value=2
    atm_condition = _build_strike_condition(atm_leg, alias)
    print(f"ATM with value=2 Strike Condition: {atm_condition}")
    
    # Get the entry SQL for ATM with value=2
    atm_sql = build_entry_sql(atm_leg, trade_date, "09:16:00", alias)
    print("\nATM with value=2 Entry SQL:")
    print(atm_sql)
    
    print("\n3. ATM with StrikeValue=2 in extra_params:")
    print("-" * 40)
    
    # Create a leg with ATM strike rule and StrikeValue=2 in extra_params
    extra_leg = base_leg.model_copy(deep=True)
    extra_leg.strike_rule = StrikeRule.ATM
    extra_leg.strike_value = None
    extra_leg.extra_params = {"StrikeValue": "2"}
    
    # Get the strike condition for ATM with StrikeValue=2 in extra_params
    extra_condition = _build_strike_condition(extra_leg, alias)
    print(f"ATM with StrikeValue=2 in extra_params Strike Condition: {extra_condition}")
    
    # Get the entry SQL for ATM with StrikeValue=2 in extra_params
    extra_sql = build_entry_sql(extra_leg, trade_date, "09:16:00", alias)
    print("\nATM with StrikeValue=2 in extra_params Entry SQL:")
    print(extra_sql)
    
    # Check if all strike conditions are equivalent
    if f"{alias}.atm_strike + (2" in atm_condition and f"{alias}.atm_strike + (2" in extra_condition:
        print("\n✅ Fix confirmed! Both methods generate the same strike condition.")
    else:
        print("\n❌ Fix not working. Strike conditions differ.")
    
    
if __name__ == "__main__":
    verify_strike_selection() 