#!/usr/bin/env python3
"""
Test TV spot price integration with nifty_spot table
"""

from heavydb import connect
from datetime import date, time, datetime
import sys

# HeavyDB connection details
HOST = "127.0.0.1"
PORT = 6274
USER = "admin"
PASSWORD = "HyperInteractive"
DATABASE = "heavyai"

def main():
    print("Testing TV Spot Price Integration with nifty_spot table\n")
    print("=" * 60)
    
    # Connect to HeavyDB
    try:
        conn = connect(
            host=HOST,
            port=PORT,
            user=USER,
            password=PASSWORD,
            dbname=DATABASE
        )
        cursor = conn.cursor()
    except Exception as e:
        print(f"Error connecting to HeavyDB: {e}")
        sys.exit(1)
    
    # Test 1: Get baseline spot price
    print("\n1. Testing baseline spot price retrieval:")
    test_date = date(2022, 1, 3)  # Using a date we know has data
    test_time = time(9, 16, 0)
    
    baseline_query = f"""
    SELECT "close" as spot
    FROM nifty_spot
    WHERE trade_date = DATE '{test_date}'
        AND trade_time = TIME '{test_time}'
    LIMIT 1
    """
    
    print(f"   Query: {baseline_query.strip()}")
    
    try:
        cursor.execute(baseline_query)
        result = cursor.fetchone()
        if result:
            baseline_price = result[0]
            print(f"   ✅ Baseline spot price at {test_time}: {baseline_price}")
        else:
            print(f"   ❌ No data found for {test_date} at {test_time}")
            cursor.close()
            conn.close()
            return
    except Exception as e:
        print(f"   ❌ Error: {e}")
        cursor.close()
        conn.close()
        return
    
    # Test 2: Test exit timing for LONG trade
    print("\n2. Testing exit timing for LONG trade (spot moves down):")
    target_price = baseline_price - 10  # Looking for 10 points down
    
    long_exit_query = f"""
    SELECT trade_time, low as exit_price
    FROM nifty_spot
    WHERE trade_date = DATE '{test_date}'
        AND trade_time BETWEEN TIME '09:16:00' AND TIME '09:20:00'
        AND low <= {target_price}
    ORDER BY trade_time ASC
    LIMIT 1
    """
    
    print(f"   Target price: {target_price} (baseline: {baseline_price})")
    print(f"   Query: {long_exit_query.strip()}")
    
    try:
        cursor.execute(long_exit_query)
        result = cursor.fetchone()
        if result:
            exit_time, exit_price = result
            print(f"   ✅ Exit found at {exit_time} with low price {exit_price}")
        else:
            print(f"   ℹ️ No exit found (price didn't reach {target_price})")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Test exit timing for SHORT trade
    print("\n3. Testing exit timing for SHORT trade (spot moves up):")
    target_price = baseline_price + 10  # Looking for 10 points up
    
    short_exit_query = f"""
    SELECT trade_time, high as exit_price
    FROM nifty_spot
    WHERE trade_date = DATE '{test_date}'
        AND trade_time BETWEEN TIME '09:16:00' AND TIME '09:20:00'
        AND high >= {target_price}
    ORDER BY trade_time ASC
    LIMIT 1
    """
    
    print(f"   Target price: {target_price} (baseline: {baseline_price})")
    print(f"   Query: {short_exit_query.strip()}")
    
    try:
        cursor.execute(short_exit_query)
        result = cursor.fetchone()
        if result:
            exit_time, exit_price = result
            print(f"   ✅ Exit found at {exit_time} with high price {exit_price}")
        else:
            print(f"   ℹ️ No exit found (price didn't reach {target_price})")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Check data availability
    print("\n4. Checking data availability in nifty_spot:")
    
    availability_query = f"""
    SELECT 
        COUNT(*) as total_records,
        MIN(trade_date) as start_date,
        MAX(trade_date) as end_date,
        COUNT(DISTINCT trade_date) as trading_days
    FROM nifty_spot
    """
    
    try:
        cursor.execute(availability_query)
        result = cursor.fetchone()
        if result:
            total, start, end, days = result
            print(f"   Total records: {total:,}")
            print(f"   Date range: {start} to {end}")
            print(f"   Trading days: {days}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 5: Sample OHLC data
    print("\n5. Sample OHLC data from nifty_spot:")
    
    sample_query = f"""
    SELECT trade_time, "open", high, low, "close"
    FROM nifty_spot
    WHERE trade_date = DATE '{test_date}'
        AND trade_time BETWEEN TIME '09:15:00' AND TIME '09:20:00'
    ORDER BY trade_time
    LIMIT 5
    """
    
    try:
        cursor.execute(sample_query)
        results = cursor.fetchall()
        print(f"   Time     | Open      | High      | Low       | Close")
        print(f"   ---------|-----------|-----------|-----------|----------")
        for row in results:
            t, o, h, l, c = row
            print(f"   {t} | {o:9.2f} | {h:9.2f} | {l:9.2f} | {c:9.2f}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Close connection
    cursor.close()
    conn.close()
    
    print("\n" + "=" * 60)
    print("Test completed!")

if __name__ == "__main__":
    main() 