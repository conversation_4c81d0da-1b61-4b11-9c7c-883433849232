# Practical Workflow for Legacy vs New Backtester Comparison

## Overview
You have:
- Windows Server: ************ (RDP port 33898) with legacy backtester
- Linux Server: Current location with new HeavyDB backtester
- MySQL Database: Accessible from both at ************:3306

## Method 1: Port Forwarding (Quickest)

### Step 1: Enable Port Forwarding on Windows
```bash
# 1. RDP to Windows server
xfreerdp /u:MahaInvest /p:'Mar<PERSON>@123' /v:************:33898

# Or use Remmina/other RDP client
```

### Step 2: On Windows (as Administrator)
```cmd
# Add port forwarding rule
netsh interface portproxy add v4tov4 listenport=5000 listenaddress=************ connectport=5000 connectaddress=***************

# Open firewall
netsh advfirewall firewall add rule name="Legacy Backtest" dir=in action=allow protocol=TCP localport=5000

# Verify
netsh interface portproxy show all
```

### Step 3: Update Linux Scripts
```python
# In run_parallel_test.py, change:
LEGACY_SERVICE_URL = "http://************:5000"  # Now accessible!
```

### Step 4: Run Comparison
```bash
cd /srv/samba/shared
python scripts/run_parallel_test.py
```

## Method 2: Shared MySQL Results (Most Reliable)

### Step 1: Create Results Table
```sql
-- Run this on MySQL at ************
CREATE TABLE IF NOT EXISTS historicaldb.backtest_comparison (
    id INT AUTO_INCREMENT PRIMARY KEY,
    test_id VARCHAR(50),
    system_type ENUM('legacy', 'new'),
    portfolio_name VARCHAR(100),
    start_date DATE,
    end_date DATE,
    input_params JSON,
    output_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_test_id (test_id)
);
```

### Step 2: Modify Legacy Backtester (on Windows)
Add this to legacy backtester to save results:
```python
import mysql.connector
import json

def save_to_mysql(results, test_id):
    conn = mysql.connector.connect(
        host="************",
        user="mahesh",
        password="mahesh_123",
        database="historicaldb"
    )
    cursor = conn.cursor()
    
    cursor.execute("""
        INSERT INTO backtest_comparison 
        (test_id, system_type, portfolio_name, start_date, end_date, output_data)
        VALUES (%s, %s, %s, %s, %s, %s)
    """, (test_id, 'legacy', portfolio_name, start_date, end_date, json.dumps(results)))
    
    conn.commit()
    cursor.close()
    conn.close()
```

### Step 3: Modify New Backtester (on Linux)
Similarly save new results to MySQL with system_type='new'

### Step 4: Compare Results
```bash
python scripts/mysql_comparison.py --test-id "test_20250412_001"
```

## Method 3: Direct File Transfer

### Option A: Using Python Script
```python
# On Windows, after running legacy backtest:
import paramiko

sftp = paramiko.SFTPClient.from_transport(
    paramiko.Transport(("linux_server_ip", 22))
)
sftp.put("legacy_output.xlsx", "/srv/samba/shared/legacy_output.xlsx")
```

### Option B: Using Shared Folder
```bash
# On Linux, mount Windows share
sudo mount -t cifs //************/backtest_output /mnt/windows \
  -o user=MahaInvest,password=Maruth@123,port=445
```

## Recommended Approach

1. **Try Method 1 (Port Forwarding) first** - It's the quickest and allows you to use existing scripts
2. **If firewall/network issues, use Method 2 (MySQL)** - Most reliable, works across any network
3. **Method 3 is fallback** if you just need to transfer output files

## Quick Test Commands

```bash
# Test if port forwarding works (after setup)
curl http://************:5000/healthcheck

# Test MySQL connection
mysql -h ************ -u mahesh -pmahesh_123 historicaldb -e "SELECT 1"

# Run new backtester only
python scripts/test_without_legacy_service.py
```

## Troubleshooting

1. **Port forwarding not working?**
   - Check Windows Firewall
   - Verify the legacy service is running on Windows
   - Try telnet ************ 5000 from Linux

2. **MySQL connection issues?**
   - Verify credentials
   - Check if MySQL allows remote connections
   - Try from MySQL Workbench first

3. **RDP issues?**
   - Use correct port 33898
   - Try different RDP clients (xfreerdp, remmina, rdesktop) 