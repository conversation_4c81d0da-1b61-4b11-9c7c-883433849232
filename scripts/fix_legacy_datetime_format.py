#!/usr/bin/env python3
"""Fix datetime format parsing issue in legacy Util.py."""

def fix_datetime_parsing():
    """Fix the datetime parsing in parseBacktestingResponse."""
    
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    
    # Read the file
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Find and replace the datetime parsing section
    # Original code that tries only one format
    old_code = '''for columnName in ["entry", "exit"]:

            # Try to parse datetime with multiple formats
            try:
                # First try the Flask service format
                orderdf[f'{columnName}_datetime'] = pd.to_datetime(orderdf[f'{columnName}_datetime'], format="%a, %d %b %Y %H:%M:%S GMT")
            except:
                # If that fails, try the LocalBacktestEngine format (already a datetime string)
                orderdf[f'{columnName}_datetime'] = pd.to_datetime(orderdf[f'{columnName}_datetime'])'''
    
    # New code that handles both formats properly
    new_code = '''for columnName in ["entry", "exit"]:

            # Try to parse datetime with multiple formats
            try:
                # First try the Flask service format
                orderdf[f'{columnName}_datetime'] = pd.to_datetime(orderdf[f'{columnName}_datetime'], format="%a, %d %b %Y %H:%M:%S GMT")
            except:
                try:
                    # If that fails, try the LocalBacktestEngine format (already a datetime string)
                    orderdf[f'{columnName}_datetime'] = pd.to_datetime(orderdf[f'{columnName}_datetime'])
                except:
                    # Last resort - let pandas infer the format
                    orderdf[f'{columnName}_datetime'] = pd.to_datetime(orderdf[f'{columnName}_datetime'], infer_datetime_format=True)'''
    
    # Replace the code
    content = content.replace(old_code, new_code)
    
    # Write back
    with open(util_path, 'w') as f:
        f.write(content)
    
    print("✅ Fixed datetime parsing issue in Util.py")

if __name__ == "__main__":
    fix_datetime_parsing()
    print("\nYou can now run the legacy backtester again") 