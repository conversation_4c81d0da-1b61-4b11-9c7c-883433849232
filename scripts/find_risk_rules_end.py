#!/usr/bin/env python3
"""Find where the risk rules if block ends."""

with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

# Find the risk rules if statement
risk_rules_line = None
for i, line in enumerate(lines):
    if 'if leg.risk_rules and not entry_df.empty' in line:
        risk_rules_line = i
        risk_indent = len(line) - len(line.lstrip())
        print(f'Risk rules if statement at line {i+1} with {risk_indent} spaces indentation')
        break

if risk_rules_line is None:
    print("Could not find risk rules if statement!")
    exit(1)

# Find where it ends
print('\nLooking for where this if block ends...')
for i in range(risk_rules_line + 1, min(risk_rules_line + 200, len(lines))):
    line = lines[i]
    indent = len(line) - len(line.lstrip())
    stripped = line.strip()
    
    if stripped and indent <= risk_indent:
        print(f'\nRisk rules block ends at line {i+1} with {indent} spaces')
        print(f'Content: {stripped[:80]}...')
        
        # Check what's after it
        print('\nNext few lines:')
        for j in range(i, min(i+10, len(lines))):
            next_line = lines[j]
            next_indent = len(next_line) - len(next_line.lstrip())
            next_stripped = next_line.strip()
            if next_stripped:
                print(f'Line {j+1} ({next_indent} spaces): {next_stripped[:60]}...')
        break 