#!/usr/bin/env python3
"""
Test the new backtester with debug logging
"""

import os
import sys
import subprocess
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

def run_with_debug():
    """Run the new backtester with verbose logging"""
    print("="*60)
    print("Running New Backtester with Debug Logging")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Set up environment
    env = os.environ.copy()
    env['PYTHONPATH'] = os.path.abspath('.')
    
    # Add debug logging
    env['LOG_LEVEL'] = 'DEBUG'
    
    cmd = [
        sys.executable,
        "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "--output-path", "comparison_outputs/new_gpu_debug.xlsx",
        "--start-date", "20241231",
        "--end-date", "20241231",
        "--verbose"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    print("\nRunning...")
    
    result = subprocess.run(
        cmd,
        env=env,
        capture_output=True,
        text=True,
        timeout=180
    )
    
    print(f"\nReturn code: {result.returncode}")
    
    if result.stdout:
        print("\nSTDOUT:")
        print("="*40)
        print(result.stdout[-2000:])  # Last 2000 chars
        
    if result.stderr:
        print("\nSTDERR:")
        print("="*40)
        print(result.stderr[-2000:])  # Last 2000 chars
        
    # Check output
    if os.path.exists("comparison_outputs/new_gpu_debug.xlsx"):
        print("\n✓ Output file created")
        
        # Check the output
        import pandas as pd
        xl = pd.ExcelFile("comparison_outputs/new_gpu_debug.xlsx")
        print(f"  Sheets: {xl.sheet_names}")
        
        # Find transaction sheet
        for sheet in xl.sheet_names:
            if 'Trans' in sheet:
                df = pd.read_excel(xl, sheet_name=sheet)
                print(f"\n  {sheet}:")
                print(f"    Rows: {len(df)}")
                if len(df) > 0:
                    print(f"    Columns: {list(df.columns)[:10]}...")
    else:
        print("\n✗ No output file created")

def analyze_strike_config():
    """Analyze the strike configuration issue"""
    print("\n" + "="*60)
    print("Strike Configuration Analysis")
    print("="*60)
    
    import pandas as pd
    
    # Read the strategy config
    strategy_file = "bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"
    leg_df = pd.read_excel(strategy_file, sheet_name='LegParameter')
    
    print("\nLeg Configuration Issues:")
    print("  Legs 3 & 4 have StrikeMethod='atm' with StrikeValue=2")
    print("  This should probably be StrikeMethod='otm2' with StrikeValue=0")
    
    print("\nCorrected configuration should be:")
    for i, row in leg_df.iterrows():
        strike_method = row['StrikeMethod']
        strike_value = row['StrikeValue']
        
        # Suggest correction
        if i >= 2 and strike_method == 'atm' and strike_value == 2:
            strike_method = 'otm2'
            strike_value = 0
            
        print(f"  Leg {i+1}: {row['Instrument'].upper()} {row['Transaction'].upper()} {strike_method.upper()}")

if __name__ == "__main__":
    run_with_debug()
    analyze_strike_config() 