#!/usr/bin/env python3
"""Script to analyze and fix case sensitivity issues in strategy names."""

import os
import sys
import pandas as pd

# Add project paths
sys.path.append('/srv/samba/shared')

def check_strategy_names():
    """Check how strategy names are being parsed from input files."""
    
    # Load input files to check original names
    portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    strategy_file = "bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"
    
    print("="*80)
    print("CHECKING STRATEGY NAME FORMATS")
    print("="*80)
    
    # Check portfolio file
    if os.path.exists(portfolio_file):
        print(f"\nPortfolio file: {portfolio_file}")
        df = pd.read_excel(portfolio_file, sheet_name="StrategySetting")
        print("Strategy settings:")
        print(df[['PortfolioName', 'StrategyType', 'StrategyExcelFilePath']])
    
    # Check strategy file
    if os.path.exists(strategy_file):
        print(f"\n\nStrategy file: {strategy_file}")
        
        # Check GeneralParameter sheet
        try:
            df = pd.read_excel(strategy_file, sheet_name="GeneralParameter")
            if 'StrategyName' in df.columns:
                print("\nStrategy names in GeneralParameter:")
                for name in df['StrategyName']:
                    print(f"  Original: '{name}'")
                    # Show what it would look like with different cases
                    print(f"  Upper: '{name.upper()}'")
                    print(f"  Title: '{name.title()}'")
                    print(f"  Current split: '{name.split(',')}'")
                    print()
        except Exception as e:
            print(f"Error reading GeneralParameter: {e}")
        
        # Check LegParameter sheet
        try:
            df = pd.read_excel(strategy_file, sheet_name="LegParameter")
            if 'StrategyName' in df.columns:
                print("\nStrategy names in LegParameter:")
                unique_names = df['StrategyName'].unique()
                for name in unique_names:
                    print(f"  - {name}")
        except Exception as e:
            print(f"Error reading LegParameter: {e}")

def analyze_output_differences():
    """Analyze the output differences between systems."""
    
    print("\n\n" + "="*80)
    print("ANALYZING OUTPUT DIFFERENCES")
    print("="*80)
    
    legacy_file = "tests/outputs/gpu_parity_run/NIF0DTE_250401_cpu.xlsx"
    heavydb_file = "heavydb_comprehensive_output.xlsx"
    
    if os.path.exists(legacy_file) and os.path.exists(heavydb_file):
        # Compare PORTFOLIO Results specifically
        print("\nPORTFOLIO Results comparison:")
        
        leg_df = pd.read_excel(legacy_file, sheet_name="PORTFOLIO Results")
        hdb_df = pd.read_excel(heavydb_file, sheet_name="PORTFOLIO Results")
        
        print(f"\nLegacy PORTFOLIO Results shape: {leg_df.shape}")
        print(f"Legacy columns: {list(leg_df.columns)}")
        print("\nFirst 5 rows:")
        print(leg_df.head())
        
        print(f"\n\nHeavyDB PORTFOLIO Results shape: {hdb_df.shape}")
        print(f"HeavyDB columns: {list(hdb_df.columns)}")
        print("\nAll rows:")
        print(hdb_df)
        
        # Compare metrics
        print("\n\nMetrics comparison:")
        leg_metrics = pd.read_excel(legacy_file, sheet_name="Metrics")
        hdb_metrics = pd.read_excel(heavydb_file, sheet_name="Metrics")
        
        print(f"\nLegacy Metrics shape: {leg_metrics.shape}")
        print(f"HeavyDB Metrics shape: {hdb_metrics.shape}")
        
        # Check if HeavyDB has duplicate entries
        if 'Strategy' in hdb_metrics.columns:
            print(f"\nHeavyDB Strategy values: {hdb_metrics['Strategy'].unique()}")
            print(f"Legacy Strategy values: {leg_metrics['Strategy'].unique()}")

def main():
    """Main execution."""
    check_strategy_names()
    analyze_output_differences()
    
    print("\n\n" + "="*80)
    print("RECOMMENDATIONS")
    print("="*80)
    print("\n1. The strategy name case differences come from the input Excel files")
    print("2. Legacy PORTFOLIO Results has a completely different structure (7 rows, 14 cols)")
    print("   vs HeavyDB's simple 2-row summary")
    print("3. Metrics sheet in HeavyDB has duplicate entries (50 rows vs 25 in legacy)")
    print("\nTo fix:")
    print("- Update PORTFOLIO Results generation to match legacy format")
    print("- Fix duplicate metrics entries")
    print("- Consider normalizing strategy names to uppercase for consistency")

if __name__ == "__main__":
    main() 