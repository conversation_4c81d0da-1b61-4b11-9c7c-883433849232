# Enterprise Integration Plan – HeavyDB GPU Back-Tester

_Last updated: 2025-05-13_

> **Note**: The standalone "## Plan Update: 2025-05-12" section appearing later in this document is now *deprecated* and kept only for historical reference. The canonical, up-to-date tasks and checklists are the ones listed above in section 3. A future cleanup will excise the obsolete block once all merge requests referencing it are closed.

This document expands on the high-level roadmap recorded in progress.md and activeContext.md.  It provides an execution-grade work-breakdown that engineering, QA, and DevOps can follow to deliver the fully-featured HeavyDB + GPU portfolio back-tester.

---
## 0  Scope & Objectives
1. Replace the legacy REST engine with a local HeavyDB/GPU pipeline while preserving functional parity (Excel inputs → metrics & trade output).
2. Maintain backward compatibility with existing Excel templates (`input_portfolio.xlsx`, `input_tbs_multi_legs.xlsx`).
3. Produce deterministic, auditable trade generation from HeavyDB option-chain views for all supported indices (NIFTY, BANKNIFTY, FINNIFTY …).
4. Enable extensibility (new indices, new strike rules, alternative data sources) through clean architecture and typed models.
5. Meet enterprise NFRs: code readability, unit-test coverage ≥ 80 %, logging/observability, GPU/CPU fallback.

---
## 1  Architecture Overview
```
┌──────────────┐  Excel  ┌───────────────┐   models   ┌──────────────────┐   SQL   ┌────────────┐
│  input_xlsx  ├────────►│ excel_parser  ├───────────►│  models.*        ├───────►│ query_sql  ├─────► HeavyDB
└──────────────┘          └───────────────┘            └──────────────────┘         └────────────┘
     ▲                                                              │                        │
     │                                      TradeRecords            │                        │
     │                                      (DataFrame)             ▼                        ▼
┌──────────────┐                                        ┌──────────────────┐      ┌────────────────┐
│  runtime/    │◄─────────────  builders  ──────────────┤ gpu_helpers      │◄────┤  Output (xls,  │
│ portfolio_   │                                         └──────────────────┘      │  json, charts) │
│ runner.py    │                                                                   └────────────────┘
└──────────────┘
```

Key packages:
* **models**      Typed pydantic representations of portfolio, strategy, leg, risk rules.
* **excel_parser** Transforms the four Excel sheets into models.
* **query_builder** Converts a `LegModel` + trade-date into HeavyDB SQL (entry & exit).
* **runtime**      Orchestrates date iteration, executes queries, feeds results into existing stats/export stack.

---
## 2  Work-Breakdown Structure

| Phase | Owner | Duration | Deliverables |
|-------|-------|----------|--------------|
|0  Model Scaffolding|Backend|0.5 day|`models/` package, unit tests, CI lint pass|
|1  Excel Parser & Input Processing Overhaul|Backend|0.5 day|`excel_parser/` with full column coverage, fixtures test|
|2a Query Resolver|Backend|✅ Done|`query_builder/resolver.py` implemented (TableMapper, ExpiryPredicate, StrikeOrderClause)|
|2b Leg SQL Builder|Backend|✅ Done|`query_builder/leg_sql.py` + `strategy_sql.py` generate CTE SQL for legs & full strategy; smoke-tested|
|3  HeavyDB Integration|Backend|✅ Done|Entry/Exit snapshot SQL, `trade_builder` assembler, writers hardened|
|4  Risk Rules Engine|Backend|1 day|Apply SL/TP/Trail intraday using tick scans (post-MVP)|
|5  Perf Optimisation|Backend|🚧 In-progress|UNION-ALL batch SQL & connection pooling|
|6  Parallel Engine|Backend|0.5 day|GPU workers, date-slice scheduling, CPU fallback|
|7  QA & Docs|QA/Docs|1 day|Test matrix, README, example notebooks|

*Total MVP ETA*: **3 development days** (Phases 0-3).

---
## 3  Detailed Tasks
### Phase 0 – Model Scaffolding
1. `models.common` – enums `ExpiryRule`, `StrikeRule`, etc.
2. `models.time_window` – `EntryWindow`, `ExitWindow` (validate HHMMSS).
3. `models.risk` – `RiskRule` + nested validation.
4. `models.leg`, `models.strategy`, `models.portfolio`.
5. pytest covering happy-path & edge-cases.

### Phase 0 – Model Scaffolding (Super-micro checklist)
* [ ] Create package `models/` with `__init__.py` exporting public classes.
* [ ] Implement `models.common` with Enum stubs (`StrikeRule`, `ExpiryRule`, `OptionType`, `TransactionType`). Write pytest verifying Enum values.
* [ ] Implement `models.time_window.EntryWindow` & `ExitWindow` (pydantic) – validate   HHMMSS string ↔ int seconds conversion; add tests for edge cases (00:00:00, 23:59:59).
* [ ] Implement `models.risk.RiskRule` dataclass with union types for SL / TP / Trail; unit-test JSON schema round-trip.
* [ ] Implement `models.leg.LegModel` (fields: id, index, option_type, transaction, quantity, strike_rule, expiry_rule, entry_window, exit_window, risk) – 100 % mypy coverage.
* [ ] Implement `models.strategy.StrategyModel` (id, name, list[LegModel], meta flags).
* [ ] Implement `models.portfolio.PortfolioModel` (name, list[StrategyModel], risk, meta).
* [ ] CI job `pytest -q models` passes; coverage ≥ 85 %.

### Phase 1 – Excel Parser & Input Processing Overhaul
*Goal: Replicate archive's comprehensive Excel input parsing logic to build a rich `bt_params` request object suitable for the HeavyDB-native pipeline. This involves correctly interpreting all columns from `PortfolioSetting`, `StrategySetting`, strategy-specific `GeneralParameter` & `LegParameter` sheets, and `lotsize.csv`.*

1.  **Modify `BTRunPortfolio_GPU.load_legacy_portfolio_config()`**:
    *   Reads `PortfolioSetting` and `StrategySetting` from `input_portfolio.xlsx`.
    *   Removes direct loading of `input_tbs_multi_legs.xlsx`; strategy-specific files are loaded based on `StrategySetting.StrategyExcelFilePath`.

2.  **Modify `BTRunPortfolio_GPU._row_build_request()`**:
    *   Orchestrates parsing: filters `StrategySetting` for the current portfolio.
    *   For each strategy row:
        *   Dynamically resolves `StrategyExcelFilePath` (from `config.INPUT_FILE_FOLDER` or absolute).
        *   Extracts `strategy_type`, `portfolio_multiplier`, `is_tick_bt`.
        *   Calls `Util.getStategyJson()` to parse the specific strategy Excel file.
    *   Populates `request['portfolio']['strategies']` with the fully parsed strategy objects.

3.  **Call `Util.runNeccesaryFunctionsBeforeStartingBT()`**:
    *   Ensure this is invoked at the start of `_row_run_backtest` in `BTRunPortfolio_GPU.py` to load `lotsize.csv` into `config.LOT_SIZE` and `Util.MARGIN_INFO`.

4.  **Verify and Enhance `Util.getStategyJson()` and its callees (`getBackendStrategyJson`, `getLegJson`, `getBackendLegJson`)**:
    *   **Full Archive Logic Replication:** Ensure these functions meticulously replicate the archive `Util.py` logic for *every column* in `GeneralParameter` and `LegParameter` (as per user-provided image schemas and archive code).
    *   **Portfolio Multiplier:** Apply `portfolio_row["Multiplier"]` to all relevant monetary fields in `GeneralParameter` and to `Lots` in `LegParameter` *before* final quantity calculation.
    *   **Quantity Calculation:** `quantity = config.LOT_SIZE[index_name] * int(LegParameter_Lots_after_multiplier)`.
    *   **Rule Transformation:** Accurately convert all Excel string values for strike selection (`StrikeMethod`, `StrikeValue`, etc.), expiry rules (`Expiry`), SL/TGT parameters, re-entry conditions (`SL_ReEntryType`, `TGT_ReEntryType`, etc.), Wait & Trade rules (`W&Type`, `W&TValue`), hedging (`OpenHedge`, `HedgeStrikeMethod`), and `OnEntry`/`OnExit` event handlers into structured semantic parameters within the generated leg/strategy JSON. These parameters are for consumption by `heavydb_helpers.LegModel`.
    *   **Indicator Logic:** Correctly construct `entry_indicators` and `exit_indicators` dictionaries in `getBackendStrategyJson` based on `Consider...ForEntry/Exit` flags and associated indicator parameter columns (`EMAPeriod`, `RsiPeriod`, `STPeriod`, etc.) from `GeneralParameter`.

5.  **Refine `Util.getStrikeValueAndMethod()`**:
    *   This function (called by `getBackendLegJson`) parses Excel strike rules (`StrikeMethod`, `StrikeValue`, `StrikePremiumCondition`, `MatchPremium`).
    *   It must produce structured *semantic rules* and parameters (e.g., `rule_name_for_legmodel: "ATM"`, `rule_params: {{}}`; or `rule_name_for_legmodel: "DELTA_TARGET"`, `rule_params: {{ 'delta_value': 0.7 }}`).
    *   These structured rules will be stored in the leg's JSON representation and later used by `heavydb_helpers.LegModel` and the `query_builder` for HeavyDB-native strike selection.

### Phase 1 – Excel Parser & Input Processing (Super-micro checklist)
*Parser core*
* [ ] New package `excel_parser/` with `__init__.py`.
* [ ] Implement `excel_parser.portfolio.load_portfolio_settings(path) -> pd.DataFrame` – strict column existence, dtype coercion.
* [ ] Implement `excel_parser.portfolio.load_strategy_settings(path) -> pd.DataFrame`.
* [ ] Implement `excel_parser.strategy.load_general_parameter(path) -> pd.DataFrame` and `.load_leg_parameter(path)`.

*Mapping logic*
* [ ] Column-to-field mapping table in `excel_parser.mapping.py`; pytest verifies every column in the 4 sheets appears exactly once.
* [ ] Function `excel_parser.to_models.build_leg(df_row, gen_row, lot_size_dict) -> LegModel`.
* [ ] Function `excel_parser.to_models.build_strategy(general_df, leg_df) -> StrategyModel`.
* [ ] Function `excel_parser.to_models.build_portfolio(port_df_row, strat_df, _general_cache) -> PortfolioModel`.

*Integration with runner*
* [ ] Replace direct Util.getStategyJson call in `_row_build_request` with `excel_parser.to_models.build_strategy` output until full migration ready.
* [ ] Add compatibility adapter `models_to_legacy(min_portfolio: PortfolioModel) -> bt_params JSON` so HeavyDB helper continues to work during transition.

*Data fixtures*
* [ ] Create `tests/fixtures/` with trimmed copies of the four Excel sheets (10 rows each) + `lotsize.csv`.
* [ ] Parametrised pytest ensuring parser can round-trip every sheet without raising.

*DAL presence check*
* [ ] Add assert in test-env that `import dal` succeeds; if not, fail CI with helpful message.

### Phase 2 – Query Builder
*2a resolver.py*
• `TableMapper.resolve(index) -> table_name`
• `ExpiryResolver.resolve(rule, trade_date, index) -> SQL predicate`
• `StrikeResolver.resolve(rule, df_alias) -> SQL predicate (moneyness/premium)`

*2b leg_sql.py*
• `build_entry_sql(leg_model, date)` – SELECT first tick ≥ EntryWindow.start.
• `build_exit_sql(leg_model, date)`  – SELECT last  tick ≤ ExitWindow.end.
• Utilises HeavyDB `QUALIFY` & window fns. (Note: `leg_model` here is the `LegModel` instance).

### Phase 2 – Query Builder (Super-micro checklist)
*2a resolver.py*
* [ ] `TableMapper` – map index → option_chain view; pytest with NIFTY/BANKNIFTY.
* [ ] `ExpiryResolver.resolve(rule, trade_date, index)` – unit-test CURRENT, NEXT, MONTHLY.
* [ ] `StrikeResolver.resolve(rule, df_alias)` – test ATM/OTM±n predicates.

*2b leg_sql.py / strategy_sql.py*
* [ ] Implement `build_entry_sql(leg_model, date)` – earliest snapshot ≥ entry_time.
* [ ] Implement `build_exit_sql(leg_model, date)`  – latest snapshot ≤ exit_time.
* [ ] Helper `_select_chain_columns(leg_model)` – selects CE/PE columns only.
* [ ] pytest using HeavyDB docker stub; validate generated SQL contains expected WHERE + QUALIFY.

### Phase 3 – HeavyDB Integration
1. Update `heavydb_helpers.get_trades_for_portfolio`:
   *   Input `bt_params` will now contain strategies with fully parsed `legs` (from Phase 1), where each `leg_dict` includes detailed semantic rules for strike, expiry, etc., derived from Excel.
   *   When creating `LegModel` instances:
        *   Extract strike selection method (e.g., "ATM", "CLOSEST_PREMIUM", "DELTA_TARGET"), expiry rule (e.g., "WEEKLY", "MONTHLY", "DTE_TARGET"), and associated parameters (target premium, strike value, DTE value, conditions) directly *from each `leg_dict`*.
        *   Map these parsed semantic string rules/values to the `StrikeRule` (enum), `ExpiryRule` (enum) and correctly populate the `LegModel` fields (e.g., `leg_model.strike_rule_enum`, `leg_model.strike_param_value`, `leg_model.expiry_rule_enum`, `leg_model.dte_target`).
   *   The `query_builder` modules (e.g., `entry_exit_sql.py`, `strategy_sql.py`) will then use these fully populated `LegModel` properties to generate precise HeavyDB SQL for fetching entry/exit snapshots.
2. Convert result to GPU (if enabled) then return schema identical to legacy engine.
3. Validate with builders.parse_backtest_response end-to-end.

### Phase 3 – HeavyDB Integration (Super-micro checklist)
* [ ] Modify `heavydb_helpers.get_trades_for_portfolio` to consume `PortfolioModel` (or legacy-json adapter) directly.
* [ ] `LegFactory.from_dict(leg_dict) -> LegModel` with full enum mapping; unit-test bad inputs raise.
* [ ] Integrate `query_builder` into `_build_union_sql` – remove placeholder strike/expiry logic.
* [ ] `trade_builder.build_trade_record` – ensure slippage & taxes params forwarded; pytest golden row.
* [ ] End-to-end smoke: one portfolio, one day, four legs; assert ≥4 TradeRecords returned.

### Phase 4 – Risk Rules Engine
1. During date loop collect intra-day tick dataframe for each leg.
2. Evaluate SL/TP/Trails at check_frequency (Strategy.general.checking_interval).
3. Generate early exit TradeRecord where rule triggers.

### Phase 4 – Risk Rules Engine (Super-micro checklist)
* [ ] Design `risk.evaluate(trade_ticks_df, risk_rule) -> exit_price/time`.
* [ ] Implement tick-scan util `ticks.slice_between(entry, exit)` using HeavyDB minute table.
* [ ] Implement SL rule, TP rule, Trail rule separately with unit tests.
* [ ] Integrate into runtime loop; if risk triggers, modify TradeRecord exit fields.
* [ ] Performance test: 10 k legs × 390 ticks/day executes < 2 s on GPU.

### Phase 5 – Performance Optimisation
1. Build UNION-ALL query grouping legs by (index, date), leveraging the detailed `LegModel` information for common rule batching. Attach `leg_id` column.
2. Split evaluation of strike predicates into CASE blocks within the SQL, or use dynamic filtering based on `LegModel` parameters.
3. Benchmarks vs SIMPLE mode, switch automatically when legs×days > threshold.

### Phase 5 – Performance Optimisation (Super-micro checklist)
* [ ] Batch UNION-ALL generator groups 100 legs per SQL call; benchmark vs single.
* [ ] Add `config.HEAVYDB_QUERY_BATCH` env var; unit test value respected.
* [ ] Implement HeavyDB connection pool (size = workers×2) in `heavydb_helpers` with contextmanager.
* [ ] GPU memory telemetry: `gpu_helpers.log_usage()` every 60 s.
* [ ] Load-test: 90 days × 50 k legs < 30 min wall-clock on A100.

### Phase 6 – Parallel Execution Engine
1. Multiprocessing pool (`--workers N`, default 8 for A100-40GB).
2. Worker receives a slice of `bt_params` (which is now correctly structured and fully parsed by the main process due to Phase 1 improvements).
3. Worker lifecycle: obtain pooled HeavyDB conn → execute batched SQL (using `heavydb_helpers` which consumes the rich `LegModel`) → build TradeRecords on GPU → Parquet chunk output.
4. Retry path: worker slice rerun on CPU when GPU OOM or cuDF error.
5. GPU memory watchdog logs utilisation every 60 s; triggers graceful drain when free < 1 GB.

### Phase 6 – Parallel Execution Engine (Super-micro checklist)
* [ ] Refactor `_backtest_worker` to accept `PortfolioModel` and date slice tuple.
* [ ] Implement slicer `scheduler.split_dates(trade_dates, max_workers)`.
* [ ] Shared read-only `heavydb_helpers.ConnectionPool` injected via multiprocessing `initializer`.
* [ ] Worker graceful shutdown writes partial parquet to `/tmp/bt_chunks/`.
* [ ] Parent merges parquet → final DataFrame; pytest that merge is order-independent.

### Phase 7 – QA & Docs
1. Test matrix, README, example notebooks.

### Phase 7 – QA & Docs (Super-micro checklist)
* [ ] Generate pytest parameter grid covering 3 indices × 4 strike methods × SL vs no-SL.
* [ ] Write golden-file comparator `tests/parity/compare_archive_vs_heavydb.py` (tolerance 0.5 %).
* [ ] GitHub Actions workflow matrix (py3.10 + HeavyDB docker) – run unit, lint, mypy.
* [ ] Sphinx docs build on CI; includes architecture diagram & API docs.
* [ ] Final README with quick-start, performance benchmark table, FAQ.

---
## 4  Naming & Coding Standards
* PEP-8 with Black auto-format.
* Public functions + classes include doctring, Google style.
* Logging via stdlib `logging`, module-level loggers.
* Exceptions use custom hierarchy `BTRunError`, `DataValidationError`, etc.
* SQL text built with `jinja2` templates for readability.

---
## 5  Testing Strategy
* pytest marker `heavydb_required` – skipped if DB not reachable.
* Fixtures: mini option-chain parquet for unit tests.
* Golden-file tests comparing outputs vs known legacy results.
* CI pipeline (GitHub Actions) runs unit + style + mypy.

---
## 6  Deployment & Ops
* Env vars: `BT_HEAVYDB_BATCH`, `BT_USE_GPU`, `BT_LOG_LEVEL`.
* Connection pool in `heavydb_helpers` with contextmanager.
* Alembic-style migration script seeds LOT_SIZE, MARGIN_INFO fixtures.
* README → architecture diagram + quick-start (docker-compose with HeavyDB).

---
## 7  Plan Update: 2025-05-12

This section details an expanded and refined plan based on deeper analysis of archive code and input Excel structures. It supersedes previous implied task details for relevant phases but maintains overall phase goals.

### Phase 0 – Model Scaffolding (No Change to Original Plan)
1. `models.common` – enums `ExpiryRule`, `StrikeRule`, etc.
2. `models.time_window` – `EntryWindow`, `ExitWindow` (validate HHMMSS).
3. `models.risk` – `RiskRule` + nested validation.
4. `models.leg`, `models.strategy`, `models.portfolio`.
5. pytest covering happy-path & edge-cases.

### Phase 1 – Excel Parser & Input Processing Overhaul (Refined)
*Goal: Replicate archive's comprehensive Excel input parsing logic to build a rich `bt_params` request object suitable for the HeavyDB-native pipeline. This involves correctly interpreting all columns from `PortfolioSetting`, `StrategySetting`, strategy-specific `GeneralParameter` & `LegParameter` sheets, and `lotsize.csv`.*

1.  **Modify `BTRunPortfolio_GPU.load_legacy_portfolio_config()`**:
    *   Reads `PortfolioSetting` and `StrategySetting` from `input_portfolio.xlsx`.
    *   Removes direct loading of `input_tbs_multi_legs.xlsx`; strategy-specific files are loaded based on `StrategySetting.StrategyExcelFilePath`.

2.  **Modify `BTRunPortfolio_GPU._row_build_request()`**:
    *   Orchestrates parsing: filters `StrategySetting` for the current portfolio.
    *   For each strategy row:
        *   Dynamically resolves `StrategyExcelFilePath` (from `config.INPUT_FILE_FOLDER` or absolute).
        *   Extracts `strategy_type`, `portfolio_multiplier`, `is_tick_bt`.
        *   Calls `Util.getStategyJson()` to parse the specific strategy Excel file.
    *   Populates `request['portfolio']['strategies']` with the fully parsed strategy objects.

3.  **Call `Util.runNeccesaryFunctionsBeforeStartingBT()`**:
    *   Ensure this is invoked at the start of `_row_run_backtest` in `BTRunPortfolio_GPU.py` to load `lotsize.csv` into `config.LOT_SIZE` and `Util.MARGIN_INFO`.

4.  **Verify and Enhance `Util.getStategyJson()` and its callees (`getBackendStrategyJson`, `getLegJson`, `getBackendLegJson`)**:
    *   **Full Archive Logic Replication:** Ensure these functions meticulously replicate the archive `Util.py` logic for *every column* in `GeneralParameter` and `LegParameter` (as per user-provided image schemas and archive code).
    *   **Portfolio Multiplier:** Apply `portfolio_row["Multiplier"]` to all relevant monetary fields in `GeneralParameter` and to `Lots` in `LegParameter` *before* final quantity calculation.
    *   **Quantity Calculation:** `quantity = config.LOT_SIZE[index_name] * int(LegParameter_Lots_after_multiplier)`.
    *   **Rule Transformation:** Accurately convert all Excel string values for strike selection (`StrikeMethod`, `StrikeValue`, etc.), expiry rules (`Expiry`), SL/TGT parameters, re-entry conditions (`SL_ReEntryType`, `TGT_ReEntryType`, etc.), Wait & Trade rules (`W&Type`, `W&TValue`), hedging (`OpenHedge`, `HedgeStrikeMethod`), and `OnEntry`/`OnExit` event handlers into structured semantic parameters within the generated leg/strategy JSON. These parameters are for consumption by `heavydb_helpers.LegModel`.
    *   **Indicator Logic:** Correctly construct `entry_indicators` and `exit_indicators` dictionaries in `getBackendStrategyJson` based on `Consider...ForEntry/Exit` flags and associated indicator parameter columns (`EMAPeriod`, `RsiPeriod`, `STPeriod`, etc.) from `GeneralParameter`.

5.  **Refine `Util.getStrikeValueAndMethod()`**:
    *   This function (called by `getBackendLegJson`) parses Excel strike rules (`StrikeMethod`, `StrikeValue`, `StrikePremiumCondition`, `MatchPremium`).
    *   It must produce structured *semantic rules* and parameters (e.g., `rule_name_for_legmodel: "ATM"`, `rule_params: {{}}`; or `rule_name_for_legmodel: "DELTA_TARGET"`, `rule_params: {{ 'delta_value': 0.7 }}`).
    *   These structured rules will be stored in the leg's JSON representation and later used by `heavydb_helpers.LegModel` and the `query_builder` for HeavyDB-native strike selection.

### Phase 2 – Query Builder (No Change to Original Plan for 2a, 2b)
*2a resolver.py*
• `TableMapper.resolve(index) -> table_name`
• `ExpiryResolver.resolve(rule, trade_date, index) -> SQL predicate`
• `StrikeResolver.resolve(rule, df_alias) -> SQL predicate (moneyness/premium)`

*2b leg_sql.py*
• `build_entry_sql(leg_model, date)` – SELECT first tick ≥ EntryWindow.start.
• `build_exit_sql(leg_model, date)`  – SELECT last  tick ≤ ExitWindow.end.
• Utilises HeavyDB `QUALIFY` & window fns. (Note: `leg_model` here is the `LegModel` instance).

### Phase 3 – HeavyDB Integration (Refined)
*Goal: Adapt `heavydb_helpers` to consume the richly populated `bt_params` (from Phase 1) and use the `query_builder` to fetch data from HeavyDB.*

1.  **Modify `heavydb_helpers.get_trades_for_portfolio()`**:
    *   The input `bt_params['portfolio']['strategies'][n]['legs']` will now be a list of rich `leg_dict` objects containing detailed semantic rules and parameters from Phase 1 parsing (e.g., `leg_dict['parsed_strike_rule'] = {{ 'method': 'DELTA', 'value': 0.5 }}`, `leg_dict['parsed_expiry_rule'] = {{ 'type': 'WEEKLY' }}`).
    *   When creating `LegModel` instances:
        *   Extract these structured semantic rules and parameters from each `leg_dict`.
        *   Map these to the correct `StrikeRule` enum, `ExpiryRule` enum, and populate corresponding fields in the `LegModel` instance (e.g., `leg_model.strike_rule = StrikeRule.DELTA_TARGET`, `leg_model.strike_params.delta = 0.5`, `leg_model.expiry_rule = ExpiryRule.CURRENT_WEEK`).
    *   The `query_builder` functions (e.g., `build_entry_sql`, `build_exit_sql` called by `_build_union_sql` or `get_leg_entry_exit_rows`) will take these populated `LegModel` instances as input and use their properties to generate the precise HeavyDB SQL queries.

2.  **Data Fetching & TradeRecord Assembly (Existing Logic Review):**
    *   Ensure `_build_union_sql` correctly generates batch SQL using the `LegModel` instances.
    *   Verify `execute_query` fetches data.
    *   Ensure `build_trade_record` correctly assembles `TradeRecord` objects from the fetched HeavyDB entry/exit rows and the `LegModel`.

3.  **Output Schema:** Ensure the final DataFrame of `TradeRecord` objects returned by `get_trades_for_portfolio` matches the schema expected by the existing `builders.parse_backtest_response` and downstream output writers.

### Phase 4 – Risk Rules Engine (No Change to Original Plan)
1. During date loop collect intra-day tick dataframe for each leg.
2. Evaluate SL/TP/Trails at check_frequency (Strategy.general.checking_interval).
3. Generate early exit TradeRecord where rule triggers.

### Phase 5 – Performance Optimisation (Refined)
*Goal: Optimize data fetching from HeavyDB and processing, especially for large backtests.*

1.  **Batch SQL (`_build_union_sql`):**
    *   Continue to use and refine UNION-ALL queries grouping legs by common characteristics (e.g., index, date) extractable from `LegModel`.
    *   The `query_builder` should generate efficient WHERE clauses based on `LegModel` strike/expiry parameters.
2.  **Connection Pooling:** Implement or verify robust connection pooling in `heavydb_helpers` for parallel workers in Phase 6.
3.  **GPU DataFrame Usage:** Maximize usage of cuDF DataFrames throughout the `heavydb_helpers` and `trade_builder` pipeline where beneficial.

### Phase 6 – Parallel Execution Engine (Refined)
*Goal: Enable parallel backtesting of portfolios or date slices using multiple workers.*

1.  **`BTRunPortfolio_GPU._row_run_backtest` & `_backtest_worker`**:
    *   The main process will perform the full Excel parsing (Phase 1) once to generate the complete `bt_params` for each portfolio.
    *   For parallel execution (e.g., by date slicing or strategy distribution if implemented), the `_backtest_worker` will receive its slice of work, including the relevant, fully parsed part of `bt_params`.
    *   The worker calls `heavydb_helpers.get_trades_for_portfolio` with its assigned `bt_params` slice.
2.  **Worker Lifecycle (No Change to Original Plan):** 
    *   Obtain pooled HeavyDB conn → execute batched SQL (via `heavydb_helpers`) → build TradeRecords on GPU → Parquet chunk output (if using chunking for large results).
3.  **Result Aggregation (No Change to Original Plan):** Parent process merges results from workers.
4.  **CPU Fallback (No Change to Original Plan):** Retry path for worker slice on CPU if GPU errors occur.

### Phase 7 – QA & Docs (No Change to Original Plan)
1. Test matrix, README, example notebooks. 