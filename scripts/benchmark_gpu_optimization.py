#!/usr/bin/env python3
"""
Comprehensive GPU Optimization Benchmark Script
Tests various worker pool and GPU optimization configurations
"""

import sys
import os
import time
import json
import argparse
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
import matplotlib.pyplot as plt
import pandas as pd
from typing import Dict, List, Tuple

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class BenchmarkRunner:
    """Run benchmarks with various GPU configurations"""
    
    def __init__(self, strategy: str, start_date: str, end_date: str):
        self.strategy = strategy
        self.start_date = start_date
        self.end_date = end_date
        self.results = []
        self.legacy_time = None
        
        # Define benchmark configurations
        self.configs = {
            'legacy': {
                'name': 'Legacy (MySQL)',
                'command': self._get_legacy_command,
                'workers': 1,
                'gpu': False
            },
            'sequential': {
                'name': 'Sequential (No GPU)',
                'command': self._get_gpu_command,
                'workers': 1,
                'gpu': False,
                'gpu_optimize': False
            },
            'gpu_hints': {
                'name': 'GPU Hints Only',
                'command': self._get_gpu_command,
                'workers': 1,
                'gpu': True,
                'gpu_optimize': True
            },
            'workers_2': {
                'name': '2 Workers + GPU',
                'command': self._get_gpu_command,
                'workers': 2,
                'gpu': True,
                'gpu_optimize': True
            },
            'workers_4': {
                'name': '4 Workers + GPU',
                'command': self._get_gpu_command,
                'workers': 4,
                'gpu': True,
                'gpu_optimize': True
            },
            'workers_8': {
                'name': '8 Workers + GPU',
                'command': self._get_gpu_command,
                'workers': 8,
                'gpu': True,
                'gpu_optimize': True
            },
            'max_opt': {
                'name': 'Max Optimization',
                'command': self._get_gpu_command,
                'workers': 'auto',
                'gpu': True,
                'gpu_optimize': True,
                'gpu_mem_threshold': 0.8
            }
        }
    
    def _get_legacy_command(self, config: Dict) -> List[str]:
        """Get command for legacy backtester"""
        # Need to update the Excel file dates first
        return [
            'python',
            'BTRunPortfolio.py'
        ]
    
    def _get_gpu_command(self, config: Dict) -> List[str]:
        """Get command for GPU backtester"""
        cmd = [
            'python', '-m',
            'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
            '--legacy-excel',
            '--portfolio-excel', f'test_data/{self.strategy.lower()}/input_portfolio_{self.strategy.lower()}_30day.xlsx',
            '--output-path', f'output/benchmark_{self.strategy}_{config.get("name", "test").replace(" ", "_")}.xlsx',
            '--start-date', self.start_date,
            '--end-date', self.end_date,
            '--workers', str(config['workers'])
        ]
        
        if config.get('gpu_optimize'):
            cmd.append('--gpu-optimize')
        
        if 'gpu_mem_threshold' in config:
            cmd.extend(['--gpu-mem-threshold', str(config['gpu_mem_threshold'])])
        
        return cmd
    
    def run_benchmark(self, config_name: str) -> Dict:
        """Run a single benchmark configuration"""
        config = self.configs[config_name]
        print(f"\n{'='*60}")
        print(f"Running: {config['name']}")
        print(f"Workers: {config['workers']}, GPU: {config.get('gpu', False)}")
        print(f"{'='*60}")
        
        # Get command
        cmd = config['command'](config)
        
        # Set working directory
        if config_name == 'legacy':
            cwd = str(project_root / 'bt' / 'archive' / 'backtester_stable' / 'BTRUN')
            # Set MySQL to use localhost
            env = os.environ.copy()
            env['MYSQL_HOST'] = 'localhost'
        else:
            cwd = str(project_root)
            env = os.environ.copy()
        
        # Run benchmark
        start_time = time.time()
        
        try:
            # Run the command
            result = subprocess.run(
                cmd,
                cwd=cwd,
                env=env,
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✓ Completed in {execution_time:.2f} seconds")
                
                # Parse output for additional metrics if available
                metrics = self._parse_output(result.stdout, result.stderr)
                
                return {
                    'config': config_name,
                    'name': config['name'],
                    'workers': config['workers'],
                    'gpu': config.get('gpu', False),
                    'execution_time': execution_time,
                    'success': True,
                    'metrics': metrics
                }
            else:
                print(f"✗ Failed with return code {result.returncode}")
                print(f"Error: {result.stderr}")
                return {
                    'config': config_name,
                    'name': config['name'],
                    'workers': config['workers'],
                    'gpu': config.get('gpu', False),
                    'execution_time': execution_time,
                    'success': False,
                    'error': result.stderr
                }
                
        except subprocess.TimeoutExpired:
            print(f"✗ Timeout after 3600 seconds")
            return {
                'config': config_name,
                'name': config['name'],
                'workers': config['workers'],
                'gpu': config.get('gpu', False),
                'execution_time': 3600,
                'success': False,
                'error': 'Timeout'
            }
        except Exception as e:
            print(f"✗ Error: {str(e)}")
            return {
                'config': config_name,
                'name': config['name'],
                'workers': config['workers'],
                'gpu': config.get('gpu', False),
                'execution_time': time.time() - start_time,
                'success': False,
                'error': str(e)
            }
    
    def _parse_output(self, stdout: str, stderr: str) -> Dict:
        """Parse output for additional metrics"""
        metrics = {}
        
        # Try to extract GPU memory usage, query count, etc.
        for line in stdout.split('\n'):
            if 'GPU Memory:' in line:
                try:
                    metrics['gpu_memory_mb'] = float(line.split(':')[1].strip().split()[0])
                except:
                    pass
            elif 'Total Queries:' in line:
                try:
                    metrics['total_queries'] = int(line.split(':')[1].strip())
                except:
                    pass
            elif 'Trades Generated:' in line:
                try:
                    metrics['trades_generated'] = int(line.split(':')[1].strip())
                except:
                    pass
        
        return metrics
    
    def run_all_benchmarks(self, configs: List[str]) -> None:
        """Run all specified benchmark configurations"""
        for config in configs:
            if config in self.configs:
                result = self.run_benchmark(config)
                self.results.append(result)
                
                # Save legacy time for speedup calculations
                if config == 'legacy' and result['success']:
                    self.legacy_time = result['execution_time']
    
    def generate_report(self, output_file: str) -> None:
        """Generate HTML report with charts"""
        # Calculate speedups
        if self.legacy_time:
            for result in self.results:
                if result['success'] and result['config'] != 'legacy':
                    result['speedup'] = self.legacy_time / result['execution_time']
                else:
                    result['speedup'] = 1.0
        
        # Create visualizations
        self._create_charts()
        
        # Generate HTML report
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>GPU Optimization Benchmark Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                th {{ background-color: #4CAF50; color: white; }}
                tr:nth-child(even) {{ background-color: #f2f2f2; }}
                .success {{ color: green; }}
                .failure {{ color: red; }}
                img {{ max-width: 100%; height: auto; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <h1>GPU Optimization Benchmark Report</h1>
            <p>Strategy: {self.strategy}</p>
            <p>Date Range: {self.start_date} to {self.end_date}</p>
            <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <h2>Summary Results</h2>
            <table>
                <tr>
                    <th>Configuration</th>
                    <th>Workers</th>
                    <th>GPU</th>
                    <th>Execution Time (s)</th>
                    <th>Speedup</th>
                    <th>Status</th>
                </tr>
        """
        
        for result in self.results:
            status_class = 'success' if result['success'] else 'failure'
            status_text = '✓ Success' if result['success'] else '✗ Failed'
            speedup = f"{result.get('speedup', 1.0):.2f}x" if result['success'] else 'N/A'
            
            html += f"""
                <tr>
                    <td>{result['name']}</td>
                    <td>{result['workers']}</td>
                    <td>{'Yes' if result['gpu'] else 'No'}</td>
                    <td>{result['execution_time']:.2f}</td>
                    <td>{speedup}</td>
                    <td class="{status_class}">{status_text}</td>
                </tr>
            """
        
        html += """
            </table>
            
            <h2>Performance Charts</h2>
            <img src="benchmark_execution_time.png" alt="Execution Time Comparison">
            <img src="benchmark_speedup.png" alt="Speedup Comparison">
            
            <h2>Detailed Results</h2>
            <pre>
        """
        
        # Add detailed JSON results
        html += json.dumps(self.results, indent=2)
        
        html += """
            </pre>
        </body>
        </html>
        """
        
        # Save report
        with open(output_file, 'w') as f:
            f.write(html)
        
        print(f"\n✓ Report generated: {output_file}")
    
    def _create_charts(self) -> None:
        """Create performance charts"""
        # Filter successful results
        successful_results = [r for r in self.results if r['success']]
        
        if not successful_results:
            return
        
        # Execution time chart
        plt.figure(figsize=(10, 6))
        names = [r['name'] for r in successful_results]
        times = [r['execution_time'] for r in successful_results]
        
        bars = plt.bar(names, times)
        
        # Color bars based on GPU usage
        for i, result in enumerate(successful_results):
            if result['gpu']:
                bars[i].set_color('green')
            else:
                bars[i].set_color('blue')
        
        plt.xlabel('Configuration')
        plt.ylabel('Execution Time (seconds)')
        plt.title(f'Execution Time Comparison - {self.strategy}')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()
        plt.savefig('benchmark_execution_time.png')
        plt.close()
        
        # Speedup chart
        if self.legacy_time:
            plt.figure(figsize=(10, 6))
            speedups = [r.get('speedup', 1.0) for r in successful_results]
            
            bars = plt.bar(names, speedups)
            
            # Color bars
            for i, result in enumerate(successful_results):
                if result['config'] == 'legacy':
                    bars[i].set_color('gray')
                elif result['gpu']:
                    bars[i].set_color('green')
                else:
                    bars[i].set_color('blue')
            
            # Add horizontal line at 1x
            plt.axhline(y=1, color='red', linestyle='--', label='Baseline')
            
            plt.xlabel('Configuration')
            plt.ylabel('Speedup Factor')
            plt.title(f'Speedup Comparison - {self.strategy}')
            plt.xticks(rotation=45, ha='right')
            plt.legend()
            plt.tight_layout()
            plt.savefig('benchmark_speedup.png')
            plt.close()

def main():
    """Main benchmark execution"""
    parser = argparse.ArgumentParser(description='GPU Optimization Benchmark')
    parser.add_argument('--strategy', default='TBS', choices=['TBS', 'ORB', 'OI', 'INDICATOR', 'TV'])
    parser.add_argument('--start-date', default='20240101')
    parser.add_argument('--end-date', default='20240131')
    parser.add_argument('--configs', default='all', help='Comma-separated list of configs to run')
    parser.add_argument('--output-report', default='benchmark_report.html')
    
    args = parser.parse_args()
    
    # Parse configs
    if args.configs == 'all':
        configs = ['legacy', 'sequential', 'gpu_hints', 'workers_2', 'workers_4', 'workers_8', 'max_opt']
    else:
        configs = args.configs.split(',')
    
    # Create output directory
    os.makedirs('output', exist_ok=True)
    
    # Run benchmarks
    runner = BenchmarkRunner(args.strategy, args.start_date, args.end_date)
    runner.run_all_benchmarks(configs)
    runner.generate_report(args.output_report)
    
    print(f"\n✓ Benchmark complete! View report: {args.output_report}")

if __name__ == '__main__':
    main() 