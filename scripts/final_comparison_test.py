#!/usr/bin/env python3
"""
Final comprehensive comparison of legacy and new backtesters
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import shutil
import glob

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

class BacktesterComparison:
    def __init__(self):
        self.portfolio_excel = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
        self.start_date = "20250401"
        self.end_date = "20250401"  # Single day for quick test
        self.legacy_output = None
        self.new_output = None
        
    def run_legacy_backtester(self):
        """Run the legacy backtester"""
        print("\n" + "="*60)
        print("Running Legacy Backtester")
        print("="*60)
        
        legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
        legacy_script = os.path.join(legacy_dir, "BTRunPortfolio.py")
        
        # Clear old outputs in Trades folder
        trades_dir = os.path.join(legacy_dir, "Trades")
        if os.path.exists(trades_dir):
            old_files = glob.glob(os.path.join(trades_dir, "*.xlsx"))
            for f in old_files:
                try:
                    os.remove(f)
                    print(f"  Removed old file: {os.path.basename(f)}")
                except:
                    pass
        
        # Run legacy backtester
        env = os.environ.copy()
        env['PYTHONPATH'] = f"{os.path.abspath('bt/archive/backtester_stable')}:{os.path.abspath('.')}"
        
        cmd = [sys.executable, legacy_script, os.path.abspath(self.portfolio_excel)]
        
        print(f"  Running: {legacy_script}")
        print(f"  Input: {self.portfolio_excel}")
        
        try:
            result = subprocess.run(
                cmd,
                cwd=legacy_dir,
                env=env,
                capture_output=True,
                text=True,
                timeout=180
            )
            
            print(f"  Return code: {result.returncode}")
            
            if result.returncode == 0:
                # Find the output file
                new_files = glob.glob(os.path.join(trades_dir, "*.xlsx"))
                if new_files:
                    self.legacy_output = max(new_files, key=os.path.getctime)
                    print(f"  ✓ Output created: {os.path.basename(self.legacy_output)}")
                    
                    # Copy to comparison directory
                    comparison_dir = "comparison_outputs"
                    os.makedirs(comparison_dir, exist_ok=True)
                    legacy_copy = os.path.join(comparison_dir, "legacy_output.xlsx")
                    shutil.copy2(self.legacy_output, legacy_copy)
                    self.legacy_output = legacy_copy
                    print(f"  ✓ Copied to: {legacy_copy}")
                    return True
                else:
                    print("  ✗ No output file created")
                    return False
            else:
                print("  ✗ Legacy backtester failed")
                if result.stderr:
                    print(f"  Error: {result.stderr[:500]}")
                return False
                
        except Exception as e:
            print(f"  ✗ Error: {e}")
            return False
    
    def run_new_backtester(self):
        """Run the new refactored backtester"""
        print("\n" + "="*60)
        print("Running New Backtester")
        print("="*60)
        
        output_path = "comparison_outputs/new_output.xlsx"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        env = os.environ.copy()
        env['PYTHONPATH'] = os.path.abspath('.')
        
        cmd = [
            sys.executable,
            "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
            "--legacy-excel",
            "--portfolio-excel", self.portfolio_excel,
            "--output-path", output_path,
            "--start-date", self.start_date,
            "--end-date", self.end_date
        ]
        
        print(f"  Running: BTRunPortfolio_GPU")
        print(f"  Input: {self.portfolio_excel}")
        print(f"  Date range: {self.start_date} to {self.end_date}")
        
        try:
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True,
                timeout=180
            )
            
            print(f"  Return code: {result.returncode}")
            
            if result.returncode == 0 and os.path.exists(output_path):
                self.new_output = output_path
                print(f"  ✓ Output created: {output_path}")
                return True
            else:
                print("  ✗ New backtester failed")
                if result.stderr:
                    print(f"  Error: {result.stderr[-500:]}")
                return False
                
        except Exception as e:
            print(f"  ✗ Error: {e}")
            return False
    
    def compare_outputs(self):
        """Compare the outputs from both backtesters"""
        print("\n" + "="*60)
        print("Comparing Outputs")
        print("="*60)
        
        if not self.legacy_output or not self.new_output:
            print("  ✗ Cannot compare - missing outputs")
            return
        
        try:
            # Load both Excel files
            legacy_xl = pd.ExcelFile(self.legacy_output)
            new_xl = pd.ExcelFile(self.new_output)
            
            print(f"\n  Legacy sheets ({len(legacy_xl.sheet_names)}): {', '.join(legacy_xl.sheet_names[:5])}")
            print(f"  New sheets ({len(new_xl.sheet_names)}): {', '.join(new_xl.sheet_names[:5])}")
            
            # Find common sheets
            common_sheets = set(legacy_xl.sheet_names).intersection(set(new_xl.sheet_names))
            if common_sheets:
                print(f"\n  Common sheets: {', '.join(list(common_sheets)[:5])}")
                
                # Compare first common sheet
                sheet = list(common_sheets)[0]
                print(f"\n  Comparing sheet: {sheet}")
                
                legacy_df = pd.read_excel(legacy_xl, sheet_name=sheet)
                new_df = pd.read_excel(new_xl, sheet_name=sheet)
                
                print(f"    Legacy: {legacy_df.shape[0]} rows, {legacy_df.shape[1]} columns")
                print(f"    New: {new_df.shape[0]} rows, {new_df.shape[1]} columns")
                
                # Check for P&L columns
                legacy_pnl_cols = [col for col in legacy_df.columns if 'pnl' in col.lower() or 'p&l' in col.lower()]
                new_pnl_cols = [col for col in new_df.columns if 'pnl' in col.lower() or 'p&l' in col.lower()]
                
                if legacy_pnl_cols:
                    print(f"\n    Legacy P&L columns: {legacy_pnl_cols}")
                if new_pnl_cols:
                    print(f"    New P&L columns: {new_pnl_cols}")
                    
            else:
                print("\n  ⚠ No common sheets found - structures are different")
                
        except Exception as e:
            print(f"  ✗ Error comparing outputs: {e}")
    
    def run_comparison(self):
        """Run the full comparison"""
        print("="*80)
        print("Backtester Comparison Test")
        print(f"Time: {datetime.now()}")
        print("="*80)
        
        # Check input file
        if not os.path.exists(self.portfolio_excel):
            print(f"\n✗ Input file not found: {self.portfolio_excel}")
            return
        
        print(f"\n✓ Input file: {self.portfolio_excel}")
        
        # Run both backtesters
        legacy_success = self.run_legacy_backtester()
        new_success = self.run_new_backtester()
        
        # Compare if both succeeded
        if legacy_success and new_success:
            self.compare_outputs()
        
        # Summary
        print("\n" + "="*80)
        print("Summary")
        print("="*80)
        print(f"Legacy Backtester: {'✓ Success' if legacy_success else '✗ Failed'}")
        print(f"New Backtester: {'✓ Success' if new_success else '✗ Failed'}")
        
        if legacy_success and new_success:
            print(f"\n✓ Both outputs saved to comparison_outputs/")
            print("  - legacy_output.xlsx")
            print("  - new_output.xlsx")
            print("\nYou can open these files to compare the results manually")
        else:
            print("\n✗ Comparison failed - check error messages above")

if __name__ == "__main__":
    comparison = BacktesterComparison()
    comparison.run_comparison() 