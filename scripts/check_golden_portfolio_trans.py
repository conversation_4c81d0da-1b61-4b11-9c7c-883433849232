#!/usr/bin/env python3
"""
Check PORTFOLIO Trans sheet in golden file
"""

import pandas as pd

golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"

try:
    # Read PORTFOLIO Trans sheet
    df = pd.read_excel(golden_file, sheet_name='PORTFOLIO Trans', engine='openpyxl')
    
    print(f"Golden file PORTFOLIO Trans sheet:")
    print(f"=" * 50)
    print(f"Shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    
    # Show date information
    if 'Entry Date' in df.columns:
        print(f"\nEntry dates:")
        print(f"  Values: {df['Entry Date'].unique()}")
        
    if 'Exit Date' in df.columns:
        print(f"\nExit dates:")
        print(f"  Values: {df['Exit Date'].unique()}")
    
    # Show all data since it's only 8 rows
    print(f"\nAll trades:")
    print(df)
    
    # Also check the Metrics sheet
    print(f"\n" + "="*50)
    print(f"Metrics sheet:")
    metrics_df = pd.read_excel(golden_file, sheet_name='Metrics', engine='openpyxl')
    print(metrics_df)
    
except Exception as e:
    print(f"Error: {e}") 