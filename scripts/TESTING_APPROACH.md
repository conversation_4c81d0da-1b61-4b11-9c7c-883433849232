# Backtester Testing Approach

## Current Situation

1. **Legacy Service**: Running on internal IP (***************:5000) - not accessible from outside
2. **New System**: HeavyDB-based backtester needs validation
3. **Golden Output**: Available for comparison (`golden_output.xlsx`)

## Recommended Testing Strategy

### Option 1: Focus on New System Validation (Recommended)

Since the legacy HTTP service is on an internal network, focus on validating the new system directly:

```bash
# Run the test without legacy service
cd /srv/samba/shared
python scripts/test_without_legacy_service.py
```

This script will:
- Run the new HeavyDB backtester
- Compare output against golden file
- Report any differences

### Option 2: SSH Tunnel (If You Have Access)

If you have SSH access to the Windows machine or a gateway:

```bash
# Establish tunnel
ssh -L 5001:***************:5000 username@gateway-host

# Then update run_parallel_test.py to use localhost:5001
# LEGACY_SERVICE_URL = "http://localhost:5001"
```

### Option 3: VPN Connection

If your organization provides VPN access:
1. Connect to company VPN
2. Access the service directly at http://***************:5000
3. Run the original parallel test

### Option 4: Extract Legacy Logic

Instead of using the HTTP service, extract core logic:

1. Copy relevant functions from legacy code
2. Create a standalone test module
3. Compare calculations directly

## Immediate Next Steps

1. **Run New System Test**:
   ```bash
   python scripts/test_without_legacy_service.py
   ```

2. **Check Results**:
   - Look for output in `bt/backtester_stable/BTRUN/output/`
   - Review any error messages
   - Compare against golden output

3. **Debug Issues**:
   - If output not generated, check logs
   - Verify HeavyDB connection
   - Ensure input data exists

## Key Files

- **Test Script**: `scripts/test_without_legacy_service.py`
- **Input Portfolio**: `bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx`
- **Golden Output**: `bt/backtester_stable/BTRUN/input_sheets/golden_output.xlsx`
- **New Output**: `bt/backtester_stable/BTRUN/output/new_test_output.xlsx`

## Troubleshooting

### If New Backtester Fails:
1. Check HeavyDB connection: `heavysql -s 127.0.0.1 -u admin -p HyperInteractive -d heavyai`
2. Verify tables exist: `SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date >= '2025-04-01';`
3. Check Python dependencies: `pip install -r requirements.txt`
4. Review error logs

### If Output Doesn't Match Golden:
1. Check date range alignment
2. Verify input parameters match
3. Review calculation differences
4. Consider rounding/precision issues

## Contact Points

If you need the legacy service accessible:
1. Request port forwarding from network admin
2. Set up reverse proxy on accessible server
3. Deploy legacy service to cloud/accessible host 