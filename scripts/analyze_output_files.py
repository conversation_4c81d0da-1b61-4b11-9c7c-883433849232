#!/usr/bin/env python3
"""Analyze output files from legacy and new backtesting systems."""

import pandas as pd
import sys
from pathlib import Path

def analyze_excel_file(file_path, label):
    """Analyze the structure and content of an Excel file."""
    print(f"\n{'='*60}")
    print(f"Analyzing {label}: {file_path}")
    print('='*60)
    
    try:
        # Get all sheet names
        xl_file = pd.ExcelFile(file_path)
        sheets = xl_file.sheet_names
        print(f"\nSheets found: {sheets}")
        
        # Analyze each sheet
        for sheet_name in sheets:
            print(f"\n--- Sheet: {sheet_name} ---")
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            # Show specific data based on sheet type
            if 'Trans' in sheet_name:
                # Transaction sheet
                if len(df) > 0:
                    print(f"\nFirst transaction:")
                    print(df.iloc[0])
                    print(f"\nTotal trades: {len(df)}")
                    if 'PNL' in df.columns:
                        print(f"Total P&L: {df['PNL'].sum()}")
                    elif 'Net PNL' in df.columns:
                        print(f"Total P&L: {df['Net PNL'].sum()}")
            elif sheet_name == 'Metrics':
                # Metrics sheet
                print("\nMetrics:")
                for _, row in df.iterrows():
                    print(f"  {row.get('Particulars', row.iloc[0])}: {row.get('portfolio', row.iloc[1])}")
            
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")

def main():
    # Check if specific output files exist from the test
    if Path("heavydb_output_synthetic_atm.xlsx").exists():
        analyze_excel_file("heavydb_output_synthetic_atm.xlsx", "New System (HeavyDB)")
    else:
        # Analyze default new system output
        new_output = "bt/backtester_stable/BTRUN/output/one_day_test_output.xlsx"
        if Path(new_output).exists():
            analyze_excel_file(new_output, "New System (HeavyDB)")
        else:
            print(f"New output not found: {new_output}")
    
    # Check if legacy output exists
    legacy_outputs = [
        "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL.xlsx",
        "bt/archive/backtester_stable/BTRUN/RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL.xlsx",
        "bt/archive/backtester_stable/BTRUN/output.xlsx",
        "legacy_output_synthetic_atm.xlsx",
        "legacy_output.xlsx"
    ]
    
    for legacy_output in legacy_outputs:
        if Path(legacy_output).exists():
            analyze_excel_file(legacy_output, "Legacy System (MySQL)")
            break
    else:
        print("\nNo legacy output found to compare")

if __name__ == "__main__":
    main() 