#!/usr/bin/env python3
"""Clean up test files created during formatting fixes."""

import os
import sys

# Add project paths
sys.path.append('/srv/samba/shared')

def cleanup_files():
    """Remove test files created during the formatting fixes."""
    
    test_files = [
        "heavydb_comprehensive_output.xlsx",
        "heavydb_legacy_formatted_output.xlsx",
        "heavydb_formatted_test_output.xlsx",
        "heavydb_fixed_output.xlsx"
    ]
    
    print("="*80)
    print("CLEANING UP TEST FILES")
    print("="*80)
    
    removed_count = 0
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"✓ Removed: {file}")
                removed_count += 1
            except Exception as e:
                print(f"✗ Error removing {file}: {e}")
        else:
            print(f"- File not found: {file}")
    
    print(f"\nTotal files removed: {removed_count}")
    
    # Also offer to remove test scripts
    print("\n" + "-"*80)
    print("Test scripts created (not removing automatically):")
    test_scripts = [
        "scripts/test_all_fixes.py",
        "scripts/test_comprehensive_formatting.py",
        "scripts/comprehensive_content_comparison.py",
        "scripts/analyze_missing_sheets.py",
        "scripts/fix_case_sensitivity.py",
        "scripts/fix_formatting_issues.py",
        "scripts/investigate_duplicate_sheets.py",
        "scripts/final_formatting_summary.py",
        "scripts/cleanup_test_files.py"  # This script itself
    ]
    
    for script in test_scripts:
        if os.path.exists(script):
            print(f"  - {script}")
    
    print("\nTo remove test scripts, run: rm scripts/*formatting*.py scripts/*test*.py")

if __name__ == "__main__":
    cleanup_files() 