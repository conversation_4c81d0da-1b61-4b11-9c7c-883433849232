#!/usr/bin/env python3
"""Fix the indentation issue in heavydb_trade_processing.py"""

import re

# Read the file
with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

# Find the problematic section (lines 296-340) and fix indentation
# These lines should be indented 4 more spaces to be inside the leg loop
start_fix = 296  # Line with "# Debug: Check if we exited risk rules block properly"
end_fix = 340    # Line with the last logger.info for appending trade record

print(f"Fixing indentation from line {start_fix} to {end_fix}")

# Add 4 spaces to each line in this range
for i in range(start_fix, min(end_fix + 1, len(lines))):
    if lines[i].strip():  # Only modify non-empty lines
        lines[i] = '    ' + lines[i]
        
# Write the fixed file
with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'w') as f:
    f.writelines(lines)

print("Fixed indentation successfully!")

# Verify the fix
with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    fixed_lines = f.readlines()
    
print("\nVerifying fix - checking indentation levels:")
for i in [115, 157, 296, 297, 325, 330, 339]:  # Key lines
    if i < len(fixed_lines):
        indent = len(fixed_lines[i]) - len(fixed_lines[i].lstrip())
        content = fixed_lines[i].strip()[:50] + '...' if len(fixed_lines[i].strip()) > 50 else fixed_lines[i].strip()
        print(f"Line {i+1}: {indent} spaces - {content}") 