#!/usr/bin/env python3
"""
Load nifty spot OHLC data into HeavyDB using batch INSERT statements
This avoids file whitelist issues with COPY command
"""

import pandas as pd
from heavydb import connect
from datetime import datetime
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# HeavyDB connection details
HOST = "127.0.0.1"
PORT = 6274
USER = "admin"
PASSWORD = "HyperInteractive"
DATABASE = "heavyai"

# File paths
CSV_FILE = "/srv/samba/shared/market_data/nifty/nifty_cash_merged_rounded_v2.csv"

def main():
    # Connect to HeavyDB
    logger.info("Connecting to HeavyDB...")
    try:
        conn = connect(
            host=HOST,
            port=PORT,
            user=USER,
            password=PASSWORD,
            dbname=DATABASE
        )
        cursor = conn.cursor()
    except Exception as e:
        logger.error(f"Error connecting to HeavyDB: {e}")
        sys.exit(1)
    
    # Create table
    logger.info("Creating nifty_spot table...")
    
    # First drop the table if it exists
    try:
        cursor.execute("DROP TABLE IF EXISTS nifty_spot")
        logger.info("Dropped existing table")
    except Exception as e:
        # Table might not exist, that's ok
        pass
    
    # Then create the new table
    create_table_sql = """
    CREATE TABLE nifty_spot (
        trade_date DATE,
        trade_time TIME,
        symbol TEXT ENCODING DICT,
        "open" DOUBLE,
        "high" DOUBLE,
        "low" DOUBLE,
        "close" DOUBLE
    ) WITH (fragment_size = 32000000)
    """
    
    try:
        cursor.execute(create_table_sql)
        logger.info("Created nifty_spot table successfully")
    except Exception as e:
        logger.error(f"Error creating table: {e}")
        sys.exit(1)
    
    # Load CSV data
    logger.info(f"Loading data from {CSV_FILE}...")
    try:
        df = pd.read_csv(CSV_FILE)
        logger.info(f"Loaded {len(df)} rows from CSV")
        
        # Process in batches
        batch_size = 1000
        total_inserted = 0
        
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i+batch_size]
            
            # Build INSERT statements
            for idx, row in batch_df.iterrows():
                # Format dates and times properly
                trade_date = row['date']  # Assuming format is YYYY-MM-DD
                trade_time = row['time']   # Assuming format is HH:MM:SS
                symbol = row['symbol']
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']
                
                # Build INSERT statement
                insert_sql = f"""
                INSERT INTO nifty_spot VALUES (
                    '{trade_date}',
                    '{trade_time}',
                    '{symbol}',
                    {open_price},
                    {high_price},
                    {low_price},
                    {close_price}
                )
                """
                
                try:
                    cursor.execute(insert_sql)
                except Exception as e:
                    logger.error(f"Error inserting row {idx}: {e}")
                    logger.error(f"SQL: {insert_sql}")
                    continue
            
            # Commit after each batch
            conn.commit()
            total_inserted += len(batch_df)
            logger.info(f"Inserted {total_inserted}/{len(df)} rows ({total_inserted/len(df)*100:.1f}%)")
        
        logger.info(f"Data load complete! Inserted {total_inserted} rows.")
        
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        sys.exit(1)
    
    # Verify data
    logger.info("\nVerifying data load...")
    try:
        cursor.execute("SELECT COUNT(*) AS total_rows FROM nifty_spot")
        total = cursor.fetchone()[0]
        logger.info(f"Total rows in table: {total}")
        
        cursor.execute("SELECT MIN(trade_date) AS start_date, MAX(trade_date) AS end_date FROM nifty_spot")
        result = cursor.fetchone()
        logger.info(f"Date range: {result[0]} to {result[1]}")
        
        cursor.execute("SELECT * FROM nifty_spot LIMIT 5")
        logger.info("\nSample data:")
        for row in cursor.fetchall():
            logger.info(row)
        
    except Exception as e:
        logger.error(f"Error verifying data: {e}")
    
    # Close connection
    cursor.close()
    conn.close()
    logger.info("\nDone!")

if __name__ == "__main__":
    main() 