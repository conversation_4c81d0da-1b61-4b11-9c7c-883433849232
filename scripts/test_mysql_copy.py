#!/usr/bin/env python3
"""
Test MySQL copy with a small sample
"""

import pymysql
import pandas as pd

# Remote MySQL connection details
REMOTE_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

# Local MySQL connection details
LOCAL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def test_copy():
    """Test copying a small sample"""
    print("Testing MySQL copy with small sample...")
    
    try:
        # Connect
        remote_conn = pymysql.connect(**REMOTE_CONFIG)
        local_conn = pymysql.connect(**LOCAL_CONFIG)
        
        # Test query - just 100 rows
        query = "SELECT * FROM nifty_call WHERE date >= 220101 LIMIT 100"
        print(f"Executing: {query}")
        
        df = pd.read_sql(query, remote_conn)
        print(f"Retrieved {len(df)} rows")
        print(f"Columns: {list(df.columns)}")
        print(f"First row: {df.iloc[0].to_dict()}")
        
        # Try to write to local
        print("\nWriting to local database...")
        df.to_sql('nifty_call', local_conn, if_exists='append', index=False, method='multi')
        
        # Verify
        local_cursor = local_conn.cursor()
        local_cursor.execute("SELECT COUNT(*) FROM nifty_call")
        count = local_cursor.fetchone()[0]
        print(f"Local nifty_call now has {count} rows")
        
        remote_conn.close()
        local_conn.close()
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_copy() 