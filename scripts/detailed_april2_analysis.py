#!/usr/bin/env python3
"""
Detailed analysis of April 2, 2025 to understand ATM calculation differences
"""

import mysql.connector
try:
    from pyheavydb import connect as heavydb_connect
except ImportError:
    from heavydb import connect as heavydb_connect

# Connection configs
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def analyze_april2_mysql():
    """Analyze MySQL data for April 2, 2025"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    date = '250402'
    time = 33360  # 9:16 AM
    expiry = '250409'
    
    print("MySQL Analysis for April 2, 2025")
    print("="*60)
    
    # Get spot price
    cursor.execute("SELECT close FROM nifty_cash WHERE date = %s AND time = %s", (date, time))
    spot = cursor.fetchone()
    spot_price = spot[0] / 100 if spot else 0
    print(f"Spot Price: {spot_price:.2f}")
    
    # Check all strikes with synthetic future calculation
    print(f"\nSynthetic Future Analysis (Expiry: {expiry}):")
    print("Strike | CE Price | PE Price | Syn Future | Diff from Spot | Has Both")
    print("-" * 75)
    
    cursor.execute("""
        SELECT 
            c.strike,
            c.close as ce_close,
            p.close as pe_close
        FROM nifty_call c
        LEFT JOIN nifty_put p ON c.date = p.date AND c.time = p.time 
                                AND c.strike = p.strike AND c.expiry = p.expiry
        WHERE c.date = %s AND c.time = %s AND c.expiry = %s
        AND c.strike BETWEEN 22000 AND 24000
        ORDER BY c.strike
    """, (date, time, expiry))
    
    min_diff = float('inf')
    best_strike = None
    strikes_with_both = []
    
    for strike, ce_close, pe_close in cursor.fetchall():
        if ce_close and pe_close:
            ce_price = ce_close / 100
            pe_price = pe_close / 100
            syn_future = strike + ce_price - pe_price
            diff = abs(syn_future - spot_price)
            
            if diff < min_diff:
                min_diff = diff
                best_strike = strike
            
            strikes_with_both.append((strike, diff))
            
            # Show strikes near ATM candidates
            if strike in [22500, 23200] or (strike >= 23100 and strike <= 23300):
                print(f"{strike:6.0f} | {ce_price:8.2f} | {pe_price:8.2f} | {syn_future:10.2f} | {diff:14.2f} | YES")
        else:
            ce_price = ce_close / 100 if ce_close else 0
            pe_price = pe_close / 100 if pe_close else 0
            # Show strikes with missing data
            if strike in [22500, 23200] or (strike >= 23100 and strike <= 23300):
                ce_str = f"{ce_price:8.2f}" if ce_close else "    N/A"
                pe_str = f"{pe_price:8.2f}" if pe_close else "    N/A"
                print(f"{strike:6.0f} | {ce_str} | {pe_str} |      N/A    |      N/A       | NO")
    
    print(f"\nMySQL Best ATM: {best_strike} (diff: {min_diff:.2f})")
    print(f"Total strikes with both CE and PE: {len(strikes_with_both)}")
    
    cursor.close()
    conn.close()

def analyze_april2_heavydb():
    """Analyze HeavyDB data for April 2, 2025"""
    conn = heavydb_connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    print("\n\nHeavyDB Analysis for April 2, 2025")
    print("="*60)
    
    # Get ATM and spot
    query = """
    SELECT spot, atm_strike, strike, ce_close, pe_close
    FROM nifty_option_chain
    WHERE trade_date = '2025-04-02' AND trade_time = '09:16:00'
    AND strike = atm_strike
    LIMIT 1
    """
    
    result = conn.execute(query).fetchone()
    if result:
        spot, atm, strike, ce_close, pe_close = result
        print(f"Spot Price: {spot:.2f}")
        print(f"ATM Strike: {int(atm)}")
        print(f"ATM Prices - CE: {ce_close:.2f}, PE: {pe_close:.2f}")
        
        syn_future = atm + ce_close - pe_close
        diff = abs(syn_future - spot)
        print(f"Synthetic Future: {syn_future:.2f}, Diff: {diff:.2f}")
    
    # Check strikes around ATM
    print("\nStrikes around ATM:")
    print("Strike | CE Price | PE Price | Syn Future | Diff from Spot")
    print("-" * 60)
    
    query2 = """
    SELECT 
        strike,
        ce_close,
        pe_close,
        strike + ce_close - pe_close as syn_future,
        ABS(strike + ce_close - pe_close - spot) as diff
    FROM nifty_option_chain
    WHERE trade_date = '2025-04-02' AND trade_time = '09:16:00'
    AND strike BETWEEN 22500 AND 23500
    AND ce_close > 0 AND pe_close > 0
    ORDER BY diff
    LIMIT 10
    """
    
    results = conn.execute(query2).fetchall()
    for strike, ce_close, pe_close, syn_future, diff in results:
        marker = " <-- ATM" if strike == 23200 else ""
        print(f"{strike:6.0f} | {ce_close:8.2f} | {pe_close:8.2f} | {syn_future:10.2f} | {diff:14.2f}{marker}")
    
    conn.close()

def check_missing_data():
    """Check for missing data patterns in MySQL"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    print("\n\nMissing Data Analysis for April 2, 2025")
    print("="*60)
    
    # Check PE options for key strikes
    strikes_to_check = [22400, 22500, 22600, 23100, 23200, 23300]
    
    print("Checking PE options for key strikes:")
    for strike in strikes_to_check:
        cursor.execute("""
            SELECT COUNT(*) 
            FROM nifty_put 
            WHERE date = '250402' AND time = 33360 
            AND strike = %s AND expiry = '250409'
        """, (strike,))
        count = cursor.fetchone()[0]
        status = "EXISTS" if count > 0 else "MISSING"
        print(f"  Strike {strike} PE: {status}")
    
    cursor.close()
    conn.close()

def main():
    print("Detailed Analysis: April 2, 2025")
    print("="*70)
    print("MySQL calculates ATM as 22500")
    print("HeavyDB calculates ATM as 23200")
    print("Difference: 700 points")
    print("="*70)
    
    analyze_april2_mysql()
    analyze_april2_heavydb()
    check_missing_data()
    
    print("\n" + "="*70)
    print("Key Findings:")
    print("1. Both systems have the same spot price (23204.80)")
    print("2. MySQL is missing PE data for several strikes")
    print("3. This forces MySQL to select a different ATM strike")
    print("4. HeavyDB has complete data and selects the correct ATM")

if __name__ == "__main__":
    main() 