#!/usr/bin/env python3
import os
import sys
import pandas as pd
import json
from datetime import datetime

# Add the archive path to sys.path
sys.path.insert(0, '/srv/samba/shared/bt/archive/backtester_stable/BTRUN')

import config
from Util import Util

# Change to the archive directory
os.chdir('/srv/samba/shared/bt/archive/backtester_stable/BTRUN')

print("Debug: Portfolio parsing issue")
print("=" * 60)

# Check the config values
print(f"INPUT_FILE_FOLDER: {config.INPUT_FILE_FOLDER}")
print(f"PORTFOLIO_FILE_PATH: {config.PORTFOLIO_FILE_PATH}")

# Full path
excel_path = os.path.join(config.INPUT_FILE_FOLDER, config.PORTFOLIO_FILE_PATH)
print(f"Full Excel path: {excel_path}")
print(f"File exists: {os.path.exists(excel_path)}")

# Read the Excel file directly
try:
    portfolio_df = pd.read_excel(excel_path, sheet_name='PortfolioSetting')
    print(f"\nPortfolioSetting sheet shape: {portfolio_df.shape}")
    print(f"Columns: {list(portfolio_df.columns)}")
    print(f"\nFirst row data:")
    for col in portfolio_df.columns:
        print(f"  {col}: {portfolio_df.iloc[0][col]}")
    
    # Check date format
    start_date = portfolio_df.iloc[0]['StartDate']
    end_date = portfolio_df.iloc[0]['EndDate']
    print(f"\nDate values:")
    print(f"  StartDate: {start_date} (type: {type(start_date)})")
    print(f"  EndDate: {end_date} (type: {type(end_date)})")
    
    # Check if it's being filtered out due to date issues
    if portfolio_df.iloc[0]['Enabled'] == 'YES':
        print("\nPortfolio is enabled")
        # Try parsing the dates
        try:
            start_parsed = datetime.strptime(str(start_date), '%d_%m_%Y')
            end_parsed = datetime.strptime(str(end_date), '%d_%m_%Y')
            print(f"  Parsed StartDate: {start_parsed}")
            print(f"  Parsed EndDate: {end_parsed}")
        except Exception as e:
            print(f"  Error parsing dates: {e}")
    
except Exception as e:
    print(f"Error reading Excel: {e}")

# Now try the Util function
print("\n" + "=" * 60)
print("Calling Util.getPortfolioJson()...")
try:
    portfolioForBt, portfolioSettingDf = Util.getPortfolioJson(excelFilePath=excel_path)
    print(f"Result: {len(portfolioForBt)} portfolios found")
    if portfolioForBt:
        for key, value in portfolioForBt.items():
            print(f"  Portfolio {key}: {value.get('portfolio', {}).get('name', 'Unknown')}")
    else:
        print("No portfolios returned!")
        
except Exception as e:
    print(f"Error in getPortfolioJson: {e}")
    import traceback
    traceback.print_exc() 