#!/usr/bin/env python3
"""Check and compare output sheet structures between legacy and new systems."""

import pandas as pd
import os
from pathlib import Path

def check_excel_sheets(file_path, label):
    """Check sheet structure of an Excel file."""
    if not os.path.exists(file_path):
        print(f"\n{label}: File not found - {file_path}")
        return []
    
    print(f"\n{label}: {file_path}")
    print("-" * 60)
    
    xl = pd.ExcelFile(file_path)
    sheets = xl.sheet_names
    
    for sheet in sheets:
        df = pd.read_excel(file_path, sheet_name=sheet)
        print(f"  Sheet: {sheet}")
        print(f"    - Shape: {df.shape}")
        print(f"    - Columns: {list(df.columns)[:5]}{'...' if len(df.columns) > 5 else ''}")
    
    return sheets

def main():
    print("="*80)
    print("Output Sheet Structure Comparison")
    print("="*80)
    
    # Check HeavyDB output
    heavydb_sheets = check_excel_sheets("heavydb_final_output.xlsx", "HeavyDB Output")
    
    # Check legacy outputs
    legacy_files = [
        "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL.xlsx",
        "NIF0DTE *.xlsx"
    ]
    
    legacy_sheets = []
    for pattern in legacy_files:
        files = list(Path(".").glob(pattern))
        if files:
            legacy_sheets = check_excel_sheets(str(files[0]), "Legacy Output")
            break
    
    # Compare structures
    print("\n" + "="*80)
    print("SHEET COMPARISON")
    print("="*80)
    
    if heavydb_sheets and legacy_sheets:
        heavydb_set = set(heavydb_sheets)
        legacy_set = set(legacy_sheets)
        
        print("\nSheets in HeavyDB but not Legacy:")
        for sheet in heavydb_set - legacy_set:
            print(f"  + {sheet}")
        
        print("\nSheets in Legacy but not HeavyDB:")
        for sheet in legacy_set - heavydb_set:
            print(f"  - {sheet}")
        
        print("\nCommon sheets:")
        for sheet in heavydb_set & legacy_set:
            print(f"  = {sheet}")
    
    # Expected sheets for proper output
    print("\n" + "="*80)
    print("EXPECTED OUTPUT STRUCTURE")
    print("="*80)
    
    expected_sheets = [
        "PortfolioParameter",
        "GeneralParameter", 
        "LegParameter",
        "Metrics",
        "Max Profit and Loss",
        "PORTFOLIO Trans",
        "[Strategy]_Trans",
        "[Strategy]_DayWise",
        "[Strategy]_MonthWise",
        "[Strategy]_MarginPct"
    ]
    
    print("\nExpected sheets in a complete output file:")
    for sheet in expected_sheets:
        print(f"  - {sheet}")

if __name__ == "__main__":
    main() 