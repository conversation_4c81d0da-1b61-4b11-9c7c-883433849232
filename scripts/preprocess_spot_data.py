#!/usr/bin/env python3
"""
Preprocess nifty spot data CSV to rename columns for HeavyDB loading
"""

import pandas as pd
import sys

# File paths
INPUT_CSV = "/srv/samba/shared/market_data/nifty/nifty_cash_merged_rounded_v2.csv"
OUTPUT_CSV = "/tmp/nifty_spot_processed.csv"

def main():
    print("Loading CSV data...")
    try:
        # Read the CSV
        df = pd.read_csv(INPUT_CSV)
        print(f"Loaded {len(df)} rows")
        
        # Rename columns to match table structure
        df.rename(columns={
            'date': 'trade_date',
            'time': 'trade_time'
        }, inplace=True)
        
        # Save to output location
        df.to_csv(OUTPUT_CSV, index=False)
        print(f"Saved processed data to {OUTPUT_CSV}")
        
        # Show sample
        print("\nSample data (first 5 rows):")
        print(df.head())
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 