#!/usr/bin/env python3
"""
Run comparison with fixed configuration
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import json

def run_legacy_mysql():
    """Run the legacy MySQL direct query script"""
    print("\n" + "="*60)
    print("Running Legacy Backtester (MySQL Direct)")
    print("="*60)
    
    result = subprocess.run(
        ["python3", "scripts/fix_legacy_simple.py"],
        capture_output=True,
        text=True
    )
    
    if result.returncode == 0:
        print("  ✓ Legacy backtester completed")
        return True
    else:
        print("  ✗ Legacy backtester failed")
        if result.stderr:
            print(f"  Error: {result.stderr[:500]}")
        return False

def run_new_backtester_fixed():
    """Run the new HeavyDB backtester with fixed configuration"""
    print("\n" + "="*60)
    print("Running New GPU Backtester with Fixed Config")
    print("="*60)
    
    # Set up environment
    env = os.environ.copy()
    env['PYTHONPATH'] = os.path.abspath('.')
    
    cmd = [
        sys.executable,
        "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio_fixed.xlsx",
        "--output-path", "comparison_outputs/new_gpu_fixed.xlsx",
        "--start-date", "20241231",
        "--end-date", "20241231"
    ]
    
    print(f"  Using fixed portfolio: input_portfolio_fixed.xlsx")
    print(f"  Running command...")
    
    result = subprocess.run(
        cmd,
        env=env,
        capture_output=True,
        text=True,
        timeout=180
    )
    
    if result.returncode == 0 and os.path.exists("comparison_outputs/new_gpu_fixed.xlsx"):
        print("  ✓ New backtester completed")
        return True
    else:
        print("  ✗ New backtester failed")
        if result.stderr:
            print(f"  Error: {result.stderr[:1000]}")
        if result.stdout:
            print(f"  Output: {result.stdout[:1000]}")
        return False

def analyze_results():
    """Analyze and compare results"""
    print("\n" + "="*60)
    print("Analyzing Results")
    print("="*60)
    
    # Legacy results
    print("\n1. Legacy Results (MySQL Direct):")
    legacy_file = "comparison_outputs/legacy_output_direct_mysql.xlsx"
    if os.path.exists(legacy_file):
        legacy_df = pd.read_excel(legacy_file, sheet_name='PORTFOLIO Trans')
        print(f"   Total trades: {len(legacy_df)}")
        print(f"   Total P&L: {legacy_df['pnl'].sum():.2f}")
        
        print("\n   Trade details:")
        for _, row in legacy_df.iterrows():
            print(f"     {row['side']} {row['strike']} {row['option_type']} @ {row['entry_price']:.2f} -> {row['exit_price']:.2f}, P&L: {row['pnl']:.2f}")
    
    # New results
    print("\n2. New Results (HeavyDB with Fixed Config):")
    new_file = "comparison_outputs/new_gpu_fixed.xlsx"
    if os.path.exists(new_file):
        xl = pd.ExcelFile(new_file)
        print(f"   Sheets: {xl.sheet_names}")
        
        # Find transaction sheet
        trans_sheet = None
        for sheet in xl.sheet_names:
            if 'Trans' in sheet:
                trans_sheet = sheet
                break
        
        if trans_sheet:
            new_df = pd.read_excel(xl, sheet_name=trans_sheet)
            print(f"   Total trades: {len(new_df)}")
            
            # Find P&L column
            pnl_col = None
            for col in new_df.columns:
                if 'PNL' in col.upper() or 'P&L' in col.upper():
                    pnl_col = col
                    break
            
            if pnl_col:
                print(f"   Total P&L: {new_df[pnl_col].sum():.2f}")
            
            print("\n   Trade details:")
            # Map columns
            for _, row in new_df.iterrows():
                # Try to find appropriate columns
                trade_col = 'Trade' if 'Trade' in new_df.columns else 'side' if 'side' in new_df.columns else None
                strike_col = 'Strike' if 'Strike' in new_df.columns else 'strike' if 'strike' in new_df.columns else None
                option_col = 'CE/PE' if 'CE/PE' in new_df.columns else 'option_type' if 'option_type' in new_df.columns else None
                entry_col = 'Entry at' if 'Entry at' in new_df.columns else 'entry_price' if 'entry_price' in new_df.columns else None
                exit_col = 'Exit at' if 'Exit at' in new_df.columns else 'exit_price' if 'exit_price' in new_df.columns else None
                
                if all([trade_col, strike_col, option_col, entry_col, exit_col, pnl_col]):
                    print(f"     {row[trade_col]} {row[strike_col]} {row[option_col]} @ {row[entry_col]:.2f} -> {row[exit_col]:.2f}, P&L: {row[pnl_col]:.2f}")

def compare_strikes():
    """Compare strike selection between systems"""
    print("\n" + "="*60)
    print("Strike Selection Comparison")
    print("="*60)
    
    print("\nLegacy system (simple rounding):")
    print("  Spot: 23559.15 → ATM: 23550")
    print("  OTM2 for CALL: 23550 + 100 = 23650")
    print("  OTM2 for PUT: 23550 - 100 = 23450")
    
    print("\nHeavyDB system (synthetic future):")
    print("  Spot: 23559.15 → ATM: 23600")
    print("  OTM2 for CALL: 23600 + 100 = 23700")
    print("  OTM2 for PUT: 23600 - 100 = 23500")
    
    print("\nThis explains the strike differences!")

def main():
    print("="*80)
    print("Backtester Comparison with Fixed Configuration")
    print(f"Test Date: 2024-12-31")
    print(f"Time: {datetime.now()}")
    print("="*80)
    
    # Run legacy
    legacy_success = run_legacy_mysql()
    
    # Run new with fixed config
    new_success = run_new_backtester_fixed()
    
    # Analyze results
    if legacy_success or new_success:
        analyze_results()
    
    # Compare strikes
    compare_strikes()
    
    # Summary
    print("\n" + "="*80)
    print("Summary")
    print("="*80)
    print(f"Legacy Backtester: {'✓ Success' if legacy_success else '✗ Failed'}")
    print(f"New Backtester: {'✓ Success' if new_success else '✗ Failed'}")
    
    print("\nKey Issues Identified:")
    print("1. ✓ Fixed strike configuration (otm2 instead of atm with value=2)")
    print("2. Different ATM calculation methods:")
    print("   - Legacy: Simple rounding")
    print("   - New: Synthetic future based")
    print("3. Need to verify all 4 trades are generated with fixed config")

if __name__ == "__main__":
    main() 