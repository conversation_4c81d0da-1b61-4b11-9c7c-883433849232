#!/usr/bin/env python3
"""Analyze ATM calculation differences between legacy and new methods."""

import pandas as pd
import numpy as np
from datetime import datetime

def simple_atm(spot_price):
    """Legacy method: Round to nearest 50."""
    return round(spot_price / 50) * 50

def synthetic_future_atm(option_chain_df, spot_price):
    """
    New method: Use synthetic future calculation.
    1. Build paired set of strikes with both Call and Put
    2. Calculate synthetic future = strike + CE_close - PE_close
    3. Choose strike with smallest |synthetic_future - spot|
    """
    # Filter for paired strikes (both call and put exist)
    paired_strikes = option_chain_df[
        (option_chain_df['ce_close'].notna()) & 
        (option_chain_df['pe_close'].notna()) & 
        (option_chain_df['ce_close'] > 0) & 
        (option_chain_df['pe_close'] > 0)
    ].copy()
    
    if paired_strikes.empty:
        # Fallback to simple rounding
        return simple_atm(spot_price)
    
    # Calculate synthetic future for each strike
    paired_strikes['synthetic_future'] = (
        paired_strikes['strike'] + 
        paired_strikes['ce_close'] - 
        paired_strikes['pe_close']
    )
    
    # Calculate absolute difference from spot
    paired_strikes['abs_diff'] = abs(paired_strikes['synthetic_future'] - spot_price)
    
    # Find strike with minimum difference
    atm_row = paired_strikes.loc[paired_strikes['abs_diff'].idxmin()]
    return atm_row['strike']

def analyze_atm_differences():
    """Analyze ATM differences for our test date."""
    print("=== ATM Calculation Analysis ===")
    print(f"Date: {datetime.now()}\n")
    
    # Test case from our backtest
    test_date = "2025-04-01"
    test_time = "09:16:00"
    
    # From the output, we know:
    # - ATM strike used: 23550
    # - Strikes in trades: 23550 (ATM), 23650 (OTM2 for call), 23450 (OTM2 for put)
    
    print(f"Test Date: {test_date}")
    print(f"Test Time: {test_time}")
    
    # Simulate some test cases
    test_spots = [23525, 23550, 23575, 23600]
    
    print("\n--- Comparison of ATM Methods ---")
    print("Spot Price | Simple ATM | Synthetic ATM | Difference")
    print("-" * 55)
    
    for spot in test_spots:
        simple = simple_atm(spot)
        # For synthetic, we'd need actual option chain data
        # For now, show what simple method gives
        print(f"{spot:10.0f} | {simple:10.0f} | (needs data)  | N/A")
    
    print("\n--- Actual Trade Analysis ---")
    print("From the backtest output:")
    print("- ATM Strike: 23550")
    print("- Call OTM2: 23650 (ATM + 100)")
    print("- Put OTM2: 23450 (ATM - 100)")
    print("\nThis suggests the index was around 23550, making simple and synthetic methods align.")
    
    # Calculate what spot price would be for different ATM strikes
    print("\n--- Spot Price Ranges for ATM Strikes ---")
    print("ATM Strike | Spot Price Range (Simple Method)")
    print("-" * 45)
    for atm in [23450, 23500, 23550, 23600, 23650]:
        min_spot = atm - 25
        max_spot = atm + 24.99
        print(f"{atm:10.0f} | {min_spot:8.0f} - {max_spot:8.2f}")

def main():
    """Run the ATM analysis."""
    analyze_atm_differences()
    
    print("\n=== Key Findings ===")
    print("1. Simple method rounds to nearest 50")
    print("2. Synthetic future method uses option prices for more accurate ATM")
    print("3. In our test case, ATM was 23550, which works for spot around 23525-23574")
    print("4. Both methods would likely give same result for this spot range")
    
    print("\n=== Next Steps ===")
    print("1. Query actual option chain data from HeavyDB to calculate synthetic ATM")
    print("2. Compare with simple method for various timestamps")
    print("3. Identify cases where methods diverge significantly")

if __name__ == "__main__":
    main() 