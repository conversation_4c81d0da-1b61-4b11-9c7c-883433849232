#!/usr/bin/env python3
"""Create 2024 full year input file and fix GPU processing"""

import pandas as pd
import shutil
import os
from datetime import datetime

def create_2024_full_year_input():
    """Create input file with proper 2024 dates"""
    
    print("🔧 Creating 2024 Full Year Input File")
    print("=" * 50)
    
    # Copy the existing file as template
    source_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio_exit_120000.xlsx"
    target_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_full_year.xlsx"
    
    if os.path.exists(source_file):
        shutil.copy2(source_file, target_file)
        print(f"✅ Copied template: {source_file} -> {target_file}")
    else:
        print(f"❌ Source file not found: {source_file}")
        return None
    
    try:
        # Read the Excel file
        with pd.ExcelFile(target_file) as xls:
            sheets = {}
            for sheet_name in xls.sheet_names:
                sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name)
        
        # Update PortfolioSetting sheet with 2024 dates
        if 'PortfolioSetting' in sheets:
            portfolio_df = sheets['PortfolioSetting']
            
            # Find and update date columns
            for idx, row in portfolio_df.iterrows():
                if 'StartDate' in str(row.iloc[0]):
                    portfolio_df.iloc[idx, 1] = '01_01_2024'  # January 1, 2024
                elif 'EndDate' in str(row.iloc[0]):
                    portfolio_df.iloc[idx, 1] = '31_12_2024'  # December 31, 2024
            
            sheets['PortfolioSetting'] = portfolio_df
            print("✅ Updated PortfolioSetting with 2024 dates")
        
        # Write back to Excel
        with pd.ExcelWriter(target_file, engine='openpyxl') as writer:
            for sheet_name, df in sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"✅ Created 2024 full year input file: {target_file}")
        return target_file
        
    except Exception as e:
        print(f"❌ Error creating input file: {e}")
        return None

def fix_gpu_worker_pool():
    """Fix GPU worker pool pickling issues"""
    
    print("\n🔧 Fixing GPU Worker Pool")
    print("=" * 50)
    
    # Read the current GPU worker pool file
    worker_pool_file = "common/gpu_worker_pool.py"
    
    if not os.path.exists(worker_pool_file):
        print(f"❌ Worker pool file not found: {worker_pool_file}")
        return
    
    # Create a fixed version
    fixed_content = '''"""
GPU Worker Pool with Dynamic Workers and Fixed Pickling
"""
import os
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import psutil
import logging

logger = logging.getLogger(__name__)

class DynamicGPUWorkerPool:
    """Dynamic GPU worker pool that avoids pickling issues"""
    
    def __init__(self, max_workers=None):
        self.max_workers = max_workers or self._get_optimal_workers()
        self.executor = None
        
    def _get_optimal_workers(self):
        """Calculate optimal number of workers based on system resources"""
        cpu_count = mp.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)
        
        # Conservative approach: use 50% of CPU cores, max 8
        optimal_workers = min(max(1, cpu_count // 2), 8)
        
        # Adjust based on available memory (need ~2GB per worker)
        memory_workers = max(1, int(memory_gb // 2))
        optimal_workers = min(optimal_workers, memory_workers)
        
        logger.info(f"Calculated optimal workers: {optimal_workers} (CPU: {cpu_count}, Memory: {memory_gb:.1f}GB)")
        return optimal_workers
    
    def __enter__(self):
        self.executor = ProcessPoolExecutor(max_workers=self.max_workers)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.executor:
            self.executor.shutdown(wait=True)
    
    def submit_batch(self, func, tasks):
        """Submit batch of tasks avoiding pickling issues"""
        if not self.executor:
            raise RuntimeError("Worker pool not initialized")
        
        # Convert complex objects to simple data structures
        simple_tasks = []
        for task in tasks:
            if hasattr(task, 'dict'):  # Pydantic model
                simple_tasks.append(task.dict())
            elif hasattr(task, '__dict__'):  # Regular object
                simple_tasks.append(task.__dict__)
            else:
                simple_tasks.append(task)
        
        # Submit tasks
        futures = []
        for task in simple_tasks:
            future = self.executor.submit(func, task)
            futures.append(future)
        
        return futures
    
    def get_results(self, futures):
        """Get results from submitted futures"""
        results = []
        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                logger.error(f"Task failed: {e}")
                results.append(None)
        
        return results

def run_with_dynamic_workers(func, tasks, max_workers=None):
    """Run tasks with dynamic worker pool"""
    with DynamicGPUWorkerPool(max_workers) as pool:
        futures = pool.submit_batch(func, tasks)
        return pool.get_results(futures)
'''
    
    # Write the fixed content
    with open(worker_pool_file, 'w') as f:
        f.write(fixed_content)
    
    print(f"✅ Fixed GPU worker pool: {worker_pool_file}")

def main():
    """Main function to create input file and fix GPU processing"""
    
    print("🚀 Setting up 2024 Full Year Backtest")
    print("=" * 60)
    
    # Create 2024 input file
    input_file = create_2024_full_year_input()
    
    # Fix GPU worker pool
    fix_gpu_worker_pool()
    
    if input_file:
        print(f"\n✅ Setup complete!")
        print(f"📁 Input file: {input_file}")
        print(f"🔧 GPU worker pool: Fixed")
        print(f"\n🚀 Run command:")
        print(f"python3 -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU \\")
        print(f"  --legacy-excel \\")
        print(f"  --portfolio-excel {input_file} \\")
        print(f"  --output-path bt/backtester_stable/BTRUN/output/tbs_2024_full_year_fixed.xlsx \\")
        print(f"  --start-date 20240101 --end-date 20241231 \\")
        print(f"  --workers auto")
    else:
        print("\n❌ Setup failed!")

if __name__ == "__main__":
    main() 