#!/usr/bin/env python3
"""Ensure legacy config.py has required settings."""

def ensure_config():
    """Ensure config.py has proper settings."""
    
    config_path = "bt/archive/backtester_stable/BTRUN/config.py"
    
    # Read the file
    with open(config_path, 'r') as f:
        content = f.read()
    
    # Check if USE_SYNTHETIC_FUTURE_ATM is already there
    if 'USE_SYNTHETIC_FUTURE_ATM = True' not in content:
        # Add it at the end
        content += '\n# ATM calculation method for parity with HeavyDB\nUSE_SYNTHETIC_FUTURE_ATM = True\n'
    
    # Add a host variable for compatibility
    if "host = 'localhost'" not in content:
        # Add before the BT_URII section
        import re
        pattern = r'(# Updated to use localhost with SSH tunnels)'
        replacement = r"# Database host\nhost = 'localhost'\n\n\1"
        content = re.sub(pattern, replacement, content)
    
    # Write back
    with open(config_path, 'w') as f:
        f.write(content)
    
    print("✅ Updated config.py with required settings")
    print("  - USE_SYNTHETIC_FUTURE_ATM = True")
    print("  - host = 'localhost'")

if __name__ == "__main__":
    ensure_config() 