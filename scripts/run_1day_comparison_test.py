#!/usr/bin/env python3
"""Run 1-day comparison test between legacy and GPU backtesters."""

import os
import subprocess
import time
import json
import pandas as pd
from datetime import datetime
from pathlib import Path

class BacktesterComparison:
    def __init__(self, test_date="20240103"):
        self.test_date = test_date
        self.test_date_formatted = f"{test_date[:4]}-{test_date[4:6]}-{test_date[6:]}"
        self.results_dir = f"comparison_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.results_dir, exist_ok=True)
        
    def log(self, message):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
        # Also write to log file
        with open(f"{self.results_dir}/comparison.log", "a") as f:
            f.write(f"[{timestamp}] {message}\n")
    
    def check_prerequisites(self):
        """Check if all prerequisites are met."""
        self.log("Checking prerequisites...")
        
        # Check MySQL data
        try:
            import mysql.connector
            conn = mysql.connector.connect(
                host='localhost',
                user='mahesh',
                password='mahesh_123',
                database='historicaldb'
            )
            cursor = conn.cursor()
            
            # Check data for test date
            date_str = self.test_date[2:]  # Convert to YYMMDD
            cursor.execute(f"""
                SELECT COUNT(*) FROM nifty_call 
                WHERE date = '{date_str}'
            """)
            call_count = cursor.fetchone()[0]
            
            cursor.execute(f"""
                SELECT COUNT(*) FROM nifty_put 
                WHERE date = '{date_str}'
            """)
            put_count = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            if call_count > 0 and put_count > 0:
                self.log(f"✅ MySQL data available for {self.test_date}: {call_count} calls, {put_count} puts")
            else:
                self.log(f"❌ No MySQL data for {self.test_date}")
                return False
                
        except Exception as e:
            self.log(f"❌ MySQL connection failed: {e}")
            return False
        
        # Check legacy config
        config_path = "bt/archive/backtester_stable/BTRUN/config.py"
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                content = f.read()
            if 'localhost' in content and 'USE_SYNTHETIC_FUTURE_ATM = True' in content:
                self.log("✅ Legacy config prepared correctly")
            else:
                self.log("❌ Legacy config not prepared. Run: python3 scripts/prepare_legacy_config.py")
                return False
        else:
            self.log("❌ Legacy config not found")
            return False
        
        # Check input files
        portfolio_path = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
        if not os.path.exists(portfolio_path):
            self.log(f"❌ Portfolio input file not found: {portfolio_path}")
            return False
        else:
            self.log("✅ Input files found")
        
        return True
    
    def update_input_dates(self):
        """Update input Excel files to use test date."""
        self.log(f"Updating input files for date {self.test_date}...")
        
        # This would normally update the Excel files
        # For now, we'll assume they're already set correctly
        self.log("⚠️  Please ensure input Excel files have dates set to 03_01_2024")
        
    def run_legacy_backtest(self):
        """Run legacy backtester."""
        self.log("Running legacy backtester...")
        
        # Change to legacy directory
        legacy_dir = "bt/archive/backtester_stable/BTRUN"
        
        # Set environment variables
        env = os.environ.copy()
        env['MYSQL_HOST'] = 'localhost'
        env['DEBUG_MODE'] = 'true'
        
        # Run legacy backtester
        cmd = ['python3', 'BTRunPortfolio.py']
        
        self.log(f"Command: cd {legacy_dir} && {' '.join(cmd)}")
        
        try:
            # Run the command
            process = subprocess.Popen(
                cmd,
                cwd=legacy_dir,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Capture output
            stdout, stderr = process.communicate(timeout=300)  # 5 minute timeout
            
            # Save output
            with open(f"{self.results_dir}/legacy_stdout.log", "w") as f:
                f.write(stdout)
            with open(f"{self.results_dir}/legacy_stderr.log", "w") as f:
                f.write(stderr)
            
            if process.returncode == 0:
                self.log("✅ Legacy backtester completed successfully")
                
                # Check for output file
                output_file = os.path.join(legacy_dir, "output.xlsx")
                if os.path.exists(output_file):
                    # Copy to results directory
                    import shutil
                    shutil.copy2(output_file, f"{self.results_dir}/legacy_output.xlsx")
                    self.log(f"✅ Legacy output saved to {self.results_dir}/legacy_output.xlsx")
                    return True
                else:
                    self.log("❌ Legacy output file not found")
                    return False
            else:
                self.log(f"❌ Legacy backtester failed with return code {process.returncode}")
                self.log(f"Error output: {stderr[:500]}...")
                return False
                
        except subprocess.TimeoutExpired:
            self.log("❌ Legacy backtester timed out")
            process.kill()
            return False
        except Exception as e:
            self.log(f"❌ Error running legacy backtester: {e}")
            return False
    
    def run_gpu_backtest(self):
        """Run GPU backtester."""
        self.log("Running GPU backtester...")
        
        # Set environment variables
        env = os.environ.copy()
        env['DEBUG_MODE'] = 'true'
        env['PYTHONPATH'] = os.getcwd()
        
        # Run GPU backtester
        cmd = [
            'python3', '-m', 'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
            '--legacy-excel',
            '--portfolio-excel', 'bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx',
            '--output-path', f'{self.results_dir}/gpu_output.xlsx',
            '--debug'
        ]
        
        self.log(f"Command: {' '.join(cmd)}")
        
        try:
            # Run the command
            process = subprocess.Popen(
                cmd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Capture output
            stdout, stderr = process.communicate(timeout=300)  # 5 minute timeout
            
            # Save output
            with open(f"{self.results_dir}/gpu_stdout.log", "w") as f:
                f.write(stdout)
            with open(f"{self.results_dir}/gpu_stderr.log", "w") as f:
                f.write(stderr)
            
            if process.returncode == 0:
                self.log("✅ GPU backtester completed successfully")
                return True
            else:
                self.log(f"❌ GPU backtester failed with return code {process.returncode}")
                self.log(f"Error output: {stderr[:500]}...")
                return False
                
        except subprocess.TimeoutExpired:
            self.log("❌ GPU backtester timed out")
            process.kill()
            return False
        except Exception as e:
            self.log(f"❌ Error running GPU backtester: {e}")
            return False
    
    def compare_results(self):
        """Compare results from both backtesters."""
        self.log("Comparing results...")
        
        legacy_file = f"{self.results_dir}/legacy_output.xlsx"
        gpu_file = f"{self.results_dir}/gpu_output.xlsx"
        
        if not os.path.exists(legacy_file):
            self.log("❌ Legacy output file not found")
            return False
        
        if not os.path.exists(gpu_file):
            self.log("❌ GPU output file not found")
            return False
        
        try:
            # Read both Excel files
            legacy_sheets = pd.read_excel(legacy_file, sheet_name=None)
            gpu_sheets = pd.read_excel(gpu_file, sheet_name=None)
            
            # Compare sheet names
            legacy_sheet_names = set(legacy_sheets.keys())
            gpu_sheet_names = set(gpu_sheets.keys())
            
            self.log(f"Legacy sheets: {sorted(legacy_sheet_names)}")
            self.log(f"GPU sheets: {sorted(gpu_sheet_names)}")
            
            missing_in_gpu = legacy_sheet_names - gpu_sheet_names
            extra_in_gpu = gpu_sheet_names - legacy_sheet_names
            
            if missing_in_gpu:
                self.log(f"⚠️  Sheets missing in GPU: {missing_in_gpu}")
            if extra_in_gpu:
                self.log(f"⚠️  Extra sheets in GPU: {extra_in_gpu}")
            
            # Compare common sheets
            common_sheets = legacy_sheet_names & gpu_sheet_names
            
            comparison_results = {}
            
            for sheet in common_sheets:
                self.log(f"\nComparing sheet: {sheet}")
                
                legacy_df = legacy_sheets[sheet]
                gpu_df = gpu_sheets[sheet]
                
                # Basic comparison
                if legacy_df.shape == gpu_df.shape:
                    self.log(f"  ✅ Shape matches: {legacy_df.shape}")
                else:
                    self.log(f"  ❌ Shape mismatch: Legacy {legacy_df.shape} vs GPU {gpu_df.shape}")
                
                # Compare P&L if present
                if 'P&L' in legacy_df.columns and 'P&L' in gpu_df.columns:
                    legacy_pnl = legacy_df['P&L'].sum()
                    gpu_pnl = gpu_df['P&L'].sum()
                    diff = abs(legacy_pnl - gpu_pnl)
                    diff_pct = (diff / abs(legacy_pnl) * 100) if legacy_pnl != 0 else 0
                    
                    if diff < 0.01:  # Less than 1 paisa
                        self.log(f"  ✅ P&L matches: {legacy_pnl:.2f}")
                    else:
                        self.log(f"  ❌ P&L mismatch: Legacy {legacy_pnl:.2f} vs GPU {gpu_pnl:.2f} (diff: {diff:.2f}, {diff_pct:.2f}%)")
                
                comparison_results[sheet] = {
                    'shape_match': legacy_df.shape == gpu_df.shape,
                    'legacy_shape': legacy_df.shape,
                    'gpu_shape': gpu_df.shape
                }
            
            # Save comparison results
            with open(f"{self.results_dir}/comparison_results.json", "w") as f:
                json.dump(comparison_results, f, indent=2)
            
            self.log(f"\n✅ Comparison complete. Results saved to {self.results_dir}/")
            return True
            
        except Exception as e:
            self.log(f"❌ Error comparing results: {e}")
            import traceback
            self.log(traceback.format_exc())
            return False
    
    def run_comparison(self):
        """Run the complete comparison."""
        self.log("="*60)
        self.log(f"Starting 1-day comparison test for {self.test_date}")
        self.log("="*60)
        
        # Check prerequisites
        if not self.check_prerequisites():
            self.log("❌ Prerequisites check failed. Aborting.")
            return False
        
        # Update input dates
        self.update_input_dates()
        
        # Run legacy backtest
        legacy_success = self.run_legacy_backtest()
        if not legacy_success:
            self.log("⚠️  Legacy backtest failed, but continuing with GPU test")
        
        # Run GPU backtest
        gpu_success = self.run_gpu_backtest()
        if not gpu_success:
            self.log("❌ GPU backtest failed")
            if not legacy_success:
                return False
        
        # Compare results if both succeeded
        if legacy_success and gpu_success:
            self.compare_results()
        
        self.log("\n" + "="*60)
        self.log("Comparison test complete")
        self.log(f"Results saved to: {self.results_dir}/")
        self.log("="*60)
        
        return True

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Run 1-day comparison test')
    parser.add_argument('--date', default='20240103', help='Test date (YYYYMMDD)')
    args = parser.parse_args()
    
    # Run comparison
    comparison = BacktesterComparison(test_date=args.date)
    success = comparison.run_comparison()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main()) 