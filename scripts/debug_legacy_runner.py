#!/usr/bin/env python3
"""Debug version of legacy backtester runner."""

import sys
import os

# Add the legacy BTRUN directory to Python path
sys.path.insert(0, "bt/archive/backtester_stable/BTRUN")

# Enable detailed logging
import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('/tmp/legacy_debug.log')
    ]
)

print("Starting debug legacy runner...")
logging.info("Starting debug legacy runner...")

try:
    print("Importing modules...")
    from datetime import datetime
    print("  ✓ datetime imported")
    
    from Util import Util
    print("  ✓ Util imported")
    
    import pandas as pd
    print("  ✓ pandas imported")
    
    import traceback
    print("  ✓ traceback imported")
    
    import config
    print("  ✓ config imported")
    
    import json
    print("  ✓ json imported")
    
    pd.set_option('mode.chained_assignment', None)
    
    STRATEGYWISE_RESULTS = True
    
    print("\nStarting main execution...")
    logging.info(f"Started ({config.VERSION_NO.get('PORTFOLIO', 'Unknown')})")
    
    try:
        print("Running necessary functions...")
        Util.runNeccesaryFunctionsBeforeStartingBT()
        print("  ✓ runNeccesaryFunctionsBeforeStartingBT completed")
        
        folderPath = "Trades"
        os.makedirs(folderPath, exist_ok=True)
        print(f"  ✓ Created/verified {folderPath} directory")
        
        print("\nGetting portfolio JSON...")
        portfolioForBt, portfolioSettingDf = Util.getPortfolioJson(
            excelFilePath=os.path.join(config.INPUT_FILE_FOLDER, config.PORTFOLIO_FILE_PATH)
        )
        print(f"  ✓ Got portfolio data: {len(portfolioForBt)} portfolios")
        
        for pNo in portfolioForBt:
            print(f"\n Processing portfolio {pNo}: {portfolioForBt[pNo]['portfolio']['name']}")
            
            btstart = datetime.now()
            logging.info(f"{btstart}, Backtester Started")
            
            btResultFile = datetime.now().strftime("%d%m%Y %H%M%S") + ".xlsx"
            btResultFile = os.path.join(folderPath, f"{portfolioForBt[pNo]['portfolio']['name']} {btResultFile}")
            print(f"  Output file will be: {btResultFile}")
            
            with open("btpara.json", "+w") as ff:
                ff.write(json.dumps(portfolioForBt[pNo], indent=4))
            print("  ✓ Wrote btpara.json")
            
            print("\n  Calling getBacktestResults...")
            btResp = Util.getBacktestResults(btPara=portfolioForBt[pNo])
            print(f"  ✓ Got response from getBacktestResults")
            print(f"    Response type: {type(btResp)}")
            print(f"    Response keys: {btResp.keys() if isinstance(btResp, dict) else 'Not a dict'}")
            
            if btResp and 'strategies' in btResp:
                print(f"    Number of orders: {len(btResp.get('strategies', {}).get('orders', []))}")
                
                if len(btResp.get('strategies', {}).get('orders', [])) > 0:
                    print("    ✓ Orders generated successfully!")
                    # Continue with processing...
                else:
                    print("    ⚠️  No orders generated")
            else:
                print("    ⚠️  Empty response or no strategies")
                
        print("\n✅ Debug run completed")
        
    except Exception as e:
        print(f"\n❌ Error during execution: {e}")
        logging.error(traceback.format_exc())
        print(traceback.format_exc())
        
except Exception as e:
    print(f"\n❌ Error during imports: {e}")
    print(traceback.format_exc())

print("\nCheck /tmp/legacy_debug.log for detailed logs") 