#!/usr/bin/env python3
"""
Debug TBS exit time issues.

This script helps debug exit time issues in TBS (Time-Based Strategy),
comparing legacy and GPU implementations.
"""

import os
import sys
import json
import logging
import argparse
import pandas as pd
import subprocess
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tbs_exit_time_debug.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('tbs_exit_time_debug')

def add_archive_path():
    """Add archive code to Python path."""
    archive_path = Path(__file__).parent.parent / "bt/archive/backtester_stable/BTRUN"
    sys.path.insert(0, str(archive_path))
    return archive_path

def add_gpu_path():
    """Add GPU code to Python path."""
    gpu_path = Path(__file__).parent.parent
    sys.path.insert(0, str(gpu_path))
    return gpu_path

def load_excel_config(excel_file):
    """Load configuration from Excel file."""
    logger.info(f"Loading Excel config: {excel_file}")
    
    # Load the Excel sheets
    portfolio_df = pd.read_excel(excel_file, sheet_name="Portfolio")
    strategy_df = pd.read_excel(excel_file, sheet_name="Strategy")
    leg_df = pd.read_excel(excel_file, sheet_name="Leg")
    
    # Extract relevant information
    portfolio_name = portfolio_df["Name"].iloc[0]
    strategy_name = strategy_df["StrategyName"].iloc[0]
    
    # Filter strategy and legs
    strategy = strategy_df[strategy_df["StrategyName"] == strategy_name].iloc[0]
    legs = leg_df[leg_df["StrategyName"] == strategy_name]
    
    # Get times
    start_time = strategy["StartTime"]
    end_time = strategy["EndTime"]
    
    return {
        "portfolio_name": portfolio_name,
        "strategy_name": strategy_name,
        "strategy": strategy,
        "legs": legs,
        "start_time": start_time,
        "end_time": end_time
    }

def run_legacy_tbs(excel_file, test_date, output_file):
    """Run legacy TBS and capture exit times."""
    logger.info("Running legacy TBS...")
    
    # Get absolute path to archive directory
    archive_path = add_archive_path()
    
    cmd = [
        f"cd {archive_path} &&",
        "export MYSQL_HOST=localhost &&",
        "export DEBUG_MODE=true &&",
        f"python3 BTRunPortfolio.py --excel {excel_file} --output {output_file}"
    ]
    
    cmd_str = " ".join(cmd)
    logger.info(f"Legacy command: {cmd_str}")
    
    try:
        # Run legacy backtester
        subprocess.run(cmd_str, shell=True, check=True)
        logger.info(f"Legacy backtester completed. Output: {output_file}")
        
        # Extract exit times from output
        legacy_exit_times = extract_legacy_exit_times(output_file)
        return legacy_exit_times
    except Exception as e:
        logger.error(f"Legacy TBS failed: {e}")
        return None

def extract_legacy_exit_times(output_file):
    """Extract exit times from legacy output."""
    try:
        # Read the Excel file
        df = pd.read_excel(output_file, sheet_name="PORTFOLIO Trans")
        
        # Get exit times
        exit_times = []
        
        if "Exit Time" in df.columns:
            exit_times = df["Exit Time"].tolist()
        
        logger.info(f"Legacy exit times: {exit_times}")
        return exit_times
    except Exception as e:
        logger.error(f"Failed to extract legacy exit times: {e}")
        return []

def run_gpu_tbs(excel_file, test_date, output_file):
    """Run GPU TBS and capture exit times."""
    logger.info("Running GPU TBS...")
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", excel_file,
        "--output-path", output_file,
        "--debug"
    ]
    
    logger.info(f"GPU command: {' '.join(cmd)}")
    
    try:
        # Run GPU backtester
        subprocess.run(cmd, check=True)
        logger.info(f"GPU TBS completed. Output: {output_file}")
        
        # Extract exit times from output
        gpu_exit_times = extract_gpu_exit_times(output_file)
        return gpu_exit_times
    except Exception as e:
        logger.error(f"GPU TBS failed: {e}")
        return None

def extract_gpu_exit_times(output_file):
    """Extract exit times from GPU output."""
    try:
        # Read the Excel file
        df = pd.read_excel(output_file, sheet_name="PORTFOLIO Trans")
        
        # Get exit times
        exit_times = []
        
        if "Exit Time" in df.columns:
            exit_times = df["Exit Time"].tolist()
        
        logger.info(f"GPU exit times: {exit_times}")
        return exit_times
    except Exception as e:
        logger.error(f"Failed to extract GPU exit times: {e}")
        return []

def debug_risk_rule_execution(excel_file):
    """Debug risk rule execution in both systems."""
    logger.info("Debugging risk rule execution...")
    
    try:
        # Load configuration
        config = load_excel_config(excel_file)
        
        # Extract exit time from config
        expected_exit_time = config["end_time"]
        logger.info(f"Expected exit time from Excel: {expected_exit_time}")
        
        # Legacy risk evaluation
        logger.info("Legacy risk evaluation approach:")
        legacy_approach = """
        Legacy Risk Evaluation (Util.py):
        1. Checks exit time: endTime
        2. SL check: slValue based on slType
        3. TP check: tgtValue based on tgtType
        4. Sets exit reason based on hit condition
        5. Force exit at EndTime if no SL/TP hit
        """
        logger.info(legacy_approach)
        
        # GPU risk evaluation
        logger.info("GPU risk evaluation approach:")
        
        # Add GPU path to import from
        add_gpu_path()
        
        # Import GPU risk module
        try:
            from bt.backtester_stable.BTRUN.models.risk import RiskRule
            
            # Extract typical SL/TP values from config
            legs = config["legs"]
            sl_types = legs["SLType"].unique().tolist()
            sl_values = legs["SLValue"].unique().tolist()
            tgt_types = legs["TGTType"].unique().tolist()
            tgt_values = legs["TGTValue"].unique().tolist()
            
            # Get a sample leg
            sample_leg = legs.iloc[0]
            
            # Create a sample risk rule
            sample_rule = {
                "exit_time": str(expected_exit_time).zfill(6),
                "sl_type": sample_leg["SLType"],
                "sl_value": sample_leg["SLValue"],
                "tgt_type": sample_leg["TGTType"],
                "tgt_value": sample_leg["TGTValue"]
            }
            
            # Display SL/TP configuration
            sl_tp_config = f"""
            SL/TP Configuration:
            - SL Types: {sl_types}
            - SL Values: {sl_values}
            - TGT Types: {tgt_types}
            - TGT Values: {tgt_values}
            - Exit Time: {expected_exit_time}
            
            Sample Risk Rule:
            {json.dumps(sample_rule, indent=2)}
            """
            logger.info(sl_tp_config)
            
            # GPU risk approach
            gpu_approach = """
            GPU Risk Evaluation (heavydb_trade_processing.py):
            1. evaluate_risk_rule() is called
            2. First converts exit_time to HH:MM:SS format
            3. For each candle, checks conditions:
               - If SL hit, returns exit info with reason="stop loss"
               - If TP hit, returns exit info with reason="target"
               - If current time >= exit_time, returns with reason="exit time hit"
            4. Issue: Early exit needs to be investigated
            """
            logger.info(gpu_approach)
            
            return True
        except Exception as e:
            logger.error(f"Failed to import GPU risk module: {e}")
            return False
    
    except Exception as e:
        logger.error(f"Risk rule debug failed: {e}")
        return False

def compare_exit_times(config, legacy_times, gpu_times):
    """Compare exit times between legacy and GPU."""
    logger.info("Comparing exit times...")
    
    if not legacy_times or not gpu_times:
        logger.error("Missing exit times to compare")
        return False
    
    # Get expected exit time
    expected_exit_time = config["end_time"]
    expected_hhmm = str(expected_exit_time).zfill(4)
    expected_hour = int(expected_hhmm[:2])
    expected_minute = int(expected_hhmm[2:])
    expected_formatted = f"{expected_hour:02d}:{expected_minute:02d}:00"
    
    logger.info(f"Expected exit time: {expected_formatted}")
    logger.info(f"Legacy exit times: {legacy_times}")
    logger.info(f"GPU exit times: {gpu_times}")
    
    # Check if all legacy times match expected
    legacy_match = all(time == expected_formatted for time in legacy_times)
    logger.info(f"All legacy exit times match expected: {legacy_match}")
    
    # Check if all GPU times match expected
    gpu_match = all(time == expected_formatted for time in gpu_times)
    logger.info(f"All GPU exit times match expected: {gpu_match}")
    
    # Compare legacy and GPU times
    times_match = legacy_times == gpu_times
    logger.info(f"Legacy and GPU exit times match: {times_match}")
    
    result = {
        "expected_time": expected_formatted,
        "legacy_match": legacy_match,
        "gpu_match": gpu_match,
        "times_match": times_match
    }
    
    logger.info(f"Exit time comparison result: {result}")
    return result

def debug_exit_time(excel_file, test_date, output_dir):
    """Debug exit time issues in TBS."""
    logger.info(f"Starting TBS exit time debug for date: {test_date}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Define output files
    legacy_output = os.path.join(output_dir, "legacy_tbs_output.xlsx")
    gpu_output = os.path.join(output_dir, "gpu_tbs_output.xlsx")
    
    # Load configuration
    config = load_excel_config(excel_file)
    
    # Debug risk rule execution
    risk_debug = debug_risk_rule_execution(excel_file)
    
    # Run legacy TBS
    legacy_times = run_legacy_tbs(excel_file, test_date, legacy_output)
    
    # Run GPU TBS
    gpu_times = run_gpu_tbs(excel_file, test_date, gpu_output)
    
    # Compare exit times
    if legacy_times and gpu_times:
        comparison = compare_exit_times(config, legacy_times, gpu_times)
        return comparison
    else:
        logger.error("Failed to get exit times")
        return None

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Debug TBS Exit Time Issues')
    parser.add_argument('--excel', required=True, help='Input Excel file path')
    parser.add_argument('--date', default=datetime.now().strftime('%Y%m%d'), 
                        help='Test date (YYYYMMDD)')
    parser.add_argument('--output-dir', default='debug_output/tbs_exit_time', 
                        help='Output directory')
    
    args = parser.parse_args()
    
    # Run the debug
    result = debug_exit_time(args.excel, args.date, args.output_dir)
    
    # Print summary
    if result:
        print("\nTBS EXIT TIME DEBUG SUMMARY:")
        print("===========================")
        print(f"Expected exit time: {result['expected_time']}")
        print(f"Legacy exit times match expected: {result['legacy_match']}")
        print(f"GPU exit times match expected: {result['gpu_match']}")
        print(f"Legacy and GPU exit times match: {result['times_match']}")
        
        # Show recommendation if there's an issue
        if not result['gpu_match'] or not result['times_match']:
            print("\nRECOMMENDED FIXES:")
            print("1. Check SL/TP values - set high enough to avoid early triggers")
            print("   - For SELL legs: SL=500%, TP=100%")
            print("   - For BUY legs: SL=50%, TP=100%")
            print("2. Verify heavydb_trade_processing.py properly enforces exit_time")
            print("3. Check if first candle is being skipped correctly")
    else:
        print("\nTBS EXIT TIME DEBUG FAILED")
    
    print("\nCheck tbs_exit_time_debug.log for detailed logs")

if __name__ == "__main__":
    main() 