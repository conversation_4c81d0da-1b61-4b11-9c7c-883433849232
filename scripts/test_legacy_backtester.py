#!/usr/bin/env python3
"""
Test the legacy backtester independently
"""

import os
import sys
import subprocess
from datetime import datetime

def test_legacy_backtester():
    """Test the legacy backtester"""
    print("="*60)
    print("Testing Legacy Backtester")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Check if legacy code exists
    legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
    legacy_script = os.path.join(legacy_dir, "BTRunPortfolio.py")
    
    if not os.path.exists(legacy_script):
        print(f"✗ Legacy script not found: {legacy_script}")
        return False
    
    print(f"✓ Legacy script found: {legacy_script}")
    
    # Configuration
    portfolio_excel = os.path.abspath("bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx")
    output_path = os.path.abspath("legacy_test_output.xlsx")
    
    # Method 1: Try running with proper PYTHONPATH
    print("\nMethod 1: Running with PYTHONPATH...")
    
    env = os.environ.copy()
    env['PYTHONPATH'] = f"{os.path.abspath('bt/archive/backtester_stable')}:{os.path.abspath('.')}"
    
    # First try the legacy command format
    cmd = [
        sys.executable,
        legacy_script,
        portfolio_excel  # Legacy might expect just the Excel file as argument
    ]
    
    print(f"Command: {' '.join(cmd)}")
    print(f"PYTHONPATH: {env['PYTHONPATH']}")
    
    try:
        result = subprocess.run(
            cmd,
            cwd=legacy_dir,
            env=env,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"\nReturn code: {result.returncode}")
        
        if result.stdout:
            print("\nSTDOUT (first 500 chars):")
            print("-" * 40)
            print(result.stdout[:500])
            
        if result.stderr:
            print("\nSTDERR (first 500 chars):")
            print("-" * 40)
            print(result.stderr[:500])
            
        if result.returncode != 0:
            # Try alternative command format
            print("\n\nMethod 2: Trying alternative command format...")
            cmd = [
                sys.executable,
                "-m", "BTRUN.BTRunPortfolio",
                portfolio_excel
            ]
            
            result = subprocess.run(
                cmd,
                cwd=os.path.abspath("bt/archive/backtester_stable"),
                env=env,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            print(f"Return code: {result.returncode}")
            if result.stderr:
                print("STDERR:", result.stderr[:500])
                
    except subprocess.TimeoutExpired:
        print("\n✗ Legacy backtester timed out")
        return False
    except Exception as e:
        print(f"\n✗ Error: {e}")
        return False
    
    # Check for any output files
    print("\n\nChecking for output files...")
    
    possible_outputs = [
        output_path,
        os.path.join(legacy_dir, "*.xlsx"),
        "*.xlsx",
        os.path.join(legacy_dir, "output", "*.xlsx")
    ]
    
    # List files in legacy directory
    print(f"\nFiles in legacy directory:")
    try:
        for f in os.listdir(legacy_dir):
            if f.endswith(('.xlsx', '.xls', '.csv')):
                print(f"  - {f}")
    except:
        pass
    
    return False

def analyze_legacy_code():
    """Analyze the legacy code to understand how to run it"""
    print("\n\nAnalyzing legacy code structure...")
    
    legacy_script = "bt/archive/backtester_stable/BTRUN/BTRunPortfolio.py"
    
    try:
        with open(legacy_script, 'r') as f:
            content = f.read()
            
        # Look for main function or entry point
        if "__main__" in content:
            print("✓ Found __main__ block")
            
            # Extract relevant lines around main
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "__main__" in line:
                    print("\nMain block context:")
                    start = max(0, i-5)
                    end = min(len(lines), i+20)
                    for j in range(start, end):
                        print(f"  {j}: {lines[j]}")
                    break
                    
        # Look for argument parsing
        if "argparse" in content or "sys.argv" in content:
            print("\n✓ Found argument parsing")
            
            # Find how arguments are parsed
            for i, line in enumerate(lines):
                if "sys.argv" in line or "ArgumentParser" in line:
                    print(f"\nArgument handling at line {i}:")
                    start = max(0, i-3)
                    end = min(len(lines), i+5)
                    for j in range(start, end):
                        print(f"  {j}: {lines[j]}")
                    if end - start > 8:
                        break
                    
    except Exception as e:
        print(f"✗ Error analyzing code: {e}")

if __name__ == "__main__":
    # First analyze the code
    analyze_legacy_code()
    
    # Then try to run it
    success = test_legacy_backtester()
    
    print("\n" + "="*60)
    if success:
        print("✓ Legacy backtester test completed")
    else:
        print("✗ Legacy backtester test failed")
        print("\nThe legacy code may have:")
        print("- Different command-line argument format")
        print("- Missing dependencies")
        print("- Incompatible import structure")
        print("\nConsider using the new backtester for comparisons")
    print("="*60) 