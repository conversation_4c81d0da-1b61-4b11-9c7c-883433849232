#!/usr/bin/env python3
"""
Run TBS comparison between legacy (archive + MySQL) and new (HeavyDB) backtesters
Testing for April 3, 2025 (expiry day with DTE=0)
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import json
import shutil

# Add project root to path
project_root = '/srv/samba/shared'
sys.path.insert(0, project_root)

def run_legacy_backtester():
    """Run the legacy backtester from archive folder"""
    print("="*70)
    print("Running LEGACY Backtester (Archive + MySQL)")
    print("="*70)
    
    # Change to legacy directory
    legacy_dir = os.path.join(project_root, 'bt/archive/backtester_stable/BTRUN')
    
    # Copy input files to legacy INPUT SHEETS folder
    legacy_input_dir = os.path.join(legacy_dir, 'INPUT SHEETS')
    if not os.path.exists(legacy_input_dir):
        os.makedirs(legacy_input_dir)
    
    # Copy portfolio and strategy files
    portfolio_src = os.path.join(project_root, 'bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx')
    strategy_src = os.path.join(project_root, 'bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx')
    
    portfolio_dst = os.path.join(legacy_input_dir, 'INPUT PORTFOLIO.xlsx')
    strategy_dst = os.path.join(legacy_input_dir, 'INPUT TBS MULTI LEGS.xlsx')
    
    print(f"Copying input files to legacy folder...")
    shutil.copy2(portfolio_src, portfolio_dst)
    shutil.copy2(strategy_src, strategy_dst)
    
    # Run legacy backtester
    cmd = [
        'python3',
        'BTRunPortfolio.py'
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    print(f"Working directory: {legacy_dir}")
    
    try:
        result = subprocess.run(
            cmd,
            cwd=legacy_dir,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        print("\nLegacy Output:")
        print(result.stdout)
        
        if result.stderr:
            print("\nLegacy Errors:")
            print(result.stderr)
        
        # Find output file in Trades folder
        trades_dir = os.path.join(legacy_dir, 'Trades')
        if os.path.exists(trades_dir):
            # Get most recent file
            files = sorted(
                [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')],
                key=lambda x: os.path.getmtime(os.path.join(trades_dir, x)),
                reverse=True
            )
            if files:
                legacy_output = os.path.join(trades_dir, files[0])
                print(f"\nLegacy output file: {legacy_output}")
                return legacy_output
        
        print("\nWarning: No legacy output file found")
        return None
        
    except subprocess.TimeoutExpired:
        print("\nError: Legacy backtester timed out")
        return None
    except Exception as e:
        print(f"\nError running legacy backtester: {e}")
        return None

def run_heavydb_backtester():
    """Run the new HeavyDB backtester"""
    print("\n" + "="*70)
    print("Running NEW Backtester (HeavyDB)")
    print("="*70)
    
    # Run for April 3 only (DTE=0 expiry day)
    cmd = [
        'python3', '-m',
        'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
        '--legacy-excel',
        '--portfolio-excel', 'bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx',
        '--output-path', 'output/tbs_apr3_heavydb.xlsx',
        '--start-date', '20250403',
        '--end-date', '20250403'
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        print("\nHeavyDB Output:")
        print(result.stdout)
        
        if result.stderr:
            print("\nHeavyDB Errors:")
            print(result.stderr)
        
        output_file = os.path.join(project_root, 'output/tbs_apr3_heavydb.xlsx')
        if os.path.exists(output_file):
            print(f"\nHeavyDB output file: {output_file}")
            return output_file
        else:
            print("\nWarning: No HeavyDB output file found")
            return None
            
    except subprocess.TimeoutExpired:
        print("\nError: HeavyDB backtester timed out")
        return None
    except Exception as e:
        print(f"\nError running HeavyDB backtester: {e}")
        return None

def compare_excel_outputs(legacy_file, heavydb_file):
    """Compare the Excel outputs from both systems"""
    print("\n" + "="*70)
    print("Comparing Outputs")
    print("="*70)
    
    if not legacy_file or not heavydb_file:
        print("Cannot compare - missing output files")
        return
    
    try:
        # Load both Excel files
        legacy_xl = pd.ExcelFile(legacy_file)
        heavydb_xl = pd.ExcelFile(heavydb_file)
        
        print(f"\nLegacy sheets: {legacy_xl.sheet_names}")
        print(f"HeavyDB sheets: {heavydb_xl.sheet_names}")
        
        # Compare key sheets
        key_sheets = ['PORTFOLIO Trans', 'Combined']
        
        for sheet in key_sheets:
            print(f"\n{'='*50}")
            print(f"Comparing sheet: {sheet}")
            print(f"{'='*50}")
            
            if sheet in legacy_xl.sheet_names and sheet in heavydb_xl.sheet_names:
                legacy_df = pd.read_excel(legacy_file, sheet_name=sheet)
                heavydb_df = pd.read_excel(heavydb_file, sheet_name=sheet)
                
                print(f"\nLegacy shape: {legacy_df.shape}")
                print(f"HeavyDB shape: {heavydb_df.shape}")
                
                # Compare trade count
                if 'Symbol' in legacy_df.columns:
                    legacy_trades = len(legacy_df[legacy_df['Symbol'].notna()])
                    heavydb_trades = len(heavydb_df[heavydb_df['Symbol'].notna()])
                    print(f"\nTrade count:")
                    print(f"  Legacy: {legacy_trades}")
                    print(f"  HeavyDB: {heavydb_trades}")
                
                # Compare P&L if available
                if 'Overall P&L' in legacy_df.columns:
                    legacy_pnl = legacy_df['Overall P&L'].sum()
                    heavydb_pnl = heavydb_df['Overall P&L'].sum()
                    print(f"\nTotal P&L:")
                    print(f"  Legacy: {legacy_pnl}")
                    print(f"  HeavyDB: {heavydb_pnl}")
                
                # Show first few rows
                print(f"\nFirst 5 rows from Legacy:")
                print(legacy_df.head())
                
                print(f"\nFirst 5 rows from HeavyDB:")
                print(heavydb_df.head())
            else:
                print(f"Sheet '{sheet}' not found in both files")
        
        # Save comparison report
        report_path = os.path.join(project_root, 'test_results/tbs_1day_comparison_report.md')
        with open(report_path, 'w') as f:
            f.write("# TBS 1-Day Comparison Report\n\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"Legacy file: {legacy_file}\n")
            f.write(f"HeavyDB file: {heavydb_file}\n\n")
            f.write("## Sheet Comparison\n\n")
            f.write(f"Legacy sheets: {legacy_xl.sheet_names}\n\n")
            f.write(f"HeavyDB sheets: {heavydb_xl.sheet_names}\n\n")
            
        print(f"\nComparison report saved to: {report_path}")
        
    except Exception as e:
        print(f"\nError comparing outputs: {e}")

def main():
    print("TBS 1-Day Comparison Test")
    print("="*70)
    print("Testing Date: April 3, 2025 (Expiry day, DTE=0)")
    print("Strategy: TBS with ATM SELL, OTM2 BUY")
    print("="*70)
    
    # Create output directory
    os.makedirs('output', exist_ok=True)
    os.makedirs('test_results', exist_ok=True)
    
    # Run both backtesters
    legacy_output = run_legacy_backtester()
    heavydb_output = run_heavydb_backtester()
    
    # Compare outputs
    compare_excel_outputs(legacy_output, heavydb_output)
    
    print("\n" + "="*70)
    print("Comparison Complete!")
    print("="*70)

if __name__ == "__main__":
    main() 