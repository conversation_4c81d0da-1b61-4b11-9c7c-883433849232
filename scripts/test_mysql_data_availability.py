#!/usr/bin/env python3
"""
Test MySQL data availability for the backtesting dates
"""

import mysql.connector as mysql
from datetime import datetime

def test_mysql_data():
    """Check what data is available in MySQL"""
    print("="*60)
    print("Testing MySQL Data Availability")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    try:
        # Connect to MySQL
        mydb = mysql.connect(
            host="************",
            user="mahesh", 
            password="mahesh_123",
            database="historicaldb"
        )
        cursor = mydb.cursor()
        print("\n✓ Connected to MySQL successfully")
        
        # Check for April 2025 data
        test_date = '2025-04-01'
        
        # Check nifty_cash table
        print(f"\nChecking data for {test_date}:")
        
        tables = ['nifty_cash', 'nifty_call', 'nifty_put']
        
        for table in tables:
            try:
                # Get date range
                query = f"""
                    SELECT MIN(date), MAX(date), COUNT(*) 
                    FROM {table} 
                    WHERE YEAR(date) = 2025
                """
                cursor.execute(query)
                result = cursor.fetchone()
                
                if result and result[2] > 0:
                    print(f"\n  {table}:")
                    print(f"    2025 data range: {result[0]} to {result[1]}")
                    print(f"    Total rows in 2025: {result[2]:,}")
                else:
                    print(f"\n  {table}: No data for 2025")
                
                # Check specifically for April 2025
                query = f"""
                    SELECT DATE(date) as trade_date, COUNT(*) as row_count
                    FROM {table} 
                    WHERE date >= '2025-04-01' AND date < '2025-05-01'
                    GROUP BY DATE(date)
                    ORDER BY trade_date
                    LIMIT 10
                """
                cursor.execute(query)
                april_data = cursor.fetchall()
                
                if april_data:
                    print(f"    April 2025 data:")
                    for row in april_data[:5]:
                        print(f"      {row[0]}: {row[1]:,} rows")
                else:
                    print(f"    No April 2025 data found")
                    
                    # Check what years have data
                    query = f"""
                        SELECT YEAR(date) as year, COUNT(*) as row_count
                        FROM {table}
                        GROUP BY YEAR(date)
                        ORDER BY year DESC
                        LIMIT 10
                    """
                    cursor.execute(query)
                    years = cursor.fetchall()
                    
                    if years:
                        print(f"    Available years:")
                        for year, count in years:
                            print(f"      {year}: {count:,} rows")
                            
            except Exception as e:
                print(f"  Error checking {table}: {e}")
        
        # Get some sample dates with data
        print("\n\nFinding dates with complete data:")
        
        query = """
            SELECT 
                DATE(c.date) as trade_date,
                COUNT(DISTINCT c.time) as cash_ticks,
                COUNT(DISTINCT call.strike) as call_strikes,
                COUNT(DISTINCT put.strike) as put_strikes
            FROM nifty_cash c
            JOIN nifty_call call ON DATE(c.date) = DATE(call.date)
            JOIN nifty_put put ON DATE(c.date) = DATE(put.date)
            WHERE c.date >= '2023-01-01'
            GROUP BY DATE(c.date)
            HAVING cash_ticks > 100 AND call_strikes > 10 AND put_strikes > 10
            ORDER BY trade_date DESC
            LIMIT 10
        """
        
        cursor.execute(query)
        good_dates = cursor.fetchall()
        
        if good_dates:
            print("\nDates with good data coverage:")
            for row in good_dates:
                print(f"  {row[0]}: {row[1]} cash ticks, {row[2]} call strikes, {row[3]} put strikes")
                
            # Suggest a date to use
            suggested_date = good_dates[0][0]
            print(f"\n✓ Suggested test date: {suggested_date}")
            print(f"  This date has complete market data available")
        
        cursor.close()
        mydb.close()
        
    except Exception as e:
        print(f"\n✗ MySQL Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mysql_data() 