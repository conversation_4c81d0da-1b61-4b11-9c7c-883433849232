# Accessing Internal Legacy Service (***************:5000)

## Problem
The legacy backtesting service runs on a private IP (***************) which is not accessible from outside the local network.

## Solutions

### 1. SSH Tunnel (Recommended)
```bash
# From your Linux server to Windows machine (if SSH is enabled on Windows)
ssh -L 5001:localhost:5000 user@***************

# Through a jump host
ssh -L 5001:***************:5000 <EMAIL>

# Then access at: http://localhost:5001
```

### 2. VPN Connection
If your organization has a VPN:
```bash
# Connect to VPN
sudo openvpn --config company-vpn.ovpn

# Then access directly
curl http://***************:5000
```

### 3. Reverse Proxy (nginx)
On a server with access to both networks:
```nginx
server {
    listen 80;
    server_name backtest-api.yourdomain.com;
    
    location / {
        proxy_pass http://***************:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 4. SOCKS Proxy
```bash
# Create SOCKS proxy
ssh -D 8080 user@gateway-server

# Configure application to use SOCKS proxy
export http_proxy=socks5://localhost:8080
export https_proxy=socks5://localhost:8080
```

### 5. Port Forwarding on Router/Firewall
Configure NAT/Port forwarding:
- External Port: 5000
- Internal IP: ***************
- Internal Port: 5000

### 6. Alternative: Skip Legacy Testing
Since the legacy service is internal-only, focus on:
1. Component testing with mock data
2. Direct database comparison
3. New system validation against golden files

## Quick Workaround for Testing

Instead of accessing the HTTP service, we can:

1. **Extract Legacy Logic**: Copy the core calculation functions from legacy code
2. **Run Locally**: Execute them directly without HTTP layer
3. **Compare Results**: Validate outputs match the golden file

Example:
```python
# Instead of HTTP call
# response = requests.post(f"{LEGACY_URL}/backtest", json=params)

# Direct function call
from legacy_code import run_backtest_logic
result = run_backtest_logic(params)
``` 