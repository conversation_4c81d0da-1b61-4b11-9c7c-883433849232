#!/usr/bin/env python3
"""
Check trading days and expiry dates
"""

from datetime import datetime
from heavydb import connect

# HeavyDB connection details
HOST = "127.0.0.1"
PORT = 6274
USER = "admin"
PASSWORD = "HyperInteractive"
DATABASE = "heavyai"

# Dates to check
dates_to_check = ['2025-04-01', '2025-04-02', '2025-04-03', '2025-04-04', 
                  '2025-04-07', '2025-04-08', '2025-04-09', '2025-04-11']

print("Trading Days Analysis")
print("=" * 60)

# Check day of week
print("\nDay of week:")
for date_str in dates_to_check:
    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
    day_name = date_obj.strftime('%A')
    day_num = date_obj.weekday() + 1  # Monday=1, Sunday=7
    print(f"  {date_str}: {day_name} (weekday={day_num})")

# Check expiry dates in the database
print("\nChecking expiry dates in database...")
try:
    conn = connect(
        host=HOST,
        port=PORT,
        user=USER,
        password=PASSWORD,
        dbname=DATABASE
    )
    cursor = conn.cursor()
    
    # Check which dates have DTE=0 (i.e., are expiry dates)
    print("\nExpiry dates (DTE=0):")
    for date_str in dates_to_check:
        query = f"""
        SELECT DISTINCT expiry_date, dte
        FROM nifty_option_chain
        WHERE trade_date = DATE '{date_str}'
          AND dte = 0
        LIMIT 5
        """
        cursor.execute(query)
        results = cursor.fetchall()
        
        if results:
            print(f"  {date_str}: EXPIRY DAY - Options expiring on {results[0][0]}")
        else:
            # Check what DTE values are available
            query2 = f"""
            SELECT DISTINCT dte, COUNT(*) as cnt
            FROM nifty_option_chain
            WHERE trade_date = DATE '{date_str}'
            GROUP BY dte
            ORDER BY dte
            LIMIT 5
            """
            cursor.execute(query2)
            dte_results = cursor.fetchall()
            if dte_results:
                dte_values = [f"DTE={r[0]}({r[1]} rows)" for r in dte_results]
                print(f"  {date_str}: NOT EXPIRY - Available: {', '.join(dte_values)}")
            else:
                print(f"  {date_str}: NO DATA")
    
    # Check what expiry dates are available in April
    print("\nAll expiry dates in April 2025:")
    query = """
    SELECT DISTINCT expiry_date, MIN(dte) as min_dte
    FROM nifty_option_chain
    WHERE trade_date BETWEEN DATE '2025-04-01' AND DATE '2025-04-30'
      AND expiry_date BETWEEN DATE '2025-04-01' AND DATE '2025-04-30'
    GROUP BY expiry_date
    ORDER BY expiry_date
    """
    cursor.execute(query)
    results = cursor.fetchall()
    
    for expiry_date, min_dte in results:
        print(f"  {expiry_date}")
        
    # Check golden file dates
    print("\nGolden file uses dates:")
    print("  April 3 (Thursday) and April 9 (Wednesday)")
    print("\nProblem identified: DTE=0 filter means only trades on expiry days!")
    print("Solution: Remove or adjust DTE filter to allow trading on non-expiry days")
    
    conn.close()
    
except Exception as e:
    print(f"Database error: {e}")
    import traceback
    traceback.print_exc() 