#!/usr/bin/env python3
"""Run a year-long backtest with GPU optimization"""

import os
import time
import subprocess
from datetime import datetime
import pandas as pd

def run_year_backtest():
    """Run a full year backtest for 2024."""
    
    print("=" * 60)
    print("YEAR-LONG GPU BACKTEST (2024)")
    print("=" * 60)
    
    # Create output directory
    output_dir = f"year_backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(output_dir, exist_ok=True)
    
    # Configuration
    portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    output_file = f"{output_dir}/year_2024_backtest.xlsx"
    
    print(f"Portfolio file: {portfolio_file}")
    print(f"Output file: {output_file}")
    print(f"Date range: 01-01-2024 to 31-12-2024")
    print("-" * 60)
    
    # Run the backtest
    print("Starting GPU backtest...")
    start_time = time.time()
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", portfolio_file,
        "--output-path", output_file,
        "--start-date", "20240101",
        "--end-date", "20241231",
        "--workers", "1"  # Single worker to avoid multiprocessing issues
    ]
    
    # Run with real-time output
    process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
    
    # Print output as it comes
    for line in iter(process.stdout.readline, ''):
        if line:
            print(f"  {line.rstrip()}")
    
    process.wait()
    duration = time.time() - start_time
    
    print("-" * 60)
    print(f"Backtest completed in {duration:.2f} seconds ({duration/60:.1f} minutes)")
    
    # Check if output was created
    if os.path.exists(output_file):
        print("\n✅ SUCCESS: Output file created")
        
        # Analyze results
        print("\nAnalyzing results...")
        try:
            # Read portfolio transactions
            df_trans = pd.read_excel(output_file, sheet_name="PORTFOLIO Trans")
            df_metrics = pd.read_excel(output_file, sheet_name="Metrics")
            
            print(f"\nTotal trades: {len(df_trans)}")
            
            if 'overall_pnl' in df_trans.columns:
                total_pnl = df_trans['overall_pnl'].sum()
                print(f"Total P&L: ₹{total_pnl:,.2f}")
                
                # Monthly breakdown
                df_trans['month'] = pd.to_datetime(df_trans['entry_datetime']).dt.to_period('M')
                monthly_pnl = df_trans.groupby('month')['overall_pnl'].sum()
                
                print("\nMonthly P&L breakdown:")
                for month, pnl in monthly_pnl.items():
                    print(f"  {month}: ₹{pnl:,.2f}")
            
            # Show key metrics
            print("\nKey Metrics:")
            key_metrics = ['Net Profit', 'Total Trades', 'Winning Trades', 
                          'Losing Trades', 'Win Rate', 'Average Profit Per Trade']
            
            # Get the portfolio column name (it's the second column after 'Particulars')
            portfolio_col = df_metrics.columns[1] if len(df_metrics.columns) > 1 else None
            
            if portfolio_col:
                for metric in key_metrics:
                    metric_row = df_metrics[df_metrics['Particulars'] == metric]
                    if not metric_row.empty and portfolio_col in metric_row.columns:
                        value = metric_row[portfolio_col].values[0]
                        print(f"  {metric}: {value}")
            else:
                print("  Unable to find portfolio column in metrics")
            
        except Exception as e:
            print(f"Error analyzing results: {e}")
            
    else:
        print("\n❌ FAILED: No output file created")
        
    print("\n" + "=" * 60)
    print("YEAR-LONG BACKTEST COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    run_year_backtest() 