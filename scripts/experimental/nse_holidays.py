#!/usr/bin/env python3
"""
NSE Holidays Parser

This script collects NSE Trading holidays from 2019 to 2026 and writes them to a CSV file
for use in the DTE calculation in HeavyDB.
"""

import csv
import datetime
from datetime import date

# Define the NSE holidays from 2019 to 2026
# Format: 'YYYY-MM-DD' (ISO format)
NSE_HOLIDAYS = [
    # 2019
    '2019-01-26', # Republic Day
    '2019-03-04', # <PERSON><PERSON><PERSON><PERSON><PERSON>
    '2019-03-21', # <PERSON><PERSON>
    '2019-04-17', # <PERSON><PERSON><PERSON>
    '2019-04-19', # Good Friday
    '2019-05-01', # Maharashtra Day
    '2019-06-05', # Eid-ul-Fitr
    '2019-08-12', # <PERSON><PERSON><PERSON>
    '2019-08-15', # Independence Day
    '2019-09-02', # <PERSON><PERSON><PERSON>
    '2019-10-02', # <PERSON>
    '2019-10-08', # <PERSON><PERSON><PERSON>
    '2019-10-28', # <PERSON><PERSON><PERSON> (La<PERSON><PERSON>)
    '2019-11-12', # <PERSON><PERSON><PERSON>
    '2019-12-25', # Christmas

    # 2020
    '2020-02-21', # <PERSON><PERSON><PERSON><PERSON><PERSON>
    '2020-03-10', # <PERSON><PERSON>
    '2020-04-02', # <PERSON>
    '2020-04-06', # <PERSON><PERSON><PERSON>
    '2020-04-10', # Good Friday
    '2020-05-01', # <PERSON>
    '2020-05-25', # <PERSON><PERSON>-ul-<PERSON>tr
    '2020-08-15', # Independence Day
    '2020-09-01', # <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>
    '2020-10-02', # <PERSON> <PERSON><PERSON>
    '2020-11-14', # Di<PERSON>i (<PERSON>x<PERSON> Pu<PERSON>)
    '2020-11-16', # <PERSON><PERSON>i Bali<PERSON><PERSON><PERSON>
    '2020-11-30', # <PERSON>nanak <PERSON><PERSON>
    '2020-12-25', # Christmas

    # 2021
    '2021-01-26', # Republic Day
    '2021-03-11', # Mahashivratri
    '2021-03-29', # Holi
    '2021-04-02', # Good Friday
    '2021-04-14', # Dr. Ambedkar Jayanti
    '2021-04-21', # Ram Navami
    '2021-05-13', # Eid-ul-Fitr
    '2021-07-21', # Bakri Eid
    '2021-08-19', # Muharram
    '2021-09-10', # Ganesh Chaturthi
    '2021-10-15', # Dussehra
    '2021-11-04', # Diwali (Laxmi Pujan)
    '2021-11-05', # Diwali Balipratipada
    '2021-11-19', # Gurunanak Jayanti

    # 2022
    '2022-01-26', # Republic Day
    '2022-03-01', # Mahashivratri
    '2022-03-18', # Holi
    '2022-04-14', # Dr. Ambedkar Jayanti
    '2022-04-15', # Good Friday
    '2022-05-03', # Eid-ul-Fitr
    '2022-08-09', # Muharram
    '2022-08-15', # Independence Day
    '2022-08-31', # Ganesh Chaturthi
    '2022-10-05', # Dussehra
    '2022-10-24', # Diwali (Laxmi Pujan)
    '2022-10-26', # Diwali Balipratipada
    '2022-11-08', # Gurunanak Jayanti

    # 2023
    '2023-01-26', # Republic Day
    '2023-03-07', # Holi
    '2023-04-04', # Mahavir Jayanti & Ram Navami
    '2023-04-07', # Good Friday
    '2023-04-14', # Dr. Ambedkar Jayanti
    '2023-04-22', # Eid-ul-Fitr
    '2023-05-01', # Maharashtra Day
    '2023-06-29', # Bakri Eid
    '2023-08-15', # Independence Day
    '2023-09-19', # Ganesh Chaturthi
    '2023-10-02', # Gandhi Jayanti
    '2023-10-24', # Dussehra
    '2023-11-12', # Diwali-Laxmi Pujan
    '2023-11-14', # Diwali Balipratipada
    '2023-11-27', # Gurunanak Jayanti
    '2023-12-25', # Christmas

    # 2024
    '2024-01-26', # Republic Day
    '2024-03-08', # Maha Shivaratri
    '2024-03-25', # Holi
    '2024-03-29', # Good Friday
    '2024-04-10', # Id-ul-Fitr (Ramzan ID)
    '2024-04-17', # Ram Navami
    '2024-05-01', # Maharashtra Day
    '2024-06-17', # Bakri Id / Eid ul-Adha
    '2024-07-17', # Muharram
    '2024-08-15', # Independence Day
    '2024-09-07', # Ganesh Chaturthi
    '2024-10-02', # Mahatma Gandhi Jayanti
    '2024-10-13', # Dasara
    '2024-11-01', # Diwali-Laxmi Pujan
    '2024-11-02', # Diwali-Balipratipada
    '2024-11-15', # Guru Nanak Jayanti
    '2024-12-25', # Christmas

    # 2025
    '2025-01-26', # Republic Day 
    '2025-02-26', # Maha Shivaratri
    '2025-03-14', # Holi
    '2025-03-31', # Eid-Ul-Fitr
    '2025-04-10', # Mahavir Jayanti
    '2025-04-14', # Dr. Baba Saheb Ambedkar Jayanti
    '2025-04-18', # Good Friday
    '2025-05-01', # Maharashtra Day
    '2025-06-07', # Bakri Eid
    '2025-07-06', # Muharram
    '2025-08-15', # Independence Day
    '2025-08-27', # Ganesh Chaturthi
    '2025-10-02', # Mahatma Gandhi Jayanti
    '2025-10-21', # Diwali-Laxmi Pujan
    '2025-10-22', # Diwali-Balipratipada
    '2025-11-05', # Gurunanak Jayanti
    '2025-12-25', # Christmas

    # 2026 (Based on available information)
    '2026-01-26', # Republic Day
    '2026-02-15', # Maha Shivaratri (estimated)
    '2026-03-05', # Holi (estimated)
    '2026-04-03', # Good Friday (estimated)
    '2026-04-14', # Dr. Ambedkar Jayanti
    '2026-05-01', # Maharashtra Day
    '2026-08-15', # Independence Day
    '2026-10-02', # Gandhi Jayanti
    '2026-11-10', # Diwali (estimated)
    '2026-11-11', # Diwali Balipratipada (estimated)
    '2026-12-25', # Christmas
]

def is_weekend(day):
    """Check if a date is a weekend (Saturday or Sunday)"""
    return day.weekday() >= 5  # 5 = Saturday, 6 = Sunday

def generate_weekend_dates(start_date, end_date):
    """Generate all weekend dates between start_date and end_date"""
    weekend_dates = []
    current_date = start_date
    
    while current_date <= end_date:
        if is_weekend(current_date):
            weekend_dates.append(current_date.strftime('%Y-%m-%d'))
        current_date += datetime.timedelta(days=1)
    
    return weekend_dates

def main():
    """Main function to generate the holiday CSV file"""
    # Convert string dates to date objects
    holiday_dates = [datetime.datetime.strptime(d, '%Y-%m-%d').date() for d in NSE_HOLIDAYS]
    
    # Generate weekend dates
    start_date = datetime.date(2019, 1, 1)
    end_date = datetime.date(2026, 12, 31)
    weekend_dates = generate_weekend_dates(start_date, end_date)
    
    # Combine holidays and weekends
    all_non_trading_days = NSE_HOLIDAYS + weekend_dates
    all_non_trading_days = sorted(list(set(all_non_trading_days)))  # Remove duplicates and sort
    
    # Write to CSV
    with open('nse_non_trading_days.csv', 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['date', 'reason'])
        
        for day in all_non_trading_days:
            if day in NSE_HOLIDAYS:
                reason = 'Holiday'
            else:
                reason = 'Weekend'
            writer.writerow([day, reason])
    
    print(f"Generated CSV file with {len(all_non_trading_days)} non-trading days from 2019 to 2026")

if __name__ == "__main__":
    main() 