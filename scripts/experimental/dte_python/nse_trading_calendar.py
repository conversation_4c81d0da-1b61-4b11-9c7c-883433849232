"""
NSE Trading Calendar Utility
- Provides trading days (excludes weekends and official NSE holidays)
- Calculates DTE (Days to Expiry) as number of trading days between two dates
"""
import pandas as pd
from typing import List

# Example: Official NSE holidays for 2024 (extend as needed)
NSE_HOLIDAYS_2024 = [
    '2024-01-26', '2024-03-08', '2024-03-25', '2024-03-29', '2024-04-11',
    '2024-04-17', '2024-05-01', '2024-06-17', '2024-07-17', '2024-08-15',
    '2024-10-02', '2024-11-01', '2024-11-15', '2024-12-25'
    # ...add all official holidays for the year
]


def get_nse_trading_days(start_date: str, end_date: str, holidays: List[str]) -> pd.DatetimeIndex:
    """
    Generate all NSE trading days (weekdays minus holidays) between start_date and end_date (inclusive).
    Args:
        start_date: Start date as 'YYYY-MM-DD' or 'YYYYMMDD'
        end_date: End date as 'YYYY-MM-DD' or 'YYYYMMDD'
        holidays: List of holiday dates as strings
    Returns:
        DatetimeIndex of trading days
    """
    # Normalize date format
    start = pd.to_datetime(start_date)
    end = pd.to_datetime(end_date)
    all_weekdays = pd.bdate_range(start=start, end=end)
    holidays_dt = pd.to_datetime(holidays)
    trading_days = all_weekdays.difference(holidays_dt)
    return trading_days


def trading_day_dte(trade_date: pd.Timestamp, expiry_date: pd.Timestamp, trading_days: pd.DatetimeIndex) -> int:
    """
    Calculate DTE (Days to Expiry) as number of trading days between trade_date and expiry_date (inclusive).
    Args:
        trade_date: Trade date as pd.Timestamp
        expiry_date: Expiry date as pd.Timestamp
        trading_days: DatetimeIndex of trading days
    Returns:
        Integer DTE (trading days)
    """
    mask = (trading_days >= trade_date) & (trading_days <= expiry_date)
    return mask.sum() 