#!/usr/bin/env python
"""
Utility script to update the trading_day_dte column in the nifty_greeks table.

This script:
1. Connects to Supabase using environment variables
2. Fetches unique trade_date and expiry_date pairs from nifty_greeks
3. Calculates trading_day_dte using the NSE trading calendar
4. Updates the database in batches
5. Verifies the updates

Usage:
    python -m src.data.update_trading_day_dte

Environment variables:
    SUPABASE_URL: URL of the Supabase instance
    SUPABASE_KEY: API key for the Supabase instance
"""

import os
import time
import logging
from typing import Dict, List, Tuple, Any
from datetime import datetime

import pandas as pd
from tqdm import tqdm
from dotenv import load_dotenv
from supabase import create_client, Client

from src.data.nse_trading_calendar import get_nse_trading_days, trading_day_dte, NSE_HOLIDAYS_2024

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("trading_day_dte_update.log")
    ]
)

logger = logging.getLogger(__name__)

def fetch_unique_date_pairs(client: Client) -> pd.DataFrame:
    """
    Fetch all unique trade_date and expiry_date pairs from nifty_greeks.
    
    Args:
        client: Supabase client
        
    Returns:
        DataFrame with unique trade_date and expiry_date pairs
    """
    logger.info("Fetching unique date pairs...")
    
    try:
        # First, check if trading_day_dte column exists
        response = client.table("nifty_greeks").select("*").limit(1).execute()
        sample_row = response.data[0] if response.data else {}
        
        if "trading_day_dte" not in sample_row:
            logger.info("Adding trading_day_dte column to nifty_greeks table...")
            # Add the column if it doesn't exist
            client.table("nifty_greeks").alter().add("trading_day_dte", "int4").execute()
            logger.info("Column added successfully")
    except Exception as e:
        logger.error(f"Error checking/adding column: {e}")
        
    try:
        # Query for unique date pairs
        result = client.table("nifty_greeks") \
            .select("trade_date, expiry_date") \
            .execute()
        
        if not result.data:
            logger.warning("No data found in nifty_greeks table")
            return pd.DataFrame(columns=["trade_date", "expiry_date"])
        
        # Convert to DataFrame and drop duplicates
        df = pd.DataFrame(result.data)
        df["trade_date"] = pd.to_datetime(df["trade_date"])
        df["expiry_date"] = pd.to_datetime(df["expiry_date"])
        
        unique_pairs = df.drop_duplicates(subset=["trade_date", "expiry_date"])
        logger.info(f"Found {len(unique_pairs)} unique date pairs")
        
        return unique_pairs
    
    except Exception as e:
        logger.error(f"Error fetching unique date pairs: {e}")
        return pd.DataFrame(columns=["trade_date", "expiry_date"])

def calculate_dte_for_pairs(date_pairs: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate trading day DTE for each date pair.
    
    Args:
        date_pairs: DataFrame with trade_date and expiry_date columns
        
    Returns:
        DataFrame with trade_date, expiry_date and trading_day_dte columns
    """
    logger.info("Calculating trading day DTE for date pairs...")
    
    if date_pairs.empty:
        logger.warning("No date pairs to calculate DTE for")
        return pd.DataFrame(columns=["trade_date", "expiry_date", "trading_day_dte"])
    
    # Get the min and max dates for creating the trading day calendar
    min_date = min(date_pairs["trade_date"].min(), date_pairs["expiry_date"].min())
    max_date = max(date_pairs["trade_date"].max(), date_pairs["expiry_date"].max())
    
    # Get trading days
    trading_days = get_nse_trading_days(min_date, max_date, NSE_HOLIDAYS_2024)
    
    # Calculate DTE for each pair
    result_data = []
    for _, row in tqdm(date_pairs.iterrows(), total=len(date_pairs), desc="Calculating DTE"):
        trade_date = row["trade_date"]
        expiry_date = row["expiry_date"]
        
        dte = trading_day_dte(trade_date, expiry_date, trading_days)
        
        result_data.append({
            "trade_date": trade_date.strftime("%Y-%m-%d"),
            "expiry_date": expiry_date.strftime("%Y-%m-%d"),
            "trading_day_dte": dte
        })
    
    result_df = pd.DataFrame(result_data)
    
    # Print some statistics
    logger.info(f"DTE calculation completed for {len(result_df)} pairs")
    logger.info(f"DTE statistics: min={result_df['trading_day_dte'].min()}, max={result_df['trading_day_dte'].max()}, mean={result_df['trading_day_dte'].mean():.2f}")
    
    return result_df

def update_database_in_batches(client: Client, date_pairs: pd.DataFrame, batch_size: int = 100) -> Tuple[int, int]:
    """
    Update the trading_day_dte column in the nifty_greeks table in batches.
    
    Args:
        client: Supabase client
        date_pairs: DataFrame with trade_date, expiry_date and trading_day_dte columns
        batch_size: Number of updates to perform in each batch
        
    Returns:
        Tuple of (successful_updates, failed_updates)
    """
    logger.info(f"Updating database in batches of {batch_size}...")
    
    if date_pairs.empty:
        logger.warning("No date pairs to update")
        return (0, 0)
    
    successful_updates = 0
    failed_updates = 0
    
    # Process in batches
    for i in tqdm(range(0, len(date_pairs), batch_size), desc="Updating batches"):
        batch = date_pairs.iloc[i:i+batch_size]
        
        for _, row in batch.iterrows():
            try:
                # Update all records with this date pair
                result = client.table("nifty_greeks") \
                    .update({"trading_day_dte": int(row["trading_day_dte"])}) \
                    .eq("trade_date", row["trade_date"]) \
                    .eq("expiry_date", row["expiry_date"]) \
                    .execute()
                
                # Count successful updates
                successful_updates += 1
                
            except Exception as e:
                logger.error(f"Error updating date pair {row['trade_date']} to {row['expiry_date']}: {e}")
                failed_updates += 1
        
        # Sleep briefly between batches to avoid rate limits
        time.sleep(0.1)
    
    logger.info(f"Database update completed: {successful_updates} successful, {failed_updates} failed")
    return (successful_updates, failed_updates)

def verify_updates(client: Client) -> Dict[str, Any]:
    """
    Verify the updates by checking the distribution of trading_day_dte values.
    
    Args:
        client: Supabase client
        
    Returns:
        Dictionary with verification results
    """
    logger.info("Verifying updates...")
    
    try:
        # Count rows with non-null trading_day_dte
        updated_count_result = client.table("nifty_greeks") \
            .select("count(*)", count="exact") \
            .not_("trading_day_dte", "is", "null") \
            .execute()
        
        # Count total rows
        total_count_result = client.table("nifty_greeks") \
            .select("count(*)", count="exact") \
            .execute()
        
        updated_count = updated_count_result.count if hasattr(updated_count_result, 'count') else 0
        total_count = total_count_result.count if hasattr(total_count_result, 'count') else 0
        
        # Get distribution of DTE values
        dte_dist_result = client.table("nifty_greeks") \
            .select("trading_day_dte, count(*)") \
            .not_("trading_day_dte", "is", "null") \
            .group("trading_day_dte") \
            .order("trading_day_dte") \
            .execute()
        
        dte_distribution = {}
        if dte_dist_result.data:
            for item in dte_dist_result.data:
                dte_distribution[item["trading_day_dte"]] = item["count"]
        
        results = {
            "updated_count": updated_count,
            "total_count": total_count,
            "percentage_updated": (updated_count / total_count * 100) if total_count > 0 else 0,
            "dte_distribution": dte_distribution
        }
        
        logger.info(f"Verification results: {updated_count}/{total_count} rows updated ({results['percentage_updated']:.2f}%)")
        return results
    
    except Exception as e:
        logger.error(f"Error verifying updates: {e}")
        return {
            "updated_count": 0,
            "total_count": 0,
            "percentage_updated": 0,
            "dte_distribution": {},
            "error": str(e)
        }

def main():
    """Main function to update trading_day_dte in nifty_greeks table."""
    logger.info("Starting trading_day_dte update process")
    
    # Load environment variables
    load_dotenv()
    
    # Check for required environment variables
    supabase_url = os.environ.get("SUPABASE_URL")
    supabase_key = os.environ.get("SUPABASE_KEY")
    
    if not supabase_url or not supabase_key:
        logger.error("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
        return
    
    # Create Supabase client
    client = create_client(supabase_url, supabase_key)
    
    # Process steps
    try:
        # 1. Fetch unique date pairs
        date_pairs = fetch_unique_date_pairs(client)
        
        if date_pairs.empty:
            logger.warning("No date pairs found, aborting")
            return
        
        # 2. Calculate DTE for each pair
        dte_data = calculate_dte_for_pairs(date_pairs)
        
        # 3. Update database in batches
        successful, failed = update_database_in_batches(client, dte_data)
        
        # 4. Verify updates
        verification = verify_updates(client)
        
        # 5. Generate summary
        logger.info("===== Update Summary =====")
        logger.info(f"Total date pairs processed: {len(date_pairs)}")
        logger.info(f"Successful updates: {successful}")
        logger.info(f"Failed updates: {failed}")
        logger.info(f"Rows with trading_day_dte: {verification['updated_count']}/{verification['total_count']} ({verification['percentage_updated']:.2f}%)")
        logger.info("=========================")
        
    except Exception as e:
        logger.error(f"Error in main process: {e}")
    
    logger.info("Trading_day_dte update process completed")

if __name__ == "__main__":
    main() 