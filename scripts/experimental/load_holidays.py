#!/usr/bin/env python3
"""
NSE Holiday Loader

This script loads NSE trading holidays directly into HeavyDB
bypassing the need for CSV whitelisting.
"""

import sys
import pymapd
import pandas as pd
from datetime import date

def connect_to_heavydb():
    """Connect to HeavyDB database"""
    try:
        conn = pymapd.connect(
            host="127.0.0.1",
            port=6274,
            user="admin",
            password="HyperInteractive",
            dbname="heavyai"
        )
        print(f"Connected to HeavyDB: {conn.server_version}")
        return conn
    except Exception as e:
        print(f"Error connecting to HeavyDB: {str(e)}")
        sys.exit(1)

def setup_holiday_table(conn):
    """Create the holiday table if it doesn't exist"""
    try:
        # Drop existing table if needed
        conn.execute("DROP TABLE IF EXISTS nse_holidays;")
        
        # Create the table
        conn.execute("""
        CREATE TABLE nse_holidays (
            holiday_date DATE NOT NULL,
            reason TEXT NOT NULL
        );
        """)
        print("Created nse_holidays table")
        return True
    except Exception as e:
        print(f"Error creating holiday table: {str(e)}")
        return False

def get_nse_holidays():
    """Get the NSE holidays from 2019 to 2026"""
    # Define the NSE holidays from 2019 to 2026
    # Format: 'YYYY-MM-DD' (ISO format)
    NSE_HOLIDAYS = [
        # 2019
        '2019-01-26', # Republic Day
        '2019-03-04', # Mahashivratri
        '2019-03-21', # Holi
        '2019-04-17', # Mahavir Jayanti
        '2019-04-19', # Good Friday
        '2019-05-01', # Maharashtra Day
        '2019-06-05', # Eid-ul-Fitr
        '2019-08-12', # Bakri Eid
        '2019-08-15', # Independence Day
        '2019-09-02', # Ganesh Chaturthi
        '2019-10-02', # Gandhi Jayanti
        '2019-10-08', # Dussehra
        '2019-10-28', # Diwali (Laxmi Pujan)
        '2019-11-12', # Gurunanak Jayanti
        '2019-12-25', # Christmas

        # 2020
        '2020-02-21', # Mahashivratri
        '2020-03-10', # Holi
        '2020-04-02', # Ram Navami
        '2020-04-06', # Mahavir Jayanti
        '2020-04-10', # Good Friday
        '2020-05-01', # Maharashtra Day
        '2020-05-25', # Eid-ul-Fitr
        '2020-08-15', # Independence Day
        '2020-09-01', # Ganesh Chaturthi
        '2020-10-02', # Gandhi Jayanti
        '2020-11-14', # Diwali (Laxmi Pujan)
        '2020-11-16', # Diwali Balipratipada
        '2020-11-30', # Gurunanak Jayanti
        '2020-12-25', # Christmas

        # 2021
        '2021-01-26', # Republic Day
        '2021-03-11', # Mahashivratri
        '2021-03-29', # Holi
        '2021-04-02', # Good Friday
        '2021-04-14', # Dr. Ambedkar Jayanti
        '2021-04-21', # Ram Navami
        '2021-05-13', # Eid-ul-Fitr
        '2021-07-21', # Bakri Eid
        '2021-08-19', # Muharram
        '2021-09-10', # Ganesh Chaturthi
        '2021-10-15', # Dussehra
        '2021-11-04', # Diwali (Laxmi Pujan)
        '2021-11-05', # Diwali Balipratipada
        '2021-11-19', # Gurunanak Jayanti

        # 2022
        '2022-01-26', # Republic Day
        '2022-03-01', # Mahashivratri
        '2022-03-18', # Holi
        '2022-04-14', # Dr. Ambedkar Jayanti
        '2022-04-15', # Good Friday
        '2022-05-03', # Eid-ul-Fitr
        '2022-08-09', # Muharram
        '2022-08-15', # Independence Day
        '2022-08-31', # Ganesh Chaturthi
        '2022-10-05', # Dussehra
        '2022-10-24', # Diwali (Laxmi Pujan)
        '2022-10-26', # Diwali Balipratipada
        '2022-11-08', # Gurunanak Jayanti

        # 2023
        '2023-01-26', # Republic Day
        '2023-03-07', # Holi
        '2023-04-04', # Mahavir Jayanti & Ram Navami
        '2023-04-07', # Good Friday
        '2023-04-14', # Dr. Ambedkar Jayanti
        '2023-04-22', # Eid-ul-Fitr
        '2023-05-01', # Maharashtra Day
        '2023-06-29', # Bakri Eid
        '2023-08-15', # Independence Day
        '2023-09-19', # Ganesh Chaturthi
        '2023-10-02', # Gandhi Jayanti
        '2023-10-24', # Dussehra
        '2023-11-12', # Diwali-Laxmi Pujan
        '2023-11-14', # Diwali Balipratipada
        '2023-11-27', # Gurunanak Jayanti
        '2023-12-25', # Christmas

        # 2024
        '2024-01-26', # Republic Day
        '2024-03-08', # Maha Shivaratri
        '2024-03-25', # Holi
        '2024-03-29', # Good Friday
        '2024-04-11', # Id-ul-Fitr (Ramzan ID)
        '2024-04-17', # Ram Navami
        '2024-05-01', # Maharashtra Day
        '2024-06-17', # Bakri Id / Eid ul-Adha
        '2024-07-17', # Muharram
        '2024-08-15', # Independence Day
        '2024-10-02', # Mahatma Gandhi Jayanti
        '2024-10-31', # Diwali-Laxmi Pujan
        '2024-11-15', # Guru Nanak Jayanti
        '2024-12-25', # Christmas

        # 2025
        '2025-01-26', # Republic Day 
        '2025-02-26', # Maha Shivaratri
        '2025-03-14', # Holi
        '2025-03-31', # Eid-Ul-Fitr
        '2025-04-18', # Good Friday
        '2025-05-01', # Maharashtra Day
        '2025-06-07', # Bakri Eid
        '2025-07-06', # Muharram
        '2025-08-15', # Independence Day
        '2025-08-27', # Ganesh Chaturthi
        '2025-10-02', # Mahatma Gandhi Jayanti
        '2025-10-20', # Diwali-Laxmi Pujan
        '2025-10-22', # Diwali-Balipratipada
        '2025-11-05', # Gurunanak Jayanti
        '2025-12-25', # Christmas

        # 2026 (Based on available information)
        '2026-01-26', # Republic Day
        '2026-02-15', # Maha Shivaratri (estimated)
        '2026-03-05', # Holi (estimated)
        '2026-04-03', # Good Friday (estimated)
        '2026-04-14', # Dr. Ambedkar Jayanti
        '2026-05-01', # Maharashtra Day
        '2026-08-15', # Independence Day
        '2026-10-02', # Gandhi Jayanti
        '2026-11-10', # Diwali (estimated)
        '2026-11-11', # Diwali Balipratipada (estimated)
        '2026-12-25', # Christmas
    ]
    
    # Prepare dataframe with dates and reasons
    holiday_data = []
    for h_date in NSE_HOLIDAYS:
        holiday_data.append({'holiday_date': h_date, 'reason': 'Holiday'})
    
    return pd.DataFrame(holiday_data)

def insert_holidays(conn, holidays_df):
    """Insert holidays into the database"""
    try:
        # Create table first
        if not setup_holiday_table(conn):
            return False
            
        # Insert records using pymapd
        conn.load_table("nse_holidays", holidays_df)
        
        # Verify the insert
        result = conn.execute("SELECT COUNT(*) AS holiday_count FROM nse_holidays;")
        count = result.fetchone()[0]
        print(f"Loaded {count} holidays into the database")
        return True
    except Exception as e:
        print(f"Error inserting holidays: {str(e)}")
        return False

def main():
    """Main function to load holidays"""
    # Connect to the database
    conn = connect_to_heavydb()
    
    # Get the holidays
    holidays_df = get_nse_holidays()
    print(f"Prepared {len(holidays_df)} NSE holidays from 2019 to 2026")
    
    # Insert the holidays
    success = insert_holidays(conn, holidays_df)
    
    # Close the connection
    conn.close()
    
    if success:
        print("Successfully loaded NSE holidays into HeavyDB")
    else:
        print("Failed to load holidays completely")

if __name__ == "__main__":
    main() 