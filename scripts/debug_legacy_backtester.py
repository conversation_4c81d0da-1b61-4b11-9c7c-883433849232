#!/usr/bin/env python3
"""
Debug version of the legacy backtester patch with extensive logging
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime
import glob
import re

def create_debug_mysql_patch():
    """Create a debug patch with extensive logging"""
    
    mysql_getBacktestResults = '''
    @staticmethod
    def getBacktestResults(btPara: dict) -> dict:
        """PATCHED VERSION - Uses direct MySQL queries with debug logging"""
        
        import mysql.connector as mysql
        from datetime import datetime, timedelta
        import numpy as np
        import json
        
        # Create debug log file
        debug_log = open("/srv/samba/shared/legacy_debug.log", "w")
        debug_log.write(f"=== LEGACY BACKTESTER DEBUG LOG ===\\n")
        debug_log.write(f"Start time: {datetime.now()}\\n\\n")
        
        startTime = datetime.now()
        logging.info(f"{startTime}, Backtesting Portfolio: {btPara['portfolio']['id']} (DIRECT MYSQL MODE)")
        
        debug_log.write(f"Portfolio ID: {btPara['portfolio']['id']}\\n")
        debug_log.write(f"Portfolio Name: {btPara['portfolio']['name']}\\n")
        debug_log.write(f"Start Date: {btPara.get('start_date', 'MISSING')}\\n")
        debug_log.write(f"End Date: {btPara.get('end_date', 'MISSING')}\\n")
        
        # Write full btPara to debug
        debug_log.write(f"\\nFull btPara:\\n{json.dumps(btPara, indent=2, default=str)}\\n\\n")
        
        try:
            # Connect to MySQL
            mydb = mysql.connect(
                host="************",
                user="mahesh", 
                password="mahesh_123",
                database="historicaldb"
            )
            cursor = mydb.cursor()
            debug_log.write("✓ MySQL connection successful\\n")
            
            # Extract date range
            start_date = btPara.get('start_date', '31_12_2024')
            end_date = btPara.get('end_date', '31_12_2024')
            
            debug_log.write(f"Raw dates: start={start_date}, end={end_date}\\n")
            
            # Convert date format
            if '_' in start_date:
                start_date = datetime.strptime(start_date, '%d_%m_%Y').strftime('%Y-%m-%d')
                end_date = datetime.strptime(end_date, '%d_%m_%Y').strftime('%Y-%m-%d')
            
            debug_log.write(f"Converted dates: start={start_date}, end={end_date}\\n")
            
            orders = []
            total_pnl = 0
            
            # Process each strategy
            strategies = btPara.get('portfolio', {}).get('strategies', [])
            debug_log.write(f"\\nNumber of strategies: {len(strategies)}\\n")
            
            for strat_idx, strategy in enumerate(strategies):
                strategy_name = strategy.get('name', 'Strategy')
                debug_log.write(f"\\nProcessing strategy {strat_idx}: {strategy_name}\\n")
                
                # Process each leg
                legs = strategy.get('legs', [])
                debug_log.write(f"  Number of legs: {len(legs)}\\n")
                
                for leg_idx, leg in enumerate(legs):
                    debug_log.write(f"\\n  Processing leg {leg_idx}:\\n")
                    debug_log.write(f"    Status: {leg.get('status', 'MISSING')}\\n")
                    
                    if leg.get('status', '') != 'ACTIVE':
                        debug_log.write(f"    Skipping inactive leg\\n")
                        continue
                    
                    # Get leg parameters
                    instrument_type = leg.get('instrument_type', 'CALL').upper()
                    position_type = leg.get('position_type', 'BUY').upper()
                    strike_method = leg.get('strike_selection', {}).get('method', 'ATM')
                    strike_value = leg.get('strike_selection', {}).get('value', 0)
                    lots = leg.get('lots', 1)
                    lot_size = 50  # NIFTY lot size
                    
                    debug_log.write(f"    Instrument: {instrument_type}\\n")
                    debug_log.write(f"    Position: {position_type}\\n")
                    debug_log.write(f"    Strike method: {strike_method}\\n")
                    debug_log.write(f"    Lots: {lots}\\n")
                    
                    # Get entry/exit times from strategy
                    entry_time = strategy.get('entry_start', '09:16:00')
                    exit_time = strategy.get('entry_end', '15:30:00')
                    
                    if ':' not in str(entry_time):
                        # Convert HHMMSS to HH:MM:SS
                        entry_time = f"{str(entry_time)[:2]}:{str(entry_time)[2:4]}:{str(entry_time)[4:6]}"
                        exit_time = f"{str(exit_time)[:2]}:{str(exit_time)[2:4]}:{str(exit_time)[4:6]}"
                    
                    debug_log.write(f"    Entry time: {entry_time}\\n")
                    debug_log.write(f"    Exit time: {exit_time}\\n")
                    
                    # Get underlying price at entry time to determine ATM
                    query = f"""
                        SELECT close/100.0 as price, time
                        FROM nifty_cash
                        WHERE DATE(date) = '{start_date}'
                        AND time >= (TIME_TO_SEC('{entry_time}'))
                        ORDER BY time ASC
                        LIMIT 1
                    """
                    debug_log.write(f"    Cash query: {query}\\n")
                    cursor.execute(query)
                    result = cursor.fetchone()
                    
                    if not result:
                        debug_log.write(f"    ✗ No cash data found\\n")
                        continue
                        
                    underlying_price = result[0]
                    entry_time_seconds = result[1]
                    debug_log.write(f"    ✓ Underlying price: {underlying_price}\\n")
                    
                    # Calculate ATM strike
                    atm_strike = round(underlying_price / 50) * 50
                    debug_log.write(f"    ATM strike: {atm_strike}\\n")
                    
                    # Determine actual strike based on method
                    if strike_method == 'ATM':
                        strike = atm_strike
                    elif strike_method.startswith('OTM'):
                        otm_level = int(strike_method[3:]) if len(strike_method) > 3 else 1
                        if instrument_type == 'CALL':
                            strike = atm_strike + (50 * otm_level)
                        else:
                            strike = atm_strike - (50 * otm_level)
                    elif strike_method.startswith('ITM'):
                        itm_level = int(strike_method[3:]) if len(strike_method) > 3 else 1
                        if instrument_type == 'CALL':
                            strike = atm_strike - (50 * itm_level)
                        else:
                            strike = atm_strike + (50 * itm_level)
                    else:
                        strike = strike_value if strike_value > 0 else atm_strike
                    
                    debug_log.write(f"    Selected strike: {strike}\\n")
                    
                    # Get option prices
                    table_name = f"nifty_{instrument_type.lower()}"
                    
                    # Get entry price
                    query = f"""
                        SELECT close/100.0 as price, time
                        FROM {table_name}
                        WHERE DATE(date) = '{start_date}'
                        AND strike = {strike}
                        AND time >= {entry_time_seconds}
                        ORDER BY time ASC
                        LIMIT 1
                    """
                    debug_log.write(f"    Entry price query: {query[:100]}...\\n")
                    cursor.execute(query)
                    entry_result = cursor.fetchone()
                    
                    if not entry_result:
                        debug_log.write(f"    ✗ No entry price found\\n")
                        continue
                        
                    entry_price = entry_result[0]
                    debug_log.write(f"    ✓ Entry price: {entry_price}\\n")
                    
                    # Get exit price
                    exit_time_seconds = int(exit_time[:2]) * 3600 + int(exit_time[3:5]) * 60 + int(exit_time[6:8])
                    
                    query = f"""
                        SELECT close/100.0 as price, time
                        FROM {table_name}
                        WHERE DATE(date) = '{start_date}'
                        AND strike = {strike}
                        AND time <= {exit_time_seconds}
                        ORDER BY time DESC
                        LIMIT 1
                    """
                    cursor.execute(query)
                    exit_result = cursor.fetchone()
                    
                    if not exit_result:
                        exit_price = entry_price  # Use entry price if no exit found
                        debug_log.write(f"    ⚠ No exit price, using entry: {exit_price}\\n")
                    else:
                        exit_price = exit_result[0]
                        debug_log.write(f"    ✓ Exit price: {exit_price}\\n")
                    
                    # Calculate P&L
                    qty = lots * lot_size
                    if position_type == 'SELL':
                        points = entry_price - exit_price
                    else:
                        points = exit_price - entry_price
                        
                    pnl = points * qty
                    total_pnl += pnl
                    debug_log.write(f"    P&L: {pnl} (points: {points}, qty: {qty})\\n")
                    
                    # Create order record
                    order = {
                        "entry_time": f"Tue, 31 Dec 2024 {entry_time} GMT",
                        "exit_time": f"Tue, 31 Dec 2024 {exit_time} GMT",
                        "option_type": instrument_type,
                        "qty": qty,
                        "entry_number": 1,
                        "strategy_name": strategy_name,
                        "side": position_type,
                        "entry_price": entry_price,
                        "exit_price": exit_price,
                        "symbol": "NIFTY",
                        "strike": strike,
                        "expiry": "250102",  # Using nearest Thursday
                        "leg_id": str(leg.get('id', leg_idx + 1)),
                        "index_entry_price": underlying_price,
                        "index_exit_price": underlying_price,  # Simplified
                        "reason": "EndTime",
                        "stop_loss_entry_number": 0,
                        "take_profit_entry_number": 0,
                        "strategy_entry_number": 1,
                        "pnl": pnl
                    }
                    orders.append(order)
                    debug_log.write(f"    ✓ Order created\\n")
            
            cursor.close()
            mydb.close()
            
            debug_log.write(f"\\nTotal orders created: {len(orders)}\\n")
            debug_log.write(f"Total P&L: {total_pnl}\\n")
            
            # Build response
            respp = {
                "strategies": {
                    "orders": orders,
                    "strategy_profits": {
                        "241231": {
                            "34560": max(0, total_pnl),  # 09:36:00
                            "43200": max(0, total_pnl)   # 12:00:00
                        }
                    },
                    "strategy_losses": {
                        "241231": {
                            "34560": min(0, total_pnl),
                            "43200": min(0, total_pnl)
                        }
                    }
                }
            }
            
            # Add portfolio name to orders
            if orders:
                orderss = pd.DataFrame(respp['strategies']['orders'])
                orderss['portfolio_name'] = btPara['portfolio']['name']
                respp['strategies']['orders'] = orderss.to_dict("records")
                debug_log.write(f"✓ Added portfolio name to orders\\n")
            
            endTime = datetime.now()
            durationn = round((endTime-startTime).total_seconds(), 2)
            logging.info(f"{endTime}, Completed MySQL backtesting portfolio: {btPara['portfolio']['id']}, Time taken: {durationn} seconds")
            
            debug_log.write(f"\\nCompleted successfully in {durationn} seconds\\n")
            debug_log.write(f"\\nFinal response structure:\\n")
            debug_log.write(f"  - orders: {len(respp['strategies']['orders'])}\\n")
            debug_log.write(f"  - strategy_profits keys: {list(respp['strategies']['strategy_profits'].keys())}\\n")
            
            debug_log.close()
            return respp
            
        except Exception as e:
            debug_log.write(f"\\n✗ ERROR: {str(e)}\\n")
            import traceback
            debug_log.write(traceback.format_exc())
            debug_log.close()
            raise
'''
    
    # Read original Util.py
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    
    # First restore original if backup exists
    backup_path = util_path + ".original"
    if os.path.exists(backup_path):
        shutil.copy2(backup_path, util_path)
        print("  ✓ Restored original Util.py before patching")
    
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Backup original
    if not os.path.exists(backup_path):
        with open(backup_path, 'w') as f:
            f.write(content)
        print(f"  ✓ Backed up original Util.py")
    
    # Find and replace the getBacktestResults method
    pattern = r'@staticmethod\s+def getBacktestResults\(btPara: dict\) -> dict:.*?(?=\n    @staticmethod|\n    [A-Z]|\Z)'
    
    # Replace with our MySQL version
    patched_content = re.sub(pattern, mysql_getBacktestResults.strip(), content, flags=re.DOTALL)
    
    # Write patched file
    with open(util_path, 'w') as f:
        f.write(patched_content)
    
    print("  ✓ Patched Util.py with debug logging")

def setup_and_run_legacy():
    """Setup and run the legacy backtester"""
    
    legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
    
    # Setup input files
    input_sheets_dir = os.path.join(legacy_dir, "INPUT SHEETS")
    os.makedirs(input_sheets_dir, exist_ok=True)
    
    # Copy input files - use the updated Excel with correct date
    source_file = os.path.abspath("bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx")
    dest_file = os.path.join(input_sheets_dir, "INPUT PORTFOLIO.xlsx")
    
    if os.path.exists(source_file):
        shutil.copy2(source_file, dest_file)
        print(f"  ✓ Copied portfolio file")
        
        # Copy strategy files
        source_dir = os.path.dirname(source_file)
        for f in os.listdir(source_dir):
            if f.endswith('.xlsx'):
                shutil.copy2(os.path.join(source_dir, f), os.path.join(input_sheets_dir, f))
                print(f"    Copied: {f}")
    
    # Clear old outputs
    trades_dir = os.path.join(legacy_dir, "Trades")
    if os.path.exists(trades_dir):
        old_files = glob.glob(os.path.join(trades_dir, "*.xlsx"))
        for f in old_files:
            try:
                os.remove(f)
            except:
                pass
    
    print("\nRunning legacy backtester with debug logging...")
    
    # Run legacy backtester
    env = os.environ.copy()
    env['PYTHONPATH'] = f"{os.path.abspath('bt/archive/backtester_stable')}:{os.path.abspath('.')}"
    
    cmd = [sys.executable, os.path.join(legacy_dir, "BTRunPortfolio.py")]
    
    try:
        result = subprocess.run(
            cmd,
            cwd=legacy_dir,
            env=env,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        print(f"  Return code: {result.returncode}")
        
        if result.stdout:
            print(f"  Output: {result.stdout[:500]}")
        
        if result.stderr:
            print(f"  Errors: {result.stderr[:500]}")
        
        # Check debug log
        debug_log_path = "/srv/samba/shared/legacy_debug.log"
        if os.path.exists(debug_log_path):
            print("\n  Debug log created. Contents:")
            with open(debug_log_path, 'r') as f:
                print(f.read())
        
        # Check for output
        if os.path.exists(trades_dir):
            output_files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')]
            if output_files:
                print(f"\n  ✓ Legacy backtester created {len(output_files)} output file(s):")
                for f in output_files:
                    print(f"    - {f}")
                    
                # Copy to comparison directory
                os.makedirs("comparison_outputs", exist_ok=True)
                latest = max([os.path.join(trades_dir, f) for f in output_files], key=os.path.getctime)
                dest = "comparison_outputs/legacy_output_mysql_debug.xlsx"
                shutil.copy2(latest, dest)
                print(f"\n  ✓ Copied output to: {dest}")
                return True
        
        print("  ✗ No output files created")
        return False
        
    except Exception as e:
        print(f"  ✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("="*60)
    print("Debug Legacy Backtester with MySQL Integration")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    print("\nPatching Util.py with debug logging...")
    create_debug_mysql_patch()
    
    print("\nSetting up and running legacy backtester...")
    success = setup_and_run_legacy()
    
    print("\n" + "="*60)
    if success:
        print("✓ Legacy backtester completed!")
        print("  Check comparison_outputs/legacy_output_mysql_debug.xlsx")
    else:
        print("✗ Failed to run legacy backtester")
        print("  Check /srv/samba/shared/legacy_debug.log for details")
    print("="*60)

if __name__ == "__main__":
    main() 