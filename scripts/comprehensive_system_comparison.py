#!/usr/bin/env python3
"""Comprehensive system comparison with dynamic worker pool for multiple datasets."""

import sys
import os
import subprocess
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing
import time
import logging

sys.path.append('/srv/samba/shared')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BacktestRunner:
    """Run backtests for both legacy and current systems."""
    
    def __init__(self, portfolio_excel, legacy_url=None):
        self.portfolio_excel = portfolio_excel
        self.legacy_url = legacy_url or "http://************:5000"
        self.results = []
        
    def run_current_system(self, start_date, end_date, output_file):
        """Run the current HeavyDB-based backtester."""
        cmd = [
            "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
            "--legacy-excel",
            "--portfolio-excel", self.portfolio_excel,
            "--output-path", output_file,
            "--start-date", start_date.strftime("%Y%m%d"),
            "--end-date", end_date.strftime("%Y%m%d")
        ]
        
        env = {
            "PYTHONPATH": "/srv/samba/shared",
            "USE_LEGACY_FORMAT": "1"
        }
        
        try:
            start_time = time.time()
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd="/srv/samba/shared",
                env={**os.environ, **env}
            )
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                # Read the output Excel
                df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
                return {
                    'success': True,
                    'trades': len(df),
                    'total_pnl': df['pnl'].sum() if 'pnl' in df.columns else 0,
                    'execution_time': execution_time,
                    'dataframe': df
                }
            else:
                return {
                    'success': False,
                    'error': result.stderr,
                    'execution_time': execution_time
                }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'execution_time': 0
            }
    
    def run_legacy_system(self, start_date, end_date):
        """Run the legacy backtester (if accessible)."""
        try:
            import requests
            
            # Check if legacy service is accessible
            response = requests.get(f"{self.legacy_url}/healthcheck", timeout=5)
            if response.status_code != 200:
                return {'success': False, 'error': 'Legacy service not accessible'}
            
            # Run backtest
            request_data = {
                "portfolio_excel": os.path.basename(self.portfolio_excel),
                "start_date": start_date.strftime("%Y%m%d"),
                "end_date": end_date.strftime("%Y%m%d"),
                "portfolio_name": "NIF0DTE"
            }
            
            start_time = time.time()
            response = requests.post(
                f"{self.legacy_url}/run_backtest",
                json=request_data,
                timeout=300
            )
            execution_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                trades_df = pd.DataFrame(result.get('trades', []))
                return {
                    'success': True,
                    'trades': len(trades_df),
                    'total_pnl': trades_df['pnl'].sum() if not trades_df.empty and 'pnl' in trades_df.columns else 0,
                    'execution_time': execution_time,
                    'dataframe': trades_df
                }
            else:
                return {
                    'success': False,
                    'error': f'Legacy service returned {response.status_code}',
                    'execution_time': execution_time
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'Legacy system error: {str(e)}',
                'execution_time': 0
            }
    
    def compare_single_date(self, start_date, end_date):
        """Compare results for a single date range."""
        date_str = f"{start_date.strftime('%Y%m%d')}_to_{end_date.strftime('%Y%m%d')}"
        output_file = f"output_{date_str}.xlsx"
        
        logger.info(f"Testing date range: {start_date} to {end_date}")
        
        # Run current system
        current_result = self.run_current_system(start_date, end_date, output_file)
        
        # Run legacy system
        legacy_result = self.run_legacy_system(start_date, end_date)
        
        # Compare results
        comparison = {
            'date_range': f"{start_date} to {end_date}",
            'current_system': current_result,
            'legacy_system': legacy_result,
            'match': False,
            'differences': []
        }
        
        if current_result['success'] and legacy_result['success']:
            # Compare trade counts
            if current_result['trades'] != legacy_result['trades']:
                comparison['differences'].append(
                    f"Trade count mismatch: Current={current_result['trades']}, Legacy={legacy_result['trades']}"
                )
            
            # Compare P&L
            pnl_diff = abs(current_result['total_pnl'] - legacy_result['total_pnl'])
            if pnl_diff > 0.01:
                comparison['differences'].append(
                    f"P&L mismatch: Current={current_result['total_pnl']:.2f}, Legacy={legacy_result['total_pnl']:.2f}"
                )
            
            comparison['match'] = len(comparison['differences']) == 0
        
        # Clean up output file
        if os.path.exists(output_file):
            os.remove(output_file)
        
        return comparison

def generate_test_dates():
    """Generate a variety of test date ranges."""
    test_dates = []
    
    # Single day tests - different days of the week
    base_date = datetime(2025, 4, 1)  # Tuesday
    for i in range(7):  # Test each day of the week
        test_date = base_date + timedelta(days=i)
        test_dates.append((test_date, test_date))
    
    # Week-long tests
    test_dates.append((datetime(2025, 4, 1), datetime(2025, 4, 7)))
    test_dates.append((datetime(2025, 4, 8), datetime(2025, 4, 14)))
    
    # Month-long test
    test_dates.append((datetime(2025, 4, 1), datetime(2025, 4, 30)))
    
    # Multi-month test
    test_dates.append((datetime(2025, 4, 1), datetime(2025, 5, 31)))
    
    return test_dates

def run_parallel_tests(portfolio_excel, max_workers=None):
    """Run tests in parallel using dynamic worker pool."""
    if max_workers is None:
        max_workers = min(multiprocessing.cpu_count(), 8)
    
    logger.info(f"Starting parallel tests with {max_workers} workers")
    
    runner = BacktestRunner(portfolio_excel)
    test_dates = generate_test_dates()
    
    results = []
    failed_tests = []
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_dates = {
            executor.submit(runner.compare_single_date, start, end): (start, end)
            for start, end in test_dates
        }
        
        # Process results as they complete
        for future in as_completed(future_to_dates):
            start_date, end_date = future_to_dates[future]
            try:
                result = future.result()
                results.append(result)
                
                if result['current_system']['success']:
                    status = "✓ MATCH" if result['match'] else "✗ MISMATCH"
                    logger.info(f"{status}: {result['date_range']}")
                    if not result['match']:
                        for diff in result['differences']:
                            logger.warning(f"  - {diff}")
                else:
                    failed_tests.append(result['date_range'])
                    logger.error(f"✗ FAILED: {result['date_range']}")
                    
            except Exception as e:
                logger.error(f"Exception for {start_date} to {end_date}: {e}")
                failed_tests.append(f"{start_date} to {end_date}")
    
    return results, failed_tests

def generate_summary_report(results):
    """Generate a summary report of all test results."""
    print("\n" + "="*80)
    print("COMPREHENSIVE SYSTEM COMPARISON REPORT")
    print("="*80)
    print(f"Generated: {datetime.now()}")
    print(f"Total Tests: {len(results)}")
    
    # Count successes
    successful_current = sum(1 for r in results if r['current_system']['success'])
    successful_legacy = sum(1 for r in results if r['legacy_system']['success'])
    matches = sum(1 for r in results if r['match'])
    
    print(f"\nCurrent System:")
    print(f"  Successful: {successful_current}/{len(results)}")
    print(f"  Average execution time: {np.mean([r['current_system']['execution_time'] for r in results if r['current_system']['success']]):.2f}s")
    
    print(f"\nLegacy System:")
    print(f"  Successful: {successful_legacy}/{len(results)}")
    if successful_legacy > 0:
        print(f"  Average execution time: {np.mean([r['legacy_system']['execution_time'] for r in results if r['legacy_system']['success']]):.2f}s")
    
    print(f"\nComparison:")
    print(f"  Matching results: {matches}/{successful_current}")
    
    # Detailed results
    print("\n" + "-"*80)
    print("DETAILED RESULTS")
    print("-"*80)
    
    for result in results:
        print(f"\nDate Range: {result['date_range']}")
        
        if result['current_system']['success']:
            print(f"  Current System: ✓ Success")
            print(f"    Trades: {result['current_system']['trades']}")
            print(f"    P&L: {result['current_system']['total_pnl']:.2f}")
            print(f"    Time: {result['current_system']['execution_time']:.2f}s")
        else:
            print(f"  Current System: ✗ Failed")
            print(f"    Error: {result['current_system']['error']}")
        
        if result['legacy_system']['success']:
            print(f"  Legacy System: ✓ Success")
            print(f"    Trades: {result['legacy_system']['trades']}")
            print(f"    P&L: {result['legacy_system']['total_pnl']:.2f}")
            print(f"    Time: {result['legacy_system']['execution_time']:.2f}s")
        else:
            print(f"  Legacy System: ✗ Not available")
        
        if result['match']:
            print(f"  Result: ✓ MATCH")
        else:
            print(f"  Result: ✗ MISMATCH")
            for diff in result['differences']:
                print(f"    - {diff}")
    
    # Save detailed report
    report_file = f"comparison_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    print(f"\nDetailed report saved to: {report_file}")

def main():
    """Run the comprehensive comparison."""
    print("=== COMPREHENSIVE BACKTESTER COMPARISON ===")
    print(f"Start Time: {datetime.now()}")
    
    portfolio_excel = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    
    # Check if portfolio file exists
    if not os.path.exists(portfolio_excel):
        print(f"Error: Portfolio file not found: {portfolio_excel}")
        return
    
    # Run parallel tests
    start_time = time.time()
    results, failed_tests = run_parallel_tests(portfolio_excel, max_workers=4)
    total_time = time.time() - start_time
    
    # Generate summary report
    generate_summary_report(results)
    
    print(f"\nTotal execution time: {total_time:.2f}s")
    print(f"Average time per test: {total_time/len(results):.2f}s")
    
    if failed_tests:
        print(f"\n⚠️ Failed tests: {len(failed_tests)}")
        for failed in failed_tests:
            print(f"  - {failed}")

if __name__ == "__main__":
    main() 