#!/usr/bin/env python3
"""
Compare fixed TBS output with golden file
"""

import pandas as pd

output_file = "/srv/samba/shared/tbs_test_fixed.xlsx"
golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"

print("Fixed Output vs Golden Comparison")
print("=" * 60)

try:
    # Read PORTFOLIO Trans sheets
    output_trans = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans', engine='openpyxl')
    golden_trans = pd.read_excel(golden_file, sheet_name='PORTFOLIO Trans', engine='openpyxl')
    
    print(f"\nOutput trades: {output_trans.shape[0]}")
    print(f"Golden trades: {golden_trans.shape[0]}")
    
    # Group by date to see daily summary
    print(f"\n{'='*20} DAILY SUMMARY {'='*20}")
    
    # Output daily summary
    output_daily = output_trans.groupby('entry_date').agg({
        'pnl': ['count', 'sum']
    })
    print(f"\nOutput daily summary:")
    print(output_daily)
    
    # Golden daily summary
    golden_daily = golden_trans.groupby('Entry Date').agg({
        'PNL': ['count', 'sum']
    })
    print(f"\nGolden daily summary:")
    print(golden_daily)
    
    # Check specific trades for April 3 and 9
    april_3_output = output_trans[output_trans['entry_date'] == '2025-04-03']
    april_9_output = output_trans[output_trans['entry_date'] == '2025-04-09']
    
    print(f"\n{'='*20} APRIL 3 TRADES {'='*20}")
    if not april_3_output.empty:
        print(f"\nOutput trades ({len(april_3_output)}):")
        for idx, row in april_3_output.iterrows():
            print(f"  {row['instrument_type']} {row['side']} @ {row['strike']}: Entry={row['entry_price']}, Exit={row['exit_price']}, PNL={row['pnl']:.2f}")
            print(f"    Exit time: {row['exit_time']}, Reason: {row['reason']}")
    
    # Golden April 3
    april_3_golden = golden_trans[golden_trans['Entry Date'] == '2025-04-03T00:00:00.000000000']
    if not april_3_golden.empty:
        print(f"\nGolden trades ({len(april_3_golden)}):")
        for idx, row in april_3_golden.iterrows():
            print(f"  {row['CE/PE']} {row['Trade']} @ {row['Strike']}: Entry={row['Entry at']}, Exit={row['Exit at.1']}, PNL={row['PNL']:.2f}")
            print(f"    Exit time: {row['Exit at']}, Reason: {row['Reason']}")
    
    print(f"\n{'='*20} APRIL 9 TRADES {'='*20}")
    if not april_9_output.empty:
        print(f"\nOutput trades ({len(april_9_output)}):")
        for idx, row in april_9_output.iterrows():
            print(f"  {row['instrument_type']} {row['side']} @ {row['strike']}: Entry={row['entry_price']}, Exit={row['exit_price']}, PNL={row['pnl']:.2f}")
            print(f"    Exit time: {row['exit_time']}, Reason: {row['reason']}")
    
    # Golden April 9
    april_9_golden = golden_trans[golden_trans['Entry Date'] == '2025-04-09T00:00:00.000000000']
    if not april_9_golden.empty:
        print(f"\nGolden trades ({len(april_9_golden)}):")
        for idx, row in april_9_golden.iterrows():
            print(f"  {row['CE/PE']} {row['Trade']} @ {row['Strike']}: Entry={row['Entry at']}, Exit={row['Exit at.1']}, PNL={row['PNL']:.2f}")
            print(f"    Exit time: {row['Exit at']}, Reason: {row['Reason']}")
    
    # Total P&L comparison
    print(f"\n{'='*20} TOTAL P&L {'='*20}")
    print(f"Output: {output_trans['pnl'].sum():.2f}")
    print(f"Golden: {golden_trans['PNL'].sum():.2f}")
    print(f"Difference: {output_trans['pnl'].sum() - golden_trans['PNL'].sum():.2f}")
    
    # Check metrics sheet
    try:
        output_metrics = pd.read_excel(output_file, sheet_name='Metrics', engine='openpyxl')
        golden_metrics = pd.read_excel(golden_file, sheet_name='Metrics', engine='openpyxl')
        
        print(f"\n{'='*20} METRICS COMPARISON {'='*20}")
        # Find Total PnL row
        output_pnl_row = output_metrics[output_metrics['Particulars'].str.contains('Total PnL', case=False, na=False)]
        golden_pnl_row = golden_metrics[golden_metrics['Particulars'].str.contains('Total PnL', case=False, na=False)]
        
        if not output_pnl_row.empty:
            print(f"Output Total PnL (from Metrics): {output_pnl_row.iloc[0]['Value']}")
        if not golden_pnl_row.empty:
            print(f"Golden Total PnL (from Metrics): {golden_pnl_row.iloc[0]['Combined']}")
            
    except Exception as e:
        print(f"Could not compare metrics: {e}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc() 