#!/usr/bin/env python3
"""Patch legacy Util.py to use LocalBacktestEngine instead of Flask services."""

import os
import shutil
from datetime import datetime

def patch_util_py():
    """Patch Util.py to use LocalBacktestEngine."""
    
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    backup_path = f"{util_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Create backup
    shutil.copy2(util_path, backup_path)
    print(f"Created backup: {backup_path}")
    
    # Read the file
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Create the new getBacktestResults implementation
    new_implementation = '''    @staticmethod
    def getBacktestResults(btPara: dict) -> dict:
        """Modified to use LocalBacktestEngine instead of Flask services."""
        
        startTime = datetime.now()
        logging.info(f"{startTime}, Backtesting Portfolio: {btPara['portfolio']['id']} using LocalBacktestEngine")
        
        try:
            # Import LocalBacktestEngine
            from LocalBacktestEngine import LocalBacktestEngine
            
            # MySQL configuration
            mysql_config = {
                'host': 'localhost',
                'user': 'mahesh',
                'password': 'mahesh_123',
                'database': 'historicaldb'
            }
            
            # Initialize local engine
            engine = LocalBacktestEngine(mysql_config)
            
            # Execute backtest
            respp = engine.execute_trade(btPara)
            
            # Close connection
            engine.close()
            
            # Add portfolio name to orders
            if 'strategies' in respp and 'orders' in respp['strategies']:
                orderss = pd.DataFrame(respp['strategies']['orders'])
                if not orderss.empty:
                    orderss['portfolio_name'] = btPara['portfolio']['name']
                    respp['strategies']['orders'] = orderss.to_dict("records")
            
            logging.info(f"LocalBacktestEngine completed successfully")
            
        except Exception as e:
            logging.error(f"Error in LocalBacktestEngine: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            respp = {}
        
        endTime = datetime.now()
        durationn = round((endTime-startTime).total_seconds(), 2)
        logging.info(f"{endTime}, Completed backtesting portfolio: {btPara['portfolio']['id']}, Time taken: {durationn} \\n")
        
        return respp'''
    
    # Find and replace the getBacktestResults method
    import re
    
    # Pattern to find the method
    pattern = r'(@staticmethod\s+def getBacktestResults\(btPara: dict\) -> dict:.*?)(?=@staticmethod|$)'
    
    # Replace the method
    new_content = re.sub(pattern, new_implementation + '\n\n    ', content, flags=re.DOTALL)
    
    # Write the modified content
    with open(util_path, 'w') as f:
        f.write(new_content)
    
    print(f"✅ Patched Util.py to use LocalBacktestEngine")
    print("✅ Legacy backtester can now run without Flask services")
    
    # Also update config.py to ensure correct settings
    config_path = "bt/archive/backtester_stable/BTRUN/config.py"
    
    with open(config_path, 'r') as f:
        config_content = f.read()
    
    # Ensure USE_SYNTHETIC_FUTURE_ATM is True
    if 'USE_SYNTHETIC_FUTURE_ATM' not in config_content:
        config_content += '\n# ATM calculation method for parity with HeavyDB\nUSE_SYNTHETIC_FUTURE_ATM = True\n'
    
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    print("✅ Updated config.py settings")

if __name__ == "__main__":
    patch_util_py()
    print("\nYou can now run the legacy backtester with:")
    print("  cd bt/archive/backtester_stable/BTRUN")
    print("  python3 BTRunPortfolio.py") 