#!/bin/bash
# deploy_nifty_option_chain.sh
# Script to deploy the nifty_option_chain materialized view to the HeavyDB database

# Database connection settings
DB_HOST="127.0.0.1"
DB_PORT="6274"
DB_USER="admin"
DB_PASSWORD="HyperInteractive"
DB_NAME="heavyai"

# Directory containing SQL functions
SQL_DIR="sql_functions"

echo "Deploying Nifty Option Chain materialized view to HeavyDB..."

# Function to execute SQL files
execute_sql_file() {
    local file=$1
    echo "Executing $file..."
    
    /opt/heavyai/bin/heavysql -s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASSWORD -d $DB_NAME -q < "$file"
    
    if [ $? -ne 0 ]; then
        echo "Error executing $file"
        exit 1
    fi
    echo "Successfully executed $file"
}

# Deploy functions in order
echo "1. Installing get_strike_increment function..."
execute_sql_file "$SQL_DIR/get_strike_increment.sql"

echo "2. Installing classify_strikes function..."
execute_sql_file "$SQL_DIR/classify_strikes.sql"

echo "3. Installing find_atm_hybrid function..."
execute_sql_file "$SQL_DIR/find_atm_hybrid.sql"

echo "4. Creating nifty_option_chain materialized view..."
execute_sql_file "$SQL_DIR/create_nifty_option_chain.sql"

echo "5. Setting up refresh logging and refresh function..."
execute_sql_file "$SQL_DIR/refresh_logging.sql"

echo "6. Performing initial refresh of the materialized view..."
/opt/heavyai/bin/heavysql -s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASSWORD -d $DB_NAME -q -c "SELECT refresh_nifty_option_chain();"

# Check if successful
if [ $? -eq 0 ]; then
    echo "Deployment successful! The nifty_option_chain materialized view is now ready for use."
    echo "You can refresh the view with new data using: SELECT refresh_nifty_option_chain();"
else
    echo "Error during deployment. Please check the error messages above."
    exit 1
fi 