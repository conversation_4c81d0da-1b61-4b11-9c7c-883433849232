#!/usr/bin/env python3
"""
Display complete data for one specific day and expiry from the view
"""

from heavyai import connect
import pandas as pd
from tabulate import tabulate

# Connect to HeavyDB
conn = connect(
    user="admin",
    password="HyperInteractive",
    host="127.0.0.1",
    port=6274,
    dbname="heavyai"
)

# Specific date and expiry
trade_date = '2023-01-18'
expiry_date = '2023-01-25'

# Query to view all data for this specific day and expiry
query = f"""
SELECT 
    trade_date, 
    expiry_date,
    underlying_price,
    atm_strike,
    strike, 
    call_moneyness, 
    put_moneyness,
    ce_close, 
    pe_close
FROM nifty_sample
WHERE trade_date = '{trade_date}' AND expiry_date = '{expiry_date}'
ORDER BY strike
"""

print(f"Querying all data for trade date {trade_date} with expiry {expiry_date}...")
try:
    # Execute query
    cursor = conn.cursor()
    cursor.execute(query)
    
    # Get column names and fetch all rows
    columns = [desc[0] for desc in cursor.description]
    rows = cursor.fetchall()
    
    # Create DataFrame
    df = pd.DataFrame(rows, columns=columns)
    
    # Print results in a nice table format
    print("\nComplete Option Chain:")
    print(tabulate(df, headers='keys', tablefmt='pretty', showindex=False))
    
    # Display summary
    print(f"\nTotal rows: {len(df)}")
    
    # Print ATM strike information
    if len(df) > 0:
        atm_strike = df['atm_strike'].iloc[0]
        underlying = df['underlying_price'].iloc[0]
        print(f"\nUnderlying price: {underlying}")
        print(f"ATM strike: {atm_strike}")
        
        # Find and print ATM options
        atm_options = df[df['call_moneyness'] == 'ATM']
        if not atm_options.empty:
            print("\nATM Options:")
            print(tabulate(atm_options, headers='keys', tablefmt='pretty', showindex=False))
        
        # Show ITM and OTM distribution
        print("\nMoneyness Distribution:")
        moneyness_counts = df.groupby('call_moneyness').size().reset_index(name='count')
        print(tabulate(moneyness_counts, headers='keys', tablefmt='pretty', showindex=False))
        
except Exception as e:
    print(f"Error executing query: {e}")
finally:
    conn.close()
    print("\nConnection closed.") 