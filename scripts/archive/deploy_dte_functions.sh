#!/bin/bash
# Deploy Trading Day DTE Functions and Updated View
# This script deploys the trading day DTE functions and updates
# the nifty_option_chain materialized view to include DTE calculations.

set -e  # Exit on any error

# Configuration
DB_HOST=${DB_HOST:-"127.0.0.1"}
DB_PORT=${DB_PORT:-"6274"}
DB_USER=${DB_USER:-"admin"}
DB_PASSWORD=${DB_PASSWORD:-"HyperInteractive"}
DB_NAME=${DB_NAME:-"heavyai"}

HEAVYSQL_CMD="/opt/heavyai/bin/heavysql"
HEAVYSQL_OPTS="-s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASSWORD -d $DB_NAME"

echo "===== Deploying NSE Trading Day DTE Implementation ====="
echo "Database: $DB_NAME on $DB_HOST:$DB_PORT"

# Function to execute SQL script
execute_sql() {
    local sql_file="$1"
    local description="$2"
    
    echo "Executing $description..."
    ${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < "$sql_file"
    
    if [ $? -eq 0 ]; then
        echo "✓ $description completed successfully"
    else
        echo "✗ $description failed"
        exit 1
    fi
}

# Deploy NSE trading day functions
echo "Step 1: Deploying NSE trading day functions..."
execute_sql "sql_functions/nse_trading_days.sql" "NSE trading day functions"

# Deploy updated materialized view with DTE calculation
echo "Step 2: Updating nifty_option_chain materialized view..."
execute_sql "nifty_view_updated.sql" "Updated materialized view"

# Create a test query for verification
echo "Step 3: Creating test query..."
cat <<EOF > test_dte.sql
SELECT DISTINCT
  trade_date,
  expiry_date,
  dte_trading_days
FROM nifty_option_chain
ORDER BY trade_date, expiry_date
LIMIT 10;
EOF

# Execute test query
echo "Step 4: Testing DTE implementation..."
echo "Sample DTE values from nifty_option_chain:"
${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < test_dte.sql

echo "===== Deployment Complete ====="
echo "Trading day DTE has been successfully implemented in the nifty_option_chain view."
echo "You can verify the implementation by querying the dte_trading_days column." 