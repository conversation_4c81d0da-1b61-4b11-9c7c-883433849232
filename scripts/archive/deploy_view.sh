#!/bin/bash
# deploy_view.sh - Deploy the nifty_option_chain view to HeavyDB

# Database connection settings
DB_HOST="127.0.0.1"
DB_PORT="6274"
DB_USER="admin"
DB_PASSWORD="HyperInteractive"
DB_NAME="heavyai"

# File containing the view creation SQL
VIEW_FILE="nifty_view_simple.sql"

echo "Deploying Nifty Option Chain view to HeavyDB..."
echo "Using connection: $DB_HOST:$DB_PORT ($DB_NAME)"

# Execute the SQL file
echo "Creating view..."
/opt/heavyai/bin/heavysql -s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASSWORD -d $DB_NAME -q < "$VIEW_FILE"

# Check if successful
if [ $? -eq 0 ]; then
    echo "Deployment successful! The nifty_option_chain view is now ready for use."
    echo "Run a test query to verify:"
    echo "SELECT COUNT(*) FROM nifty_option_chain LIMIT 10;"
else
    echo "Error during deployment. Please check the error messages above."
    exit 1
fi 