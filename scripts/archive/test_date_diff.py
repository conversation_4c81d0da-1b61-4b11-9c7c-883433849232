#!/usr/bin/env python3
"""
Test script to manually calculate DTE from trade_date and expiry_date
"""

from heavyai import connect
import pandas as pd
from tabulate import tabulate
from datetime import datetime

# Connect to HeavyDB
conn = connect(
    user="admin",
    password="HyperInteractive",
    host="127.0.0.1",
    port=6274,
    dbname="heavyai"
)

try:
    # Create a cursor
    cursor = conn.cursor()
    
    print("Testing manual DTE calculation from dates in nifty_option_chain...")
    
    # Query to get distinct trade dates and expiry dates
    date_query = """
    SELECT DISTINCT
        trade_date,
        expiry_date
    FROM nifty_option_chain
    ORDER BY trade_date, expiry_date
    LIMIT 20
    """
    
    cursor.execute(date_query)
    columns = [desc[0] for desc in cursor.description]
    rows = cursor.fetchall()
    
    if not rows:
        print("No data found in nifty_option_chain view")
        exit(1)
    
    # Create a DataFrame
    df = pd.DataFrame(rows, columns=columns)
    
    # Format the data and calculate DTE manually
    results = []
    for _, row in df.iterrows():
        trade_date = row['trade_date']
        expiry_date = row['expiry_date']
        
        # Calculate calendar days manually
        t_date = datetime.strptime(trade_date, '%Y-%m-%d')
        e_date = datetime.strptime(expiry_date, '%Y-%m-%d')
        cal_days = (e_date - t_date).days
        
        # Approximate business days (5/7 of calendar days)
        approx_bdays = int(cal_days * 5 / 7)
        
        results.append([
            trade_date,
            expiry_date,
            cal_days,
            approx_bdays
        ])
    
    # Display the results
    headers = ["Trade Date", "Expiry Date", "Calendar DTE", "Approx Business DTE"]
    print("\nDate Difference Calculation Results:")
    print(tabulate(results, headers=headers, tablefmt='grid'))
    
    # Calculate statistics
    print("\nStatistics:")
    
    # Get unique expiries
    unique_expiries = df['expiry_date'].unique()
    print(f"Number of unique expiry dates: {len(unique_expiries)}")
    
    # Calculate average DTE
    cal_dte_values = [row[2] for row in results]
    avg_cal_dte = sum(cal_dte_values) / len(cal_dte_values)
    print(f"Average calendar DTE: {avg_cal_dte:.2f} days")
    
    # Count weekly, monthly
    weekly = sum(1 for days in cal_dte_values if days <= 8)
    monthly = sum(1 for days in cal_dte_values if 20 <= days <= 32)
    print(f"Weekly expiries (DTE ≤ 8 days): {weekly}")
    print(f"Monthly expiries (20 ≤ DTE ≤ 32 days): {monthly}")
    
    # Show a few example expiries
    print("\nSample expiry dates:")
    for exp in sorted(unique_expiries)[:5]:
        print(f"- {exp}")

except Exception as e:
    print(f"Error: {e}")
finally:
    conn.close()
    print("\nConnection closed.") 