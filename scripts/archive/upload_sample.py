#!/usr/bin/env python3
import os
import subprocess
import sys
import time

def execute_command(cmd):
    """Execute a shell command and return the output"""
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        shell=True
    )
    stdout, stderr = process.communicate()
    return process.returncode, stdout.decode('utf-8'), stderr.decode('utf-8')

def upload_to_heavydb(csv_file, table_name, db_user, db_password):
    """Upload CSV file to HeavyDB"""
    print(f"Processing {csv_file}...")
    
    # Create a temporary SQL file with the COPY command
    temp_sql_file = f"/tmp/copy_{int(time.time())}.sql"
    with open(temp_sql_file, 'w') as f:
        copy_command = f"""
        COPY {table_name} FROM '{os.path.abspath(csv_file)}' 
        WITH (header='true', 
              delimiter=',',
              quoted='false',
              null_string='',
              date_format='%y%m%d');
        """
        f.write(copy_command)
    
    # Execute the COPY command
    cmd = f"/opt/heavyai/bin/heavysql -u {db_user} -p {db_password} < {temp_sql_file}"
    print(f"Executing command: {cmd}")
    returncode, stdout, stderr = execute_command(cmd)
    
    # Clean up temporary file
    try:
        os.remove(temp_sql_file)
    except:
        pass
    
    if returncode != 0:
        print(f"ERROR: Failed to upload {csv_file}")
        print(f"STDERR: {stderr}")
        return False
    
    print(f"Successfully uploaded {csv_file}")
    return True

# Configuration
csv_file = "market_data/nifty/IV_2023_jan_nifty_cleaned.csv"
table_name = "nifty_greeks"
db_user = "admin"
db_password = "HyperInteractive"

# Upload the file
upload_to_heavydb(csv_file, table_name, db_user, db_password) 