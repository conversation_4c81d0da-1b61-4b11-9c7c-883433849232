#!/bin/bash
# install.sh
# Automated installation script for the Nifty Option Chain materialized view
# This script connects to HeavyDB and runs the installation SQL script

# Default connection parameters
HOST="127.0.0.1"
PORT="6274"
USER="admin"
PASSWORD="HyperInteractive"
DATABASE="heavyai"
INSTALL_MODE="full" # Options: full, functions-only, refresh-only

# Usage information
function show_usage {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --host HOST         HeavyDB host (default: $HOST)"
    echo "  -p, --port PORT         HeavyDB port (default: $PORT)"
    echo "  -u, --user USER         HeavyDB user (default: $USER)"
    echo "  -P, --password PASSWORD HeavyDB password"
    echo "  -d, --database DATABASE HeavyDB database (default: $DATABASE)"
    echo "  -m, --mode MODE         Installation mode: full, functions-only, refresh-only (default: $INSTALL_MODE)"
    echo "  --help                  Show this help message"
    echo ""
    echo "Example:"
    echo "  $0 --host ************* --port 6274 --user admin"
    exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -h|--host)
            HOST="$2"
            shift
            shift
            ;;
        -p|--port)
            PORT="$2"
            shift
            shift
            ;;
        -u|--user)
            USER="$2"
            shift
            shift
            ;;
        -P|--password)
            PASSWORD="$2"
            shift
            shift
            ;;
        -d|--database)
            DATABASE="$2"
            shift
            shift
            ;;
        -m|--mode)
            INSTALL_MODE="$2"
            shift
            shift
            ;;
        --help)
            show_usage
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            ;;
    esac
done

# Validate installation mode
if [[ "$INSTALL_MODE" != "full" && "$INSTALL_MODE" != "functions-only" && "$INSTALL_MODE" != "refresh-only" ]]; then
    echo "Error: Invalid installation mode. Must be one of: full, functions-only, refresh-only"
    show_usage
fi

# Function to run SQL script
function run_sql_script {
    local script=$1
    echo "Running SQL script: $script"
    /opt/heavyai/bin/heavysql -s "$HOST" --port "$PORT" -u "$USER" -p "$PASSWORD" -d "$DATABASE" < "$script"
    
    if [ $? -ne 0 ]; then
        echo "Error: Failed to run SQL script: $script"
        exit 1
    fi
}

# Create temporary installation file based on mode
TEMP_SQL_FILE=$(mktemp)
echo "-- Generated temporary installation file" > "$TEMP_SQL_FILE"

case "$INSTALL_MODE" in
    full)
        echo "\\i sql_functions/install_nifty_option_chain.sql" >> "$TEMP_SQL_FILE"
        ;;
    functions-only)
        echo "\\i sql_functions/get_strike_increment.sql" >> "$TEMP_SQL_FILE"
        echo "\\i sql_functions/classify_strikes.sql" >> "$TEMP_SQL_FILE"
        echo "\\i sql_functions/find_atm_hybrid.sql" >> "$TEMP_SQL_FILE"
        echo "\\i sql_functions/refresh_nifty_option_chain.sql" >> "$TEMP_SQL_FILE"
        ;;
    refresh-only)
        echo "SELECT refresh_nifty_option_chain();" >> "$TEMP_SQL_FILE"
        ;;
esac

# Main installation process
echo "========================================================"
echo "Nifty Option Chain Installation"
echo "========================================================"
echo "Host:     $HOST"
echo "Port:     $PORT"
echo "User:     $USER"
echo "Database: $DATABASE"
echo "Mode:     $INSTALL_MODE"
echo "========================================================"

# Run the installation
run_sql_script "$TEMP_SQL_FILE"

# Cleanup
rm "$TEMP_SQL_FILE"

echo "========================================================"
echo "Installation completed successfully!"
echo "========================================================"

exit 0 