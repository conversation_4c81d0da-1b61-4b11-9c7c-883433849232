#!/usr/bin/env python3
import os
import sys
import glob
import argparse
import logging
import pandas as pd
from heavydb.dbapi import connect

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def list_csv_files(directory):
    """List all CSV files in the specified directory"""
    return glob.glob(os.path.join(directory, "*.csv"))

def connect_to_heavydb(host, port, dbname, username, password):
    """Connect to HeavyDB using the PyHeavyDB driver"""
    try:
        conn = connect(
            host=host,
            port=port,
            user=username,
            password=password,
            dbname=dbname
        )
        logger.info(f"Successfully connected to HeavyDB at {host}:{port}")
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to HeavyDB: {str(e)}")
        return None

def create_table_if_not_exists(conn, table_name):
    """Create the table if it doesn't exist"""
    # Table definition for nifty_greeks
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS nifty_greeks (
      trade_date DATE ENCODING DAYS(32),
      trade_time TIME,
      expiry_date DATE ENCODING DAYS(32),
      strike DOUBLE,
      ce_symbol TEXT ENCODING DICT(32),
      ce_open DOUBLE,
      ce_high DOUBLE,
      ce_low DOUBLE,
      ce_close DOUBLE,
      ce_volume DOUBLE,
      ce_oi DOUBLE,
      ce_coi DOUBLE,
      ce_iv DOUBLE,
      ce_delta DOUBLE,
      ce_gamma DOUBLE,
      ce_theta DOUBLE,
      ce_vega DOUBLE,
      ce_rho DOUBLE,
      pe_symbol TEXT ENCODING DICT(32),
      pe_open DOUBLE,
      pe_high DOUBLE,
      pe_low DOUBLE,
      pe_close DOUBLE,
      pe_volume DOUBLE,
      pe_oi DOUBLE,
      pe_coi DOUBLE,
      pe_iv DOUBLE,
      pe_delta DOUBLE,
      pe_gamma DOUBLE,
      pe_theta DOUBLE,
      pe_vega DOUBLE,
      pe_rho DOUBLE,
      underlying_price DOUBLE
    );
    """
    try:
        cursor = conn.cursor()
        cursor.execute(create_table_sql)
        conn.commit()
        logger.info(f"Table {table_name} created or already exists")
        cursor.close()
        return True
    except Exception as e:
        logger.error(f"Failed to create table: {str(e)}")
        return False

def upload_file_to_heavydb(conn, csv_file, table_name, delimiter=',', has_header=True, date_format='%y%m%d'):
    """Upload CSV file to HeavyDB using pandas and the PyHeavyDB driver"""
    try:
        # Load CSV file into pandas DataFrame
        logger.info(f"Loading {os.path.basename(csv_file)} into memory")
        df = pd.read_csv(csv_file, delimiter=delimiter, header=0 if has_header else None)
        
        # Convert date columns to proper format if needed
        if 'trade_date' in df.columns:
            df['trade_date'] = pd.to_datetime(df['trade_date'], format=date_format).dt.date
        if 'expiry_date' in df.columns:
            df['expiry_date'] = pd.to_datetime(df['expiry_date'], format=date_format).dt.date
        
        # Use INSERT method since COPY FROM might not be available in DBAPI
        # Convert DataFrame records to list of tuples for executemany
        records = list(df.itertuples(index=False, name=None))
        
        # Create placeholders for SQL INSERT
        placeholders = ','.join(['?'] * len(df.columns))
        insert_sql = f"INSERT INTO {table_name} VALUES ({placeholders})"
        
        # Insert records using executemany
        logger.info(f"Uploading {len(records)} rows to table {table_name}")
        cursor = conn.cursor()
        cursor.executemany(insert_sql, records)
        conn.commit()
        cursor.close()
        
        logger.info(f"Successfully uploaded {os.path.basename(csv_file)}")
        return True
    except Exception as e:
        logger.error(f"Failed to upload {os.path.basename(csv_file)}: {str(e)}")
        return False

def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Upload CSV files to HeavyDB using PyHeavyDB')
    parser.add_argument('--data-dir', default='market_data/nifty', help='Directory containing CSV files')
    parser.add_argument('--table', default='nifty_greeks', help='Target table name')
    parser.add_argument('--host', default='localhost', help='HeavyDB host')
    parser.add_argument('--port', default='6279', help='HeavyDB port', type=int)
    parser.add_argument('--db', default='heavyai', help='Database name')
    parser.add_argument('--user', default='admin', help='Database user')
    parser.add_argument('--password', default='HyperInteractive', help='Database password')
    parser.add_argument('--sample', action='store_true', help='Upload only one file as a test')
    parser.add_argument('--delimiter', default=',', help='CSV delimiter character')
    parser.add_argument('--header', action='store_true', default=True, help='Whether files have header row')
    parser.add_argument('--date-format', default='%y%m%d', help='Format for date columns')
    parser.add_argument('--create-table', action='store_true', help='Create table if it doesn\'t exist')
    args = parser.parse_args()
    
    # Connect to HeavyDB
    logger.info(f"Connecting to HeavyDB at {args.host}:{args.port}")
    conn = connect_to_heavydb(args.host, args.port, args.db, args.user, args.password)
    if not conn:
        logger.error("Unable to establish database connection. Exiting.")
        sys.exit(1)
    
    # Create table if requested
    if args.create_table:
        if not create_table_if_not_exists(conn, args.table):
            conn.close()
            sys.exit(1)
    
    # List CSV files
    csv_files = list_csv_files(args.data_dir)
    if not csv_files:
        logger.error(f"No CSV files found in {args.data_dir}")
        conn.close()
        sys.exit(1)
    
    # Sort CSV files by name
    csv_files.sort()
    
    if args.sample:
        # Just take the first file for testing
        csv_files = [csv_files[0]]
        logger.info(f"Running in sample mode. Will upload only: {csv_files[0]}")
    else:
        logger.info(f"Found {len(csv_files)} CSV files to upload")
    
    # Upload each file
    success_count = 0
    for csv_file in csv_files:
        if upload_file_to_heavydb(conn, csv_file, args.table, 
                                args.delimiter, args.header, args.date_format):
            success_count += 1
    
    logger.info(f"Upload complete. Successfully uploaded {success_count} out of {len(csv_files)} files.")
    
    # Close the connection
    conn.close()

if __name__ == "__main__":
    main() 