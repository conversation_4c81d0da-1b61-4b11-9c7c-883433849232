#!/usr/bin/env python3
"""
Test script to validate the trading day DTE implementation in the nifty_option_chain view
by comparing with manual Python calculation using the NSE trading calendar logic.
"""

from heavyai import connect
import pandas as pd
from tabulate import tabulate
from datetime import datetime, timedelta

# NSE holidays for 2022-2024
NSE_HOLIDAYS = [
    # 2022 holidays
    '2022-01-26', '2022-03-01', '2022-03-18', '2022-04-14', '2022-04-15', 
    '2022-05-03', '2022-08-09', '2022-08-15', '2022-08-31', '2022-10-05', 
    '2022-10-24', '2022-10-26', '2022-11-08',
    
    # 2023 holidays
    '2023-01-26', '2023-03-07', '2023-03-30', '2023-04-04', '2023-04-07', 
    '2023-04-14', '2023-05-01', '2023-06-28', '2023-08-15', '2023-09-19', 
    '2023-10-02', '2023-10-24', '2023-11-14', '2023-11-27', '2023-12-25',
    
    # 2024 holidays
    '2024-01-26', '2024-03-08', '2024-03-25', '2024-03-29', '2024-04-11', 
    '2024-04-17', '2024-05-01', '2024-06-17', '2024-07-17', '2024-08-15', 
    '2024-10-02', '2024-11-01', '2024-11-15', '2024-12-25'
]

def is_weekend(dt):
    """Check if a date is a weekend (Saturday or Sunday)"""
    return dt.weekday() >= 5  # 5 = Saturday, 6 = Sunday

def is_nse_holiday(dt):
    """Check if a date is an NSE holiday"""
    dt_str = dt.strftime('%Y-%m-%d')
    return dt_str in NSE_HOLIDAYS

def is_trading_day(dt):
    """Check if a date is a trading day (not weekend and not holiday)"""
    return not is_weekend(dt) and not is_nse_holiday(dt)

def calc_trading_days_dte(trade_date, expiry_date):
    """Calculate trading day DTE between trade_date and expiry_date (inclusive)"""
    if isinstance(trade_date, str):
        trade_date = datetime.strptime(trade_date, '%Y-%m-%d')
    if isinstance(expiry_date, str):
        expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d')
        
    # If trade_date is after expiry_date, return 0
    if trade_date > expiry_date:
        return 0
    
    # Count trading days
    dte = 0
    curr_date = trade_date
    while curr_date <= expiry_date:
        if is_trading_day(curr_date):
            dte += 1
        curr_date += timedelta(days=1)
    
    return dte

# Connect to HeavyDB
conn = connect(
    user="admin",
    password="HyperInteractive",
    host="127.0.0.1",
    port=6274,
    dbname="heavyai"
)

try:
    # Get distinct date pairs from nifty_option_chain
    cursor = conn.cursor()
    
    # Get distinct date pairs and DTE values
    query = """
    SELECT DISTINCT
        trade_date,
        expiry_date,
        dte_trading_days
    FROM nifty_option_chain
    WHERE dte_trading_days > 0
    ORDER BY trade_date, expiry_date
    LIMIT 20
    """
    
    cursor.execute(query)
    
    # Process results
    results = []
    for row in cursor.fetchall():
        trade_date, expiry_date, db_dte = row
        
        # Calculate DTE manually
        py_dte = calc_trading_days_dte(trade_date, expiry_date)
        
        # Check if DTEs match
        match = "✓" if py_dte == db_dte else "✗"
        
        results.append([
            trade_date.strftime('%Y-%m-%d'),
            expiry_date.strftime('%Y-%m-%d'),
            py_dte,
            db_dte,
            match
        ])
    
    # Show results
    print("\nTrading Day DTE Validation:")
    headers = ["Trade Date", "Expiry Date", "Python DTE", "DB DTE", "Match"]
    print(tabulate(results, headers=headers, tablefmt='grid'))
    
    # Count matches and mismatches
    matches = sum(1 for row in results if row[4] == "✓")
    mismatches = len(results) - matches
    
    print(f"\nSummary:")
    print(f"Total rows tested: {len(results)}")
    print(f"Matching DTEs: {matches}")
    print(f"Mismatches: {mismatches}")
    
    if mismatches > 0:
        print("\nDetailed analysis of mismatches:")
        for row in results:
            if row[4] == "✗":
                trade_date = row[0]
                expiry_date = row[1]
                py_dte = row[2]
                db_dte = row[3]
                
                print(f"\nMismatch for {trade_date} to {expiry_date}:")
                print(f"  Python calculation: {py_dte}")
                print(f"  Database value: {db_dte}")
                print(f"  Difference: {abs(py_dte - db_dte)}")
                
                # Show detailed day-by-day analysis
                start_dt = datetime.strptime(trade_date, '%Y-%m-%d')
                end_dt = datetime.strptime(expiry_date, '%Y-%m-%d')
                
                print("\n  Day-by-day breakdown:")
                curr_dt = start_dt
                while curr_dt <= end_dt:
                    date_str = curr_dt.strftime('%Y-%m-%d')
                    weekday = curr_dt.strftime('%A')
                    trading_day = is_trading_day(curr_dt)
                    status = "Trading day" if trading_day else "Non-trading day"
                    reason = ""
                    
                    if is_weekend(curr_dt):
                        reason = "Weekend"
                    elif is_nse_holiday(curr_dt):
                        reason = "NSE Holiday"
                        
                    print(f"  {date_str} ({weekday}): {status}{f' - {reason}' if reason else ''}")
                    curr_dt += timedelta(days=1)
    
except Exception as e:
    print(f"Error: {e}")
    
finally:
    conn.close()
    print("\nConnection closed.") 