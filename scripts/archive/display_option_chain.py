#!/usr/bin/env python3
"""
Display a properly formatted option chain with the data from nifty_option_chain view
Limited to strikes near ATM to handle large datasets
"""

from heavyai import connect
import pandas as pd
from tabulate import tabulate
import sys

# Date and expiry to query (command line arguments)
if len(sys.argv) > 2:
    trade_date = sys.argv[1]
    expiry_date = sys.argv[2]
else:
    trade_date = '2023-01-18'
    expiry_date = '2023-01-25'

# Range of strikes to display (in strike distances)
MAX_STRIKE_DISTANCE = 5  # Display strikes within 5 units of ATM

print(f"Querying option chain for trade date {trade_date}, expiry {expiry_date}")

# Connect to HeavyDB
conn = connect(
    user="admin",
    password="HyperInteractive",
    host="127.0.0.1",
    port=6274,
    dbname="heavyai"
)

try:
    # Create a cursor and execute a query
    cursor = conn.cursor()
    
    # Query for basic information about ATM and price
    info_query = f"""
    SELECT DISTINCT 
        trade_date, 
        expiry_date, 
        underlying_price, 
        atm_strike, 
        selection_method
    FROM nifty_option_chain
    WHERE trade_date = '{trade_date}' AND expiry_date = '{expiry_date}'
    LIMIT 1
    """
    
    cursor.execute(info_query)
    info_row = cursor.fetchone()
    
    if not info_row:
        print(f"No data found for trade date {trade_date} and expiry {expiry_date}")
        sys.exit(1)
        
    # Unpack basic info
    trade_date, expiry_date, underlying_price, atm_strike, selection_method = info_row
    
    print(f"\nNifty Option Chain")
    print(f"Date: {trade_date}, Expiry: {expiry_date}")
    print(f"Underlying Price: {underlying_price}, ATM Strike: {atm_strike}")
    print(f"ATM Selection Method: {selection_method}")
    
    # Now get option data for strikes within range of ATM
    query = f"""
    SELECT
        strike,
        strike_distance,
        call_moneyness_code,
        put_moneyness_code,
        ce_close,
        ce_volume,
        ce_oi,
        ce_iv,
        ce_delta,
        ce_gamma,
        ce_theta,
        ce_vega,
        pe_close,
        pe_volume,
        pe_oi,
        pe_iv,
        pe_delta,
        pe_gamma,
        pe_theta,
        pe_vega
    FROM nifty_option_chain
    WHERE trade_date = '{trade_date}' 
      AND expiry_date = '{expiry_date}'
      AND strike_distance <= {MAX_STRIKE_DISTANCE}
    ORDER BY strike
    """
    
    cursor.execute(query)
    columns = [desc[0] for desc in cursor.description]
    rows = cursor.fetchall()
    
    # Create DataFrame
    df = pd.DataFrame(rows, columns=columns)
    
    if len(df) == 0:
        print("No option data found")
        sys.exit(1)
    
    # Convert numeric moneyness codes to displayable strings
    def format_call_moneyness(code):
        if code == 0:
            return "ATM"
        elif code < 0:
            return f"ITM{abs(code)}"
        else:
            return f"OTM{code}"
    
    def format_put_moneyness(code):
        if code == 0:
            return "ATM"
        elif code < 0:
            return f"ITM{abs(code)}"
        else:
            return f"OTM{code}"
    
    # Prepare data for display
    option_chain = []
    
    for _, row in df.iterrows():
        strike = row['strike']
        
        # Format moneyness strings
        call_type = format_call_moneyness(row['call_moneyness_code'])
        put_type = format_put_moneyness(row['put_moneyness_code'])
        
        # Format row data
        option_row = [
            # Call side
            row['ce_oi'],         # OI
            "",                   # Change (not available)
            row['ce_volume'],     # Volume
            row['ce_iv'],         # IV
            row['ce_close'],      # LTP
            call_type,            # Type
            row['ce_delta'],      # Delta
            row['ce_gamma'],      # Gamma
            row['ce_theta'],      # Theta
            row['ce_vega'],       # Vega
            
            # Strike
            strike,
            "ATM" if strike == atm_strike else "",
            
            # Put side
            row['pe_delta'],      # Delta
            row['pe_gamma'],      # Gamma
            row['pe_theta'],      # Theta
            row['pe_vega'],       # Vega
            put_type,             # Type
            row['pe_close'],      # LTP
            row['pe_iv'],         # IV
            row['pe_volume'],     # Volume
            "",                   # Change (not available)
            row['pe_oi']          # OI
        ]
        
        option_chain.append(option_row)
    
    # Create headers
    headers = [
        # Call side
        "Call OI", "Chng", "Volume", "IV", "LTP", "Type",
        "Delta", "Gamma", "Theta", "Vega",
        # Strike
        "Strike", "Status",
        # Put side
        "Delta", "Gamma", "Theta", "Vega", 
        "Type", "LTP", "IV", "Volume", "Chng", "Put OI"
    ]
    
    # Format and print the option chain
    print(f"\nOption Chain (showing strikes within {MAX_STRIKE_DISTANCE} steps of ATM):")
    option_chain_df = pd.DataFrame(option_chain, columns=headers)
    
    # Format the DataFrame for better readability
    formatted_df = option_chain_df.copy()
    
    # Add styling
    for col in ["Call OI", "Volume", "Put OI"]:
        formatted_df[col] = formatted_df[col].apply(lambda x: f"{int(x):,}" if pd.notnull(x) and x != "" else x)
    
    for col in ["IV", "Delta", "Gamma", "Theta", "Vega"]:
        formatted_df[col] = formatted_df[col].apply(lambda x: f"{x:.4f}" if pd.notnull(x) and x != "" else x)
    
    for col in ["LTP"]:
        formatted_df[col] = formatted_df[col].apply(lambda x: f"{x:.2f}" if pd.notnull(x) and x != "" else x)
    
    # Print the options chain in a nice table format
    print(tabulate(formatted_df, headers='keys', tablefmt='pretty', showindex=False))
    
    # Add summary
    print(f"\nDisplayed strikes: {len(df)}")
    
except Exception as e:
    print(f"Error: {e}")
finally:
    conn.close()
    print("\nConnection closed.") 