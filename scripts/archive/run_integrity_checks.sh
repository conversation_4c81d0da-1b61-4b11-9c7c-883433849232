#!/bin/bash
# Run Data Integrity Checks for nifty_greeks and nifty_option_chain
# This script runs various integrity checks before updating the materialized view

set -e  # Exit on any error

# Configuration
DB_HOST=${DB_HOST:-"127.0.0.1"}
DB_PORT=${DB_PORT:-"6274"}
DB_USER=${DB_USER:-"admin"}
DB_PASSWORD=${DB_PASSWORD:-"HyperInteractive"}
DB_NAME=${DB_NAME:-"heavyai"}

HEAVYSQL_CMD="/opt/heavyai/bin/heavysql"
HEAVYSQL_OPTS="-s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASSWORD -d $DB_NAME"

echo "===== Running Data Integrity Checks ====="
echo "Database: $DB_NAME on $DB_HOST:$DB_PORT"

# Function to run SQL query from data_integrity_checks.sql by line numbers
run_check() {
    local check_name="$1"
    local start_line="$2"
    local end_line="$3"
    
    echo -e "\n----- $check_name -----"
    
    # Extract query from data_integrity_checks.sql
    sed -n "${start_line},${end_line}p" data_integrity_checks.sql > "temp_${check_name}.sql"
    
    # Run query and capture output
    output=$(${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < "temp_${check_name}.sql")
    
    # Display output
    echo "$output"
    
    # Clean up temporary file
    rm "temp_${check_name}.sql"
}

# Run each check with proper line numbers from data_integrity_checks.sql
echo "Running row count comparison..."
run_check "row_count" 5 16

echo "Running date range checks..."
run_check "date_range" 20 38

echo "Checking for duplicates in nifty_option_chain..."
run_check "duplicates" 42 48

echo "Checking ATM strike consistency..."
run_check "atm_consistency" 52 60

echo "Checking for missing data..."
run_check "missing_data" 64 72

echo "Checking strike range coverage..."
run_check "strike_range" 76 90

echo "===== Data Integrity Checks Completed ====="
echo "Please review the results above before proceeding with view updates." 