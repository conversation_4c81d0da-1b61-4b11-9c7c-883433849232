#!/usr/bin/env python3
"""
Test script to verify DTE (Days to Expiry) calculation in nifty_option_chain view
"""

from heavyai import connect
import pandas as pd
from tabulate import tabulate
from datetime import datetime

# Connect to HeavyDB
conn = connect(
    user="admin",
    password="HyperInteractive",
    host="127.0.0.1",
    port=6274,
    dbname="heavyai"
)

try:
    # Create a cursor
    cursor = conn.cursor()
    
    print("Testing DTE calculation in nifty_option_chain...")
    
    # Query to get distinct trade dates and expiry dates with DTE values
    dte_query = """
    SELECT DISTINCT
        trade_date,
        expiry_date,
        dte,
        dte_bdays
    FROM nifty_option_chain
    ORDER BY trade_date, expiry_date
    LIMIT 50
    """
    
    cursor.execute(dte_query)
    columns = [desc[0] for desc in cursor.description]
    rows = cursor.fetchall()
    
    if not rows:
        print("No data found in nifty_option_chain view")
        exit(1)
    
    # Create a DataFrame
    df = pd.DataFrame(rows, columns=columns)
    
    # Format the data
    formatted_data = []
    for _, row in df.iterrows():
        trade_date = row['trade_date']
        expiry_date = row['expiry_date']
        dte = row['dte']
        dte_bdays = row['dte_bdays']
        
        # Calculate calendar days manually for verification
        t_date = datetime.strptime(trade_date, '%Y-%m-%d')
        e_date = datetime.strptime(expiry_date, '%Y-%m-%d')
        cal_days = (e_date - t_date).days
        
        # Flag if the calculated calendar days doesn't match the stored DTE
        dte_mismatch = "✓" if cal_days == dte else "✗"
        
        formatted_data.append([
            trade_date,
            expiry_date,
            dte,
            cal_days,
            dte_mismatch,
            dte_bdays
        ])
    
    # Display the results
    headers = ["Trade Date", "Expiry Date", "DTE (View)", "DTE (Calc)", "Match", "DTE (Business Days)"]
    print("\nDTE Calculation Test Results:")
    print(tabulate(formatted_data, headers=headers, tablefmt='grid'))
    
    # Basic statistics
    match_count = sum(1 for row in formatted_data if row[4] == "✓")
    total_count = len(formatted_data)
    
    print(f"\nSummary:")
    print(f"Total records tested: {total_count}")
    print(f"Calendar DTE matches: {match_count}")
    print(f"Mismatch count: {total_count - match_count}")
    
    # Statistics on business days vs calendar days
    avg_ratio = df['dte_bdays'] / df['dte']
    print(f"\nBusiness days to calendar days ratio:")
    print(f"Min: {avg_ratio.min():.2f}")
    print(f"Max: {avg_ratio.max():.2f}")
    print(f"Mean: {avg_ratio.mean():.2f}")
    
    # Additional check for consistency in specific rows
    print("\nDetailed check of a few examples:")
    examples = df.head(5).copy()
    
    for _, row in examples.iterrows():
        print(f"Trade date: {row['trade_date']}, Expiry: {row['expiry_date']}")
        print(f"  - Calendar DTE: {row['dte']}")
        print(f"  - Business DTE: {row['dte_bdays']}")
        print(f"  - Expected difference (weekends/holidays): {row['dte'] - row['dte_bdays']}")
        print()

except Exception as e:
    print(f"Error: {e}")
finally:
    conn.close()
    print("\nConnection closed.") 