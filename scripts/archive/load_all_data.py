#!/usr/bin/env python3
import os
import glob
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def list_all_months_files():
    # Get all CSV files
    all_files = glob.glob("market_data/nifty/IV_*.csv")
    
    # Skip January 2023 which is already loaded
    files_to_load = [f for f in all_files if not f.endswith("IV_2023_jan_nifty_cleaned.csv")]
    
    # Sort files by year and month to load chronologically
    def sort_key(filename):
        parts = os.path.basename(filename).split('_')
        year = parts[1]
        month = parts[2]
        # Convert month to numeric for sorting
        month_order = {
            'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04', 
            'may': '05', 'june': '06', 'july': '07', 'aug': '08',
            'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
        }
        return f"{year}{month_order.get(month, '13')}"  # Default to high value if not found
    
    return sorted(files_to_load, key=sort_key)

def main():
    files = list_all_months_files()
    logger.info(f"Found {len(files)} files to load")
    
    for i, file in enumerate(files):
        logger.info(f"Loading file {i+1}/{len(files)}: {os.path.basename(file)}")
        
        # Run the simple_upload.py script for each file
        cmd = f"./simple_upload.py --file {file}"
        
        try:
            subprocess.run(cmd, shell=True, check=True)
            logger.info(f"Successfully loaded {os.path.basename(file)}")
        except subprocess.SubprocessError as e:
            logger.error(f"Failed to load {os.path.basename(file)}: {str(e)}")
    
    # After loading all files, run the analyze command
    logger.info("Running final analysis...")
    try:
        subprocess.run("./simple_upload.py --analyze --gpu-optimize", shell=True, check=True)
        logger.info("Analysis complete")
    except subprocess.SubprocessError as e:
        logger.error(f"Failed to run analysis: {str(e)}")

if __name__ == "__main__":
    main() 