#!/usr/bin/env python3
import os
import sys
import glob
import argparse
import logging
import time
import subprocess
import concurrent.futures
from datetime import datetime
from tqdm import tqdm
from heavyai import connect

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def list_csv_files(directory):
    """List all CSV files in the specified directory"""
    return glob.glob(os.path.join(directory, "*.csv"))

def connect_to_heavydb(host="127.0.0.1", port=6274, user="admin", password="HyperInteractive", dbname="heavyai"):
    """Connect to HeavyDB using the Python API"""
    try:
        logger.info(f"Connecting to HeavyDB at {host}:{port}")
        conn = connect(host=host, port=port, user=user, password=password, dbname=dbname)
        logger.info("Successfully connected to HeavyDB")
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to HeavyDB: {str(e)}")
        return None

def create_table_if_not_exists(conn, table_name):
    """Create the table if it doesn't exist using the Python API"""
    try:
        # First drop the table if it exists
        logger.info(f"Dropping table {table_name} if it exists")
        conn.execute(f"DROP TABLE IF EXISTS {table_name};")
        
        # Also drop any temporary table that might exist
        temp_table_name = f"{table_name}_temp"
        logger.info(f"Dropping temporary table {temp_table_name} if it exists")
        conn.execute(f"DROP TABLE IF EXISTS {temp_table_name};")
        
        # Create temporary table with TEXT types for initial loading
        logger.info(f"Creating temporary table {temp_table_name}")
        temp_table_sql = f"""
        CREATE TABLE {temp_table_name} (
          trade_date TEXT ENCODING DICT(32),
          trade_time TEXT ENCODING DICT(32),
          expiry_date TEXT ENCODING DICT(32),
          strike DOUBLE,
          ce_symbol TEXT ENCODING DICT(32),
          ce_open DOUBLE,
          ce_high DOUBLE,
          ce_low DOUBLE,
          ce_close DOUBLE,
          ce_volume DOUBLE,
          ce_oi DOUBLE,
          ce_coi DOUBLE,
          ce_iv DOUBLE,
          ce_delta DOUBLE,
          ce_gamma DOUBLE,
          ce_theta DOUBLE,
          ce_vega DOUBLE,
          ce_rho DOUBLE,
          pe_symbol TEXT ENCODING DICT(32),
          pe_open DOUBLE,
          pe_high DOUBLE,
          pe_low DOUBLE,
          pe_close DOUBLE,
          pe_volume DOUBLE,
          pe_oi DOUBLE,
          pe_coi DOUBLE,
          pe_iv DOUBLE,
          pe_delta DOUBLE,
          pe_gamma DOUBLE,
          pe_theta DOUBLE,
          pe_vega DOUBLE,
          pe_rho DOUBLE,
          underlying_price DOUBLE
        );"""
        conn.execute(temp_table_sql)
        logger.info(f"Temporary table {temp_table_name} created successfully")
        
        # Table definition with optimized data types for GPU processing
        # Adding a uniqueness constraint to prevent duplicates
        create_table_sql = f"""
        CREATE TABLE {table_name} (
          trade_date DATE ENCODING DAYS(32),
          trade_time TIME ENCODING FIXED(32),
          expiry_date DATE ENCODING DAYS(32),
          strike DOUBLE,
          ce_symbol TEXT ENCODING DICT(32),
          ce_open DOUBLE,
          ce_high DOUBLE,
          ce_low DOUBLE,
          ce_close DOUBLE,
          ce_volume DOUBLE,
          ce_oi DOUBLE,
          ce_coi DOUBLE,
          ce_iv DOUBLE,
          ce_delta DOUBLE,
          ce_gamma DOUBLE,
          ce_theta DOUBLE,
          ce_vega DOUBLE,
          ce_rho DOUBLE,
          pe_symbol TEXT ENCODING DICT(32),
          pe_open DOUBLE,
          pe_high DOUBLE,
          pe_low DOUBLE,
          pe_close DOUBLE,
          pe_volume DOUBLE,
          pe_oi DOUBLE,
          pe_coi DOUBLE,
          pe_iv DOUBLE,
          pe_delta DOUBLE,
          pe_gamma DOUBLE,
          pe_theta DOUBLE,
          pe_vega DOUBLE,
          pe_rho DOUBLE,
          underlying_price DOUBLE,
          UNIQUE (trade_date, trade_time, expiry_date, strike, ce_symbol, pe_symbol)
        );"""
        
        logger.info(f"Creating main table {table_name}")
        conn.execute(create_table_sql)
        logger.info(f"Main table {table_name} created successfully")
        
        return True
    except Exception as e:
        logger.error(f"Failed to create table: {str(e)}")
        return False

def prepare_copy_sql(table_name, target_path, max_reject=1000):
    """Generate SQL for the COPY command with date format specifiers"""
    return f"""COPY {table_name} FROM '{target_path}' WITH (
        header='true', 
        delimiter=',', 
        quoted='false', 
        max_reject={max_reject}
    );"""

def upload_file_to_heavydb(conn, csv_file, table_name, max_reject=1000, retry_attempts=3, chunk_size=500000):
    """Upload CSV file to HeavyDB using the Python API with a two-stage approach"""
    try:
        # Use the temporary table for loading
        temp_table_name = f"{table_name}_temp"
        
        # Get the absolute path to the CSV file
        abs_path = os.path.abspath(csv_file)
        if not os.path.exists(abs_path):
            logger.error(f"File not found: {abs_path}")
            return False, 0, 0
            
        basename = os.path.basename(csv_file)
        logger.info(f"Uploading {basename} to temporary table {temp_table_name}")
        
        # We need to copy the file to the import directory first
        import_dir = "/var/lib/heavyai/storage/import"
        target_path = os.path.join(import_dir, basename)
        
        # Use subprocess for better error handling
        copy_cmd = f"sudo cp {abs_path} {target_path}"
        logger.info(f"Copying file to import directory: {copy_cmd}")
        process = subprocess.Popen(
            copy_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True
        )
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            logger.error(f"Failed to copy file to import directory: {stderr.decode('utf-8')}")
            return False, 0, 0
        
        # Check file size to determine if we need to split
        file_size_cmd = f"wc -l {target_path}"
        process = subprocess.Popen(
            file_size_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True
        )
        stdout, stderr = process.communicate()
        
        try:
            total_lines = int(stdout.decode('utf-8').strip().split()[0])
            logger.info(f"File contains {total_lines} lines")
            
            # If the file is large, we'll need to split it
            if total_lines > chunk_size:
                logger.info(f"Large file detected ({total_lines} lines). Processing in chunks.")
                return upload_large_file(conn, target_path, table_name, total_lines, chunk_size, max_reject)
        except Exception as e:
            logger.warning(f"Could not determine file size, proceeding with normal upload: {str(e)}")
        
        # Clear temporary table
        conn.execute(f"TRUNCATE TABLE {temp_table_name}")
        
        # Execute COPY command to the temporary table
        copy_sql = prepare_copy_sql(temp_table_name, target_path, max_reject)
        
        # Use the execute method to run the COPY command with retry mechanism
        attempt = 0
        success = False
        start_time = time.time()
        
        while attempt < retry_attempts and not success:
            try:
                attempt += 1
                conn.execute(copy_sql)
                success = True
            except Exception as e:
                if "watchdog" in str(e).lower() and attempt < retry_attempts:
                    logger.warning(f"Watchdog limit hit on attempt {attempt}. Trying with smaller chunks...")
                    # Clean up the imported file
                    cleanup_cmd = f"sudo rm -f {target_path}"
                    subprocess.run(cleanup_cmd, shell=True)
                    return upload_large_file(conn, abs_path, table_name, chunk_size=250000, max_reject=max_reject)
                elif attempt < retry_attempts:
                    logger.warning(f"Upload attempt {attempt} failed: {str(e)}. Retrying...")
                    time.sleep(2)  # Wait before retry
                else:
                    logger.error(f"All {retry_attempts} upload attempts failed: {str(e)}")
                    # Clean up the imported file
                    cleanup_cmd = f"sudo rm -f {target_path}"
                    subprocess.run(cleanup_cmd, shell=True)
                    return False, 0, 0
        
        # Check if successful by querying row count after import
        count_query = f"SELECT COUNT(*) FROM {temp_table_name}"
        row_count = conn.execute(count_query).fetchone()[0]
        
        if row_count == 0:
            logger.error(f"No records loaded into temporary table {temp_table_name}")
            # Clean up the imported file
            cleanup_cmd = f"sudo rm -f {target_path}"
            subprocess.run(cleanup_cmd, shell=True)
            return False, 0, 0
        
        logger.info(f"Successfully loaded {row_count} records into temporary table")
        
        # Transform and transfer data to final table with proper date types
        transfer_sql = f"""
        INSERT INTO {table_name} 
        SELECT 
            TRY_CAST('20' || SUBSTRING(trade_date FROM 1 FOR 2) || '-' || 
                 SUBSTRING(trade_date FROM 3 FOR 2) || '-' || 
                 SUBSTRING(trade_date FROM 5 FOR 2) AS DATE) AS trade_date,
            TRY_CAST(trade_time AS TIME) AS trade_time,
            TRY_CAST('20' || SUBSTRING(expiry_date FROM 1 FOR 2) || '-' || 
                 SUBSTRING(expiry_date FROM 3 FOR 2) || '-' || 
                 SUBSTRING(expiry_date FROM 5 FOR 2) AS DATE) AS expiry_date,
            strike, ce_symbol, ce_open, ce_high, ce_low, ce_close, ce_volume, 
            ce_oi, ce_coi, ce_iv, ce_delta, ce_gamma, ce_theta, ce_vega, ce_rho,
            pe_symbol, pe_open, pe_high, pe_low, pe_close, pe_volume, 
            pe_oi, pe_coi, pe_iv, pe_delta, pe_gamma, pe_theta, pe_vega, pe_rho,
            underlying_price
        FROM {temp_table_name}
        """
        
        logger.info(f"Transferring data to main table with date/time conversion")
        transfer_start = time.time()
        conn.execute(transfer_sql)
        
        # Verify transfer was successful
        final_count_query = f"SELECT COUNT(*) FROM {table_name}"
        final_count = conn.execute(final_count_query).fetchone()[0]
        transfer_end = time.time()
        
        logger.info(f"Successfully transferred {final_count} records to main table in {transfer_end - transfer_start:.2f} seconds")
        
        # Clear temporary table
        conn.execute(f"TRUNCATE TABLE {temp_table_name}")
        
        end_time = time.time()
        upload_duration = end_time - start_time
        logger.info(f"Successfully uploaded {basename} - {final_count} records loaded in {upload_duration:.2f} seconds")
        
        # Clean up the imported file
        cleanup_cmd = f"sudo rm -f {target_path}"
        subprocess.run(cleanup_cmd, shell=True)
        
        return True, final_count, upload_duration
            
    except Exception as e:
        logger.error(f"Failed to upload: {str(e)}")
        return False, 0, 0

def upload_large_file(conn, file_path, table_name, total_lines=None, chunk_size=500000, max_reject=1000):
    """Process a large file by splitting it into smaller chunks"""
    try:
        # Use the temporary table for loading
        temp_table_name = f"{table_name}_temp"
        
        basename = os.path.basename(file_path)
        logger.info(f"Processing large file {basename} in chunks of {chunk_size} lines")
        
        # Create a temporary directory for the chunks
        temp_dir = "/tmp/heavydb_chunks"
        os.makedirs(temp_dir, exist_ok=True)
        
        # Split the file into chunks
        split_cmd = f"sudo split -l {chunk_size} --numeric-suffixes=1 --suffix-length=4 {file_path} {temp_dir}/chunk_"
        logger.info(f"Splitting file: {split_cmd}")
        process = subprocess.Popen(
            split_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True
        )
        stdout, stderr = process.communicate()
        
        if process.returncode != 0:
            logger.error(f"Failed to split file: {stderr.decode('utf-8')}")
            return False, 0, 0
            
        # Add header to each chunk except the first one (which already has it)
        get_header_cmd = f"head -1 {file_path}"
        process = subprocess.Popen(
            get_header_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True
        )
        stdout, stderr = process.communicate()
        
        if process.returncode != 0:
            logger.error(f"Failed to get header: {stderr.decode('utf-8')}")
            return False, 0, 0
            
        header = stdout.decode('utf-8').strip()
        
        # Find all chunk files
        chunk_files = sorted(glob.glob(f"{temp_dir}/chunk_*"))
        
        if not chunk_files:
            logger.error(f"No chunks were created")
            return False, 0, 0
            
        logger.info(f"File split into {len(chunk_files)} chunks")
        
        # Fix headers in chunks after the first one
        for i, chunk_file in enumerate(chunk_files):
            if i > 0:  # Skip the first chunk which already has a header
                # Add header to this chunk
                add_header_cmd = f"sudo bash -c 'echo \"{header}\" | cat - {chunk_file} > {chunk_file}.tmp && sudo mv {chunk_file}.tmp {chunk_file}'"
                subprocess.run(add_header_cmd, shell=True)
        
        # Process each chunk
        total_records = 0
        total_duration = 0
        
        # Clear temporary table initially
        conn.execute(f"TRUNCATE TABLE {temp_table_name}")
        
        for i, chunk_file in enumerate(chunk_files):
            logger.info(f"Processing chunk {i+1}/{len(chunk_files)}: {chunk_file}")
            
            # Copy to import directory
            import_dir = "/var/lib/heavyai/storage/import"
            chunk_basename = os.path.basename(chunk_file)
            target_path = os.path.join(import_dir, chunk_basename)
            
            copy_cmd = f"sudo cp {chunk_file} {target_path}"
            process = subprocess.Popen(
                copy_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True
            )
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                logger.error(f"Failed to copy chunk to import directory: {stderr.decode('utf-8')}")
                continue
                
            # Execute COPY command to temporary table
            copy_sql = prepare_copy_sql(temp_table_name, target_path, max_reject)
            
            try:
                start_time = time.time()
                
                # Load this chunk into temporary table
                conn.execute(copy_sql)
                
                # Get count of records in temp table
                count_query = f"SELECT COUNT(*) FROM {temp_table_name}"
                temp_count = conn.execute(count_query).fetchone()[0]
                
                if temp_count == 0:
                    logger.warning(f"No records loaded from chunk {i+1}")
                    continue
                
                logger.info(f"Loaded {temp_count} records from chunk {i+1} into temporary table")
                
                # Transfer to main table with data type conversion
                transfer_sql = f"""
                INSERT INTO {table_name} 
                SELECT 
                    TRY_CAST('20' || SUBSTRING(trade_date FROM 1 FOR 2) || '-' || 
                         SUBSTRING(trade_date FROM 3 FOR 2) || '-' || 
                         SUBSTRING(trade_date FROM 5 FOR 2) AS DATE) AS trade_date,
                    TRY_CAST(trade_time AS TIME) AS trade_time,
                    TRY_CAST('20' || SUBSTRING(expiry_date FROM 1 FOR 2) || '-' || 
                         SUBSTRING(expiry_date FROM 3 FOR 2) || '-' || 
                         SUBSTRING(expiry_date FROM 5 FOR 2) AS DATE) AS expiry_date,
                    strike, ce_symbol, ce_open, ce_high, ce_low, ce_close, ce_volume, 
                    ce_oi, ce_coi, ce_iv, ce_delta, ce_gamma, ce_theta, ce_vega, ce_rho,
                    pe_symbol, pe_open, pe_high, pe_low, pe_close, pe_volume, 
                    pe_oi, pe_coi, pe_iv, pe_delta, pe_gamma, pe_theta, pe_vega, pe_rho,
                    underlying_price
                FROM {temp_table_name}
                """
                
                # Execute the transfer
                conn.execute(transfer_sql)
                
                # Get new total in final table
                final_count_query = f"SELECT COUNT(*) FROM {table_name}"
                current_count = conn.execute(final_count_query).fetchone()[0]
                
                # Calculate records added in this chunk
                chunk_records = current_count - total_records
                total_records = current_count
                
                # Clear temporary table for next chunk
                conn.execute(f"TRUNCATE TABLE {temp_table_name}")
                
                end_time = time.time()
                duration = end_time - start_time
                total_duration += duration
                
                logger.info(f"Successfully processed chunk {i+1}: {chunk_records} records in {duration:.2f} seconds")
                
            except Exception as e:
                logger.error(f"Failed to process chunk {i+1}: {str(e)}")
                # Clear temporary table for next chunk
                conn.execute(f"TRUNCATE TABLE {temp_table_name}")
                
            finally:
                # Clean up the imported chunk
                cleanup_cmd = f"sudo rm -f {target_path}"
                subprocess.run(cleanup_cmd, shell=True)
        
        # Clean up all chunks
        cleanup_cmd = f"sudo rm -rf {temp_dir}"
        subprocess.run(cleanup_cmd, shell=True)
        
        # Clean up the original file in import directory
        cleanup_cmd = f"sudo rm -f {file_path}"
        subprocess.run(cleanup_cmd, shell=True)
        
        if total_records > 0:
            logger.info(f"Successfully processed all chunks: {total_records} total records in {total_duration:.2f} seconds")
            return True, total_records, total_duration
        else:
            logger.error("Failed to process any chunks successfully")
            return False, 0, 0
            
    except Exception as e:
        logger.error(f"Failed to process large file: {str(e)}")
        return False, 0, 0

def process_batch(conn, files_batch, table_name, max_reject=1000):
    """Process a batch of files and return statistics"""
    total_records = 0
    total_duration = 0
    successful_files = 0
    
    for csv_file in files_batch:
        success, records, duration = upload_file_to_heavydb(conn, csv_file, table_name, max_reject)
        if success:
            successful_files += 1
            total_records += records
            total_duration += duration
    
    return successful_files, total_records, total_duration

def upload_files_parallel(conn, csv_files, table_name, max_reject=1000, max_workers=4, batch_size=5):
    """Upload files in parallel with batching for better performance"""
    # Split files into batches
    batches = [csv_files[i:i + batch_size] for i in range(0, len(csv_files), batch_size)]
    logger.info(f"Processing {len(csv_files)} files in {len(batches)} batches with {max_workers} workers")
    
    total_records = 0
    successful_files = 0
    start_time = time.time()
    
    # Process batches in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit batch jobs
        future_to_batch = {
            executor.submit(
                process_batch, 
                connect_to_heavydb(),  # Create a new connection for each worker
                batch, 
                table_name, 
                max_reject
            ): i for i, batch in enumerate(batches)
        }
        
        # Process results as they complete with progress bar
        with tqdm(total=len(batches), desc="Processing batches") as pbar:
            for future in concurrent.futures.as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    success_count, records, duration = future.result()
                    successful_files += success_count
                    total_records += records
                    logger.info(f"Batch {batch_idx+1}/{len(batches)} completed: {success_count} files, {records} records in {duration:.2f}s")
                except Exception as e:
                    logger.error(f"Batch {batch_idx+1} failed: {str(e)}")
                finally:
                    pbar.update(1)
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    logger.info(f"Parallel processing complete: {successful_files}/{len(csv_files)} files uploaded successfully")
    logger.info(f"Total records: {total_records}, Total duration: {total_duration:.2f}s")
    
    return successful_files

def optimize_for_gpu(conn, table_name):
    """Apply GPU-specific optimizations to the table"""
    try:
        logger.info(f"Applying GPU optimizations to {table_name}")
        
        # Configure HeavyDB to use GPU memory efficiently
        try:
            conn.execute("ALTER SESSION SET enable_watchdog = false;")
            logger.info("Disabled watchdog for better GPU performance")
        except:
            logger.warning("Could not disable watchdog")
            
        try:
            conn.execute("ALTER SESSION SET allow_loop_joins = true;")
            logger.info("Enabled loop joins for better query performance")
        except:
            logger.warning("Could not enable loop joins")
            
        # Set memory allocation for GPU
        try:
            conn.execute("SET memory_level = 'gpu';")
            logger.info("Set memory level to GPU")
        except:
            logger.warning("Could not set memory level to GPU")
            
        logger.info(f"GPU optimizations applied successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to apply some GPU optimizations: {str(e)}")
        # Return True anyway since some optimizations might have succeeded
        return True

def analyze_table_statistics(conn, table_name):
    """Analyze and print table statistics"""
    try:
        logger.info(f"Analyzing table statistics for {table_name}")
        
        # Get total record count
        count_query = f"SELECT COUNT(*) FROM {table_name}"
        total_records = conn.execute(count_query).fetchone()[0]
        
        # Get date range
        date_range_query = f"""
        SELECT 
            MIN(trade_date) as min_date, 
            MAX(trade_date) as max_date,
            COUNT(DISTINCT trade_date) as unique_dates
        FROM {table_name}
        """
        date_range = conn.execute(date_range_query).fetchone()
        
        # Get strike price statistics
        strike_query = f"""
        SELECT 
            MIN(strike) as min_strike, 
            MAX(strike) as max_strike,
            AVG(strike) as avg_strike,
            COUNT(DISTINCT strike) as unique_strikes
        FROM {table_name}
        """
        strike_stats = conn.execute(strike_query).fetchone()
        
        # Print the statistics
        logger.info(f"Total records in {table_name}: {total_records}")
        logger.info(f"Date range: {date_range[0]} to {date_range[1]} ({date_range[2]} unique dates)")
        logger.info(f"Strike price range: {strike_stats[0]} to {strike_stats[1]} (avg: {strike_stats[2]:.2f}, {strike_stats[3]} unique values)")
        
        return True
    except Exception as e:
        logger.error(f"Failed to analyze table statistics: {str(e)}")
        return False

def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Upload CSV files to HeavyDB using Python API with optimized date handling')
    parser.add_argument('--data-dir', default='market_data/nifty', help='Directory containing CSV files')
    parser.add_argument('--file', help='Process a single specific CSV file instead of a directory')
    parser.add_argument('--table', default='nifty_greeks', help='Target table name')
    parser.add_argument('--host', default='127.0.0.1', help='HeavyDB host')
    parser.add_argument('--port', default=6274, type=int, help='HeavyDB port')
    parser.add_argument('--user', default='admin', help='Database user')
    parser.add_argument('--password', default='HyperInteractive', help='Database password')
    parser.add_argument('--dbname', default='heavyai', help='Database name')
    parser.add_argument('--sample', action='store_true', help='Upload only one file as a test')
    parser.add_argument('--create-table', action='store_true', help='Create table if it doesn\'t exist')
    parser.add_argument('--max-reject', default=1000, type=int, help='Maximum number of rejected records')
    parser.add_argument('--gpu-optimize', action='store_true', help='Apply GPU-specific optimizations')
    parser.add_argument('--parallel', action='store_true', help='Use parallel processing for faster uploads')
    parser.add_argument('--workers', default=4, type=int, help='Number of parallel workers')
    parser.add_argument('--batch-size', default=5, type=int, help='Number of files to process in each batch')
    parser.add_argument('--chunk-size', default=500000, type=int, help='Maximum number of rows to process in a single chunk')
    parser.add_argument('--analyze', action='store_true', help='Analyze and print table statistics after loading')
    args = parser.parse_args()
    
    # Record start time for overall processing
    overall_start_time = time.time()
    
    # Connect to HeavyDB
    conn = connect_to_heavydb(args.host, args.port, args.user, args.password, args.dbname)
    if not conn:
        sys.exit(1)
    
    try:
        # Create table if requested
        if args.create_table:
            if not create_table_if_not_exists(conn, args.table):
                sys.exit(1)
        
        # Process either a single file or a directory
        if args.file:
            if not os.path.exists(args.file):
                logger.error(f"File not found: {args.file}")
                sys.exit(1)
                
            logger.info(f"Processing single file: {args.file}")
            success, records, duration = upload_file_to_heavydb(
                conn, 
                args.file, 
                args.table, 
                args.max_reject, 
                chunk_size=args.chunk_size
            )
            
            if success:
                logger.info(f"Successfully uploaded file with {records} records in {duration:.2f} seconds")
                success_count = 1
            else:
                logger.error(f"Failed to upload file")
                success_count = 0
        else:
            # List CSV files from directory
            csv_files = list_csv_files(args.data_dir)
            if not csv_files:
                logger.error(f"No CSV files found in {args.data_dir}")
                sys.exit(1)
            
            # Sort CSV files by name
            csv_files.sort()
            
            if args.sample:
                # Just take the first file for testing
                csv_files = [csv_files[0]]
                logger.info(f"Running in sample mode. Will upload only: {csv_files[0]}")
            else:
                logger.info(f"Found {len(csv_files)} CSV files to upload")
            
            # Choose between sequential and parallel processing
            if args.parallel and len(csv_files) > 1:
                success_count = upload_files_parallel(
                    conn, 
                    csv_files, 
                    args.table, 
                    args.max_reject,
                    args.workers,
                    args.batch_size
                )
            else:
                # Upload each file sequentially
                success_count = 0
                total_records = 0
                
                with tqdm(total=len(csv_files), desc="Processing files") as pbar:
                    for csv_file in csv_files:
                        success, records, duration = upload_file_to_heavydb(
                            conn, 
                            csv_file, 
                            args.table, 
                            args.max_reject,
                            chunk_size=args.chunk_size
                        )
                        if success:
                            success_count += 1
                            total_records += records
                        pbar.update(1)
                
                logger.info(f"Sequential processing complete: {success_count}/{len(csv_files)} files uploaded successfully")
                logger.info(f"Total records: {total_records}")
        
        # Apply GPU optimizations if requested
        if args.gpu_optimize:
            optimize_for_gpu(conn, args.table)
        
        # Analyze table statistics if requested
        if args.analyze:
            analyze_table_statistics(conn, args.table)
        
        # Log overall processing time
        overall_end_time = time.time()
        overall_duration = overall_end_time - overall_start_time
        
        if args.file:
            logger.info(f"Upload complete. Successfully uploaded file: {success_count == 1}")
        else:
            logger.info(f"Upload complete. Successfully uploaded {success_count} out of {len(csv_files)} files.")
        
        logger.info(f"Total processing time: {overall_duration:.2f} seconds")
    
    finally:
        # Close connection
        if conn:
            conn.close()
            logger.info("Database connection closed")

if __name__ == "__main__":
    main() 