#!/usr/bin/env python3
"""
Display an option chain from a sample CSV file
"""

import pandas as pd
from tabulate import tabulate

# Load the sample data
csv_file = 'sample_option_chain.csv'
print(f"Loading option chain data from {csv_file}")

try:
    # Read CSV file
    df = pd.read_csv(csv_file)
    
    # Display basic information
    print("\nNifty Option Chain")
    print("Date: 2023-01-18, Expiry: 2023-01-25")
    print("Underlying Price: 18,162.50, ATM Strike: 18,100")
    
    # Format the data
    formatted_data = []
    
    for _, row in df.iterrows():
        # Highlight ATM row
        is_atm = row['call_type'] == 'ATM'
        
        formatted_data.append([
            # Call side
            f"{int(row['call_oi']):,}",
            f"{int(row['call_volume']):,}",
            f"{row['call_iv']:.2f}",
            f"{row['call_price']:.2f}",
            row['call_type'],
            
            # Strike
            row['strike'],
            
            # Put side
            row['put_type'],
            f"{row['put_price']:.2f}",
            f"{row['put_iv']:.2f}",
            f"{int(row['put_volume']):,}",
            f"{int(row['put_oi']):,}"
        ])
    
    # Create headers
    headers = [
        "Call OI", "Volume", "IV", "Price", "Type",
        "Strike",
        "Type", "Price", "IV", "Volume", "Put OI"
    ]
    
    # Display the option chain
    print("\nOption Chain:")
    print(tabulate(formatted_data, headers=headers, tablefmt='grid'))
    
    # Summary
    print(f"\nDisplayed {len(df)} strikes")
    
    # Add some analysis
    atm_row = df[df['call_type'] == 'ATM'].iloc[0]
    pcr_oi = df['put_oi'].sum() / df['call_oi'].sum()
    pcr_vol = df['put_volume'].sum() / df['call_volume'].sum()
    
    print("\nAnalysis:")
    print(f"ATM Strike: {atm_row['strike']}")
    print(f"ATM Call Premium: {atm_row['call_price']:.2f}")
    print(f"ATM Put Premium: {atm_row['put_price']:.2f}")
    print(f"ATM Straddle: {atm_row['call_price'] + atm_row['put_price']:.2f}")
    print(f"Put-Call Ratio (OI): {pcr_oi:.2f}")
    print(f"Put-Call Ratio (Volume): {pcr_vol:.2f}")
    
except Exception as e:
    print(f"Error: {e}") 