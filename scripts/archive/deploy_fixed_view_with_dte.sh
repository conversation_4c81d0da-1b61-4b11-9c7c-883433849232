#!/bin/bash
# Deploy Trading Day DTE Functions and Fixed View
# This script deploys the trading day DTE functions and updates
# the nifty_option_chain materialized view to address duplicates and add DTE calculations.

set -e  # Exit on any error

# Configuration
DB_HOST=${DB_HOST:-"127.0.0.1"}
DB_PORT=${DB_PORT:-"6274"}
DB_USER=${DB_USER:-"admin"}
DB_PASSWORD=${DB_PASSWORD:-"HyperInteractive"}
DB_NAME=${DB_NAME:-"heavyai"}

HEAVYSQL_CMD="/opt/heavyai/bin/heavysql"
HEAVYSQL_OPTS="-s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASSWORD -d $DB_NAME"

echo "===== Deploying NSE Trading Day DTE Implementation with Fixed View ====="
echo "Database: $DB_NAME on $DB_HOST:$DB_PORT"

# Function to execute SQL script
execute_sql() {
    local sql_file="$1"
    local description="$2"
    
    echo "Executing $description..."
    ${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < "$sql_file"
    
    if [ $? -eq 0 ]; then
        echo "✓ $description completed successfully"
    else
        echo "✗ $description failed"
        exit 1
    fi
}

# Deploy NSE trading day functions
echo "Step 1: Deploying NSE trading day functions..."
execute_sql "sql_functions/nse_trading_days.sql" "NSE trading day functions"

# Deploy updated materialized view with DTE calculation and duplicate handling
echo "Step 2: Updating nifty_option_chain materialized view..."
execute_sql "nifty_view_fixed_with_dte.sql" "Fixed materialized view with DTE"

# Create post-deployment integrity checks
echo "Step 3: Creating post-deployment integrity check..."
cat <<EOF > check_post_deployment.sql
SELECT COUNT(*) AS row_count FROM nifty_option_chain;
EOF

cat <<EOF > check_duplicates_post.sql
SELECT COUNT(*) AS duplicate_count
FROM (
    SELECT 
        trade_date, expiry_date, strike, 
        COUNT(*) AS row_count
    FROM nifty_option_chain
    GROUP BY trade_date, expiry_date, strike
    HAVING COUNT(*) > 1
) AS temp;
EOF

cat <<EOF > check_dte.sql
SELECT COUNT(*) AS rows_with_dte
FROM nifty_option_chain
WHERE dte_trading_days IS NOT NULL;
EOF

# Execute post-deployment checks
echo "Step 4: Running post-deployment checks..."
echo "Checking row count:"
${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < check_post_deployment.sql

echo "Checking for duplicates:"
${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < check_duplicates_post.sql

echo "Checking DTE implementation:"
${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < check_dte.sql

echo "===== Deployment Complete ====="
echo "Trading day DTE has been successfully implemented in the nifty_option_chain view."
echo "Data integrity issues have been addressed, and duplicates have been eliminated."
echo "You can verify the implementation by querying the dte_trading_days column." 