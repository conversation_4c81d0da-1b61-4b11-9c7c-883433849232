#!/bin/bash
# Deploy Updated View with DTE and Duplicate Elimination
# This script drops and recreates the nifty_option_chain view with the new structure

set -e  # Exit on any error

# Configuration
DB_HOST=${DB_HOST:-"127.0.0.1"}
DB_PORT=${DB_PORT:-"6274"}
DB_USER=${DB_USER:-"admin"}
DB_PASSWORD=${DB_PASSWORD:-"HyperInteractive"}
DB_NAME=${DB_NAME:-"heavyai"}

HEAVYSQL_CMD="/opt/heavyai/bin/heavysql"
HEAVYSQL_OPTS="-s $DB_HOST --port $DB_PORT -u $DB_USER -p $DB_PASSWORD -d $DB_NAME"

echo "===== Deploying Updated Nifty Option Chain View ====="
echo "Database: $DB_NAME on $DB_HOST:$DB_PORT"

# Begin implementation
echo "Step 1: Executing DROP+CREATE view script..."
${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < implement_fixed_view_with_dte.sql

if [ $? -eq 0 ]; then
    echo "✓ View successfully updated"
else
    echo "✗ View update failed"
    exit 1
fi

# Create post-deployment validation queries
echo "Step 2: Creating validation queries..."

# Check row count
cat <<EOF > check_row_count.sql
SELECT COUNT(*) FROM nifty_option_chain;
EOF

# Check for duplicates
cat <<EOF > check_for_duplicates.sql
SELECT COUNT(*) AS duplicate_count
FROM (
    SELECT 
        trade_date, expiry_date, strike, 
        COUNT(*) AS row_count
    FROM nifty_option_chain
    GROUP BY trade_date, expiry_date, strike
    HAVING COUNT(*) > 1
) AS temp;
EOF

# Check DTE column
cat <<EOF > check_dte_column.sql
SELECT 
    MIN(dte) AS min_dte,
    MAX(dte) AS max_dte,
    AVG(dte) AS avg_dte
FROM nifty_option_chain;
EOF

# Sample data
cat <<EOF > check_sample_data.sql
SELECT 
    trade_date,
    expiry_date,
    strike,
    atm_strike,
    dte,
    call_moneyness_code,
    put_moneyness_code
FROM nifty_option_chain
WHERE strike = atm_strike
LIMIT 5;
EOF

# Run validation queries
echo "Step 3: Validating implementation..."

echo "Checking row count:"
${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < check_row_count.sql

echo "Checking for duplicates:"
${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < check_for_duplicates.sql

echo "Checking DTE column statistics:"
${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < check_dte_column.sql

echo "Viewing sample data:"
${HEAVYSQL_CMD} ${HEAVYSQL_OPTS} -q < check_sample_data.sql

echo "===== Deployment Complete ====="
echo "The nifty_option_chain view has been successfully updated with the following changes:"
echo " - Duplicate rows have been eliminated"
echo " - DTE column has been added"
echo " - Structure has been optimized for better performance"
echo "You can verify the implementation by querying the view directly." 