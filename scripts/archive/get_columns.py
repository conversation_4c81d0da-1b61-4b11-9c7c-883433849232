#!/usr/bin/env python3
"""
Get column names from nifty_option_chain view
"""

from heavyai import connect

# Connect to HeavyDB
conn = connect(
    user="admin",
    password="HyperInteractive",
    host="127.0.0.1",
    port=6274,
    dbname="heavyai"
)

try:
    # Create a cursor
    cursor = conn.cursor()
    
    print("Getting column names from nifty_option_chain view...")
    
    # Execute a simple query to get column descriptions
    cursor.execute("SELECT * FROM nifty_option_chain LIMIT 1")
    
    # Get the column names
    columns = [desc[0] for desc in cursor.description]
    
    print("\nColumn names in nifty_option_chain:")
    for i, column in enumerate(columns, 1):
        print(f"{i:3d}. {column}")
    
    # Check if DTE-related columns exist
    dte_columns = [col for col in columns if 'dte' in col.lower()]
    
    if dte_columns:
        print("\nDTE-related columns found:")
        for col in dte_columns:
            print(f"- {col}")
    else:
        print("\nNo DTE-related columns found.")

except Exception as e:
    print(f"Error: {e}")
finally:
    conn.close()
    print("\nConnection closed.") 