#!/usr/bin/env python3
import os
import sys
import glob
import argparse
import logging
import time
import subprocess
from heavyai import connect

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_heavydb(host="127.0.0.1", port=6274, user="admin", password="HyperInteractive", dbname="heavyai"):
    """Connect to HeavyDB using the Python API"""
    try:
        logger.info(f"Connecting to HeavyDB at {host}:{port}")
        conn = connect(host=host, port=port, user=user, password=password, dbname=dbname)
        logger.info("Successfully connected to HeavyDB")
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to HeavyDB: {str(e)}")
        return None

def create_tables(conn):
    """Create the necessary tables with unique constraints"""
    try:
        # Drop tables if they exist
        logger.info("Dropping existing tables if they exist")
        conn.execute("DROP TABLE IF EXISTS nifty_greeks_temp;")
        conn.execute("DROP TABLE IF EXISTS nifty_greeks;")
        
        # Create temporary table for initial data loading
        logger.info("Creating temporary table for initial loading")
        temp_table_sql = """
        CREATE TABLE nifty_greeks_temp (
          trade_date TEXT ENCODING DICT(32),
          trade_time TEXT ENCODING DICT(32),
          expiry_date TEXT ENCODING DICT(32),
          strike DOUBLE,
          ce_symbol TEXT ENCODING DICT(32),
          ce_open DOUBLE,
          ce_high DOUBLE,
          ce_low DOUBLE,
          ce_close DOUBLE,
          ce_volume DOUBLE,
          ce_oi DOUBLE,
          ce_coi DOUBLE,
          ce_iv DOUBLE,
          ce_delta DOUBLE,
          ce_gamma DOUBLE,
          ce_theta DOUBLE,
          ce_vega DOUBLE,
          ce_rho DOUBLE,
          pe_symbol TEXT ENCODING DICT(32),
          pe_open DOUBLE,
          pe_high DOUBLE,
          pe_low DOUBLE,
          pe_close DOUBLE,
          pe_volume DOUBLE,
          pe_oi DOUBLE,
          pe_coi DOUBLE,
          pe_iv DOUBLE,
          pe_delta DOUBLE,
          pe_gamma DOUBLE,
          pe_theta DOUBLE,
          pe_vega DOUBLE,
          pe_rho DOUBLE,
          underlying_price DOUBLE
        );"""
        conn.execute(temp_table_sql)
        
        # Create final table with optimized data types and unique constraint
        logger.info("Creating main table with DATE/TIME types")
        main_table_sql = """
        CREATE TABLE nifty_greeks (
          trade_date DATE ENCODING DAYS(32),
          trade_time TIME ENCODING FIXED(32),
          expiry_date DATE ENCODING DAYS(32),
          strike DOUBLE,
          ce_symbol TEXT ENCODING DICT(32),
          ce_open DOUBLE,
          ce_high DOUBLE,
          ce_low DOUBLE,
          ce_close DOUBLE,
          ce_volume DOUBLE,
          ce_oi DOUBLE,
          ce_coi DOUBLE,
          ce_iv DOUBLE,
          ce_delta DOUBLE,
          ce_gamma DOUBLE,
          ce_theta DOUBLE,
          ce_vega DOUBLE,
          ce_rho DOUBLE,
          pe_symbol TEXT ENCODING DICT(32),
          pe_open DOUBLE,
          pe_high DOUBLE,
          pe_low DOUBLE,
          pe_close DOUBLE,
          pe_volume DOUBLE,
          pe_oi DOUBLE,
          pe_coi DOUBLE,
          pe_iv DOUBLE,
          pe_delta DOUBLE,
          pe_gamma DOUBLE,
          pe_theta DOUBLE,
          pe_vega DOUBLE,
          pe_rho DOUBLE,
          underlying_price DOUBLE
        );"""
        conn.execute(main_table_sql)
        
        # Create a table to keep track of processed files to avoid duplicates
        logger.info("Creating tracking table for processed files")
        tracking_table_sql = """
        CREATE TABLE IF NOT EXISTS processed_files (
            filename TEXT ENCODING DICT(32),
            processed_time TIMESTAMP,
            record_count INTEGER
        );
        """
        conn.execute(tracking_table_sql)
        
        logger.info("Tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to create tables: {str(e)}")
        return False

def is_file_processed(conn, filename):
    """Check if a file has already been processed"""
    try:
        query = f"SELECT COUNT(*) FROM processed_files WHERE filename = '{os.path.basename(filename)}'"
        count = conn.execute(query).fetchone()[0]
        return count > 0
    except Exception as e:
        logger.error(f"Failed to check if file is processed: {str(e)}")
        return False
        
def mark_file_processed(conn, filename, record_count):
    """Mark a file as processed"""
    try:
        basename = os.path.basename(filename)
        query = f"""
        INSERT INTO processed_files (filename, processed_time, record_count)
        VALUES ('{basename}', NOW(), {record_count})
        """
        conn.execute(query)
        logger.info(f"Marked file {basename} as processed")
        return True
    except Exception as e:
        logger.error(f"Failed to mark file as processed: {str(e)}")
        return False

def list_csv_files():
    """List all CSV files in the data directory"""
    files = glob.glob("market_data/nifty/IV_*.csv")
    # Sort files chronologically by year and month
    def sort_key(filename):
        parts = os.path.basename(filename).split('_')
        year = parts[1]
        month = parts[2]
        # Convert month to numeric for sorting
        month_order = {
            'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04', 
            'may': '05', 'june': '06', 'july': '07', 'aug': '08',
            'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
        }
        return f"{year}{month_order.get(month, '13')}"  # Default to high value if not found
    
    return sorted(files, key=sort_key)

def load_csv_to_temp(conn, csv_file):
    """Load a CSV file into the temporary table"""
    try:
        # Copy the file to the import directory
        import_dir = "/var/lib/heavyai/storage/import"
        basename = os.path.basename(csv_file)
        target_path = os.path.join(import_dir, basename)
        
        logger.info(f"Copying {basename} to import directory")
        copy_cmd = f"sudo cp {csv_file} {target_path}"
        subprocess.run(copy_cmd, shell=True, check=True)
        
        # Load the data using COPY command
        logger.info(f"Loading {basename} into temporary table")
        copy_sql = f"""
        COPY nifty_greeks_temp FROM '{target_path}' 
        WITH (header='true', delimiter=',', quoted='false');
        """
        conn.execute(copy_sql)
        
        # Check row count
        count_query = "SELECT COUNT(*) FROM nifty_greeks_temp"
        row_count = conn.execute(count_query).fetchone()[0]
        logger.info(f"Loaded {row_count} rows into temporary table")
        
        # Clean up import file
        cleanup_cmd = f"sudo rm -f {target_path}"
        subprocess.run(cleanup_cmd, shell=True)
        
        return row_count
    except Exception as e:
        logger.error(f"Failed to load {csv_file}: {str(e)}")
        return 0

def transfer_to_final(conn):
    """Transfer data from temporary table to final table with transformation"""
    try:
        logger.info("Transferring data to final table with date/time conversion")
        transfer_start = time.time()
        
        # Transfer with data type conversion
        transfer_sql = """
        INSERT INTO nifty_greeks 
        SELECT 
            TRY_CAST('20' || SUBSTRING(trade_date FROM 1 FOR 2) || '-' || 
                     SUBSTRING(trade_date FROM 3 FOR 2) || '-' || 
                     SUBSTRING(trade_date FROM 5 FOR 2) AS DATE) AS trade_date,
            TRY_CAST(trade_time AS TIME) AS trade_time,
            TRY_CAST('20' || SUBSTRING(expiry_date FROM 1 FOR 2) || '-' || 
                     SUBSTRING(expiry_date FROM 3 FOR 2) || '-' || 
                     SUBSTRING(expiry_date FROM 5 FOR 2) AS DATE) AS expiry_date,
            strike, ce_symbol, ce_open, ce_high, ce_low, ce_close, ce_volume, 
            ce_oi, ce_coi, ce_iv, ce_delta, ce_gamma, ce_theta, ce_vega, ce_rho,
            pe_symbol, pe_open, pe_high, pe_low, pe_close, pe_volume, 
            pe_oi, pe_coi, pe_iv, pe_delta, pe_gamma, pe_theta, pe_vega, pe_rho,
            underlying_price
        FROM nifty_greeks_temp
        """
        conn.execute(transfer_sql)
        
        # Get count of inserted rows
        count_query = "SELECT COUNT(*) FROM nifty_greeks"
        final_count = conn.execute(count_query).fetchone()[0]
        
        transfer_end = time.time()
        logger.info(f"Transferred {final_count} rows to final table in {transfer_end - transfer_start:.2f} seconds")
        
        # Clear temporary table
        conn.execute("TRUNCATE TABLE nifty_greeks_temp")
        
        return final_count
    except Exception as e:
        logger.error(f"Failed to transfer data: {str(e)}")
        return 0

def apply_gpu_optimizations(conn):
    """Apply GPU-specific optimizations to the table"""
    try:
        logger.info("Applying GPU optimizations")
        
        # Configure HeavyDB to use GPU memory efficiently
        try:
            conn.execute("ALTER SESSION SET enable_watchdog = false;")
            logger.info("Disabled watchdog for better GPU performance")
        except:
            logger.warning("Could not disable watchdog")
            
        try:
            conn.execute("ALTER SESSION SET allow_loop_joins = true;")
            logger.info("Enabled loop joins for better query performance")
        except:
            logger.warning("Could not enable loop joins")
            
        # Set memory allocation for GPU
        try:
            conn.execute("SET memory_level = 'gpu';")
            logger.info("Set memory level to GPU")
        except:
            logger.warning("Could not set memory level to GPU")
            
        logger.info("GPU optimizations applied successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to apply GPU optimizations: {str(e)}")
        return False

def analyze_table(conn):
    """Print statistics about the loaded data"""
    try:
        logger.info("Analyzing loaded data")
        
        # Get total count
        count_query = "SELECT COUNT(*) FROM nifty_greeks"
        total_count = conn.execute(count_query).fetchone()[0]
        logger.info(f"Total records: {total_count}")
        
        # Date range
        date_range_query = "SELECT MIN(trade_date), MAX(trade_date) FROM nifty_greeks"
        min_date, max_date = conn.execute(date_range_query).fetchone()
        logger.info(f"Date range: {min_date} to {max_date}")
        
        # Records by year
        year_query = "SELECT DATEPART('year', trade_date) as year, COUNT(*) FROM nifty_greeks GROUP BY year ORDER BY year"
        results = conn.execute(year_query).fetchall()
        for year, count in results:
            logger.info(f"Year {year}: {count} records")
        
        # Check for NULL values
        null_check_query = """
        SELECT 
            SUM(CASE WHEN trade_date IS NULL THEN 1 ELSE 0 END) as null_date,
            SUM(CASE WHEN trade_time IS NULL THEN 1 ELSE 0 END) as null_time,
            SUM(CASE WHEN expiry_date IS NULL THEN 1 ELSE 0 END) as null_expiry
        FROM nifty_greeks
        """
        null_date, null_time, null_expiry = conn.execute(null_check_query).fetchone()
        logger.info(f"NULL values: date={null_date}, time={null_time}, expiry={null_expiry}")
        
        return True
    except Exception as e:
        logger.error(f"Failed to analyze data: {str(e)}")
        return False

def main():
    # Connect to the database
    conn = connect_to_heavydb()
    if not conn:
        sys.exit(1)
    
    try:
        # Create the tables
        if not create_tables(conn):
            sys.exit(1)
        
        # Get list of CSV files
        csv_files = list_csv_files()
        logger.info(f"Found {len(csv_files)} CSV files to process")
        
        # Process each file
        total_loaded = 0
        for i, csv_file in enumerate(csv_files):
            basename = os.path.basename(csv_file)
            logger.info(f"Processing file {i+1}/{len(csv_files)}: {basename}")
            
            # Skip if already processed
            if is_file_processed(conn, csv_file):
                logger.info(f"Skipping {basename} as it has already been processed")
                continue
            
            # Load to temp table
            rows_loaded = load_csv_to_temp(conn, csv_file)
            if rows_loaded > 0:
                # Get current count before transfer
                current_count_query = "SELECT COUNT(*) FROM nifty_greeks"
                current_count = conn.execute(current_count_query).fetchone()[0]
                
                # Transfer to final table
                transfer_to_final(conn)
                
                # Get new count after transfer
                new_count_query = "SELECT COUNT(*) FROM nifty_greeks"
                new_count = conn.execute(new_count_query).fetchone()[0]
                
                # Calculate rows added
                rows_added = new_count - current_count
                total_loaded += rows_added
                
                # Mark file as processed
                mark_file_processed(conn, csv_file, rows_added)
            
            logger.info(f"Progress: {total_loaded} total records loaded")
        
        # Apply GPU optimizations
        apply_gpu_optimizations(conn)
        
        # Analyze the loaded data
        analyze_table(conn)
        
        logger.info(f"Data loading completed successfully. Total records: {total_loaded}")
    
    finally:
        # Close connection
        if conn:
            conn.close()
            logger.info("Database connection closed")

if __name__ == "__main__":
    main() 