#!/usr/bin/env python3
import os
import glob
import subprocess
import sys
import time
import argparse
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def list_csv_files(directory):
    """List all CSV files in the specified directory"""
    return glob.glob(os.path.join(directory, "*.csv"))

def execute_command(cmd):
    """Execute a shell command and return the output"""
    logger.info(f"Executing: {cmd}")
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        shell=True
    )
    stdout, stderr = process.communicate()
    return process.returncode, stdout.decode('utf-8'), stderr.decode('utf-8')

def test_heavydb_connection(host, port, dbname, username, password):
    """Test connection to HeavyDB using heavysql"""
    # Try without specifying database name in the command-line args
    test_cmd = f"/opt/heavyai/bin/heavysql -u {username} -p {password} -s {host} --port {port}"
    
    # Create a temporary SQL file with a simple query
    temp_sql_file = f"/tmp/test_connection_{int(time.time())}.sql"
    with open(temp_sql_file, 'w') as f:
        f.write(f"\\c {dbname};\nSELECT 1;")
    
    # Execute the SQL command with input redirection
    cmd = f"{test_cmd} < {temp_sql_file}"
    returncode, stdout, stderr = execute_command(cmd)
    
    # Clean up
    try:
        os.remove(temp_sql_file)
    except Exception:
        pass
    
    if returncode == 0 and "Connection refused" not in stderr:
        logger.info("Successfully connected to HeavyDB")
        return True
    
    logger.error(f"Failed to connect to HeavyDB: {stdout} {stderr}")
    return False

def create_table_if_not_exists(host, port, dbname, username, password, table_name):
    """Create the table if it doesn't exist"""
    # Table definition for nifty_greeks
    create_table_sql = f"""
    \\c {dbname};
    CREATE TABLE IF NOT EXISTS nifty_greeks (
      trade_date DATE ENCODING DAYS(32),
      trade_time TIME,
      expiry_date DATE ENCODING DAYS(32),
      strike DOUBLE,
      ce_symbol TEXT ENCODING DICT(32),
      ce_open DOUBLE,
      ce_high DOUBLE,
      ce_low DOUBLE,
      ce_close DOUBLE,
      ce_volume DOUBLE,
      ce_oi DOUBLE,
      ce_coi DOUBLE,
      ce_iv DOUBLE,
      ce_delta DOUBLE,
      ce_gamma DOUBLE,
      ce_theta DOUBLE,
      ce_vega DOUBLE,
      ce_rho DOUBLE,
      pe_symbol TEXT ENCODING DICT(32),
      pe_open DOUBLE,
      pe_high DOUBLE,
      pe_low DOUBLE,
      pe_close DOUBLE,
      pe_volume DOUBLE,
      pe_oi DOUBLE,
      pe_coi DOUBLE,
      pe_iv DOUBLE,
      pe_delta DOUBLE,
      pe_gamma DOUBLE,
      pe_theta DOUBLE,
      pe_vega DOUBLE,
      pe_rho DOUBLE,
      underlying_price DOUBLE
    );
    """
    
    # Create a temporary SQL file
    temp_sql_file = f"/tmp/create_table_{int(time.time())}.sql"
    with open(temp_sql_file, 'w') as f:
        f.write(create_table_sql)
    
    # Execute the SQL command
    cmd = f"/opt/heavyai/bin/heavysql -u {username} -p {password} -s {host} --port {port} < {temp_sql_file}"
    returncode, stdout, stderr = execute_command(cmd)
    
    # Clean up
    try:
        os.remove(temp_sql_file)
    except Exception:
        pass
    
    if returncode != 0 or "Error" in stdout or "Error" in stderr:
        logger.error(f"Failed to create table: {stdout} {stderr}")
        return False
    
    logger.info(f"Table {table_name} created or already exists")
    return True

def upload_file_to_heavydb(host, port, dbname, username, password, csv_file, table_name, options=None):
    """Upload CSV file to HeavyDB using COPY FROM command"""
    if options is None:
        options = {}
    
    # Generate WITH clause if options are provided
    with_clause = ""
    if options:
        with_options = []
        for key, value in options.items():
            with_options.append(f"{key}='{value}'")
        with_clause = f" WITH ({', '.join(with_options)})"
    
    # Use the whitelisted path via the import directory which has a symbolic link to our shared directory
    # Since there's already a symlink from /var/lib/heavyai/storage/import/shared to /srv/samba/shared
    # We can use that path for the CSV file
    import_path = f"/var/lib/heavyai/storage/import/shared/{os.path.basename(csv_file)}"
    
    # Build the COPY FROM command
    copy_cmd = f"""
    \\c {dbname};
    COPY {table_name} FROM '{import_path}'{with_clause};
    """
    
    # Create a temporary SQL file
    temp_sql_file = f"/tmp/copy_{int(time.time())}.sql"
    with open(temp_sql_file, 'w') as f:
        f.write(copy_cmd)
    
    # Execute the SQL command
    cmd = f"/opt/heavyai/bin/heavysql -u {username} -p {password} -s {host} --port {port} < {temp_sql_file}"
    
    logger.info(f"Uploading {os.path.basename(csv_file)} to table {table_name}")
    returncode, stdout, stderr = execute_command(cmd)
    
    # Clean up
    try:
        os.remove(temp_sql_file)
    except Exception:
        pass
    
    if returncode != 0 or "Error" in stdout or "Error" in stderr:
        logger.error(f"Failed to upload {os.path.basename(csv_file)}: {stdout} {stderr}")
        return False
    
    logger.info(f"Successfully uploaded {os.path.basename(csv_file)}")
    return True

def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Upload CSV files to HeavyDB')
    parser.add_argument('--data-dir', default='market_data/nifty', help='Directory containing CSV files')
    parser.add_argument('--table', default='nifty_greeks', help='Target table name')
    parser.add_argument('--host', default='localhost', help='HeavyDB host')
    parser.add_argument('--port', default='6279', help='HeavyDB port')
    parser.add_argument('--db', default='heavyai', help='Database name')
    parser.add_argument('--user', default='admin', help='Database user')
    parser.add_argument('--password', default='HyperInteractive', help='Database password')
    parser.add_argument('--sample', action='store_true', help='Upload only one file as a test')
    parser.add_argument('--delimiter', default=',', help='CSV delimiter character')
    parser.add_argument('--header', default='true', help='Whether files have header row')
    parser.add_argument('--quoted', default='true', help='Whether fields are quoted')
    parser.add_argument('--date-format', default='%y%m%d', help='Format for date columns')
    parser.add_argument('--create-table', action='store_true', help='Create table if it doesn\'t exist')
    parser.add_argument('--max-reject', default='1000', help='Maximum number of rejected records')
    args = parser.parse_args()
    
    # Test connection
    logger.info(f"Testing connection to HeavyDB at {args.host}:{args.port}")
    if not test_heavydb_connection(args.host, args.port, args.db, args.user, args.password):
        logger.error("Unable to establish database connection. Exiting.")
        sys.exit(1)
    
    # Create table if requested
    if args.create_table:
        if not create_table_if_not_exists(args.host, args.port, args.db, args.user, args.password, args.table):
            sys.exit(1)
    
    # List CSV files
    csv_files = list_csv_files(args.data_dir)
    if not csv_files:
        logger.error(f"No CSV files found in {args.data_dir}")
        sys.exit(1)
    
    # Sort CSV files by name
    csv_files.sort()
    
    if args.sample:
        # Just take the first file for testing
        csv_files = [csv_files[0]]
        logger.info(f"Running in sample mode. Will upload only: {csv_files[0]}")
    else:
        logger.info(f"Found {len(csv_files)} CSV files to upload")
    
    # Set up options for COPY FROM command
    copy_options = {
        'delimiter': args.delimiter,
        'header': args.header,
        'quoted': args.quoted,
        'date_format': args.date_format,
        'max_reject': args.max_reject
    }
    
    # Upload each file
    success_count = 0
    for csv_file in csv_files:
        if upload_file_to_heavydb(args.host, args.port, args.db, args.user, args.password, 
                                 csv_file, args.table, copy_options):
            success_count += 1
    
    logger.info(f"Upload complete. Successfully uploaded {success_count} out of {len(csv_files)} files.")

if __name__ == "__main__":
    main() 