#!/usr/bin/env python3
"""
Display data from HeavyDB in a clean, tabular format
Using the heavyai Python package as per documentation at
https://heavyai.readthedocs.io/en/latest/
"""

from heavyai import connect
import pandas as pd
from tabulate import tabulate  # For nice table formatting

# Connect to HeavyDB following the documentation
conn = connect(
    user="admin",
    password="HyperInteractive",
    host="127.0.0.1",
    port=6274,
    dbname="heavyai"
)

# Query to run - getting ATM strikes and nearby strikes
query = """
SELECT 
    trade_date, 
    expiry_date,
    underlying_price,
    atm_strike,
    strike, 
    call_moneyness, 
    put_moneyness,
    ce_close, 
    pe_close
FROM nifty_sample
ORDER BY trade_date, expiry_date, strike
LIMIT 100
"""

# Execute query and get results as pandas DataFrame
print("Executing query...")
try:
    # Create a cursor and execute the query
    cursor = conn.cursor()
    cursor.execute(query)
    
    # Fetch column names
    columns = [desc[0] for desc in cursor.description]
    
    # Fetch all rows
    rows = cursor.fetchall()
    
    # Create a pandas DataFrame
    df = pd.DataFrame(rows, columns=columns)
    
    # Display the data in a clean format
    print("\nQuery Results:")
    print(tabulate(df, headers='keys', tablefmt='pretty', showindex=False))
    
    # Display some summary statistics
    print("\nSummary:")
    print(f"Number of rows: {len(df)}")
    
    if len(df) > 0:
        unique_dates = df['trade_date'].nunique()
        unique_expiries = df['expiry_date'].nunique()
        unique_strikes = df['strike'].nunique()
        print(f"Unique trade dates: {unique_dates}")
        print(f"Unique expiry dates: {unique_expiries}")
        print(f"Unique strike prices: {unique_strikes}")
        
        # Show ATM strikes by date/expiry
        print("\nATM Strikes:")
        atm_data = df[['trade_date', 'expiry_date', 'underlying_price', 'atm_strike']].drop_duplicates()
        print(tabulate(atm_data, headers='keys', tablefmt='pretty', showindex=False))
    
except Exception as e:
    print(f"Error executing query: {e}")
finally:
    # Close connection
    conn.close()
    print("\nConnection closed.") 