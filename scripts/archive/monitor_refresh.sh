#!/bin/bash
# monitor_refresh.sh
# Monitoring script for the Nifty Option Chain materialized view
# This script checks if new data is available in the source table and refreshes the view if needed
# Intended to be run as a scheduled task (e.g., via cron)

# Default connection parameters
HOST="127.0.0.1"
PORT="6274"
USER="admin"
PASSWORD="HyperInteractive"
DATABASE="heavyai"
FORCE_REFRESH=0
LOG_FILE="/var/log/nifty_option_chain_refresh.log"
LOCK_FILE="/tmp/nifty_option_chain_refresh.lock"

# Usage information
function show_usage {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --host HOST         HeavyDB host (default: $HOST)"
    echo "  -p, --port PORT         HeavyDB port (default: $PORT)"
    echo "  -u, --user USER         HeavyDB user (default: $USER)"
    echo "  -P, --password PASSWORD HeavyDB password"
    echo "  -d, --database DATABASE HeavyDB database (default: $DATABASE)"
    echo "  -f, --force             Force refresh regardless of data changes"
    echo "  -l, --log-file FILE     Log file path (default: $LOG_FILE)"
    echo "  --help                  Show this help message"
    exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        -h|--host)
            HOST="$2"
            shift
            shift
            ;;
        -p|--port)
            PORT="$2"
            shift
            shift
            ;;
        -u|--user)
            USER="$2"
            shift
            shift
            ;;
        -P|--password)
            PASSWORD="$2"
            shift
            shift
            ;;
        -d|--database)
            DATABASE="$2"
            shift
            shift
            ;;
        -f|--force)
            FORCE_REFRESH=1
            shift
            ;;
        -l|--log-file)
            LOG_FILE="$2"
            shift
            shift
            ;;
        --help)
            show_usage
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            ;;
    esac
done

# Function to log messages
function log_message {
    local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] $1" >> "$LOG_FILE"
    echo "[$timestamp] $1"
}

# Check if another instance is running
if [ -f "$LOCK_FILE" ]; then
    pid=$(cat "$LOCK_FILE")
    if ps -p "$pid" > /dev/null; then
        log_message "Another instance is already running with PID $pid. Exiting."
        exit 1
    else
        log_message "Removing stale lock file from crashed process."
        rm -f "$LOCK_FILE"
    fi
fi

# Create lock file
echo $$ > "$LOCK_FILE"

# Make sure lock file is removed when script exits
trap "rm -f $LOCK_FILE; exit" INT TERM EXIT

# Function to run a SQL query and get the result
function run_sql_query {
    local query=$1
    local result=$(/opt/heavyai/bin/heavysql -s "$HOST" --port "$PORT" -u "$USER" -p "$PASSWORD" -d "$DATABASE" -q -t -c "$query")
    echo "$result"
}

# Start monitoring
log_message "Starting Nifty Option Chain monitor"

# Check if the view exists
view_exists=$(run_sql_query "SELECT COUNT(*) FROM pg_catalog.pg_class c JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace WHERE c.relname = 'nifty_option_chain' AND n.nspname = current_schema() AND c.relkind = 'm'")

if [ "$view_exists" -eq "0" ]; then
    log_message "ERROR: The nifty_option_chain materialized view does not exist."
    exit 1
fi

# Check if refresh is needed by comparing source and view data counts
if [ $FORCE_REFRESH -eq 0 ]; then
    source_count=$(run_sql_query "SELECT COUNT(*) FROM nifty_greeks")
    view_count=$(run_sql_query "SELECT COUNT(*) FROM nifty_option_chain")
    
    source_max_date=$(run_sql_query "SELECT MAX(trade_date) FROM nifty_greeks")
    view_max_date=$(run_sql_query "SELECT MAX(trade_date) FROM nifty_option_chain")
    
    log_message "Source table count: $source_count, View count: $view_count"
    log_message "Source max date: $source_max_date, View max date: $view_max_date"
    
    if [ "$source_count" -eq "0" ]; then
        log_message "WARNING: The source table appears to be empty."
        exit 0
    fi
    
    if [ "$view_count" -gt "0" ] && [ "$source_max_date" = "$view_max_date" ]; then
        log_message "No new data detected. Refresh not needed."
        exit 0
    fi
    
    log_message "New data detected. Refreshing materialized view."
else
    log_message "Forced refresh requested."
fi

# Perform the refresh
start_time=$(date +%s)
log_message "Starting refresh of nifty_option_chain..."

refresh_result=$(run_sql_query "SELECT refresh_nifty_option_chain()")
refresh_status=$?

end_time=$(date +%s)
execution_time=$((end_time - start_time))

if [ $refresh_status -eq 0 ]; then
    log_message "Refresh completed successfully in $execution_time seconds."
    
    # Get updated counts for verification
    new_count=$(run_sql_query "SELECT COUNT(*) FROM nifty_option_chain")
    log_message "New view count: $new_count"
else
    log_message "ERROR: Refresh failed with status $refresh_status"
    exit 1
fi

# Remove lock file
rm -f "$LOCK_FILE"

exit 0 