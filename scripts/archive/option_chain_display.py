#!/usr/bin/env python3
"""
Display a complete option chain with call data on left, strikes in middle, and put data on right
Using the nifty_option_chain materialized view
"""

from heavyai import connect
import pandas as pd
from tabulate import tabulate
import numpy as np

# Connect to HeavyDB
conn = connect(
    user="admin",
    password="HyperInteractive",
    host="127.0.0.1",
    port=6274,
    dbname="heavyai"
)

# Specific date and expiry
trade_date = '2023-01-18'
expiry_date = '2023-01-25'

# Query to get all options data from the materialized view
query = f"""
SELECT 
    trade_date, 
    expiry_date,
    underlying_price,
    atm_strike,
    strike, 
    call_moneyness, 
    put_moneyness,
    ce_symbol,
    ce_open,
    ce_high,
    ce_low,
    ce_close,
    ce_volume,
    ce_oi,
    ce_iv,
    ce_delta,
    ce_gamma,
    ce_theta,
    ce_vega,
    pe_symbol,
    pe_open,
    pe_high,
    pe_low,
    pe_close,
    pe_volume,
    pe_oi,
    pe_iv,
    pe_delta,
    pe_gamma,
    pe_theta,
    pe_vega
FROM nifty_option_chain
WHERE trade_date = '{trade_date}' AND expiry_date = '{expiry_date}'
ORDER BY strike
"""

print(f"Fetching option chain from materialized view for trade date {trade_date} with expiry {expiry_date}...")
try:
    # Execute query
    cursor = conn.cursor()
    cursor.execute(query)
    
    # Get column names and fetch all rows
    columns = [desc[0] for desc in cursor.description]
    rows = cursor.fetchall()
    
    # Create DataFrame
    df = pd.DataFrame(rows, columns=columns)
    
    if len(df) == 0:
        print("No data found for the specified date and expiry.")
        exit()
    
    # Get the ATM strike
    atm_strike = df['atm_strike'].iloc[0]
    
    # Format data for option chain display
    option_chain = []
    
    # Filter out duplicates by strike
    unique_strikes = df['strike'].unique()
    
    for strike in sorted(unique_strikes):
        strike_data = df[df['strike'] == strike].iloc[0]
        
        # Get moneyness directly from the view
        ce_status = strike_data['call_moneyness']
        pe_status = strike_data['put_moneyness']
        
        # Determine if this strike is ATM
        strike_status = "ATM" if ce_status == "ATM" else ""
        
        # Call side data
        call_oi = strike_data['ce_oi'] if 'ce_oi' in strike_data else 0
        call_change = "" # Would calculate change from previous day if available
        call_volume = strike_data['ce_volume'] if 'ce_volume' in strike_data else 0
        call_iv = strike_data['ce_iv'] if 'ce_iv' in strike_data else 0
        call_ltp = strike_data['ce_close'] if 'ce_close' in strike_data else 0
        call_delta = strike_data['ce_delta'] if 'ce_delta' in strike_data else 0
        call_gamma = strike_data['ce_gamma'] if 'ce_gamma' in strike_data else 0
        call_theta = strike_data['ce_theta'] if 'ce_theta' in strike_data else 0
        call_vega = strike_data['ce_vega'] if 'ce_vega' in strike_data else 0
        
        # Put side data
        put_oi = strike_data['pe_oi'] if 'pe_oi' in strike_data else 0
        put_change = "" # Would calculate change from previous day if available
        put_volume = strike_data['pe_volume'] if 'pe_volume' in strike_data else 0
        put_iv = strike_data['pe_iv'] if 'pe_iv' in strike_data else 0
        put_ltp = strike_data['pe_close'] if 'pe_close' in strike_data else 0
        put_delta = strike_data['pe_delta'] if 'pe_delta' in strike_data else 0
        put_gamma = strike_data['pe_gamma'] if 'pe_gamma' in strike_data else 0
        put_theta = strike_data['pe_theta'] if 'pe_theta' in strike_data else 0
        put_vega = strike_data['pe_vega'] if 'pe_vega' in strike_data else 0
        
        option_chain.append([
            # Call side
            call_oi, call_change, call_volume, call_iv, call_ltp, ce_status, 
            call_delta, call_gamma, call_theta, call_vega,
            # Strike
            strike, strike_status,
            # Put side
            put_delta, put_gamma, put_theta, put_vega, 
            pe_status, put_ltp, put_iv, put_volume, put_change, put_oi
        ])
    
    # Create headers similar to the image
    headers = [
        # Call side
        "Call OI", "Chng", "Volume", "IV", "LTP", "Type",
        "Delta", "Gamma", "Theta", "Vega",
        # Strike
        "Strike", "Status",
        # Put side
        "Delta", "Gamma", "Theta", "Vega", 
        "Type", "LTP", "IV", "Volume", "Chng", "Put OI"
    ]
    
    # Format numbers for better display
    option_chain_df = pd.DataFrame(option_chain, columns=headers)
    
    # Print underlying information
    underlying = df['underlying_price'].iloc[0]
    selection_method = df['selection_method'].iloc[0] if 'selection_method' in df.columns else "N/A"
    print(f"\nNifty Option Chain for {trade_date}, Expiry: {expiry_date}")
    print(f"Underlying Price: {underlying:.2f}, ATM Strike: {atm_strike}")
    print(f"ATM Selection Method: {selection_method}")
    print(f"Total Strikes: {len(unique_strikes)}")
    
    # Print option chain
    print("\nOption Chain:")
    print(tabulate(option_chain_df, headers='keys', tablefmt='pretty', showindex=False, 
                  floatfmt=".4f"))
    
except Exception as e:
    print(f"Error executing query: {e}")
finally:
    conn.close()
    print("\nConnection closed.") 