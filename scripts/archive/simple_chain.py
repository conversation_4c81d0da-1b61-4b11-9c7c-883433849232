#!/usr/bin/env python3
"""
Display a simple option chain for a specific date and expiry
"""

from heavyai import connect
import pandas as pd
from tabulate import tabulate

# Specific query parameters
trade_date = '2023-01-18'
expiry_date = '2023-01-25'

# Connect to HeavyDB
conn = connect(
    user="admin",
    password="HyperInteractive",
    host="127.0.0.1",
    port=6274,
    dbname="heavyai"
)

try:
    # Create a cursor
    cursor = conn.cursor()
    
    # First, get the ATM strike
    atm_query = f"""
    SELECT DISTINCT underlying_price, atm_strike
    FROM nifty_option_chain
    WHERE trade_date = '{trade_date}' AND expiry_date = '{expiry_date}'
    LIMIT 1
    """
    
    cursor.execute(atm_query)
    underlying_price, atm_strike = cursor.fetchone()
    
    print(f"Nifty Option Chain for {trade_date}, Expiry: {expiry_date}")
    print(f"Underlying Price: {underlying_price:.2f}, ATM Strike: {atm_strike}")
    
    # Calculate specific strikes to query
    increment = 100 if atm_strike >= 10000 else 50
    strike_range = []
    
    # Get 5 strikes below and 5 strikes above ATM
    for i in range(-5, 6):
        strike_range.append(atm_strike + (i * increment))
    
    # Build query for specific strikes
    strikes_str = ", ".join(str(strike) for strike in strike_range)
    
    data_query = f"""
    SELECT 
        trade_date,
        expiry_date,
        strike,
        ce_close,
        ce_oi,
        ce_volume,
        ce_iv,
        pe_close,
        pe_oi,
        pe_volume,
        pe_iv,
        call_moneyness_code,
        put_moneyness_code
    FROM nifty_option_chain
    WHERE trade_date = '{trade_date}'
      AND expiry_date = '{expiry_date}'
      AND strike IN ({strikes_str})
    ORDER BY strike
    """
    
    cursor.execute(data_query)
    columns = [desc[0] for desc in cursor.description]
    data = cursor.fetchall()
    
    # Convert to DataFrame
    df = pd.DataFrame(data, columns=columns)
    
    if len(df) == 0:
        print("No data found")
        exit()
    
    # Convert moneyness codes to strings
    def get_call_type(code):
        if code == 0:
            return "ATM"
        elif code < 0:
            return f"ITM{abs(code)}"
        else:
            return f"OTM{code}"
            
    def get_put_type(code):
        if code == 0:
            return "ATM"
        elif code < 0:
            return f"ITM{abs(code)}"
        else:
            return f"OTM{code}"
    
    # Format data for display
    formatted_data = []
    
    for _, row in df.iterrows():
        # Get moneyness labels
        call_type = get_call_type(row['call_moneyness_code'])
        put_type = get_put_type(row['put_moneyness_code'])
        
        formatted_data.append([
            # Call side
            f"{int(row['ce_oi']):,}",
            f"{int(row['ce_volume']):,}",
            f"{row['ce_iv']:.2f}",
            f"{row['ce_close']:.2f}",
            call_type,
            
            # Strike
            row['strike'],
            
            # Put side
            put_type,
            f"{row['pe_close']:.2f}",
            f"{row['pe_iv']:.2f}",
            f"{int(row['pe_volume']):,}",
            f"{int(row['pe_oi']):,}",
        ])
    
    # Create headers
    headers = [
        "Call OI", "Volume", "IV", "Price", "Type",
        "Strike",
        "Type", "Price", "IV", "Volume", "Put OI"
    ]
    
    # Display the data
    print("\nOption Chain (around ATM strikes):")
    print(tabulate(formatted_data, headers=headers, tablefmt='pretty'))
    
    # Summary
    print(f"\nDisplayed {len(df)} strikes")
    
except Exception as e:
    print(f"Error: {e}")
finally:
    conn.close()
    print("\nConnection closed.") 