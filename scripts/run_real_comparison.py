#!/usr/bin/env python3
"""
Run both backtesters with actual data from 2024-12-31
No mock data - using real MySQL and HeavyDB data
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import shutil
import openpyxl

def update_excel_dates(excel_path, start_date, end_date):
    """Update dates in the Excel file"""
    print(f"Updating dates in {excel_path}...")
    
    wb = openpyxl.load_workbook(excel_path)
    
    # Update PortfolioSetting sheet
    if 'PortfolioSetting' in wb.sheetnames:
        ws = wb['PortfolioSetting']
        # Find and update StartDate and EndDate
        for row in ws.iter_rows(min_row=2):
            if row[3].value == 'YES':  # Enabled column
                row[0].value = start_date  # StartDate
                row[1].value = end_date    # EndDate
                print(f"  Updated portfolio dates to {start_date} - {end_date}")
                break
    
    wb.save(excel_path)
    print("  ✓ Excel dates updated")

def update_legacy_mysql_patch(test_date):
    """Update the legacy MySQL patch to use the correct date"""
    patch_script = "scripts/fix_legacy_with_mysql.py"
    
    with open(patch_script, 'r') as f:
        content = f.read()
    
    # Update the hardcoded dates in the patch
    content = content.replace("'2025-04-01'", f"'{test_date}'")
    content = content.replace("250401", "241231")
    content = content.replace("250403", "250102")  # Next Thursday after Dec 31
    content = content.replace("Mon, 01 Apr 2025", "Tue, 31 Dec 2024")
    
    with open(patch_script, 'w') as f:
        f.write(content)
    
    print("  ✓ Updated legacy MySQL patch with correct date")

def run_legacy_backtester():
    """Run the legacy backtester with MySQL data"""
    print("\n" + "="*60)
    print("Running Legacy Backtester with MySQL Data")
    print("="*60)
    
    # First run the MySQL patch script
    result = subprocess.run(
        ["python3", "scripts/fix_legacy_with_mysql.py"],
        capture_output=True,
        text=True,
        timeout=180
    )
    
    if result.returncode == 0:
        print("  ✓ Legacy backtester completed")
        # Check if output was created
        legacy_output = "comparison_outputs/legacy_output_mysql.xlsx"
        if os.path.exists(legacy_output):
            print(f"  ✓ Output saved to: {legacy_output}")
            return legacy_output
    else:
        print("  ✗ Legacy backtester failed")
        if result.stderr:
            print(f"  Error: {result.stderr[:500]}")
    
    return None

def run_new_backtester(portfolio_excel, output_path):
    """Run the new GPU backtester"""
    print("\n" + "="*60)
    print("Running New GPU Backtester with HeavyDB")
    print("="*60)
    
    env = os.environ.copy()
    env['PYTHONPATH'] = os.path.abspath('.')
    
    cmd = [
        sys.executable,
        "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", portfolio_excel,
        "--output-path", output_path,
        "--start-date", "20241231",
        "--end-date", "20241231"
    ]
    
    print(f"  Running: {' '.join(cmd[-6:])}")
    
    result = subprocess.run(
        cmd,
        env=env,
        capture_output=True,
        text=True,
        timeout=180
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"  ✓ New backtester completed")
        print(f"  ✓ Output saved to: {output_path}")
        return output_path
    else:
        print("  ✗ New backtester failed")
        if result.stderr:
            print(f"  Error: {result.stderr[:500]}")
        return None

def compare_outputs(legacy_output, new_output):
    """Compare the outputs from both backtesters"""
    print("\n" + "="*60)
    print("Comparing Outputs")
    print("="*60)
    
    try:
        # Load both Excel files
        legacy_xl = pd.ExcelFile(legacy_output)
        new_xl = pd.ExcelFile(new_output)
        
        print(f"\nLegacy sheets: {legacy_xl.sheet_names}")
        print(f"New sheets: {new_xl.sheet_names}")
        
        # Compare key metrics
        # Look for trades/transactions sheet
        for sheet_name in ['PORTFOLIO Trans', 'Metrics', 'Summary', 'Trades']:
            if sheet_name in legacy_xl.sheet_names and sheet_name in new_xl.sheet_names:
                print(f"\nComparing sheet: {sheet_name}")
                
                legacy_df = pd.read_excel(legacy_xl, sheet_name=sheet_name)
                new_df = pd.read_excel(new_xl, sheet_name=sheet_name)
                
                print(f"  Legacy rows: {len(legacy_df)}")
                print(f"  New rows: {len(new_df)}")
                
                # Show first few rows
                if len(legacy_df) > 0:
                    print("\n  Legacy sample:")
                    print(legacy_df.head(3))
                
                if len(new_df) > 0:
                    print("\n  New sample:")
                    print(new_df.head(3))
                
                break
        
    except Exception as e:
        print(f"  Error comparing outputs: {e}")

def main():
    print("="*80)
    print("Real Data Backtester Comparison")
    print(f"Test Date: 2024-12-31")
    print(f"Time: {datetime.now()}")
    print("="*80)
    
    # Test date configuration
    excel_date = "31_12_2024"
    mysql_date = "241231"
    heavydb_date = "2024-12-31"
    
    # Create output directory
    os.makedirs("comparison_outputs", exist_ok=True)
    
    # Step 1: Update input Excel with test date
    portfolio_excel = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    
    if os.path.exists(portfolio_excel):
        print(f"\n1. Updating input Excel with date {excel_date}")
        update_excel_dates(portfolio_excel, excel_date, excel_date)
    else:
        print(f"\n✗ Portfolio Excel not found: {portfolio_excel}")
        return
    
    # Step 2: Update legacy MySQL patch
    print(f"\n2. Updating legacy MySQL patch")
    update_legacy_mysql_patch(heavydb_date)
    
    # Step 3: Run legacy backtester
    print(f"\n3. Running legacy backtester")
    legacy_output = run_legacy_backtester()
    
    # Step 4: Run new backtester
    print(f"\n4. Running new backtester")
    new_output = "comparison_outputs/new_gpu_output_real.xlsx"
    new_result = run_new_backtester(portfolio_excel, new_output)
    
    # Step 5: Compare outputs
    if legacy_output and new_result:
        compare_outputs(legacy_output, new_result)
    
    # Summary
    print("\n" + "="*80)
    print("Summary")
    print("="*80)
    print(f"Test Date: {heavydb_date}")
    print(f"Legacy Output: {'✓ Created' if legacy_output else '✗ Failed'}")
    print(f"New Output: {'✓ Created' if new_result else '✗ Failed'}")
    
    if legacy_output and new_result:
        print("\n✓ Both backtesters ran successfully with REAL data")
        print("  - Legacy used MySQL data directly")
        print("  - New used HeavyDB data")
        print("  - No mock data was used")
        print("\nManually compare the Excel files to verify results match")

if __name__ == "__main__":
    main() 