@echo off
REM Setup Legacy Backtester on Windows Server
REM Run this script on the Windows server after RDP connection

echo ============================================
echo Legacy Backtester Setup Script
echo ============================================
echo.

REM Check Python installation
echo Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    exit /b 1
)

REM Install required packages
echo.
echo Installing required Python packages...
pip install flask flask-cors pandas mysql-connector-python requests openpyxl

REM Check if backend service ports are available
echo.
echo Checking if ports 5000 and 5001 are available...
netstat -an | findstr :5000
if %errorlevel% equ 0 (
    echo WARNING: Port 5000 is already in use
)

netstat -an | findstr :5001
if %errorlevel% equ 0 (
    echo WARNING: Port 5001 is already in use
)

REM Test MySQL connection
echo.
echo Testing MySQL connection...
echo import mysql.connector; conn = mysql.connector.connect(host='************', user='mahesh', password='mahesh_123', database='historicaldb'); print('MySQL connection successful'); conn.close() | python
if %errorlevel% neq 0 (
    echo ERROR: MySQL connection failed
    exit /b 1
)

REM Create start scripts for backend services
echo.
echo Creating backend service start scripts...

REM Create start_backend_5000.bat
echo @echo off > start_backend_5000.bat
echo echo Starting Backend Service on Port 5000... >> start_backend_5000.bat
echo cd /d "%%~dp0" >> start_backend_5000.bat
echo python run.py --host 0.0.0.0 --port 5000 >> start_backend_5000.bat

REM Create start_backend_5001.bat
echo @echo off > start_backend_5001.bat
echo echo Starting Backend Service on Port 5001... >> start_backend_5001.bat
echo cd /d "%%~dp0" >> start_backend_5001.bat
echo python run.py --host 0.0.0.0 --port 5001 >> start_backend_5001.bat

REM Create test_backend.py
echo Creating test script...
(
echo import requests
echo import time
echo 
echo print("Testing backend services..."^)
echo time.sleep(2^)
echo 
echo # Test port 5000
echo try:
echo     response = requests.get('http://localhost:5000/healthcheck'^)
echo     print(f"Port 5000: {response.status_code}"^)
echo except:
echo     print("Port 5000: Not accessible"^)
echo 
echo # Test port 5001
echo try:
echo     response = requests.get('http://localhost:5001/healthcheck'^)
echo     print(f"Port 5001: {response.status_code}"^)
echo except:
echo     print("Port 5001: Not accessible"^)
) > test_backend.py

REM Create firewall rules
echo.
echo Creating firewall rules...
netsh advfirewall firewall add rule name="Backtester Port 5000" dir=in action=allow protocol=TCP localport=5000
netsh advfirewall firewall add rule name="Backtester Port 5001" dir=in action=allow protocol=TCP localport=5001

echo.
echo ============================================
echo Setup Complete!
echo ============================================
echo.
echo Next steps:
echo 1. Start backend services:
echo    - Run start_backend_5000.bat in one window
echo    - Run start_backend_5001.bat in another window
echo.
echo 2. Test services:
echo    python test_backend.py
echo.
echo 3. Run legacy backtester:
echo    cd BTRUN
echo    python BTRunPortfolio.py
echo.
echo For external access from Linux:
echo Use SSH tunnel: ssh -L 5000:localhost:5000 -L 5001:localhost:5001 MahaInvest@************ -p 33898
echo.
pause 