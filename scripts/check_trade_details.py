#!/usr/bin/env python3
"""Check trade details from test output."""

import pandas as pd

# Read the transaction sheet
df = pd.read_excel('/srv/samba/shared/test_results/gpu_TBS_1day.xlsx', sheet_name='PORTFOLIO Trans')

print("="*80)
print("TRADE DETAILS")
print("="*80)

# Show all columns
print("\nColumns:", list(df.columns))

# Show trades
print(f"\nTotal trades: {len(df)}")
print("\nTrade breakdown:")
for idx, row in df.iterrows():
    print(f"\nTrade {idx+1}:")
    print(f"  Strategy: {row.get('Strategy Name', 'N/A')}")
    print(f"  Instrument: {row.get('Instrument Type', 'N/A')}")
    print(f"  Side: {row.get('Side', 'N/A')}")
    print(f"  Strike: {row.get('Strike', 'N/A')}")
    print(f"  Strike Type: {row.get('Strike Type', 'N/A')}")
    print(f"  Entry Time: {row.get('Entry Time', 'N/A')}")
    print(f"  Exit Time: {row.get('Exit Time', 'N/A')}")
    print(f"  Entry Price: {row.get('Entry Price', 'N/A')}")
    print(f"  Exit Price: {row.get('Exit Price', 'N/A')}")
    print(f"  Quantity: {row.get('Quantity', 'N/A')}")
    print(f"  P&L: {row.get('PnL', 'N/A')}")
    print(f"  Reason: {row.get('Reason', 'N/A')}")

print(f"\nTotal P&L: {df['PnL'].sum():.2f}")

# Check parameters
print("\n" + "="*80)
print("CHECKING PARAMETERS")
print("="*80)

# Read GeneralParameter
gen_df = pd.read_excel('/srv/samba/shared/test_results/gpu_TBS_1day.xlsx', sheet_name='GeneralParameter')
print("\nGeneralParameter:")
print(gen_df[['StrategyName', 'StartTime', 'EndTime', 'DTE']])

# Read LegParameter
leg_df = pd.read_excel('/srv/samba/shared/test_results/gpu_TBS_1day.xlsx', sheet_name='LegParameter')
print("\nLegParameter:")
print(leg_df[['LegID', 'Instrument', 'Transaction', 'StrikeMethod', 'SLValue', 'TGTValue']]) 