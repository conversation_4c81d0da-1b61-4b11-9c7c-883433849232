@echo off
REM This script exposes the internal legacy service (***************:5000) to external access
REM Run this as Administrator on the Windows server (************)

echo ==========================================
echo Exposing Legacy Service to External Access
echo ==========================================

REM Step 1: Add port forwarding rule
echo.
echo Step 1: Adding port forwarding rule...
netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=*************** protocol=tcp

REM Step 2: Open Windows Firewall
echo.
echo Step 2: Opening Windows Firewall port 5000...
netsh advfirewall firewall add rule name="Legacy Backtest Service External" dir=in action=allow protocol=TCP localport=5000

REM Step 3: Show current port proxy rules
echo.
echo Step 3: Current port forwarding rules:
netsh interface portproxy show all

echo.
echo ==========================================
echo Setup Complete!
echo.
echo The legacy service should now be accessible at:
echo   http://************:5000
echo.
echo To test from Linux:
echo   curl http://************:5000/healthcheck
echo ==========================================

REM Optional: Remove rules (uncomment if needed)
REM netsh interface portproxy delete v4tov4 listenport=5000 listenaddress=0.0.0.0
REM netsh advfirewall firewall delete rule name="Legacy Backtest Service External"

pause 