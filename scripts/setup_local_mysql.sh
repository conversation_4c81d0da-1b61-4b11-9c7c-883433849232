#!/bin/bash
# Script to set up local MySQL environment for legacy backtester

echo "Setting up Local MySQL Environment for Legacy Backtester"
echo "========================================================"

# Check if MySQL is installed
if ! command -v mysql &> /dev/null; then
    echo "MySQL is not installed. Installing MySQL..."
    sudo apt-get update
    sudo apt-get install -y mysql-server mysql-client
    
    # Start MySQL service
    sudo systemctl start mysql
    sudo systemctl enable mysql
else
    echo "MySQL is already installed."
fi

# Create database and user
echo "Creating database and user..."
sudo mysql << EOF
CREATE DATABASE IF NOT EXISTS historicaldb;
CREATE USER IF NOT EXISTS 'mahesh'@'localhost' IDENTIFIED BY 'mahesh_123';
GRANT ALL PRIVILEGES ON historicaldb.* TO 'mahesh'@'localhost';
FLUSH PRIVILEGES;
EOF

echo "Local MySQL setup complete!"
echo "Database: historicaldb"
echo "User: mahesh"
echo "Password: mahesh_123"
echo "Host: localhost" 