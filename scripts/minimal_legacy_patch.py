#!/usr/bin/env python3
"""Minimal patch for legacy Util.py to use LocalBacktestEngine."""

import re

def apply_minimal_patch():
    """Apply minimal patch to Util.py."""
    
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    
    # Read the file
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Find the getBacktestResults method and replace just the HTTP request part
    # Original pattern with HTTP request
    original_pattern = r'''urii = config\.BT_URII\['tick'\] if btPara\['istickbt'\] else config\.BT_URII\['minute'\]\s*
        
        try:
            btResp = requests\.post\(urii, json=btPara\)
            respp = btResp\.json\(\)

            orderss = pd\.DataFrame\(respp\['strategies'\]\['orders'\]\)
            orderss\['portfolio_name'\] = btPara\['portfolio'\]\['name'\]

            respp\['strategies'\]\['orders'\] = orderss\.to_dict\("records"\)

        except Exception:
            logging\.error\(f"Error while running BT: \{btResp\.text\}"\)
            respp = \{\}'''
    
    # Replacement with LocalBacktestEngine
    replacement = '''# Modified to use LocalBacktestEngine instead of Flask services
        try:
            from LocalBacktestEngine import LocalBacktestEngine
            
            # MySQL configuration
            mysql_config = {
                'host': 'localhost',
                'user': 'mahesh',
                'password': 'mahesh_123',
                'database': 'historicaldb'
            }
            
            # Initialize local engine
            engine = LocalBacktestEngine(mysql_config)
            
            # Execute backtest
            respp = engine.execute_trade(btPara)
            
            # Close connection
            engine.close()
            
            # Add portfolio name to orders if they exist
            if 'strategies' in respp and 'orders' in respp['strategies']:
                orderss = pd.DataFrame(respp['strategies']['orders'])
                if not orderss.empty:
                    orderss['portfolio_name'] = btPara['portfolio']['name']
                    respp['strategies']['orders'] = orderss.to_dict("records")
            
            logging.info(f"LocalBacktestEngine completed successfully")
            
        except Exception as e:
            logging.error(f"Error in LocalBacktestEngine: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            respp = {}'''
    
    # Apply the replacement
    new_content = re.sub(original_pattern, replacement, content, flags=re.DOTALL)
    
    # Write the modified content
    with open(util_path, 'w') as f:
        f.write(new_content)
    
    print("✅ Applied minimal patch to Util.py")
    print("✅ Legacy backtester can now run without Flask services")

if __name__ == "__main__":
    apply_minimal_patch()
    print("\nYou can now run the legacy backtester:")
    print("  cd bt/archive/backtester_stable/BTRUN")
    print("  python3 BTRunPortfolio.py") 