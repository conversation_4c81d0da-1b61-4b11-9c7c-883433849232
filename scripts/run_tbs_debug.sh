#!/bin/bash
# TBS Debug Suite Runner
# Runs all TBS debugging scripts and generates reports

set -e

# Set up environment
export PYTHONPATH=/srv/samba/shared
export MYSQL_HOST=localhost
export DEBUG_MODE=true
export PATH=$PATH:/usr/bin:/usr/local/bin

# Create necessary directories
mkdir -p test_data
mkdir -p debug_output/{tbs,tbs_exit_time,traces,comparisons}

# Get the absolute path to the repository
REPO_PATH="$(pwd)"
ARCHIVE_PATH="${REPO_PATH}/bt/archive/backtester_stable/BTRUN"

# Function to display colored output
function echo_color() {
  case $1 in
    "green") echo -e "\e[32m$2\e[0m" ;;
    "red") echo -e "\e[31m$2\e[0m" ;;
    "yellow") echo -e "\e[33m$2\e[0m" ;;
    "blue") echo -e "\e[34m$2\e[0m" ;;
    *) echo "$2" ;;
  esac
}

# Print header
echo_color "blue" "====================================="
echo_color "blue" "       TBS Debug Suite Runner        "
echo_color "blue" "====================================="
echo ""

# Display environment
echo_color "yellow" "Environment setup:"
echo "PYTHONPATH: $PYTHONPATH"
echo "MYSQL_HOST: $MYSQL_HOST"
echo "ARCHIVE_PATH: $ARCHIVE_PATH"
echo "Python version: $(python3 --version)"
echo ""

# Step 1: Generate test data
echo_color "yellow" "Step 1: Generating TBS test data..."
python3 scripts/create_tbs_test_data.py --output test_data/tbs_test_1day.xlsx --legs 2
if [ $? -ne 0 ]; then
  echo_color "red" "Error generating test data. Exiting."
  exit 1
fi
echo_color "green" "Test data generation complete."
echo ""

# Verify Python and module imports
echo_color "yellow" "Verifying Python setup..."
python3 -c "
import sys
print('Python path:', sys.path)
try:
    from bt.backtester_stable.BTRUN.heavydb_connection import get_connection
    print('Heavydb connection import: OK')
except Exception as e:
    print('Heavydb connection import failed:', e)

try:
    sys.path.append('${ARCHIVE_PATH}')
    import config
    print('Legacy config import: OK')
except Exception as e:
    print('Legacy config import failed:', e)
"
echo ""

# Step 2: Run strike selection debug
echo_color "yellow" "Step 2: Running strike selection debug..."
python3 scripts/debug_tbs_strike_selection.py --excel test_data/tbs_test_1day.xlsx --date 20240103
if [ $? -ne 0 ]; then
  echo_color "red" "Strike selection debug failed. Continuing anyway."
else
  echo_color "green" "Strike selection debug complete."
fi
echo ""

# Step 3: Run exit time debug
echo_color "yellow" "Step 3: Running exit time debug..."

# Patch the exit time debug script to fix paths
sed -i 's/cmd = \["python"/cmd = \["python3"/g' scripts/debug_tbs_exit_time.py
sed -i "s|cd {add_archive_path()}|cd ${ARCHIVE_PATH}|g" scripts/debug_tbs_exit_time.py

python3 scripts/debug_tbs_exit_time.py --excel test_data/tbs_test_1day.xlsx --date 20240103 --output-dir debug_output/tbs_exit_time
if [ $? -ne 0 ]; then
  echo_color "red" "Exit time debug failed. Continuing anyway."
else
  echo_color "green" "Exit time debug complete."
fi
echo ""

# Step 4: Run full parity test
echo_color "yellow" "Step 4: Running full parity test..."

# Patch the parity test script to fix paths
sed -i 's/cmd = \["python"/cmd = \["python3"/g' scripts/test_tbs_parity.py
sed -i 's|"cd", env\["archive_path"\]|"cd", "'${ARCHIVE_PATH}'"|g' scripts/test_tbs_parity.py

python3 scripts/test_tbs_parity.py --excel test_data/tbs_test_1day.xlsx --date 20240103 --output-dir debug_output/tbs
if [ $? -ne 0 ]; then
  echo_color "red" "TBS parity test failed."
else
  echo_color "green" "TBS parity test complete."
fi
echo ""

# Step 5: Generate summary report
echo_color "yellow" "Step 5: Generating summary report..."
cat > debug_output/tbs_debug_summary.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>TBS Debug Summary</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .section { margin-bottom: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .links { margin-top: 10px; }
        a { color: #0066cc; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>TBS Debug Summary</h1>
    <div class="section">
        <h2>Test Data</h2>
        <p>Test Excel: test_data/tbs_test_1day.xlsx</p>
        <p>Test Date: 2024-01-03</p>
    </div>
    
    <div class="section">
        <h2>Debug Results</h2>
        <h3>1. Strike Selection Debug</h3>
        <p>Check <a href="file://$(pwd)/tbs_strike_debug.log">tbs_strike_debug.log</a> for details</p>
        
        <h3>2. Exit Time Debug</h3>
        <p>Check <a href="file://$(pwd)/tbs_exit_time_debug.log">tbs_exit_time_debug.log</a> for details</p>
        <p>Results: <a href="file://$(pwd)/debug_output/tbs_exit_time/legacy_tbs_output.xlsx">Legacy Output</a> | <a href="file://$(pwd)/debug_output/tbs_exit_time/gpu_tbs_output.xlsx">GPU Output</a></p>
        
        <h3>3. Full Parity Test</h3>
        <p>Check <a href="file://$(pwd)/tbs_parity_test.log">tbs_parity_test.log</a> for details</p>
        <p>Results: <a href="file://$(pwd)/debug_output/tbs/legacy_tbs_output.xlsx">Legacy Output</a> | <a href="file://$(pwd)/debug_output/tbs/gpu_tbs_output.xlsx">GPU Output</a></p>
        <p>Comparison: <a href="file://$(pwd)/debug_output/tbs/tbs_comparison.html">HTML Comparison Report</a></p>
    </div>
    
    <div class="section">
        <h2>Next Steps</h2>
        <p>1. Fix any issues identified in the debug logs</p>
        <p>2. Update SL/TP values if exit time issues were found</p>
        <p>3. Run multi-leg test (4 legs) for comprehensive validation</p>
        <p>4. Progress to 30-day testing once 1-day tests pass</p>
    </div>
    
    <div class="section">
        <h2>Debug Commands</h2>
        <pre>
# Strike Selection Debug
python3 scripts/debug_tbs_strike_selection.py --excel test_data/tbs_test_1day.xlsx --date 20240103

# Exit Time Debug
python3 scripts/debug_tbs_exit_time.py --excel test_data/tbs_test_1day.xlsx --date 20240103 --output-dir debug_output/tbs_exit_time

# Full Parity Test
python3 scripts/test_tbs_parity.py --excel test_data/tbs_test_1day.xlsx --date 20240103 --output-dir debug_output/tbs
        </pre>
    </div>
</body>
</html>
EOF

echo_color "green" "Summary report generated: debug_output/tbs_debug_summary.html"
echo ""

# Final summary
echo_color "blue" "====================================="
echo_color "blue" "         Debug Suite Complete        "
echo_color "blue" "====================================="
echo ""
echo_color "yellow" "Debug logs:"
echo "- Strike selection: tbs_strike_debug.log"
echo "- Exit time: tbs_exit_time_debug.log"
echo "- Parity test: tbs_parity_test.log"
echo ""
echo_color "yellow" "Output files:"
echo "- Test data: test_data/tbs_test_1day.xlsx"
echo "- Summary report: debug_output/tbs_debug_summary.html"
echo "- Comparison report: debug_output/tbs/tbs_comparison.html"
echo ""
echo_color "green" "Run the following to view the summary report:"
echo "xdg-open debug_output/tbs_debug_summary.html"
echo "" 