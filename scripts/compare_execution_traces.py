#!/usr/bin/env python3
"""
Script to compare execution traces from legacy and GPU backtesters.

Generates an HTML report highlighting differences in:
- Strike selection
- Trade entry/exit
- P&L calculations
- Output format
"""

import json
import argparse
import difflib
from pathlib import Path
from datetime import datetime

def load_trace(file_path):
    """Load execution trace from JSON file."""
    with open(file_path, 'r') as f:
        return json.load(f)

def extract_key_events(trace):
    """Extract key events from execution trace."""
    events = {
        'strike_selections': [],
        'trade_entries': [],
        'trade_exits': [],
        'pnl_calculations': [],
        'risk_evaluations': []
    }
    
    for event in trace.get('execution_trace', []):
        event_type = event.get('type', '')
        if event_type == 'strike_selection':
            events['strike_selections'].append(event)
        elif event_type == 'trade_entry':
            events['trade_entries'].append(event)
        elif event_type == 'trade_exit':
            events['trade_exits'].append(event)
        elif event_type == 'pnl_calculation':
            events['pnl_calculations'].append(event)
        elif event_type == 'risk_evaluation':
            events['risk_evaluations'].append(event)
    
    return events

def compare_strike_selections(legacy_strikes, gpu_strikes):
    """Compare strike selection logic between systems."""
    differences = []
    
    # Match strikes by timestamp
    for legacy_strike in legacy_strikes:
        timestamp = legacy_strike.get('inputs', {}).get('time')
        
        # Find matching GPU strike
        gpu_match = None
        for gpu_strike in gpu_strikes:
            if gpu_strike.get('inputs', {}).get('time') == timestamp:
                gpu_match = gpu_strike
                break
        
        if gpu_match:
            # Compare ATM strikes
            legacy_atm = legacy_strike.get('result', {}).get('atm_strike')
            gpu_atm = gpu_match.get('result', {}).get('atm_strike')
            
            if legacy_atm != gpu_atm:
                differences.append({
                    'type': 'strike_mismatch',
                    'timestamp': timestamp,
                    'legacy_atm': legacy_atm,
                    'gpu_atm': gpu_atm,
                    'legacy_method': legacy_strike.get('method'),
                    'gpu_method': gpu_match.get('method')
                })
        else:
            differences.append({
                'type': 'missing_gpu_strike',
                'timestamp': timestamp,
                'legacy_strike': legacy_strike
            })
    
    return differences

def compare_pnl_calculations(legacy_pnls, gpu_pnls):
    """Compare P&L calculations between systems."""
    differences = []
    
    for i, legacy_pnl in enumerate(legacy_pnls):
        if i < len(gpu_pnls):
            gpu_pnl = gpu_pnls[i]
            
            # Compare gross P&L
            legacy_gross = legacy_pnl.get('gross_pnl', 0)
            gpu_gross = gpu_pnl.get('gross_pnl', 0)
            
            if abs(legacy_gross - gpu_gross) > 0.01:  # 1 paisa tolerance
                differences.append({
                    'type': 'gross_pnl_mismatch',
                    'trade_id': legacy_pnl.get('trade_id'),
                    'legacy_gross': legacy_gross,
                    'gpu_gross': gpu_gross,
                    'difference': gpu_gross - legacy_gross
                })
            
            # Compare net P&L
            legacy_net = legacy_pnl.get('net_pnl', 0)
            gpu_net = gpu_pnl.get('net_pnl', 0)
            
            if abs(legacy_net - gpu_net) > 0.01:  # 1 paisa tolerance
                differences.append({
                    'type': 'net_pnl_mismatch',
                    'trade_id': legacy_pnl.get('trade_id'),
                    'legacy_net': legacy_net,
                    'gpu_net': gpu_net,
                    'difference': gpu_net - legacy_net
                })
    
    return differences

def generate_html_report(legacy_trace, gpu_trace, differences, output_file):
    """Generate HTML report of differences."""
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Backtester Execution Trace Comparison</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1 {{ color: #333; }}
        h2 {{ color: #666; }}
        .summary {{ background-color: #f0f0f0; padding: 10px; border-radius: 5px; }}
        .difference {{ background-color: #ffe0e0; padding: 10px; margin: 10px 0; border-radius: 5px; }}
        .match {{ background-color: #e0ffe0; padding: 10px; margin: 10px 0; border-radius: 5px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .critical {{ color: red; font-weight: bold; }}
        .warning {{ color: orange; }}
        .info {{ color: blue; }}
        pre {{ background-color: #f5f5f5; padding: 10px; overflow-x: auto; }}
    </style>
</head>
<body>
    <h1>Backtester Execution Trace Comparison</h1>
    
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Legacy Trace:</strong> {legacy_trace.get('input_excel', 'Unknown')}</p>
        <p><strong>GPU Trace:</strong> {gpu_trace.get('input_excel', 'Unknown')}</p>
        <p><strong>Timestamp:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>Total Differences Found:</strong> <span class="critical">{len(differences)}</span></p>
    </div>
    
    <h2>Configuration Comparison</h2>
    <table>
        <tr>
            <th>Parameter</th>
            <th>Legacy</th>
            <th>GPU</th>
            <th>Match</th>
        </tr>
        <tr>
            <td>ATM Method</td>
            <td>{legacy_trace.get('config', {}).get('use_synthetic_atm', 'Unknown')}</td>
            <td>{gpu_trace.get('config', {}).get('atm_method', 'Unknown')}</td>
            <td>{'✓' if 'synthetic' in str(gpu_trace.get('config', {}).get('atm_method', '')).lower() else '✗'}</td>
        </tr>
    </table>
    
    <h2>Detailed Differences</h2>
"""
    
    # Group differences by type
    grouped_diffs = {}
    for diff in differences:
        diff_type = diff.get('type', 'unknown')
        if diff_type not in grouped_diffs:
            grouped_diffs[diff_type] = []
        grouped_diffs[diff_type].append(diff)
    
    # Generate sections for each type
    for diff_type, diffs in grouped_diffs.items():
        html_content += f"<h3>{diff_type.replace('_', ' ').title()}</h3>\n"
        
        if diff_type == 'strike_mismatch':
            html_content += "<table>\n"
            html_content += "<tr><th>Time</th><th>Legacy ATM</th><th>GPU ATM</th><th>Difference</th></tr>\n"
            for diff in diffs:
                html_content += f"""<tr>
                    <td>{diff.get('timestamp', '')}</td>
                    <td>{diff.get('legacy_atm', '')}</td>
                    <td>{diff.get('gpu_atm', '')}</td>
                    <td class="critical">{diff.get('gpu_atm', 0) - diff.get('legacy_atm', 0)}</td>
                </tr>\n"""
            html_content += "</table>\n"
            
        elif diff_type in ['gross_pnl_mismatch', 'net_pnl_mismatch']:
            html_content += "<table>\n"
            html_content += "<tr><th>Trade ID</th><th>Legacy</th><th>GPU</th><th>Difference</th></tr>\n"
            for diff in diffs:
                pnl_type = 'gross' if 'gross' in diff_type else 'net'
                html_content += f"""<tr>
                    <td>{diff.get('trade_id', '')}</td>
                    <td>{diff.get(f'legacy_{pnl_type}', '')}</td>
                    <td>{diff.get(f'gpu_{pnl_type}', '')}</td>
                    <td class="{'critical' if abs(diff.get('difference', 0)) > 1 else 'warning'}">{diff.get('difference', '')}</td>
                </tr>\n"""
            html_content += "</table>\n"
        else:
            # Generic display for other types
            for diff in diffs:
                html_content += f'<div class="difference"><pre>{json.dumps(diff, indent=2)}</pre></div>\n'
    
    html_content += """
    <h2>Recommendations</h2>
    <ul>
        <li>Ensure both systems use synthetic future ATM calculation</li>
        <li>Verify slippage percentage is identical</li>
        <li>Check tax calculation formulas match</li>
        <li>Confirm exit time enforcement logic</li>
    </ul>
</body>
</html>
"""
    
    with open(output_file, 'w') as f:
        f.write(html_content)
    
    print(f"HTML report generated: {output_file}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Compare execution traces')
    parser.add_argument('--legacy-trace', required=True, help='Legacy trace JSON file')
    parser.add_argument('--gpu-trace', required=True, help='GPU trace JSON file')
    parser.add_argument('--output', default='trace_diff.html', help='Output HTML report')
    parser.add_argument('--highlight-differences', action='store_true', help='Highlight differences')
    
    args = parser.parse_args()
    
    # Load traces
    legacy_trace = load_trace(args.legacy_trace)
    gpu_trace = load_trace(args.gpu_trace)
    
    # Extract events
    legacy_events = extract_key_events(legacy_trace)
    gpu_events = extract_key_events(gpu_trace)
    
    # Compare events
    differences = []
    
    # Compare strike selections
    strike_diffs = compare_strike_selections(
        legacy_events['strike_selections'],
        gpu_events['strike_selections']
    )
    differences.extend(strike_diffs)
    
    # Compare P&L calculations
    pnl_diffs = compare_pnl_calculations(
        legacy_events['pnl_calculations'],
        gpu_events['pnl_calculations']
    )
    differences.extend(pnl_diffs)
    
    # Generate report
    generate_html_report(legacy_trace, gpu_trace, differences, args.output)
    
    # Print summary
    print(f"Found {len(differences)} differences")
    if differences:
        print("\nSummary by type:")
        type_counts = {}
        for diff in differences:
            diff_type = diff.get('type', 'unknown')
            type_counts[diff_type] = type_counts.get(diff_type, 0) + 1
        
        for diff_type, count in type_counts.items():
            print(f"  {diff_type}: {count}")

if __name__ == '__main__':
    main() 