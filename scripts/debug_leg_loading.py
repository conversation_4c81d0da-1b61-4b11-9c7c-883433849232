#!/usr/bin/env python3
"""
Debug leg loading from Excel
"""

import sys
sys.path.insert(0, '/srv/samba/shared/bt')

from backtester_stable.BTRUN.excel_parser.portfolio_parser import parse_portfolio_excel

portfolio_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"

print("Debugging Leg Loading")
print("=" * 60)

try:
    # Parse the portfolio
    portfolios = parse_portfolio_excel(portfolio_file)
    
    print(f"\nFound {len(portfolios)} portfolio(s)")
    
    for portfolio_name, portfolio in portfolios.items():
        print(f"\nPortfolio: {portfolio.portfolio_name}")
        print(f"  Start Date: {portfolio.start_date}")
        print(f"  End Date: {portfolio.end_date}")
        print(f"  Strategies: {len(portfolio.strategies)}")
        
        for i, strategy in enumerate(portfolio.strategies):
            print(f"\n  Strategy {i+1}: {strategy.strategy_name}")
            print(f"    Type: {strategy.evaluator}")
            print(f"    Entry Start: {strategy.entry_start}")
            print(f"    Entry End: {strategy.entry_end}")
            print(f"    Legs: {len(strategy.legs)}")
            
            # Check DTE filter
            dte_filter = strategy.extra_params.get('DTE', 'N/A')
            weekdays = strategy.extra_params.get('Weekdays', 'N/A')
            print(f"    DTE Filter: {dte_filter}")
            print(f"    Weekdays: {weekdays}")
            
            for j, leg in enumerate(strategy.legs):
                print(f"\n    Leg {j+1} (ID: {leg.leg_id}):")
                print(f"      Instrument: {leg.option_type}")
                print(f"      Transaction: {leg.transaction}")
                print(f"      Strike Rule: {leg.strike_rule}")
                print(f"      Strike Distance: {leg.strike_distance}")
                print(f"      Strike Value: {leg.strike_value}")
                print(f"      Fixed Strike: {leg.fixed_strike}")
                print(f"      Entry Time: {leg.entry_time}")
                print(f"      Exit Time: {leg.exit_time}")
                print(f"      Lots: {leg.lots}")
                print(f"      Risk Rules: {len(leg.risk_rules)}")
                for rule in leg.risk_rules:
                    print(f"        - {rule.rule_type}: {rule.value} ({rule.number_type})")
                    
                # Check IsIdle flag
                is_idle = leg.extra_params.get('IsIdle', 'no')
                print(f"      IsIdle: {is_idle}")
                
                # Check StrikeValue in extra_params
                strike_value_extra = leg.extra_params.get('StrikeValue', 'N/A')
                print(f"      StrikeValue (extra): {strike_value_extra}")
                
                # Print all extra_params to debug
                print(f"      Extra params: {leg.extra_params}")
                
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc() 