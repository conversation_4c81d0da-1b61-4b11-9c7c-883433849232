#!/usr/bin/env python3
"""Final comparison between legacy backtester with local engine and GPU backtester."""

import os
import sys
import subprocess
import json
import pandas as pd
from datetime import datetime
import time
import glob

def run_legacy_backtester():
    """Run legacy backtester with local engine."""
    print("=" * 60)
    print("RUNNING LEGACY BACKTESTER WITH LOCAL ENGINE")
    print("=" * 60)
    
    # Change to legacy directory
    legacy_dir = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN"
    os.chdir(legacy_dir)
    
    # Set environment
    env = os.environ.copy()
    env['MYSQL_HOST'] = 'localhost'
    env['USE_LOCAL_ENGINE'] = 'true'
    
    # Run backtester
    start_time = datetime.now()
    result = subprocess.run(['python3', 'BTRunPortfolio.py'], 
                          capture_output=True, text=True, env=env)
    duration = (datetime.now() - start_time).total_seconds()
    
    print(f"Legacy backtester completed in {duration:.2f} seconds")
    
    # Check for output
    output_files = [f for f in os.listdir("Trades") if f.endswith(".xlsx")] if os.path.exists("Trades") else []
    
    if output_files:
        output_path = f"Trades/{output_files[-1]}"
        print(f"✅ Legacy output created: {output_path}")
        
        # Read and summarize
        try:
            df_trans = pd.read_excel(output_path, sheet_name="PORTFOLIO Trans")
            print(f"\nLegacy Results:")
            print(f"  Total trades: {len(df_trans)}")
            print(f"  Total P&L: {df_trans['Net PNL'].sum():.2f}" if 'Net PNL' in df_trans.columns else "")
            
            return True, output_path, df_trans
        except Exception as e:
            print(f"Error reading legacy output: {e}")
            return False, output_path, None
    else:
        print("❌ No legacy output created")
        
        # Check local_engine_result.json
        if os.path.exists("local_engine_result.json"):
            with open("local_engine_result.json", 'r') as f:
                result = json.load(f)
            orders = result.get('strategies', {}).get('orders', [])
            print(f"\nLocal engine generated {len(orders)} orders")
            if orders:
                total_pnl = sum(order['pnl'] for order in orders)
                print(f"Total P&L from engine: {total_pnl:.2f}")
        
        # Debug: Check latest log file
        log_files = glob.glob('Logs/*.log')
        if log_files:
            latest_log = max(log_files, key=os.path.getctime)
            print(f"\nLatest log file: {latest_log}")
            with open(latest_log, 'r') as f:
                log_content = f.read()
                if 'Error' in log_content or 'error' in log_content:
                    print("Log contains errors:")
                    print(log_content)
        
        # Debug: Check btpara.json structure
        if os.path.exists('btpara.json'):
            with open('btpara.json', 'r') as f:
                btpara = json.load(f)
            print(f"\nbtPara structure keys: {list(btpara.keys())}")
            if 'portfolio' in btpara:
                print(f"portfolio keys: {list(btpara['portfolio'].keys())}")
                if 'strategies' in btpara['portfolio']:
                    print(f"Number of strategies: {len(btpara['portfolio']['strategies'])}")
        
        return False, None, None

def run_gpu_backtester():
    """Run GPU backtester."""
    print("\n" + "=" * 60)
    print("RUNNING GPU BACKTESTER")
    print("=" * 60)
    
    os.chdir("/srv/samba/shared")
    
    # Run GPU backtester
    start_time = datetime.now()
    result = subprocess.run([
        'python3', '-m', 'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
        '--legacy-excel',
        '--portfolio-excel', 'test_data/tbs/input_portfolio_tbs_1day.xlsx',
        '--output-path', 'gpu_final_comparison.xlsx'
    ], capture_output=True, text=True)
    duration = (datetime.now() - start_time).total_seconds()
    
    print(f"GPU backtester completed in {duration:.2f} seconds")
    
    if os.path.exists("gpu_final_comparison.xlsx"):
        print("✅ GPU output created")
        
        # Read and summarize
        try:
            df_trans = pd.read_excel("gpu_final_comparison.xlsx", sheet_name="PORTFOLIO Trans")
            print(f"\nGPU Results:")
            print(f"  Total trades: {len(df_trans)}")
            print(f"  Total P&L: {df_trans['Net PNL'].sum():.2f}" if 'Net PNL' in df_trans.columns else "")
            
            return True, "gpu_final_comparison.xlsx", df_trans
        except Exception as e:
            print(f"Error reading GPU output: {e}")
            return False, None, None
    else:
        print("❌ GPU output not created")
        if result.stderr:
            print(f"Error: {result.stderr[:500]}")
        return False, None, None

def compare_results(legacy_df, gpu_df):
    """Compare results between legacy and GPU."""
    print("\n" + "=" * 60)
    print("COMPARISON RESULTS")
    print("=" * 60)
    
    if legacy_df is None or gpu_df is None:
        print("Cannot compare - one or both outputs missing")
        return
    
    # Compare trade counts
    print(f"\nTrade Count:")
    print(f"  Legacy: {len(legacy_df)}")
    print(f"  GPU: {len(gpu_df)}")
    
    # Compare total P&L
    if 'Net PNL' in legacy_df.columns and 'Net PNL' in gpu_df.columns:
        legacy_pnl = legacy_df['Net PNL'].sum()
        gpu_pnl = gpu_df['Net PNL'].sum()
        print(f"\nTotal P&L:")
        print(f"  Legacy: {legacy_pnl:.2f}")
        print(f"  GPU: {gpu_pnl:.2f}")
        print(f"  Difference: {abs(legacy_pnl - gpu_pnl):.2f}")
    
    # Compare individual trades
    print(f"\nIndividual Trade Comparison:")
    
    # Map columns
    legacy_cols = legacy_df.columns.tolist()
    gpu_cols = gpu_df.columns.tolist()
    
    print(f"\nLegacy columns: {legacy_cols[:10]}...")
    print(f"GPU columns: {gpu_cols[:10]}...")

def main():
    """Main comparison function."""
    
    # Run legacy
    legacy_success, legacy_output, legacy_df = run_legacy_backtester()
    
    # Run GPU
    gpu_success, gpu_output, gpu_df = run_gpu_backtester()
    
    # Compare if both successful
    if legacy_success and gpu_success:
        compare_results(legacy_df, gpu_df)
        print("\n✅ Both backtesters completed successfully!")
    else:
        print("\n❌ One or both backtesters failed")
        if not legacy_success:
            print("  - Legacy backtester failed or produced no output")
        if not gpu_success:
            print("  - GPU backtester failed")

if __name__ == "__main__":
    main() 