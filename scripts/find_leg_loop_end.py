#!/usr/bin/env python3
"""Find where the leg loop ends by tracking indentation."""

with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

# Find the leg loop
leg_loop_start = None
for i, line in enumerate(lines):
    if 'for leg in strategy.legs:' in line:
        leg_loop_start = i
        leg_loop_indent = len(line) - len(line.lstrip())
        print(f'Leg loop starts at line {i+1} with {leg_loop_indent} spaces indentation')
        break

if leg_loop_start is None:
    print("Could not find leg loop!")
    exit(1)

# Track where it ends
print(f'\n=== Tracking leg loop content ===')
inside_leg_loop = True
risk_rule_block_start = None

for i in range(leg_loop_start + 1, min(leg_loop_start + 300, len(lines))):
    line = lines[i]
    indent = len(line) - len(line.lstrip())
    stripped = line.strip()
    
    if not stripped:  # Skip empty lines
        continue
    
    # Check if we've exited the leg loop
    if indent <= leg_loop_indent:
        print(f'\nLine {i+1} ({indent} spaces): LEG LOOP ENDS HERE')
        print(f'  Content: {stripped[:60]}...')
        inside_leg_loop = False
        break
    
    # Track significant lines inside the loop
    if 'if leg.risk_rules' in line:
        risk_rule_block_start = i
        print(f'Line {i+1} ({indent} spaces): Risk rules block starts')
    elif 'build_trade_record' in line:
        print(f'Line {i+1} ({indent} spaces): >>> build_trade_record <<<')
        print(f'  Inside leg loop: {inside_leg_loop}')
        print(f'  Inside risk rules: {risk_rule_block_start is not None and i > risk_rule_block_start}')
    elif 'trade_records.append' in line:
        print(f'Line {i+1} ({indent} spaces): >>> trade_records.append <<<')
        print(f'  Inside leg loop: {inside_leg_loop}')
    elif 'continue' in line:
        print(f'Line {i+1} ({indent} spaces): continue statement')

# Find where build_trade_record and append are
print(f'\n=== Final Analysis ===')
build_line = None
append_line = None

for i, line in enumerate(lines):
    if 'record = build_trade_record' in line:
        build_line = i
    elif 'trade_records.append(record)' in line:
        append_line = i

if build_line and append_line:
    build_indent = len(lines[build_line]) - len(lines[build_line].lstrip())
    append_indent = len(lines[append_line]) - len(lines[append_line].lstrip())
    
    print(f'build_trade_record at line {build_line+1} with {build_indent} spaces')
    print(f'trade_records.append at line {append_line+1} with {append_indent} spaces')
    print(f'Leg loop indent: {leg_loop_indent} spaces')
    
    if build_indent > leg_loop_indent and append_indent > leg_loop_indent:
        print('✓ Both are inside the leg loop')
    else:
        print('✗ One or both are OUTSIDE the leg loop!') 