#!/usr/bin/env python3
"""
Test multiple days to compare MySQL vs HeavyDB ATM calculations and P&L
"""

import mysql.connector
try:
    from pyheavydb import connect as heavydb_connect
except ImportError:
    from heavydb import connect as heavydb_connect

# Connection configs
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

HEAVYDB_CONFIG = {
    'host': '127.0.0.1',
    'port': 6274,
    'user': 'admin',
    'password': 'HyperInteractive',
    'dbname': 'heavyai'
}

def get_mysql_atm(date, time, expiry):
    """Calculate ATM using MySQL with synthetic future method"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    # Get all strikes with synthetic future calculation
    query = """
    SELECT 
        c.strike,
        c.close as ce_close,
        p.close as pe_close,
        cash.close as spot_price,
        ABS(c.strike + (c.close/100) - (p.close/100) - (cash.close/100)) as syn_diff
    FROM nifty_call c
    JOIN nifty_put p ON c.date = p.date AND c.time = p.time 
                      AND c.strike = p.strike AND c.expiry = p.expiry
    JOIN nifty_cash cash ON c.date = cash.date AND c.time = cash.time
    WHERE c.date = %s AND c.time = %s AND c.expiry = %s
    AND c.close > 0 AND p.close > 0
    ORDER BY syn_diff
    LIMIT 1
    """
    
    cursor.execute(query, (date, time, expiry))
    result = cursor.fetchone()
    
    atm_strike = None
    spot_price = None
    if result:
        atm_strike = result[0]
        spot_price = result[3] / 100
    
    cursor.close()
    conn.close()
    
    return atm_strike, spot_price

def get_heavydb_atm(date_str):
    """Get ATM from HeavyDB for a specific date"""
    conn = heavydb_connect(**HEAVYDB_CONFIG)
    
    # Convert date format from YYMMDD to YYYY-MM-DD
    heavydb_date = f"20{date_str[:2]}-{date_str[2:4]}-{date_str[4:6]}"
    
    query = f"""
    SELECT spot, atm_strike
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}' AND trade_time = '09:16:00'
    LIMIT 1
    """
    
    result = conn.execute(query).fetchone()
    
    atm_strike = None
    spot_price = None
    if result:
        spot_price = result[0]
        atm_strike = result[1]
    
    conn.close()
    
    return atm_strike, spot_price

def calculate_mysql_pnl(atm_strike, date, expiry):
    """Calculate P&L using MySQL data"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    entry_time = 33360  # 9:16 AM
    exit_time = 43200   # 12:00 PM
    
    # Define trades
    trades = [
        {'strike': atm_strike, 'option': 'CE', 'side': 'SELL', 'qty': 50},
        {'strike': atm_strike, 'option': 'PE', 'side': 'SELL', 'qty': 50},
        {'strike': atm_strike + 100, 'option': 'CE', 'side': 'BUY', 'qty': 50},
        {'strike': atm_strike - 100, 'option': 'PE', 'side': 'BUY', 'qty': 50}
    ]
    
    total_pnl = 0
    trades_executed = 0
    
    for trade in trades:
        if trade['option'] == 'CE':
            query = "SELECT close FROM nifty_call WHERE date = %s AND time = %s AND strike = %s AND expiry = %s"
        else:
            query = "SELECT close FROM nifty_put WHERE date = %s AND time = %s AND strike = %s AND expiry = %s"
        
        # Get entry price
        cursor.execute(query, (date, entry_time, trade['strike'], expiry))
        entry_result = cursor.fetchone()
        
        # Get exit price
        cursor.execute(query, (date, exit_time, trade['strike'], expiry))
        exit_result = cursor.fetchone()
        
        if entry_result and exit_result:
            entry_price = entry_result[0] / 100
            exit_price = exit_result[0] / 100
            
            if trade['side'] == 'SELL':
                pnl = (entry_price - exit_price) * trade['qty']
            else:
                pnl = (exit_price - entry_price) * trade['qty']
            
            total_pnl += pnl
            trades_executed += 1
    
    cursor.close()
    conn.close()
    
    return total_pnl, trades_executed

def test_date(mysql_date, expiry):
    """Test a specific date and compare results"""
    print(f"\n{'='*70}")
    print(f"Testing Date: {mysql_date} (Expiry: {expiry})")
    print(f"{'='*70}")
    
    # Get ATM from both systems
    mysql_atm, mysql_spot = get_mysql_atm(mysql_date, 33360, expiry)
    heavydb_atm, heavydb_spot = get_heavydb_atm(mysql_date)
    
    print(f"\nSpot Price:")
    print(f"  MySQL:   {mysql_spot:.2f}" if mysql_spot else "  MySQL:   No data")
    print(f"  HeavyDB: {heavydb_spot:.2f}" if heavydb_spot else "  HeavyDB: No data")
    
    print(f"\nATM Strike:")
    print(f"  MySQL:   {mysql_atm}" if mysql_atm else "  MySQL:   No ATM found")
    print(f"  HeavyDB: {int(heavydb_atm)}" if heavydb_atm else "  HeavyDB: No ATM found")
    
    if mysql_atm and heavydb_atm:
        # Calculate P&L with both ATM strikes
        print(f"\nP&L Calculations:")
        
        # P&L with MySQL ATM
        mysql_pnl, mysql_trades = calculate_mysql_pnl(mysql_atm, mysql_date, expiry)
        print(f"  MySQL ATM ({mysql_atm}): P&L = {mysql_pnl:.2f} ({mysql_trades}/4 trades)")
        
        # P&L with HeavyDB ATM
        heavydb_pnl, heavydb_trades = calculate_mysql_pnl(int(heavydb_atm), mysql_date, expiry)
        print(f"  HeavyDB ATM ({int(heavydb_atm)}): P&L = {heavydb_pnl:.2f} ({heavydb_trades}/4 trades)")
        
        # Summary
        print(f"\nDifferences:")
        print(f"  ATM Strike Difference: {abs(mysql_atm - int(heavydb_atm))} points")
        print(f"  P&L Difference: {abs(mysql_pnl - heavydb_pnl):.2f}")

def main():
    print("Multi-Day MySQL vs HeavyDB Comparison")
    print("="*70)
    
    # Test dates with their expiries
    test_dates = [
        ('250401', '250409'),  # April 1 -> April 9 expiry
        ('250402', '250409'),  # April 2 -> April 9 expiry
        ('250403', '250409'),  # April 3 -> April 9 expiry
        ('250404', '250409'),  # April 4 -> April 9 expiry (Friday)
        ('250407', '250409'),  # April 7 -> April 9 expiry (Monday)
        ('250408', '250409'),  # April 8 -> April 9 expiry
    ]
    
    for date, expiry in test_dates:
        test_date(date, expiry)
    
    print("\n" + "="*70)
    print("Summary:")
    print("The comparison shows whether data quality issues are consistent")
    print("across multiple days or specific to certain dates.")

if __name__ == "__main__":
    main() 