#!/usr/bin/env python3
"""Final comprehensive parity comparison between legacy and new systems."""

import os
import sys
import pandas as pd
import numpy as np
import subprocess
import json
import shutil
from datetime import datetime
from pathlib import Path
import openpyxl

# Add project paths
sys.path.append('/srv/samba/shared')

def update_portfolio_dates(portfolio_file, start_date="01_04_2025", end_date="01_04_2025"):
    """Update dates in portfolio Excel file."""
    print(f"\n1. Updating dates in {portfolio_file}")
    
    # Read and update PortfolioSetting sheet
    wb = openpyxl.load_workbook(portfolio_file)
    ws = wb['PortfolioSetting']
    
    # Find StartDate and EndDate columns
    header_row = 1
    start_col = end_col = None
    
    for col in range(1, ws.max_column + 1):
        cell_value = ws.cell(row=header_row, column=col).value
        if cell_value == 'StartDate':
            start_col = col
        elif cell_value == 'EndDate':
            end_col = col
    
    # Update dates
    for row in range(2, ws.max_row + 1):
        if ws.cell(row=row, column=1).value:  # If row has data
            ws.cell(row=row, column=start_col).value = start_date
            ws.cell(row=row, column=end_col).value = end_date
    
    # Save the workbook
    wb.save(portfolio_file)
    print(f"   Updated dates to {start_date} - {end_date}")

def run_heavydb_backtest():
    """Run the new HeavyDB backtester."""
    print("\n2. Running HeavyDB backtester...")
    
    output_file = "heavydb_final_output.xlsx"
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "--output-path", output_file,
        "--start-date", "20250401",
        "--end-date", "20250401"
    ]
    
    env = os.environ.copy()
    env['PYTHONPATH'] = '/srv/samba/shared'
    
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    
    if result.returncode != 0:
        print(f"   Error running HeavyDB backtester: {result.stderr}")
        return None
    
    print(f"   HeavyDB output saved to: {output_file}")
    return output_file

def analyze_output(excel_file, label):
    """Analyze the output Excel file."""
    print(f"\n{label}:")
    print("-" * 60)
    
    try:
        # Read transaction sheet
        xl_file = pd.ExcelFile(excel_file)
        
        # Find the strategy transaction sheet
        trans_sheet = None
        for sheet in xl_file.sheet_names:
            if 'Trans' in sheet and 'PORTFOLIO' not in sheet and 'RS,916' in sheet:
                trans_sheet = sheet
                break
        
        if not trans_sheet:
            # Try PORTFOLIO Trans sheet
            trans_sheet = 'PORTFOLIO Trans'
        
        df = pd.read_excel(excel_file, sheet_name=trans_sheet)
        
        print(f"Total trades: {len(df)}")
        
        # Analyze trades
        for i, row in df.iterrows():
            leg_id = row.get('leg_id', row.get('ID', 'N/A'))
            strike = row.get('strike', row.get('Strike', 'N/A'))
            instrument = row.get('instrument_type', row.get('CE/PE', 'N/A'))
            side = row.get('side', row.get('Trade', 'N/A'))
            entry_price = row.get('entry_price', row.get('Entry at', 0))
            exit_price = row.get('exit_price', row.get('Exit at', 0))
            pnl = row.get('pnl', row.get('PNL', 0))
            
            print(f"\nTrade {i+1}:")
            print(f"  Leg: {leg_id} | Strike: {strike} | {instrument} {side}")
            print(f"  Entry: {entry_price:.2f} | Exit: {exit_price:.2f} | P&L: {pnl:.2f}")
        
        # Total P&L
        total_pnl = df.get('pnl', df.get('PNL', pd.Series([0]))).sum()
        print(f"\nTotal P&L: {total_pnl:.2f}")
        
        # Check dates
        entry_date = df.get('entry_date', df.get('Entry Date', pd.Series(['N/A']))).iloc[0]
        print(f"Trade Date: {entry_date}")
        
        # Check ATM strike
        strikes = df.get('strike', df.get('Strike', pd.Series([]))).unique()
        print(f"Strikes used: {sorted(strikes)}")
        
    except Exception as e:
        print(f"Error analyzing {excel_file}: {e}")

def create_legacy_comparison_data():
    """Create expected legacy data based on MySQL analysis."""
    print("\n3. Expected Legacy System Results (with Synthetic Future ATM):")
    print("-" * 60)
    
    # Based on MySQL data for April 1, 2025
    print("Expected ATM Strike: 23450 (using synthetic future calculation)")
    print("\nExpected Trades:")
    print("  Trade 1: SELL_CE | Strike: 23450 | Entry: ~100 | Exit: ~124")
    print("  Trade 2: SELL_PE | Strike: 23450 | Entry: ~130 | Exit: ~107")
    print("  Trade 3: BUY_CE  | Strike: 23550 | Entry: ~60  | Exit: ~85")
    print("  Trade 4: BUY_PE  | Strike: 23350 | Entry: ~90  | Exit: ~70")
    print("\nExpected Total P&L: Approximately -1000 to -1500")

def main():
    """Main execution."""
    print("="*80)
    print("Final Parity Comparison: Legacy vs HeavyDB")
    print("Test Date: April 1, 2025")
    print("="*80)
    
    # Step 1: Update portfolio dates
    portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    update_portfolio_dates(portfolio_file, "01_04_2025", "01_04_2025")
    
    # Step 2: Run HeavyDB backtest
    heavydb_output = run_heavydb_backtest()
    
    if heavydb_output and os.path.exists(heavydb_output):
        # Analyze HeavyDB output
        analyze_output(heavydb_output, "HeavyDB System Output")
    
    # Step 3: Show expected legacy results
    create_legacy_comparison_data()
    
    print("\n" + "="*80)
    print("COMPARISON SUMMARY:")
    print("="*80)
    
    print("\nKey Points:")
    print("1. Both systems should use ATM strike 23450 (synthetic future method)")
    print("2. Both should generate 4 trades (2 SELL ATM, 2 BUY OTM)")
    print("3. All trades should exit at 12:00:00")
    print("4. P&L should be similar (within reasonable slippage tolerance)")
    
    print("\nNOTE: The legacy system with MySQL patch should produce identical results")
    print("      when using the synthetic future ATM calculation method.")

if __name__ == "__main__":
    main() 