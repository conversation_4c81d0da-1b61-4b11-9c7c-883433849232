#!/usr/bin/env python3
"""
Run legacy and new backtesters in parallel for comparison
Uses external access to legacy service after port forwarding is set up
"""

import os
import sys
import requests
import json
import pandas as pd
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configuration - Updated to use external address
LEGACY_SERVICE_URL = "http://************:5000"  # External access after port forwarding
PORTFOLIO_EXCEL = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
STRATEGY_EXCEL = "bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"
LEGACY_OUTPUT = "legacy_output.xlsx"
NEW_OUTPUT = "new_output.xlsx"

def test_legacy_connection():
    """Test if legacy service is accessible"""
    print("Testing legacy service connection...")
    try:
        response = requests.get(f"{LEGACY_SERVICE_URL}/healthcheck", timeout=5)
        if response.status_code == 200:
            print(f"✓ Legacy service is accessible at {LEGACY_SERVICE_URL}")
            return True
        else:
            print(f"✗ Legacy service returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Cannot connect to legacy service: {e}")
        return False

def run_legacy_backtester():
    """Run the legacy backtester via HTTP service"""
    print("\n" + "="*60)
    print("Running Legacy Backtester")
    print("="*60)
    
    if not test_legacy_connection():
        print("\nERROR: Legacy service is not accessible!")
        print("\nPlease follow these steps:")
        print("1. RDP to Windows server: ************:33898")
        print("2. Run 'expose_legacy_service.bat' as Administrator")
        print("3. Verify service is running on Windows")
        print("4. Try this script again")
        return None
    
    try:
        # Prepare request data
        params = {
            "portfolio_excel": PORTFOLIO_EXCEL,
            "strategy_excel": STRATEGY_EXCEL,
            "start_date": "20250401",
            "end_date": "20250411",
            "output_path": LEGACY_OUTPUT
        }
        
        # Call legacy service
        response = requests.post(f"{LEGACY_SERVICE_URL}/run_backtest", json=params)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Legacy backtester completed")
            print(f"  Output saved to: {result.get('output_path', LEGACY_OUTPUT)}")
            return result
        else:
            print(f"✗ Legacy backtester failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Error running legacy backtester: {e}")
        return None

def run_new_backtester():
    """Run the new HeavyDB backtester"""
    print("\n" + "="*60)
    print("Running New HeavyDB Backtester")
    print("="*60)
    
    try:
        from bt.backtester_stable.BTRUN.BTRunPortfolio_GPU import main as run_gpu_backtester
        
        # Set up arguments
        sys.argv = [
            'BTRunPortfolio_GPU.py',
            '--legacy-excel',
            '--portfolio-excel', PORTFOLIO_EXCEL,
            '--output-path', NEW_OUTPUT,
            '--start-date', '20250401',
            '--end-date', '20250411'
        ]
        
        # Run the backtester
        run_gpu_backtester()
        print(f"✓ New backtester completed")
        print(f"  Output saved to: {NEW_OUTPUT}")
        return True
        
    except Exception as e:
        print(f"✗ Error running new backtester: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_results():
    """Compare outputs from both backtesters"""
    print("\n" + "="*60)
    print("Comparing Results")
    print("="*60)
    
    if not os.path.exists(LEGACY_OUTPUT):
        print(f"✗ Legacy output not found: {LEGACY_OUTPUT}")
        return
        
    if not os.path.exists(NEW_OUTPUT):
        print(f"✗ New output not found: {NEW_OUTPUT}")
        return
    
    try:
        # Load Excel files
        legacy_xl = pd.ExcelFile(LEGACY_OUTPUT)
        new_xl = pd.ExcelFile(NEW_OUTPUT)
        
        # Compare sheet names
        legacy_sheets = set(legacy_xl.sheet_names)
        new_sheets = set(new_xl.sheet_names)
        
        print(f"\nSheet comparison:")
        print(f"  Legacy sheets: {legacy_sheets}")
        print(f"  New sheets: {new_sheets}")
        
        if legacy_sheets != new_sheets:
            print(f"  ⚠ Sheet names differ!")
        
        # Compare common sheets
        common_sheets = legacy_sheets.intersection(new_sheets)
        for sheet in common_sheets:
            print(f"\nComparing sheet: {sheet}")
            
            legacy_df = pd.read_excel(legacy_xl, sheet_name=sheet)
            new_df = pd.read_excel(new_xl, sheet_name=sheet)
            
            # Basic comparison
            if legacy_df.shape != new_df.shape:
                print(f"  ⚠ Shape differs - Legacy: {legacy_df.shape}, New: {new_df.shape}")
            else:
                # Compare values
                differences = 0
                for col in legacy_df.columns:
                    if col in new_df.columns:
                        if legacy_df[col].dtype in ['float64', 'float32']:
                            # Numeric comparison with tolerance
                            diff = (legacy_df[col] - new_df[col]).abs().max()
                            if diff > 0.01:  # 1 cent tolerance
                                differences += 1
                                print(f"  ⚠ Column '{col}' has max difference: {diff}")
                        else:
                            # Exact comparison
                            if not legacy_df[col].equals(new_df[col]):
                                differences += 1
                                print(f"  ⚠ Column '{col}' differs")
                
                if differences == 0:
                    print(f"  ✓ Sheet matches perfectly!")
                    
    except Exception as e:
        print(f"✗ Error comparing results: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main execution function"""
    print("="*60)
    print("Legacy vs New Backtester Comparison")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Run legacy backtester
    legacy_result = run_legacy_backtester()
    
    if legacy_result is None:
        print("\n⚠ Skipping comparison due to legacy service issues")
        print("Running only new backtester...")
    
    # Run new backtester
    new_result = run_new_backtester()
    
    # Compare results if both completed
    if legacy_result and new_result:
        compare_results()
    
    print("\n" + "="*60)
    print("Comparison Complete")
    print("="*60)

if __name__ == "__main__":
    main() 