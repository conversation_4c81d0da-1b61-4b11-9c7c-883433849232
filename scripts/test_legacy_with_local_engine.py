#!/usr/bin/env python3
"""
Test the legacy backtester with the local backtesting engine
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime
import pandas as pd

def prepare_legacy_test():
    """Prepare the legacy backtester for testing with local engine."""
    
    print("=" * 60)
    print("TESTING LEGACY BACKTESTER WITH LOCAL ENGINE")
    print("=" * 60)
    
    # Change to legacy directory
    legacy_dir = "bt/archive/backtester_stable/BTRUN"
    os.chdir(legacy_dir)
    
    # Create INPUT SHEETS directory if it doesn't exist
    os.makedirs("INPUT SHEETS", exist_ok=True)
    
    # Copy test files
    test_portfolio = "/srv/samba/shared/test_data/tbs/input_portfolio_tbs_1day.xlsx"
    test_strategy = "/srv/samba/shared/test_data/tbs/input_tbs_multi_legs.xlsx"
    
    if os.path.exists(test_portfolio):
        shutil.copy(test_portfolio, "INPUT SHEETS/INPUT PORTFOLIO.xlsx")
        print("✅ Copied portfolio file")
    else:
        print("❌ Portfolio file not found")
        return False, None
    
    if os.path.exists(test_strategy):
        shutil.copy(test_strategy, "INPUT SHEETS/input_tbs_multi_legs.xlsx")
        print("✅ Copied strategy file")
    else:
        print("❌ Strategy file not found")
        return False, None
    
    print("\nRunning legacy backtester with local engine...")
    print("-" * 60)
    
    # Set environment variables
    env = os.environ.copy()
    env['MYSQL_HOST'] = 'localhost'
    env['DEBUG_MODE'] = 'true'
    env['PYTHONPATH'] = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN'
    
    # Run the backtester
    start_time = datetime.now()
    
    cmd = ["python3", "BTRunPortfolio.py"]
    process = subprocess.Popen(cmd, env=env, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
    
    # Capture output
    output_lines = []
    for line in iter(process.stdout.readline, ''):
        if line:
            print(f"  {line.rstrip()}")
            output_lines.append(line)
    
    process.wait()
    duration = (datetime.now() - start_time).total_seconds()
    
    print("-" * 60)
    print(f"Legacy backtester completed in {duration:.2f} seconds")
    
    # Check if output was created
    output_files = [f for f in os.listdir("Trades") if f.endswith(".xlsx")] if os.path.exists("Trades") else []
    
    if output_files:
        print(f"\n✅ Output file created: Trades/{output_files[-1]}")
        
        # Analyze the output
        output_path = f"Trades/{output_files[-1]}"
        try:
            # Read key sheets
            df_trans = pd.read_excel(output_path, sheet_name="PORTFOLIO Trans")
            df_metrics = pd.read_excel(output_path, sheet_name="Metrics")
            
            print(f"\nTotal trades: {len(df_trans)}")
            print(f"Unique legs: {df_trans['ID'].unique() if 'ID' in df_trans.columns else 'N/A'}")
            
            # Show some trades
            if not df_trans.empty:
                print("\nFirst few trades:")
                cols_to_show = ['ID', 'Entry Date', 'Enter On', 'Exit Date', 'Exit at', 
                               'Strike', 'CE/PE', 'Trade', 'Net PNL']
                available_cols = [col for col in cols_to_show if col in df_trans.columns]
                print(df_trans[available_cols].head())
            
            return True, output_path
            
        except Exception as e:
            print(f"Error analyzing output: {e}")
            return True, output_path
    else:
        print("\n❌ No output file created")
        
        # Check for errors in output
        error_lines = [line for line in output_lines if 'error' in line.lower() or 'exception' in line.lower()]
        if error_lines:
            print("\nErrors found:")
            for line in error_lines[:5]:
                print(f"  {line.rstrip()}")
        
        return False, None

def compare_with_gpu():
    """Run GPU backtester and compare results."""
    
    print("\n" + "=" * 60)
    print("RUNNING GPU BACKTESTER FOR COMPARISON")
    print("=" * 60)
    
    os.chdir("/srv/samba/shared")
    
    # Run GPU backtester
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "test_data/tbs/input_portfolio_tbs_1day.xlsx",
        "--output-path", "gpu_comparison_output.xlsx"
    ]
    
    start_time = datetime.now()
    result = subprocess.run(cmd, capture_output=True, text=True)
    duration = (datetime.now() - start_time).total_seconds()
    
    print(f"GPU backtester completed in {duration:.2f} seconds")
    
    if os.path.exists("gpu_comparison_output.xlsx"):
        print("✅ GPU output created")
        
        # Read GPU output
        try:
            df_gpu = pd.read_excel("gpu_comparison_output.xlsx", sheet_name="PORTFOLIO Trans")
            print(f"\nGPU Total trades: {len(df_gpu)}")
            
            return True, "gpu_comparison_output.xlsx"
        except Exception as e:
            print(f"Error reading GPU output: {e}")
            return False, None
    else:
        print("❌ GPU output not created")
        if result.stderr:
            print(f"Error: {result.stderr[:500]}")
        return False, None

def main():
    """Main test function."""
    
    # Test legacy with local engine
    legacy_success, legacy_output = prepare_legacy_test()
    
    if legacy_success:
        print("\n✅ Legacy backtester with local engine ran successfully!")
        
        # Run GPU for comparison
        gpu_success, gpu_output = compare_with_gpu()
        
        if gpu_success:
            print("\n" + "=" * 60)
            print("COMPARISON SUMMARY")
            print("=" * 60)
            print(f"Legacy output: {legacy_output}")
            print(f"GPU output: {gpu_output}")
            print("\nBoth backtesters completed successfully!")
            print("You can now compare the Excel outputs for exact parity.")
        else:
            print("\n❌ GPU backtester failed")
    else:
        print("\n❌ Legacy backtester with local engine failed")
        print("\nTroubleshooting tips:")
        print("1. Check if MySQL is running: sudo systemctl status mysql")
        print("2. Verify MySQL data: python3 scripts/check_mysql_data_status.py")
        print("3. Check LocalBacktestEngine.py for syntax errors")
        print("4. Review error messages above")

if __name__ == "__main__":
    main() 