#!/usr/bin/env python3
"""
Data Integrity Check: Legacy MySQL vs New HeavyDB
Compares nifty options data between the two systems
"""

import os
import sys
import mysql.connector
import pandas as pd
from datetime import datetime, date
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Database configurations
MYSQL_CONFIG = {
    "host": "************",
    "user": "mahesh",
    "password": "mahesh_123",
    "database": "historicaldb"
}

HEAVYDB_CONFIG = {
    "host": "127.0.0.1",
    "port": 6274,
    "user": "admin",
    "password": "HyperInteractive",
    "dbname": "heavyai"
}

def connect_mysql():
    """Connect to legacy MySQL database"""
    try:
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        print("✓ Connected to MySQL (Legacy)")
        return conn
    except Exception as e:
        print(f"✗ MySQL connection failed: {e}")
        return None

def connect_heavydb():
    """Connect to HeavyDB"""
    try:
        # Try heavydb module first
        try:
            from heavydb import connect
        except ImportError:
            # Fallback to pymapd
            import pymapd
            connect = pymapd.connect
            
        conn = connect(
            host=HEAVYDB_CONFIG['host'],
            port=HEAVYDB_CONFIG['port'],
            user=HEAVYDB_CONFIG['user'],
            password=HEAVYDB_CONFIG['password'],
            dbname=HEAVYDB_CONFIG['dbname']
        )
        print("✓ Connected to HeavyDB (New)")
        return conn
    except Exception as e:
        print(f"✗ HeavyDB connection failed: {e}")
        return None

def check_mysql_tables(cursor):
    """Check available tables in MySQL"""
    print("\n" + "="*60)
    print("MySQL Tables Check")
    print("="*60)
    
    cursor.execute("SHOW TABLES LIKE '%nifty%'")
    tables = cursor.fetchall()
    
    nifty_tables = {
        'call': None,
        'put': None,
        'cash': None,
        'future': None
    }
    
    for table in tables:
        table_name = table[0]
        print(f"  - {table_name}")
        
        if 'nifty_call' in table_name:
            nifty_tables['call'] = table_name
        elif 'nifty_put' in table_name:
            nifty_tables['put'] = table_name
        elif 'nifty_cash' in table_name:
            nifty_tables['cash'] = table_name
        elif 'nifty_future' in table_name:
            nifty_tables['future'] = table_name
    
    return nifty_tables

def check_heavydb_tables(cursor):
    """Check available tables in HeavyDB"""
    print("\n" + "="*60)
    print("HeavyDB Tables Check")
    print("="*60)
    
    cursor.execute("SELECT name FROM _tables WHERE name LIKE '%nifty%'")
    tables = cursor.fetchall()
    
    for table in tables:
        print(f"  - {table[0]}")
    
    # Check for main option chain table
    cursor.execute("SELECT COUNT(*) FROM _tables WHERE name = 'nifty_option_chain'")
    has_option_chain = cursor.fetchone()[0] > 0
    
    return has_option_chain

def compare_data_ranges(mysql_cursor, heavydb_cursor, date_range=None):
    """Compare date ranges between databases"""
    print("\n" + "="*60)
    print("Date Range Comparison")
    print("="*60)
    
    # MySQL date range
    print("\nMySQL Data Range:")
    for table_type in ['call', 'put', 'cash']:
        table_name = f'nifty_{table_type}'
        try:
            query = f"SELECT MIN(date), MAX(date), COUNT(DISTINCT date) FROM {table_name}"
            mysql_cursor.execute(query)
            min_date, max_date, count = mysql_cursor.fetchone()
            print(f"  {table_name}: {min_date} to {max_date} ({count} days)")
        except Exception as e:
            print(f"  {table_name}: Error - {e}")
    
    # HeavyDB date range
    print("\nHeavyDB Data Range:")
    try:
        query = """
        SELECT MIN(trade_date), MAX(trade_date), COUNT(DISTINCT trade_date)
        FROM nifty_option_chain
        """
        heavydb_cursor.execute(query)
        min_date, max_date, count = heavydb_cursor.fetchone()
        print(f"  nifty_option_chain: {min_date} to {max_date} ({count} days)")
    except Exception as e:
        print(f"  nifty_option_chain: Error - {e}")

def compare_specific_date(mysql_cursor, heavydb_cursor, test_date):
    """Compare data for a specific date"""
    print("\n" + "="*60)
    print(f"Data Comparison for {test_date}")
    print("="*60)
    
    # MySQL data count
    mysql_counts = {}
    for table_type in ['call', 'put']:
        table_name = f'nifty_{table_type}'
        try:
            query = f"SELECT COUNT(*) FROM {table_name} WHERE DATE(date) = '{test_date}'"
            mysql_cursor.execute(query)
            count = mysql_cursor.fetchone()[0]
            mysql_counts[table_type] = count
        except Exception as e:
            mysql_counts[table_type] = f"Error: {e}"
    
    # HeavyDB data count
    try:
        # Count call options
        query = f"""
        SELECT COUNT(*) FROM nifty_option_chain 
        WHERE trade_date = DATE '{test_date}' AND ce_symbol IS NOT NULL
        """
        heavydb_cursor.execute(query)
        heavydb_call_count = heavydb_cursor.fetchone()[0]
        
        # Count put options
        query = f"""
        SELECT COUNT(*) FROM nifty_option_chain 
        WHERE trade_date = DATE '{test_date}' AND pe_symbol IS NOT NULL
        """
        heavydb_cursor.execute(query)
        heavydb_put_count = heavydb_cursor.fetchone()[0]
        
        # Unique strikes
        query = f"""
        SELECT COUNT(DISTINCT strike) FROM nifty_option_chain 
        WHERE trade_date = DATE '{test_date}'
        """
        heavydb_cursor.execute(query)
        unique_strikes = heavydb_cursor.fetchone()[0]
        
    except Exception as e:
        heavydb_call_count = f"Error: {e}"
        heavydb_put_count = f"Error: {e}"
        unique_strikes = "Error"
    
    # Display comparison
    print(f"\nRow Counts:")
    print(f"  MySQL Call Options: {mysql_counts.get('call', 'N/A')}")
    print(f"  HeavyDB Call Options: {heavydb_call_count}")
    print(f"  MySQL Put Options: {mysql_counts.get('put', 'N/A')}")
    print(f"  HeavyDB Put Options: {heavydb_put_count}")
    print(f"  HeavyDB Unique Strikes: {unique_strikes}")

def sample_data_comparison(mysql_cursor, heavydb_cursor, test_date, strike=None):
    """Compare sample records between databases"""
    print("\n" + "="*60)
    print(f"Sample Data Comparison for {test_date}")
    print("="*60)
    
    # Get sample from MySQL
    if strike:
        mysql_query = f"""
        SELECT date, time, strike, close, oi, volume
        FROM nifty_call
        WHERE DATE(date) = '{test_date}' AND strike = {strike}
        ORDER BY time
        LIMIT 5
        """
    else:
        mysql_query = f"""
        SELECT date, time, strike, close, oi, volume
        FROM nifty_call
        WHERE DATE(date) = '{test_date}'
        ORDER BY time, strike
        LIMIT 5
        """
    
    try:
        mysql_cursor.execute(mysql_query)
        mysql_data = mysql_cursor.fetchall()
        
        print("\nMySQL Sample (Call Options):")
        print("Date       | Time   | Strike | Close | OI      | Volume")
        print("-" * 60)
        for row in mysql_data:
            print(f"{row[0]} | {row[1]:6} | {row[2]:6} | {row[3]:6} | {row[4]:8} | {row[5]:8}")
    except Exception as e:
        print(f"MySQL sample error: {e}")
    
    # Get sample from HeavyDB
    if strike:
        heavydb_query = f"""
        SELECT trade_date, trade_time, strike, ce_close, ce_oi, ce_volume
        FROM nifty_option_chain
        WHERE trade_date = DATE '{test_date}' AND strike = {strike}
        AND ce_symbol IS NOT NULL
        ORDER BY trade_time
        LIMIT 5
        """
    else:
        heavydb_query = f"""
        SELECT trade_date, trade_time, strike, ce_close, ce_oi, ce_volume
        FROM nifty_option_chain
        WHERE trade_date = DATE '{test_date}'
        AND ce_symbol IS NOT NULL
        ORDER BY trade_time, strike
        LIMIT 5
        """
    
    try:
        heavydb_cursor.execute(heavydb_query)
        heavydb_data = heavydb_cursor.fetchall()
        
        print("\nHeavyDB Sample (Call Options):")
        print("Date       | Time     | Strike | Close | OI      | Volume")
        print("-" * 60)
        for row in heavydb_data:
            print(f"{row[0]} | {row[1]} | {row[2]:6.0f} | {row[3]:6.2f} | {row[4]:8} | {row[5]:8}")
    except Exception as e:
        print(f"HeavyDB sample error: {e}")

def check_data_quality(mysql_cursor, heavydb_cursor, test_date):
    """Check data quality metrics"""
    print("\n" + "="*60)
    print(f"Data Quality Check for {test_date}")
    print("="*60)
    
    # Check for nulls in HeavyDB
    try:
        quality_checks = [
            ("NULL strikes", "SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{}' AND strike IS NULL"),
            ("NULL call prices", "SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{}' AND ce_symbol IS NOT NULL AND ce_close IS NULL"),
            ("NULL put prices", "SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{}' AND pe_symbol IS NOT NULL AND pe_close IS NULL"),
            ("Zero OI calls", "SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{}' AND ce_symbol IS NOT NULL AND ce_oi = 0"),
            ("Negative prices", "SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{}' AND (ce_close < 0 OR pe_close < 0)")
        ]
        
        print("\nHeavyDB Data Quality:")
        for check_name, query in quality_checks:
            heavydb_cursor.execute(query.format(test_date))
            count = heavydb_cursor.fetchone()[0]
            status = "✓" if count == 0 else "⚠"
            print(f"  {status} {check_name}: {count}")
            
    except Exception as e:
        print(f"  Error in quality check: {e}")

def main():
    """Main execution"""
    print("="*60)
    print("Nifty Options Data Integrity Check")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Connect to databases
    mysql_conn = connect_mysql()
    heavydb_conn = connect_heavydb()
    
    if not mysql_conn or not heavydb_conn:
        print("\n✗ Cannot proceed without both database connections")
        return
    
    mysql_cursor = mysql_conn.cursor()
    heavydb_cursor = heavydb_conn.cursor()
    
    try:
        # Check tables
        mysql_tables = check_mysql_tables(mysql_cursor)
        has_heavydb_table = check_heavydb_tables(heavydb_cursor)
        
        if not has_heavydb_table:
            print("\n✗ nifty_option_chain table not found in HeavyDB")
            return
        
        # Compare date ranges
        compare_data_ranges(mysql_cursor, heavydb_cursor)
        
        # Test specific dates
        test_dates = ['2025-04-01', '2025-04-03', '2025-04-09']  # Including expiry days
        
        for test_date in test_dates:
            compare_specific_date(mysql_cursor, heavydb_cursor, test_date)
            sample_data_comparison(mysql_cursor, heavydb_cursor, test_date, strike=23000)
            check_data_quality(mysql_cursor, heavydb_cursor, test_date)
        
        print("\n" + "="*60)
        print("Data Integrity Check Complete")
        print("="*60)
        
    except Exception as e:
        print(f"\n✗ Error during integrity check: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        mysql_cursor.close()
        mysql_conn.close()
        heavydb_cursor.close()
        heavydb_conn.close()

if __name__ == "__main__":
    main() 