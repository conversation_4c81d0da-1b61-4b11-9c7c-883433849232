#!/usr/bin/env python3
"""
Run legacy backtester with proper configuration setup
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime

def setup_legacy_environment():
    """Setup the environment for legacy backtester"""
    print("Setting up legacy environment...")
    
    legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
    input_sheets_dir = os.path.join(legacy_dir, "INPUT SHEETS")
    
    # Create INPUT SHEETS directory
    os.makedirs(input_sheets_dir, exist_ok=True)
    
    # Copy input files to expected location
    source_file = os.path.abspath("bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx")
    dest_file = os.path.join(input_sheets_dir, "INPUT PORTFOLIO.xlsx")
    
    if os.path.exists(source_file):
        shutil.copy2(source_file, dest_file)
        print(f"  ✓ Copied input file to: {dest_file}")
        
        # Also copy any strategy files referenced
        source_dir = os.path.dirname(source_file)
        for f in os.listdir(source_dir):
            if f.endswith('.xlsx') and 'tbs' in f.lower():
                shutil.copy2(os.path.join(source_dir, f), os.path.join(input_sheets_dir, f))
                print(f"  ✓ Copied strategy file: {f}")
    else:
        print(f"  ✗ Source file not found: {source_file}")
        return False
    
    return True

def run_legacy_backtester():
    """Run the legacy backtester"""
    print("\nRunning legacy backtester...")
    
    legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
    legacy_script = os.path.join(legacy_dir, "BTRunPortfolio.py")
    
    # Set up environment
    env = os.environ.copy()
    env['PYTHONPATH'] = f"{os.path.abspath('bt/archive/backtester_stable')}:{os.path.abspath('.')}"
    
    # Run the script
    cmd = [sys.executable, legacy_script]
    
    print(f"  Command: {' '.join(cmd)}")
    print(f"  Working directory: {legacy_dir}")
    
    try:
        result = subprocess.run(
            cmd,
            cwd=legacy_dir,
            env=env,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        print(f"  Return code: {result.returncode}")
        
        if result.stdout:
            print("\n  STDOUT (last 500 chars):")
            print("  " + result.stdout[-500:].replace("\n", "\n  "))
            
        if result.stderr:
            print("\n  STDERR (last 500 chars):")
            print("  " + result.stderr[-500:].replace("\n", "\n  "))
        
        # Check for output files
        trades_dir = os.path.join(legacy_dir, "Trades")
        if os.path.exists(trades_dir):
            output_files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')]
            if output_files:
                print(f"\n  ✓ Found {len(output_files)} output file(s):")
                for f in output_files:
                    print(f"    - {f}")
                    
                # Copy latest to comparison directory
                latest = max([os.path.join(trades_dir, f) for f in output_files], key=os.path.getctime)
                os.makedirs("comparison_outputs", exist_ok=True)
                dest = "comparison_outputs/legacy_output_configured.xlsx"
                shutil.copy2(latest, dest)
                print(f"\n  ✓ Copied latest output to: {dest}")
                return True
        
        return False
        
    except Exception as e:
        print(f"  ✗ Error: {e}")
        return False

def main():
    print("="*60)
    print("Legacy Backtester with Configuration")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Setup environment
    if not setup_legacy_environment():
        print("\n✗ Failed to setup environment")
        return
    
    # Run backtester
    success = run_legacy_backtester()
    
    print("\n" + "="*60)
    if success:
        print("✓ Legacy backtester completed successfully")
        print("  Output saved to: comparison_outputs/legacy_output_configured.xlsx")
    else:
        print("✗ Legacy backtester failed")
    print("="*60)

if __name__ == "__main__":
    main() 