#!/usr/bin/env python3
"""Check all sheets in the output Excel file."""

import pandas as pd

excel_file = "trace_output.xlsx"

# Get all sheet names
xl_file = pd.ExcelFile(excel_file)
sheet_names = xl_file.sheet_names

print(f"=== Sheets in {excel_file} ===")
print(f"Total sheets: {len(sheet_names)}")
print(f"Sheet names: {sheet_names}")

# Read each sheet and show its structure
for sheet_name in sheet_names:
    print(f"\n=== Sheet: {sheet_name} ===")
    df = pd.read_excel(excel_file, sheet_name=sheet_name)
    print(f"Rows: {len(df)}")
    print(f"Columns: {list(df.columns)}")
    
    # Show first few rows
    print(f"\nFirst 5 rows:")
    print(df.head())
    
    # For trade-like sheets, show unique values in key columns
    if any(col in df.columns for col in ['leg_id', 'LegID', 'strike', 'Strike', 'option_type', 'instrument_type']):
        print("\nKey column values:")
        for col in ['leg_id', 'LegID', 'strike', 'Strike', 'option_type', 'instrument_type', 'side', 'Trade']:
            if col in df.columns:
                print(f"  {col}: {df[col].unique()}") 