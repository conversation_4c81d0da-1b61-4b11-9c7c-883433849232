#!/usr/bin/env python3
"""
Quick parallel copy of remaining NIFTY data with immediate feedback
"""

import pandas as pd
from sqlalchemy import create_engine, text
import time
import sys
from datetime import datetime
from multiprocessing import Process, Queue

# Connection URLs
REMOTE_URL = 'mysql+pymysql://mahesh:mahesh_123@************:3306/historicaldb'
LOCAL_URL = 'mysql+pymysql://mahesh:mahesh_123@localhost:3306/historicaldb'

def copy_nifty_put_remaining(status_queue):
    """Copy remaining nifty_put data"""
    try:
        status_queue.put(f"[nifty_put] Starting...")
        remote_engine = create_engine(REMOTE_URL, pool_pre_ping=True)
        local_engine = create_engine(LOCAL_URL, pool_pre_ping=True)
        
        # Count remaining rows
        with remote_engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) FROM nifty_put WHERE date > 241113 AND date <= 241231"))
            total_rows = result.scalar()
        
        status_queue.put(f"[nifty_put] {total_rows:,} rows to copy (after Nov 13)")
        
        # Copy in batches
        batch_size = 50000
        offset = 0
        start_time = time.time()
        
        while offset < total_rows:
            query = text("""
            SELECT * FROM nifty_put 
            WHERE date > 241113 AND date <= 241231
            ORDER BY date, time
            LIMIT :limit OFFSET :offset
            """)
            
            with remote_engine.connect() as conn:
                df = pd.read_sql_query(query, conn, params={"limit": batch_size, "offset": offset})
            
            if df.empty:
                break
            
            with local_engine.begin() as conn:
                df.to_sql('nifty_put', conn, if_exists='append', index=False, method='multi')
            
            offset += len(df)
            elapsed = time.time() - start_time
            rate = offset / elapsed if elapsed > 0 else 0
            status_queue.put(f"[nifty_put] {offset:,}/{total_rows:,} ({offset/total_rows*100:.1f}%) - {rate:.0f} rows/s")
        
        status_queue.put(f"[nifty_put] ✓ COMPLETED - {total_rows:,} rows")
        
    except Exception as e:
        status_queue.put(f"[nifty_put] ERROR: {str(e)}")

def copy_table_simple(table_name, status_queue):
    """Copy a complete table"""
    try:
        status_queue.put(f"[{table_name}] Starting...")
        remote_engine = create_engine(REMOTE_URL, pool_pre_ping=True)
        local_engine = create_engine(LOCAL_URL, pool_pre_ping=True)
        
        # Get table structure
        with remote_engine.connect() as conn:
            result = conn.execute(text(f"SHOW CREATE TABLE {table_name}"))
            create_stmt = result.fetchone()[1]
        
        # Create table
        with local_engine.connect() as conn:
            conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
            conn.execute(text(create_stmt))
            conn.commit()
        
        # Count rows
        with remote_engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name} WHERE date >= 240101 AND date <= 241231"))
            total_rows = result.scalar()
        
        status_queue.put(f"[{table_name}] {total_rows:,} rows to copy")
        
        # Copy in batches
        batch_size = 50000
        offset = 0
        start_time = time.time()
        
        while offset < total_rows:
            query = text(f"""
            SELECT * FROM {table_name}
            WHERE date >= 240101 AND date <= 241231
            ORDER BY date, time
            LIMIT :limit OFFSET :offset
            """)
            
            with remote_engine.connect() as conn:
                df = pd.read_sql_query(query, conn, params={"limit": batch_size, "offset": offset})
            
            if df.empty:
                break
            
            with local_engine.begin() as conn:
                df.to_sql(table_name, conn, if_exists='append', index=False, method='multi')
            
            offset += len(df)
            elapsed = time.time() - start_time
            rate = offset / elapsed if elapsed > 0 else 0
            
            if offset % (batch_size * 2) == 0:  # Update every 2 batches
                status_queue.put(f"[{table_name}] {offset:,}/{total_rows:,} ({offset/total_rows*100:.1f}%) - {rate:.0f} rows/s")
        
        status_queue.put(f"[{table_name}] ✓ COMPLETED - {total_rows:,} rows")
        
    except Exception as e:
        status_queue.put(f"[{table_name}] ERROR: {str(e)}")

def main():
    print("Quick Parallel NIFTY Copy - Remaining Data")
    print("=" * 70)
    print(f"Started at: {datetime.now()}")
    print("=" * 70)
    
    # Create status queue
    status_queue = Queue()
    
    # Start processes
    processes = []
    
    # nifty_put remaining
    p1 = Process(target=copy_nifty_put_remaining, args=(status_queue,))
    p1.start()
    processes.append(p1)
    
    # nifty_cash
    p2 = Process(target=copy_table_simple, args=('nifty_cash', status_queue))
    p2.start()
    processes.append(p2)
    
    # nifty_future
    p3 = Process(target=copy_table_simple, args=('nifty_future', status_queue))
    p3.start()
    processes.append(p3)
    
    print("Started 3 parallel processes...")
    print("")
    
    # Monitor progress
    active_processes = len(processes)
    while active_processes > 0:
        try:
            # Check for status updates
            while not status_queue.empty():
                status = status_queue.get_nowait()
                print(f"{datetime.now().strftime('%H:%M:%S')} {status}")
                sys.stdout.flush()
            
            # Check if processes are still alive
            active_processes = sum(1 for p in processes if p.is_alive())
            
            time.sleep(0.5)
            
        except KeyboardInterrupt:
            print("\nInterrupted! Terminating processes...")
            for p in processes:
                p.terminate()
            break
    
    # Wait for all processes to complete
    for p in processes:
        p.join()
    
    # Final status
    print("\n" + "=" * 70)
    print(f"Completed at: {datetime.now()}")
    print("=" * 70)
    
    # Show final counts
    try:
        local_engine = create_engine(LOCAL_URL, pool_pre_ping=True)
        with local_engine.connect() as conn:
            for table in ['nifty_put', 'nifty_cash', 'nifty_future']:
                result = conn.execute(text(f"SELECT COUNT(*) as cnt, MIN(date) as min_date, MAX(date) as max_date FROM {table}"))
                row = result.fetchone()
                print(f"{table}: {row.cnt:,} rows ({row.min_date} to {row.max_date})")
    except:
        pass
    
    print("\n✓ Copy complete! Ready for backtester comparison.")

if __name__ == "__main__":
    main() 