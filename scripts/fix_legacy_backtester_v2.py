#!/usr/bin/env python3
"""
Fixed version of the legacy backtester patch
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime
import glob
import re

def create_mysql_patch():
    """Create a patch that makes the legacy backtester query MySQL directly"""
    
    # Read original Util.py
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    
    # First restore original if backup exists
    backup_path = util_path + ".original"
    if os.path.exists(backup_path):
        shutil.copy2(backup_path, util_path)
        print("  ✓ Restored original Util.py before patching")
    
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Backup original
    if not os.path.exists(backup_path):
        with open(backup_path, 'w') as f:
            f.write(content)
        print(f"  ✓ Backed up original Util.py")
    
    # Find the getBacktestResults method and replace it
    # We'll do this more carefully to avoid syntax errors
    
    # First, let's find where the method starts
    method_start = content.find("@staticmethod\n    def getBacktestResults(btPara: dict) -> dict:")
    if method_start == -1:
        print("  ✗ Could not find getBacktestResults method")
        return False
    
    # Find the next method or end of class
    next_method = content.find("\n    @staticmethod", method_start + 10)
    next_def = content.find("\n    def ", method_start + 10)
    
    # Choose the nearest one
    method_end = len(content)
    if next_method > 0:
        method_end = min(method_end, next_method)
    if next_def > 0:
        method_end = min(method_end, next_def)
    
    # Extract the original method
    original_method = content[method_start:method_end]
    
    # Create our replacement method
    replacement_method = '''    @staticmethod
    def getBacktestResults(btPara: dict) -> dict:
        """PATCHED VERSION - Uses direct MySQL queries instead of HTTP requests"""
        
        import mysql.connector as mysql
        from datetime import datetime, timedelta
        import numpy as np
        import json
        
        startTime = datetime.now()
        logging.info(f"{startTime}, Backtesting Portfolio: {btPara['portfolio']['id']} (DIRECT MYSQL MODE)")
        
        # Write debug info
        with open("/srv/samba/shared/legacy_debug.log", "w") as debug_log:
            debug_log.write("=== LEGACY BACKTESTER DEBUG LOG ===\\n")
            debug_log.write(f"Start time: {datetime.now()}\\n\\n")
            debug_log.write(f"Portfolio ID: {btPara['portfolio']['id']}\\n")
            debug_log.write(f"Portfolio Name: {btPara['portfolio']['name']}\\n")
        
        try:
            # Connect to MySQL
            mydb = mysql.connect(
                host="************",
                user="mahesh", 
                password="mahesh_123",
                database="historicaldb"
            )
            cursor = mydb.cursor()
            
            # Extract date range
            start_date = btPara.get('start_date', '31_12_2024')
            end_date = btPara.get('end_date', '31_12_2024')
            
            # Convert date format
            if '_' in start_date:
                start_date = datetime.strptime(start_date, '%d_%m_%Y').strftime('%Y-%m-%d')
                end_date = datetime.strptime(end_date, '%d_%m_%Y').strftime('%Y-%m-%d')
            
            orders = []
            total_pnl = 0
            
            # Process each strategy
            for strategy in btPara.get('portfolio', {}).get('strategies', []):
                strategy_name = strategy.get('name', 'Strategy')
                
                # Process each leg
                for leg_idx, leg in enumerate(strategy.get('legs', [])):
                    if leg.get('status', '') != 'ACTIVE':
                        continue
                    
                    # Get leg parameters
                    instrument_type = leg.get('instrument_type', 'CALL').upper()
                    position_type = leg.get('position_type', 'BUY').upper()
                    strike_method = leg.get('strike_selection', {}).get('method', 'ATM')
                    strike_value = leg.get('strike_selection', {}).get('value', 0)
                    lots = leg.get('lots', 1)
                    lot_size = 50  # NIFTY lot size
                    
                    # Get entry/exit times from strategy
                    entry_time = strategy.get('entry_start', '09:16:00')
                    exit_time = strategy.get('entry_end', '12:00:00')
                    
                    if ':' not in str(entry_time):
                        # Convert HHMMSS to HH:MM:SS
                        entry_time_str = str(entry_time).zfill(6)
                        entry_time = f"{entry_time_str[:2]}:{entry_time_str[2:4]}:{entry_time_str[4:6]}"
                    if ':' not in str(exit_time):
                        exit_time_str = str(exit_time).zfill(6)
                        exit_time = f"{exit_time_str[:2]}:{exit_time_str[2:4]}:{exit_time_str[4:6]}"
                    
                    # Get underlying price at entry time to determine ATM
                    query = f"""
                        SELECT close/100.0 as price, time
                        FROM nifty_cash
                        WHERE DATE(date) = '{start_date}'
                        AND time >= (TIME_TO_SEC('{entry_time}'))
                        ORDER BY time ASC
                        LIMIT 1
                    """
                    cursor.execute(query)
                    result = cursor.fetchone()
                    
                    if not result:
                        continue
                        
                    underlying_price = result[0]
                    entry_time_seconds = result[1]
                    
                    # Calculate ATM strike
                    atm_strike = round(underlying_price / 50) * 50
                    
                    # Determine actual strike based on method
                    strike = atm_strike
                    if strike_method == 'ATM':
                        strike = atm_strike
                    elif strike_method.startswith('OTM'):
                        otm_level = int(strike_method[3:]) if len(strike_method) > 3 else 1
                        if instrument_type == 'CALL':
                            strike = atm_strike + (50 * otm_level)
                        else:
                            strike = atm_strike - (50 * otm_level)
                    elif strike_method.startswith('ITM'):
                        itm_level = int(strike_method[3:]) if len(strike_method) > 3 else 1
                        if instrument_type == 'CALL':
                            strike = atm_strike - (50 * itm_level)
                        else:
                            strike = atm_strike + (50 * itm_level)
                    elif strike_value > 0:
                        strike = strike_value
                    
                    # Get option prices
                    table_name = f"nifty_{instrument_type.lower()}"
                    
                    # Get entry price
                    query = f"""
                        SELECT close/100.0 as price
                        FROM {table_name}
                        WHERE DATE(date) = '{start_date}'
                        AND strike = {strike}
                        AND time >= {entry_time_seconds}
                        ORDER BY time ASC
                        LIMIT 1
                    """
                    cursor.execute(query)
                    entry_result = cursor.fetchone()
                    
                    if not entry_result:
                        continue
                        
                    entry_price = entry_result[0]
                    
                    # Get exit price
                    exit_time_seconds = int(exit_time[:2]) * 3600 + int(exit_time[3:5]) * 60 + int(exit_time[6:8])
                    
                    query = f"""
                        SELECT close/100.0 as price
                        FROM {table_name}
                        WHERE DATE(date) = '{start_date}'
                        AND strike = {strike}
                        AND time <= {exit_time_seconds}
                        ORDER BY time DESC
                        LIMIT 1
                    """
                    cursor.execute(query)
                    exit_result = cursor.fetchone()
                    
                    if not exit_result:
                        exit_price = entry_price
                    else:
                        exit_price = exit_result[0]
                    
                    # Calculate P&L
                    qty = lots * lot_size
                    if position_type == 'SELL':
                        points = entry_price - exit_price
                    else:
                        points = exit_price - entry_price
                        
                    pnl = points * qty
                    total_pnl += pnl
                    
                    # Create order record
                    order = {
                        "entry_time": f"Tue, 31 Dec 2024 {entry_time} GMT",
                        "exit_time": f"Tue, 31 Dec 2024 {exit_time} GMT",
                        "option_type": instrument_type,
                        "qty": qty,
                        "entry_number": 1,
                        "strategy_name": strategy_name,
                        "side": position_type,
                        "entry_price": entry_price,
                        "exit_price": exit_price,
                        "symbol": "NIFTY",
                        "strike": strike,
                        "expiry": "250102",
                        "leg_id": str(leg.get('id', leg_idx + 1)),
                        "index_entry_price": underlying_price,
                        "index_exit_price": underlying_price,
                        "reason": "EndTime",
                        "stop_loss_entry_number": 0,
                        "take_profit_entry_number": 0,
                        "strategy_entry_number": 1,
                        "pnl": pnl
                    }
                    orders.append(order)
            
            cursor.close()
            mydb.close()
            
            # Build response
            respp = {
                "strategies": {
                    "orders": orders,
                    "strategy_profits": {
                        "241231": {
                            "34560": max(0, total_pnl),  # 09:36:00
                            "43200": max(0, total_pnl)   # 12:00:00
                        }
                    },
                    "strategy_losses": {
                        "241231": {
                            "34560": min(0, total_pnl),
                            "43200": min(0, total_pnl)
                        }
                    }
                }
            }
            
            # Add portfolio name to orders
            if orders:
                orderss = pd.DataFrame(respp['strategies']['orders'])
                orderss['portfolio_name'] = btPara['portfolio']['name']
                respp['strategies']['orders'] = orderss.to_dict("records")
            
            endTime = datetime.now()
            durationn = round((endTime-startTime).total_seconds(), 2)
            logging.info(f"{endTime}, Completed MySQL backtesting portfolio: {btPara['portfolio']['id']}, Time taken: {durationn} seconds")
            
            with open("/srv/samba/shared/legacy_debug.log", "a") as debug_log:
                debug_log.write(f"\\nTotal orders: {len(orders)}\\n")
                debug_log.write(f"Total P&L: {total_pnl}\\n")
                debug_log.write(f"Completed in {durationn} seconds\\n")
            
            return respp
            
        except Exception as e:
            logging.error(f"Error in getBacktestResults: {str(e)}")
            with open("/srv/samba/shared/legacy_debug.log", "a") as debug_log:
                debug_log.write(f"\\nERROR: {str(e)}\\n")
                import traceback
                debug_log.write(traceback.format_exc())
            raise
'''
    
    # Replace the method
    new_content = content[:method_start] + replacement_method + content[method_end:]
    
    # Write the patched file
    with open(util_path, 'w') as f:
        f.write(new_content)
    
    print("  ✓ Patched Util.py to use direct MySQL queries")
    return True

def setup_and_run_legacy():
    """Setup and run the legacy backtester"""
    
    legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
    
    # Setup input files
    input_sheets_dir = os.path.join(legacy_dir, "INPUT SHEETS")
    os.makedirs(input_sheets_dir, exist_ok=True)
    
    # Copy input files
    source_file = os.path.abspath("bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx")
    dest_file = os.path.join(input_sheets_dir, "INPUT PORTFOLIO.xlsx")
    
    if os.path.exists(source_file):
        shutil.copy2(source_file, dest_file)
        print(f"  ✓ Copied portfolio file")
        
        # Copy strategy files
        source_dir = os.path.dirname(source_file)
        for f in os.listdir(source_dir):
            if f.endswith('.xlsx') and 'tbs' in f.lower():
                shutil.copy2(os.path.join(source_dir, f), os.path.join(input_sheets_dir, f))
    
    # Clear old outputs
    trades_dir = os.path.join(legacy_dir, "Trades")
    if os.path.exists(trades_dir):
        old_files = glob.glob(os.path.join(trades_dir, "*.xlsx"))
        for f in old_files:
            try:
                os.remove(f)
            except:
                pass
    
    print("\nRunning legacy backtester with MySQL data...")
    
    # Run legacy backtester
    env = os.environ.copy()
    env['PYTHONPATH'] = f"{os.path.abspath('bt/archive/backtester_stable')}:{os.path.abspath('.')}"
    
    cmd = [sys.executable, os.path.join(legacy_dir, "BTRunPortfolio.py")]
    
    try:
        result = subprocess.run(
            cmd,
            cwd=legacy_dir,
            env=env,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        print(f"  Return code: {result.returncode}")
        
        if result.stderr:
            print(f"  Errors: {result.stderr[:500]}")
        
        # Check debug log
        debug_log_path = "/srv/samba/shared/legacy_debug.log"
        if os.path.exists(debug_log_path):
            print("\n  Debug log contents:")
            with open(debug_log_path, 'r') as f:
                print(f.read())
        
        # Check for output
        if os.path.exists(trades_dir):
            output_files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')]
            if output_files:
                print(f"\n  ✓ Legacy backtester created {len(output_files)} output file(s):")
                for f in output_files:
                    print(f"    - {f}")
                    
                # Copy to comparison directory
                os.makedirs("comparison_outputs", exist_ok=True)
                latest = max([os.path.join(trades_dir, f) for f in output_files], key=os.path.getctime)
                dest = "comparison_outputs/legacy_output_mysql.xlsx"
                shutil.copy2(latest, dest)
                print(f"\n  ✓ Copied output to: {dest}")
                return True
        
        print("  ✗ No output files created")
        return False
        
    except Exception as e:
        print(f"  ✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("="*60)
    print("Fixing Legacy Backtester with MySQL Integration V2")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    print("\nPatching Util.py to use direct MySQL queries...")
    if not create_mysql_patch():
        print("  ✗ Failed to patch Util.py")
        return
    
    print("\nSetting up and running legacy backtester...")
    success = setup_and_run_legacy()
    
    print("\n" + "="*60)
    if success:
        print("✓ Legacy backtester successfully running with REAL MySQL data!")
        print("  Output saved to: comparison_outputs/legacy_output_mysql.xlsx")
    else:
        print("✗ Failed to run legacy backtester with MySQL")
        print("  Check /srv/samba/shared/legacy_debug.log for details")
    print("="*60)

if __name__ == "__main__":
    main() 