#!/usr/bin/env python3
"""
Run 1-year backtest with GPU optimization
"""

import os
import sys
import time
import subprocess
from datetime import datetime
import pandas as pd

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def update_portfolio_dates(start_date, end_date):
    """Update the portfolio Excel file with new date range"""
    portfolio_file = os.path.join(project_root, 'bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx')
    
    print(f"Updating portfolio dates: {start_date} to {end_date}")
    
    # Read the Excel file
    df = pd.read_excel(portfolio_file, sheet_name='PortfolioSetting')
    df.loc[0, 'StartDate'] = start_date
    df.loc[0, 'EndDate'] = end_date
    
    # Write back using openpyxl
    with pd.ExcelWriter(portfolio_file, engine='openpyxl', mode='a', if_sheet_exists='overlay') as writer:
        df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
    
    print("Portfolio dates updated successfully")

def run_gpu_backtest():
    """Run the backtest with GPU optimization"""
    print("\n" + "="*70)
    print("Starting 1-Year GPU-Optimized Backtest")
    print("="*70)
    
    # Set up the command
    cmd = [
        sys.executable, '-m', 'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
        '--legacy-excel',
        '--portfolio-excel', 'bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx',
        '--output-path', 'output/1year_gpu_backtest.xlsx',
        '--use-gpu-optimization',  # Enable GPU optimization
        '--gpu-workers', '4',  # Use 4 GPU workers
        '--batch-days', '10',  # Process 10 days at a time
        '--debug'
    ]
    
    # Set environment variables for GPU optimization
    env = os.environ.copy()
    env['PYTHONPATH'] = project_root
    env['CUDA_VISIBLE_DEVICES'] = '0'  # Use first GPU
    env['HEAVYDB_GPU_MODE'] = '1'  # Enable GPU mode for HeavyDB
    
    print("\nCommand:", ' '.join(cmd))
    print("\nStarting backtest at:", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    start_time = time.time()
    
    # Run the backtest with real-time output
    process = subprocess.Popen(
        cmd,
        env=env,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        bufsize=1
    )
    
    # Monitor output
    trade_count = 0
    last_progress = ""
    error_lines = []
    
    for line in process.stdout:
        # Print important lines
        if any(keyword in line for keyword in ['ERROR', 'WARNING', 'Generated', 'trades', 'Calculating', 'Query executed', 'usage:', 'Exception', 'Traceback']):
            print(line.strip())
        
        # Collect error lines
        if 'ERROR' in line or 'Exception' in line or 'Traceback' in line:
            error_lines.append(line.strip())
        
        # Track progress
        if 'Processing date:' in line:
            last_progress = line.strip()
        elif 'Generated' in line and 'trade records' in line:
            try:
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == 'Generated' and i+1 < len(parts):
                        trade_count += int(parts[i+1])
            except:
                pass
    
    process.wait()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "="*70)
    print(f"Backtest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Total duration: {duration:.2f} seconds ({duration/60:.2f} minutes)")
    print(f"Total trades processed: {trade_count}")
    
    if process.returncode != 0 and error_lines:
        print("\nERRORS ENCOUNTERED:")
        print("-" * 50)
        for err in error_lines[-10:]:  # Last 10 error lines
            print(err)
    
    print("="*70)
    
    return process.returncode

def check_results():
    """Check the backtest results"""
    output_file = os.path.join(project_root, 'output/1year_gpu_backtest.xlsx')
    
    if not os.path.exists(output_file):
        print("\nERROR: Output file not found!")
        return False
    
    print("\nChecking results...")
    
    # Load and analyze results
    try:
        # Check file size
        file_size_mb = os.path.getsize(output_file) / (1024 * 1024)
        print(f"Output file size: {file_size_mb:.2f} MB")
        
        # Load key sheets
        xl = pd.ExcelFile(output_file)
        print(f"Available sheets: {', '.join(xl.sheet_names)}")
        
        # Check metrics
        if 'Metrics' in xl.sheet_names:
            metrics_df = pd.read_excel(output_file, sheet_name='Metrics')
            print("\nKey Metrics:")
            key_metrics = ['Total Trades', 'Net P&L', 'Win Rate', 'Sharpe Ratio', 'Max Drawdown']
            for metric in key_metrics:
                rows = metrics_df[metrics_df['Particulars'].str.contains(metric, case=False, na=False)]
                if not rows.empty:
                    print(f"  {metric}: {rows.iloc[0]['Value']}")
        
        # Check transactions
        if 'PORTFOLIO Trans' in xl.sheet_names:
            trans_df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
            print(f"\nTotal transactions: {len(trans_df)}")
            
            # Date range
            if 'Entry Date' in trans_df.columns:
                trans_df['Entry Date'] = pd.to_datetime(trans_df['Entry Date'])
                print(f"Date range: {trans_df['Entry Date'].min()} to {trans_df['Entry Date'].max()}")
                print(f"Trading days: {trans_df['Entry Date'].nunique()}")
        
        # Check Max Profit/Loss
        if 'Max Profit and Loss' in xl.sheet_names:
            max_pl_df = pd.read_excel(output_file, sheet_name='Max Profit and Loss')
            print(f"\nMax Profit/Loss records: {len(max_pl_df)}")
            if not max_pl_df.empty:
                print("Sample records:")
                print(max_pl_df.head())
        
        return True
        
    except Exception as e:
        print(f"\nERROR checking results: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    # Set date range for 1 year (2024)
    start_date = '01_01_2024'
    end_date = '31_12_2024'
    
    # Update portfolio dates
    update_portfolio_dates(start_date, end_date)
    
    # Run the backtest
    return_code = run_gpu_backtest()
    
    if return_code == 0:
        print("\nBacktest completed successfully!")
        # Check results
        if check_results():
            print("\nResults verified successfully!")
        else:
            print("\nWarning: Issues found in results verification")
    else:
        print(f"\nBacktest failed with return code: {return_code}")
    
    return return_code

if __name__ == "__main__":
    sys.exit(main()) 