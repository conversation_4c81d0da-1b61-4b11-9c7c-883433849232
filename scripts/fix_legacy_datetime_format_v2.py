#!/usr/bin/env python3
"""Fix datetime format parsing issue in legacy Util.py - version 2."""

def fix_datetime_parsing():
    """Fix the datetime parsing in parseBacktestingResponse."""
    
    util_path = "bt/archive/backtester_stable/BTRUN/Util.py"
    
    # Read the file
    with open(util_path, 'r') as f:
        content = f.read()
    
    # Find the problematic line and replace it
    old_line = '''orderdf[f'{columnName}_datetime'] = pd.to_datetime(orderdf[f'{columnName}_datetime'], format="%a, %d %b %Y %H:%M:%S GMT")'''
    
    # New code that tries multiple formats
    new_code = '''# Try multiple datetime formats
            try:
                # First try the Flask service format
                orderdf[f'{columnName}_datetime'] = pd.to_datetime(orderdf[f'{columnName}_datetime'], format="%a, %d %b %Y %H:%M:%S GMT")
            except:
                try:
                    # If that fails, try the LocalBacktestEngine format
                    orderdf[f'{columnName}_datetime'] = pd.to_datetime(orderdf[f'{columnName}_datetime'])
                except:
                    # Last resort - let pandas infer the format
                    orderdf[f'{columnName}_datetime'] = pd.to_datetime(orderdf[f'{columnName}_datetime'], infer_datetime_format=True)'''
    
    # Replace the line
    content = content.replace(old_line, new_code)
    
    # Write back
    with open(util_path, 'w') as f:
        f.write(content)
    
    print("✅ Fixed datetime parsing issue in Util.py (version 2)")

if __name__ == "__main__":
    fix_datetime_parsing()
    print("\nYou can now run the legacy backtester again") 