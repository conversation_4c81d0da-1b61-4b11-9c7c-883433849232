#!/usr/bin/env python3
"""Check the actual Excel data to see what's in LegParameter sheet."""
import pandas as pd

excel_file = "bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"

# Read the LegParameter sheet
df = pd.read_excel(excel_file, sheet_name="LegParameter")

print("=== LegParameter Sheet Data ===")
print(f"Total rows: {len(df)}")
print(f"Columns: {list(df.columns)}")

# Show all rows for the active strategy
active_strategy = "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL"
strategy_rows = df[df['StrategyName'] == active_strategy]

print(f"\nRows for strategy '{active_strategy}':")
print(strategy_rows[['LegID', 'Instrument', 'Transaction', 'StrikeMethod', 'StrikeValue', 'IsIdle', 'Lots']])

# Check if there are other strategies
print(f"\nAll unique strategies in sheet:")
print(df['StrategyName'].unique())

# Save full data for inspection
df.to_csv('leg_parameter_data.csv', index=False)
print("\nFull data saved to leg_parameter_data.csv") 