#!/usr/bin/env python3
"""Trace where build_trade_record is called and find the indentation issue."""

with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

# Find all occurrences of build_trade_record and trade_records.append
print("=== Finding build_trade_record and append calls ===\n")

build_lines = []
append_lines = []

for i, line in enumerate(lines):
    if 'build_trade_record' in line and 'record =' in line:
        indent = len(line) - len(line.lstrip())
        build_lines.append((i+1, indent, line.strip()))
        print(f'Line {i+1} ({indent} spaces): {line.strip()[:60]}')
    
    if 'trade_records.append' in line:
        indent = len(line) - len(line.lstrip())
        append_lines.append((i+1, indent, line.strip()))
        print(f'Line {i+1} ({indent} spaces): {line.strip()}')

print(f'\nFound {len(build_lines)} build_trade_record calls')
print(f'Found {len(append_lines)} trade_records.append calls')

# Now trace the control flow around these lines
if build_lines and append_lines:
    build_line_num = build_lines[0][0] - 1  # Convert to 0-based
    append_line_num = append_lines[0][0] - 1
    
    print(f'\n=== Analyzing control flow ===')
    print(f'Build at line {build_line_num+1}, Append at line {append_line_num+1}')
    
    # Find what loops we're in at the build line
    print('\n=== Loop context at build_trade_record ===')
    for i in range(max(0, build_line_num-200), build_line_num):
        line = lines[i]
        if 'for ' in line and line.strip().startswith('for'):
            indent = len(line) - len(line.lstrip())
            print(f'Line {i+1} ({indent} spaces): {line.strip()}')
    
    # Check indentation between build and append
    print('\n=== Code between build_trade_record and append ===')
    for i in range(build_line_num, min(append_line_num+1, build_line_num+20)):
        line = lines[i]
        indent = len(line) - len(line.lstrip())
        if line.strip():  # Skip empty lines
            marker = ' <<<' if i == append_line_num else ''
            print(f'Line {i+1} ({indent:2d} spaces): {line.strip()[:70]}{marker}') 