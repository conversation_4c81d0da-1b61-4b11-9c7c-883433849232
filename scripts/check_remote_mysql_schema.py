#!/usr/bin/env python3
"""
Check remote MySQL database schema and table structure
"""

import pymysql
import pandas as pd
from datetime import datetime

# Remote MySQL connection details
REMOTE_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def check_remote_schema():
    """Check the schema of remote MySQL database"""
    print("Checking Remote MySQL Schema")
    print("=" * 70)
    
    try:
        # Connect to remote MySQL
        conn = pymysql.connect(**REMOTE_CONFIG)
        cursor = conn.cursor()
        
        # Get list of tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print(f"\nFound {len(tables)} tables in historicaldb:")
        print("-" * 50)
        
        table_info = {}
        
        for (table_name,) in tables:
            print(f"\nTable: {table_name}")
            
            # Get table structure
            cursor.execute(f"DESCRIBE {table_name}")
            columns = cursor.fetchall()
            
            print("  Columns:")
            for col in columns:
                print(f"    {col[0]} - {col[1]} {'(NULL)' if col[2] == 'YES' else '(NOT NULL)'}")
            
            # Get row count and date range if applicable
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count = cursor.fetchone()[0]
            print(f"  Row count: {row_count:,}")
            
            # Check for date columns
            date_cols = [col[0] for col in columns if 'date' in col[1].lower() or col[0].lower() == 'date']
            if date_cols and row_count > 0:
                for date_col in date_cols:
                    try:
                        cursor.execute(f"SELECT MIN({date_col}), MAX({date_col}) FROM {table_name} WHERE {date_col} IS NOT NULL")
                        min_date, max_date = cursor.fetchone()
                        if min_date:
                            print(f"  Date range ({date_col}): {min_date} to {max_date}")
                    except:
                        pass
            
            # Store info
            table_info[table_name] = {
                'columns': columns,
                'row_count': row_count
            }
        
        # Look for option-related tables
        print("\n" + "=" * 70)
        print("Option-related tables:")
        print("-" * 50)
        
        option_tables = [t for t in tables if any(keyword in t[0].lower() for keyword in ['nifty', 'option', 'call', 'put', 'spot', 'future'])]
        
        for (table_name,) in option_tables:
            print(f"  - {table_name} ({table_info[table_name]['row_count']:,} rows)")
        
        cursor.close()
        conn.close()
        
        return table_info
        
    except Exception as e:
        print(f"Error connecting to remote MySQL: {e}")
        return None

def check_data_sample():
    """Get sample data from key tables"""
    print("\n" + "=" * 70)
    print("Checking sample data from key tables")
    print("=" * 70)
    
    try:
        conn = pymysql.connect(**REMOTE_CONFIG)
        
        # Check nifty_spot table
        tables_to_check = ['nifty_spot', 'nifty_cash', 'nifty_call', 'nifty_put', 'nifty_future']
        
        for table in tables_to_check:
            try:
                query = f"SELECT * FROM {table} LIMIT 5"
                df = pd.read_sql(query, conn)
                
                print(f"\nSample from {table}:")
                print(df)
                
            except Exception as e:
                print(f"\nError reading {table}: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Main function"""
    table_info = check_remote_schema()
    
    if table_info:
        check_data_sample()
        
        # Save schema info
        print("\n" + "=" * 70)
        print("Saving schema information...")
        
        with open('remote_mysql_schema.txt', 'w') as f:
            f.write("Remote MySQL Schema Information\n")
            f.write("=" * 70 + "\n")
            f.write(f"Generated at: {datetime.now()}\n")
            f.write(f"Database: {REMOTE_CONFIG['database']}\n")
            f.write(f"Host: {REMOTE_CONFIG['host']}\n\n")
            
            for table_name, info in table_info.items():
                f.write(f"\nTable: {table_name}\n")
                f.write(f"Row count: {info['row_count']:,}\n")
                f.write("Columns:\n")
                for col in info['columns']:
                    f.write(f"  {col[0]} - {col[1]} {'(NULL)' if col[2] == 'YES' else '(NOT NULL)'}\n")
        
        print("Schema information saved to remote_mysql_schema.txt")

if __name__ == "__main__":
    main() 