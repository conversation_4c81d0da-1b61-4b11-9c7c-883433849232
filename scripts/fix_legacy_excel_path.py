#!/usr/bin/env python3
"""
Fix the strategy Excel file path in the legacy portfolio settings
"""
import pandas as pd
import os

# Change to archive directory
os.chdir('/srv/samba/shared/bt/archive/backtester_stable/BTRUN')

# Path to the portfolio Excel file
portfolio_excel = 'INPUT SHEETS/INPUT PORTFOLIO.xlsx'

# Read the StrategySetting sheet
df = pd.read_excel(portfolio_excel, sheet_name='StrategySetting')

# Update the path for the enabled TBS strategy
# Change from 'test_data/tbs/input_tbs_multi_legs.xlsx' to correct path
df.loc[df['Enabled'] == 'YES', 'StrategyExcelFilePath'] = 'INPUT SHEETS/INPUT TBS MULTI LEGS.xlsx'

# Write back to Excel
with pd.ExcelWriter(portfolio_excel, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
    df.to_excel(writer, sheet_name='StrategySetting', index=False)

print("✅ Updated strategy Excel file path to: INPUT SHEETS/INPUT TBS MULTI LEGS.xlsx") 