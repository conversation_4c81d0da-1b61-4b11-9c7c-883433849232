#!/usr/bin/env python3
"""
Mock HTTP service for legacy backtester testing
This creates a simple HTTP server that mimics the legacy backtesting API
"""

from flask import Flask, request, jsonify
from datetime import datetime
import random
import threading

app = Flask(__name__)

@app.route('/')
def home():
    return "Mock Backtesting Service Running"

@app.route('/backtest/start', methods=['POST'])
def backtest():
    """Mock the backtesting endpoint"""
    
    # Get the request data
    data = request.get_json()
    
    # Generate mock response based on the structure expected by legacy code
    # This is a simplified mock - real service would do actual backtesting
    
    mock_orders = []
    
    # Extract strategy info from request
    strategies = data.get('strategies', [])
    
    for strategy in strategies:
        strategy_name = strategy.get('strategy_name', 'Unknown')
        legs = strategy.get('legs', [])
        
        # Generate some mock trades
        for i, leg in enumerate(legs):
            # Create a mock order
            order = {
                'strategy_name': strategy_name,
                'leg_id': leg.get('leg_id', str(i+1)),
                'symbol': 'NIFTY',
                'strike': leg.get('strike', 22400),
                'option_type': leg.get('option_type', 'CE'),
                'side': leg.get('side', 'BUY'),
                'qty': leg.get('qty', 50),
                'entry_time': 'Mon, 03 Apr 2025 09:16:00 GMT',
                'exit_time': 'Mon, 03 Apr 2025 12:00:00 GMT',
                'entry_price': random.uniform(50, 200),
                'exit_price': random.uniform(50, 200),
                'entry_number': 1,
                'expiry': '250409',  # YYMMDD format
                'reason': 'TIME_EXIT',
                'max_profit': random.uniform(0, 5000),
                'max_loss': random.uniform(-5000, 0),
                'strategy_entry_number': 1,
                'stop_loss_entry_number': 0,
                'take_profit_entry_number': 0,
                'index_entry_price': 22400.0,
                'index_exit_price': 22450.0
            }
            mock_orders.append(order)
    
    # Create strategy profits/losses structure
    strategy_profits = {
        '250403': {str(t): random.uniform(0, 1000) for t in range(33300, 54000, 300)}
    }
    
    strategy_losses = {
        '250403': {str(t): random.uniform(-1000, 0) for t in range(33300, 54000, 300)}
    }
    
    response = {
        'strategies': {
            'orders': mock_orders,
            'strategy_profits': strategy_profits,
            'strategy_losses': strategy_losses
        }
    }
    
    return jsonify(response)

def run_server(port):
    """Run the Flask server"""
    app.run(host='127.0.0.1', port=port, debug=False)

def main():
    print("Mock Legacy Backtesting Service")
    print("="*50)
    
    # Start two servers on different ports
    print("Starting mock services...")
    
    # Start tick data service on port 5000
    tick_thread = threading.Thread(target=run_server, args=(5000,))
    tick_thread.daemon = True
    tick_thread.start()
    print("✓ Tick data service started on http://127.0.0.1:5000")
    
    # Start minute data service on port 5001
    minute_thread = threading.Thread(target=run_server, args=(5001,))
    minute_thread.daemon = True
    minute_thread.start()
    print("✓ Minute data service started on http://127.0.0.1:5001")
    
    print("\nMock services are running!")
    print("Press Ctrl+C to stop")
    
    try:
        # Keep the main thread alive
        while True:
            threading.Event().wait(1)
    except KeyboardInterrupt:
        print("\nShutting down mock services...")

if __name__ == "__main__":
    main() 