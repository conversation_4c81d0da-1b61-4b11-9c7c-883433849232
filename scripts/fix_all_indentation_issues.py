#!/usr/bin/env python3
"""Fix all indentation issues in heavydb_trade_processing.py"""

# Read the file
with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

# Fix the indentation for all problematic lines
fixes = {
    # Line numbers are 1-indexed, but array is 0-indexed
    339: 20,  # logger.info line should have 20 spaces
    341: 20,  # logger.info line should have 20 spaces
    # Also remove the extra indentation from earlier debug lines
    325: 20,  # logger.info line
    328: 20,  # logger.info line
}

for line_num, spaces in fixes.items():
    idx = line_num - 1  # Convert to 0-indexed
    if idx < len(lines):
        # Get the line content without leading spaces
        content = lines[idx].lstrip()
        # Reconstruct with correct indentation
        lines[idx] = ' ' * spaces + content

# Write the fixed file
with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'w') as f:
    f.writelines(lines)

print('Fixed all indentation issues') 