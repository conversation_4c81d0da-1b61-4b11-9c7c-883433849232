#!/usr/bin/env python3
"""
Verify test data structure is complete and ready for Phase 2.D testing.
"""

import os
import openpyxl
from datetime import datetime

def verify_portfolio_file(file_path, expected_start, expected_end):
    """Verify a portfolio file has correct dates and structure."""
    try:
        wb = openpyxl.load_workbook(file_path)
        issues = []
        
        # Check PortfolioSetting sheet
        if 'PortfolioSetting' not in wb.sheetnames:
            issues.append("Missing PortfolioSetting sheet")
        else:
            ws = wb['PortfolioSetting']
            # Check for dates in enabled rows
            for row in ws.iter_rows(min_row=2):
                if row[0].value == 'YES' or (len(row) > 3 and row[3].value == 'YES'):
                    # Find date columns
                    header_row = list(ws.iter_rows(min_row=1, max_row=1))[0]
                    for idx, cell in enumerate(header_row):
                        if cell.value == 'StartDate' and idx < len(row) - 1:
                            start_date = ws.cell(row=row[0].row, column=idx + 1).value
                            if start_date != expected_start:
                                issues.append(f"StartDate is '{start_date}', expected '{expected_start}'")
                        elif cell.value == 'EndDate' and idx < len(row) - 1:
                            end_date = ws.cell(row=row[0].row, column=idx + 1).value
                            if end_date != expected_end:
                                issues.append(f"EndDate is '{end_date}', expected '{expected_end}'")
        
        # Check StrategySetting sheet
        if 'StrategySetting' not in wb.sheetnames:
            issues.append("Missing StrategySetting sheet")
        else:
            ws = wb['StrategySetting']
            # Check if strategy file is referenced
            strategy_file_found = False
            for row in ws.iter_rows(min_row=2):
                if row[0].value == 'YES':  # Enabled
                    if len(row) > 3 and row[3].value:  # StrategyExcelFilePath
                        strategy_file_found = True
                        # Check if file exists
                        strategy_path = row[3].value
                        full_path = os.path.join(os.path.dirname(file_path), os.path.basename(strategy_path))
                        if not os.path.exists(full_path):
                            issues.append(f"Referenced strategy file not found: {strategy_path}")
            if not strategy_file_found:
                issues.append("No strategy file referenced")
        
        return issues
    except Exception as e:
        return [f"Error reading file: {str(e)}"]

def verify_tv_file(file_path, expected_start, expected_end):
    """Verify a TV file has correct dates and signal file."""
    try:
        wb = openpyxl.load_workbook(file_path)
        issues = []
        
        # Check Setting sheet
        if 'Setting' not in wb.sheetnames:
            issues.append("Missing Setting sheet")
        else:
            ws = wb['Setting']
            # Check for required fields
            signal_file_found = False
            for row in ws.iter_rows(min_row=2):
                if len(row) > 1 and row[1].value == 'YES':  # Enabled
                    header_row = list(ws.iter_rows(min_row=1, max_row=1))[0]
                    for idx, cell in enumerate(header_row):
                        if cell.value == 'SignalFilePath' and idx < len(row) - 1:
                            signal_path = ws.cell(row=row[0].row, column=idx + 1).value
                            if signal_path:
                                signal_file_found = True
                                # Check if signal file exists
                                full_path = os.path.join(os.path.dirname(file_path), os.path.basename(signal_path))
                                if not os.path.exists(full_path):
                                    issues.append(f"Signal file not found: {signal_path}")
                        elif cell.value == 'StartDate' and idx < len(row) - 1:
                            start_date = ws.cell(row=row[0].row, column=idx + 1).value
                            if start_date != expected_start:
                                issues.append(f"StartDate is '{start_date}', expected '{expected_start}'")
                        elif cell.value == 'EndDate' and idx < len(row) - 1:
                            end_date = ws.cell(row=row[0].row, column=idx + 1).value
                            if end_date != expected_end:
                                issues.append(f"EndDate is '{end_date}', expected '{expected_end}'")
            
            if not signal_file_found:
                issues.append("No signal file referenced")
        
        return issues
    except Exception as e:
        return [f"Error reading file: {str(e)}"]

def verify_test_data():
    """Verify all test data files are properly set up."""
    print("Verifying Phase 2.D Test Data Structure...\n")
    
    strategies = ['tbs', 'orb', 'oi', 'indicator', 'tv']
    all_good = True
    
    for strategy in strategies:
        print(f"\n{strategy.upper()} Strategy:")
        print("-" * 40)
        
        base_dir = f"test_data/{strategy}"
        if not os.path.exists(base_dir):
            print(f"  ❌ Directory not found: {base_dir}")
            all_good = False
            continue
        
        # Check for required files
        if strategy == 'tv':
            # TV has different structure
            required_files = {
                'input_tv.xlsx': 'Base TV configuration',
                'input_tv_1day.xlsx': '1-day TV configuration',
                'input_tv_30day.xlsx': '30-day TV configuration',
                'signals_1day.xlsx': '1-day signals',
                'signals_30day.xlsx': '30-day signals'
            }
            
            for file, desc in required_files.items():
                file_path = os.path.join(base_dir, file)
                if os.path.exists(file_path):
                    print(f"  ✓ {file} - {desc}")
                    
                    # Verify TV files
                    if file == 'input_tv_1day.xlsx':
                        issues = verify_tv_file(file_path, "03_01_2024", "03_01_2024")
                        if issues:
                            for issue in issues:
                                print(f"    ⚠️  {issue}")
                            all_good = False
                    elif file == 'input_tv_30day.xlsx':
                        issues = verify_tv_file(file_path, "01_01_2024", "31_01_2024")
                        if issues:
                            for issue in issues:
                                print(f"    ⚠️  {issue}")
                            all_good = False
                else:
                    print(f"  ❌ Missing: {file} - {desc}")
                    all_good = False
        else:
            # Other strategies
            required_files = {
                f'input_portfolio_{strategy}.xlsx': 'Base portfolio',
                f'input_portfolio_{strategy}_1day.xlsx': '1-day portfolio',
                f'input_portfolio_{strategy}_30day.xlsx': '30-day portfolio',
                f'input_{strategy}_strategy.xlsx': 'Strategy configuration'
            }
            
            # Special case for TBS
            if strategy == 'tbs':
                required_files['input_tbs_multi_legs.xlsx'] = 'Multi-leg strategy'
                del required_files['input_tbs_strategy.xlsx']
            
            for file, desc in required_files.items():
                file_path = os.path.join(base_dir, file)
                if os.path.exists(file_path):
                    print(f"  ✓ {file} - {desc}")
                    
                    # Verify portfolio files
                    if '1day' in file:
                        issues = verify_portfolio_file(file_path, "03_01_2024", "03_01_2024")
                        if issues:
                            for issue in issues:
                                print(f"    ⚠️  {issue}")
                            all_good = False
                    elif '30day' in file:
                        issues = verify_portfolio_file(file_path, "01_01_2024", "31_01_2024")
                        if issues:
                            for issue in issues:
                                print(f"    ⚠️  {issue}")
                            all_good = False
                else:
                    print(f"  ❌ Missing: {file} - {desc}")
                    all_good = False
    
    print("\n" + "=" * 60)
    if all_good:
        print("✅ All test data files are properly set up and ready for Phase 2.D testing!")
    else:
        print("❌ Some issues found. Please fix them before running tests.")
    print("=" * 60)

if __name__ == '__main__':
    verify_test_data() 