#!/usr/bin/env python3
"""
Direct comparison between MySQL and HeavyDB data
Works without importing backtester modules
"""

import mysql.connector as mysql
from datetime import datetime
import heavydb
import pandas as pd
import os

def get_heavydb_connection():
    """Create a direct HeavyDB connection"""
    return heavydb.connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )

def find_common_test_date():
    """Find a date with data in both databases"""
    print("="*60)
    print("Finding Common Test Date")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Connect to MySQL
    print("\n1. Checking MySQL...")
    mydb = mysql.connect(
        host="************",
        user="mahesh", 
        password="mahesh_123",
        database="historicaldb"
    )
    cursor = mydb.cursor()
    
    # Get a recent date with good data from MySQL
    query = """
        SELECT 
            date,
            COUNT(DISTINCT time) as ticks
        FROM nifty_cash
        WHERE date >= 240401 AND date < 250101
        GROUP BY date
        HAVING ticks > 300
        ORDER BY date DESC
        LIMIT 10
    """
    cursor.execute(query)
    mysql_dates = cursor.fetchall()
    
    print(f"  Found {len(mysql_dates)} good dates in MySQL")
    
    # Connect to HeavyDB
    print("\n2. Checking HeavyDB...")
    conn = get_heavydb_connection()
    
    # Check date range in HeavyDB
    query = """
        SELECT 
            MIN(trade_date) as min_date,
            MAX(trade_date) as max_date,
            COUNT(DISTINCT trade_date) as total_dates
        FROM nifty_option_chain
    """
    result = conn.execute(query).fetchone()
    print(f"  HeavyDB range: {result[0]} to {result[1]} ({result[2]} dates)")
    
    # Find matching date
    print("\n3. Finding matching date...")
    
    for mysql_date, ticks in mysql_dates:
        # Convert MySQL date format to HeavyDB format
        date_str = str(mysql_date)
        formatted_date = f"20{date_str[:2]}-{date_str[2:4]}-{date_str[4:6]}"
        
        # Check if this date exists in HeavyDB
        query = f"""
            SELECT COUNT(*) as row_count
            FROM nifty_option_chain
            WHERE trade_date = DATE '{formatted_date}'
        """
        result = conn.execute(query).fetchone()
        
        if result[0] > 0:
            print(f"\n✓ Found common date: {formatted_date}")
            print(f"  MySQL: {mysql_date} ({ticks} ticks)")
            print(f"  HeavyDB: {result[0]:,} rows")
            
            cursor.close()
            mydb.close()
            conn.close()
            
            return {
                'mysql': mysql_date,
                'heavydb': formatted_date,
                'excel': f"{date_str[4:6]}_{date_str[2:4]}_20{date_str[:2]}"
            }
    
    print("\n✗ No common dates found")
    return None

def run_comparison_test(test_date):
    """Run a simple comparison test for the given date"""
    print("\n" + "="*60)
    print("Running Comparison Test")
    print("="*60)
    
    mysql_date = test_date['mysql']
    heavydb_date = test_date['heavydb']
    
    # Connect to MySQL
    mydb = mysql.connect(
        host="************",
        user="mahesh", 
        password="mahesh_123",
        database="historicaldb"
    )
    cursor = mydb.cursor()
    
    # Get ATM strike from MySQL at 9:16 AM
    print("\n1. MySQL Data (Legacy approach):")
    
    # Get underlying price at 9:16
    query = f"""
        SELECT close/100.0 as price, time
        FROM nifty_cash
        WHERE date = {mysql_date}
        AND time >= 33360  -- 9:16:00 in seconds
        ORDER BY time ASC
        LIMIT 1
    """
    cursor.execute(query)
    result = cursor.fetchone()
    
    if result:
        underlying_price = result[0]
        atm_strike = round(underlying_price / 50) * 50
        print(f"  Underlying at 9:16: {underlying_price:.2f}")
        print(f"  ATM Strike: {atm_strike}")
        
        # Get option prices
        for option_type in ['call', 'put']:
            query = f"""
                SELECT close/100.0 as price
                FROM nifty_{option_type}
                WHERE date = {mysql_date}
                AND strike = {atm_strike}
                AND time >= 33360
                ORDER BY time ASC
                LIMIT 1
            """
            cursor.execute(query)
            result = cursor.fetchone()
            if result:
                print(f"  ATM {option_type.upper()} price: {result[0]:.2f}")
    
    cursor.close()
    mydb.close()
    
    # Connect to HeavyDB
    print("\n2. HeavyDB Data (New approach):")
    conn = get_heavydb_connection()
    
    query = f"""
        SELECT 
            trade_time,
            spot,
            atm_strike,
            ce_close,
            pe_close
        FROM nifty_option_chain
        WHERE trade_date = DATE '{heavydb_date}'
        AND trade_time = TIME '09:16:00'
        AND strike = atm_strike
        LIMIT 1
    """
    result = conn.execute(query).fetchone()
    
    if result:
        print(f"  Underlying at 9:16: {result[1]:.2f}")
        print(f"  ATM Strike: {result[2]}")
        print(f"  ATM CALL price: {result[3]:.2f}")
        print(f"  ATM PUT price: {result[4]:.2f}")
    
    conn.close()
    
    print("\n✓ Data comparison complete")
    print("  Note: Prices should match between MySQL and HeavyDB")

def main():
    # Find a common test date
    test_date = find_common_test_date()
    
    if test_date:
        # Run comparison
        run_comparison_test(test_date)
        
        print("\n" + "="*60)
        print("Next Steps:")
        print("="*60)
        print(f"1. Create/update input Excel with date: {test_date['excel']}")
        print("2. Update legacy MySQL patch to use this date")
        print("3. Run both backtesters:")
        print("   - Legacy: python3 scripts/fix_legacy_with_mysql.py")
        print("   - New: python3 -m bt.backtester_stable.BTRUN.BTRunPortfolio_GPU --legacy-excel")

if __name__ == "__main__":
    main() 