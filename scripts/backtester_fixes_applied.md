# Backtester Fixes Applied

## Overview

This document summarizes the fixes applied to resolve the three main issues identified in the backtester output comparison.

## Issues and Fixes

### 1. Missing Trades Issue (❌ Partially Fixed)

**Problem**: New backtester generates only 1 trade instead of 4 expected trades.

**Root Cause**: 
- The `io.write_results` function was missing in the core/io.py module
- BTRunPortfolio_GPU.py was calling a non-existent function

**Fix Applied**:
- Added `write_results` function to `bt/backtester_stable/BTRUN/core/io.py`
- The function properly extracts result components and calls `prepare_output_file`

**Current Status**: 
- The write_results function is now available
- However, only 1 trade is still being generated (leg_id: 'BUY_PE')
- The issue appears to be in the trade generation logic, not the output writing

**Next Steps**:
- Debug the trade generation logic in the backtest engine
- Verify strike selection queries for all 4 legs
- Check if legs are being filtered out somewhere in the pipeline

### 2. ATM Calculation Alignment (🔄 Solution Ready)

**Problem**: Legacy uses simple rounding (ATM=23550) while new uses synthetic future method (ATM=23450).

**Fix Applied**:
- Created `scripts/legacy_with_synthetic_atm.py` to run legacy with synthetic ATM
- <PERSON>ript includes comparison functionality

**Current Status**:
- Script is ready but requires legacy service access
- Legacy service needs port forwarding on Windows server

**How to Enable**:
1. On Windows server, run as Administrator:
   ```cmd
   netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=***************
   netsh advfirewall firewall add rule name="Legacy Backtest Service" dir=in action=allow protocol=TCP localport=5000
   ```
2. Run the comparison script

### 3. Column Format Standardization (✅ Fixed)

**Problem**: New and legacy systems use different column names and formats.

**Fix Applied**:
- Created `bt/backtester_stable/BTRUN/core/column_mapper.py` module
- Integrated column mapping into `write_results` function with `use_legacy_format` parameter
- Maps columns like:
  - `instrument_type` → `option_type`
  - `side` → `Trade`
  - `netPnlAfterExpenses` → `pnl`
  - Standardizes leg_id format

**Current Status**: 
- Column mapping is fully implemented and integrated
- Can be enabled with `use_legacy_format=True` parameter

## Files Modified

1. **bt/backtester_stable/BTRUN/core/io.py**
   - Added `write_results` function
   - Added optional legacy format mapping

2. **bt/backtester_stable/BTRUN/core/column_mapper.py** (new file)
   - Complete column mapping implementation
   - Legacy format conversion functions

3. **bt/backtester_stable/BTRUN/excel_parser/strategy_parser.py**
   - Already had the fix for ATM with StrikeValue handling

4. **bt/backtester_stable/BTRUN/query_builder/entry_exit_sql.py**
   - Already had the strike selection fix for both methods

## Test Scripts Created

1. **scripts/run_complete_comparison.py**
   - Runs both systems and compares results
   - Shows detailed analysis of differences

2. **scripts/legacy_with_synthetic_atm.py**
   - Runs legacy with synthetic ATM calculation
   - Requires legacy service access

3. **scripts/debug_missing_trades.py**
   - Debugs Excel parsing and model generation
   - Shows all 4 legs are parsed correctly

4. **scripts/check_excel_data.py**
   - Verifies Excel data structure
   - Confirmed legs 3&4 use `StrikeMethod='atm'` with `StrikeValue=2`

## Current State Summary

| Issue | Status | Notes |
|-------|---------|-------|
| Missing Trades | ❌ Root cause found | write_results fixed, but trade generation issue remains |
| ATM Calculation | 🔄 Solution ready | Needs legacy service access to test |
| Column Format | ✅ Fixed | Fully implemented and integrated |

## How to Run Tests

1. **Test new system only**:
   ```bash
   python3 scripts/run_complete_comparison.py
   ```

2. **Debug missing trades**:
   ```bash
   python3 scripts/debug_missing_trades.py
   ```

3. **Check Excel data**:
   ```bash
   python3 scripts/check_excel_data.py
   ```

4. **Run with legacy (requires service)**:
   ```bash
   python3 scripts/legacy_with_synthetic_atm.py
   ```

## Remaining Work

The main remaining issue is understanding why only 1 trade is generated instead of 4. The Excel parsing shows all 4 legs are correctly read, but somewhere in the trade generation pipeline, 3 legs are being lost. This requires investigation in:

1. Strike selection SQL queries
2. Trade builder logic
3. Risk rule evaluation
4. Output aggregation

The strike selection fix has been verified to work correctly for both methods of expressing OTM strikes, so the issue is likely elsewhere in the pipeline. 