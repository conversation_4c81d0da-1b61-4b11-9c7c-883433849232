#!/usr/bin/env python3
"""
Verify that HeavyDB ATM is pre-computed and check the actual data quality issue
"""

try:
    from pyheavydb import connect as heavydb_connect
except ImportError:
    from heavydb import connect as heavydb_connect

def check_heavydb_atm_logic(date_str):
    """Check how ATM is stored in HeavyDB and what data is available"""
    conn = heavydb_connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    # Convert date format
    heavydb_date = f"20{date_str[:2]}-{date_str[2:4]}-{date_str[4:6]}"
    
    print(f"\n{'='*70}")
    print(f"Date: {heavydb_date}")
    print(f"{'='*70}")
    
    # 1. Check what ATM strike is stored
    query1 = f"""
    SELECT DISTINCT
        spot,
        atm_strike,
        expiry_date,
        expiry_bucket
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}'
    AND trade_time = '09:16:00'
    ORDER BY expiry_date
    """
    
    print("\n1. ATM Strikes by Expiry:")
    print("Spot   | ATM Strike | Expiry Date | Bucket")
    print("-" * 50)
    
    results = conn.execute(query1).fetchall()
    for spot, atm, expiry, bucket in results:
        print(f"{spot:6.2f} | {int(atm):10d} | {expiry} | {bucket}")
    
    # 2. Check if we have complete option pairs at ATM
    query2 = f"""
    SELECT 
        strike,
        COUNT(CASE WHEN ce_close > 0 THEN 1 END) as ce_count,
        COUNT(CASE WHEN pe_close > 0 THEN 1 END) as pe_count,
        MIN(ce_close) as ce_price,
        MIN(pe_close) as pe_price,
        expiry_date
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}'
    AND trade_time = '09:16:00'
    AND strike = atm_strike
    GROUP BY strike, expiry_date
    ORDER BY expiry_date
    """
    
    print("\n2. Option Data at ATM Strike:")
    print("Strike | CE Count | PE Count | CE Price | PE Price | Expiry")
    print("-" * 65)
    
    results = conn.execute(query2).fetchall()
    for strike, ce_cnt, pe_cnt, ce_price, pe_price, expiry in results:
        ce_str = f"{ce_price:7.2f}" if ce_price else "    N/A"
        pe_str = f"{pe_price:7.2f}" if pe_price else "    N/A"
        print(f"{int(strike):6d} | {ce_cnt:8d} | {pe_cnt:8d} | {ce_str} | {pe_str} | {expiry}")
    
    # 3. Check strikes around ATM for April 3 expiry
    query3 = f"""
    SELECT 
        strike,
        ce_close,
        pe_close,
        CASE WHEN strike = atm_strike THEN 'ATM' ELSE '' END as is_atm,
        call_strike_type,
        put_strike_type
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}'
    AND trade_time = '09:16:00'
    AND expiry_date = '2025-04-03'
    AND ABS(strike - spot) <= 200
    ORDER BY strike
    """
    
    print("\n3. Strikes Around Spot (April 3 Expiry):")
    print("Strike | CE Price | PE Price | Type | Call Type | Put Type")
    print("-" * 65)
    
    results = conn.execute(query3).fetchall()
    for strike, ce_close, pe_close, is_atm, call_type, put_type in results:
        ce_str = f"{ce_close:7.2f}" if ce_close else "    N/A"
        pe_str = f"{pe_close:7.2f}" if pe_close else "    N/A"
        print(f"{int(strike):6d} | {ce_str} | {pe_str} | {is_atm:4s} | {call_type:9s} | {put_type}")
    
    # 4. Check if the issue is missing data for required strikes
    query4 = f"""
    WITH required_trades AS (
        SELECT atm_strike as strike, 'CE' as opt_type FROM nifty_option_chain 
        WHERE trade_date = '{heavydb_date}' AND trade_time = '09:16:00' AND expiry_date = '2025-04-03'
        UNION ALL
        SELECT atm_strike as strike, 'PE' as opt_type FROM nifty_option_chain 
        WHERE trade_date = '{heavydb_date}' AND trade_time = '09:16:00' AND expiry_date = '2025-04-03'
        UNION ALL
        SELECT atm_strike + 100 as strike, 'CE' as opt_type FROM nifty_option_chain 
        WHERE trade_date = '{heavydb_date}' AND trade_time = '09:16:00' AND expiry_date = '2025-04-03'
        UNION ALL
        SELECT atm_strike - 100 as strike, 'PE' as opt_type FROM nifty_option_chain 
        WHERE trade_date = '{heavydb_date}' AND trade_time = '09:16:00' AND expiry_date = '2025-04-03'
    )
    SELECT DISTINCT
        rt.strike,
        rt.opt_type,
        CASE 
            WHEN rt.opt_type = 'CE' AND oc.ce_close > 0 THEN 'Available'
            WHEN rt.opt_type = 'PE' AND oc.pe_close > 0 THEN 'Available'
            ELSE 'MISSING'
        END as status,
        CASE 
            WHEN rt.opt_type = 'CE' THEN oc.ce_close
            ELSE oc.pe_close
        END as price
    FROM required_trades rt
    LEFT JOIN nifty_option_chain oc 
        ON rt.strike = oc.strike 
        AND oc.trade_date = '{heavydb_date}'
        AND oc.trade_time = '09:16:00'
        AND oc.expiry_date = '2025-04-03'
    ORDER BY rt.strike, rt.opt_type
    """
    
    print("\n4. Required Trades Availability (April 3 Expiry):")
    print("Strike | Type | Status    | Price")
    print("-" * 40)
    
    results = conn.execute(query4).fetchall()
    missing_count = 0
    for strike, opt_type, status, price in results:
        price_str = f"{price:7.2f}" if price else "    N/A"
        print(f"{int(strike):6d} | {opt_type:4s} | {status:9s} | {price_str}")
        if status == 'MISSING':
            missing_count += 1
    
    print(f"\nMissing trades: {missing_count}/4")
    
    conn.close()

def main():
    print("HeavyDB Pre-computed ATM Verification")
    print("="*70)
    print("Checking if the issue is with pre-computed ATM values")
    print("or missing option data for the required strikes")
    
    dates = ['250401', '250402', '250403']
    
    for date in dates:
        check_heavydb_atm_logic(date)
    
    print("\n" + "="*70)
    print("FINDINGS:")
    print("1. HeavyDB has pre-computed ATM strikes loaded")
    print("2. The ATM calculation appears correct")
    print("3. The issue is likely missing option data for some strikes")
    print("4. This explains why trades can't be generated")
    print("\nSOLUTION:")
    print("Need to ensure complete option chain data is loaded for all")
    print("required strikes, especially for near expiries like April 3")

if __name__ == "__main__":
    main() 