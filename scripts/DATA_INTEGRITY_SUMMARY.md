# Data Integrity Check Summary

## Overview

We've created comprehensive scripts to check data integrity between:
- **Legacy System**: MySQL database at ************ (historicaldb)
- **New System**: HeavyDB at 127.0.0.1:6274 (heavyai)

## Key Scripts Created

### 1. **data_integrity_check_fixed.py**
Comprehensive comparison tool that:
- Connects to both databases
- Compares table structures
- Checks date ranges
- Compares row counts by date
- Samples actual data values
- Performs data quality checks

### 2. **verify_test_data.py**
Focused on April 2025 test period:
- Checks data availability for backtesting dates
- Verifies trading days coverage
- Checks expiry dates
- Validates critical data points

### 3. **quick_data_check.py**
Simplified check for rapid verification:
- Quick row counts
- Date range validation
- Price range checks

## Data Structure Comparison

### MySQL (Legacy)
Tables:
- `nifty_call` - Call option data
- `nifty_put` - Put option data  
- `nifty_cash` - Spot/index data
- `nifty_future` - Future data
- `nifty_expiry` - Expiry dates

Key characteristics:
- Time stored as seconds since midnight
- Prices stored as integers (need /100 conversion)
- Separate tables for calls and puts

### HeavyDB (New)
Table:
- `nifty_option_chain` - Consolidated option chain data

Key characteristics:
- Time stored as TIME type
- Prices stored as DOUBLE
- Single table with both calls and puts
- Pre-calculated fields: ATM strike, DTE, strike types

## Critical Fields Mapping

| MySQL Field | HeavyDB Field | Notes |
|------------|---------------|-------|
| date | trade_date | Date of trade |
| time | trade_time | Time conversion needed |
| strike | strike | Strike price |
| close | ce_close/pe_close | Price /100 in MySQL |
| oi | ce_oi/pe_oi | Open interest |
| volume | ce_volume/pe_volume | Volume |
| N/A | atm_strike | Pre-calculated in HeavyDB |
| N/A | dte | Pre-calculated in HeavyDB |

## Data Availability

### MySQL
- Date range: 2018-01-01 to 2025-05-23
- April 2025 data: ✓ Available
- Records for test period: ~497,418 (calls)

### HeavyDB
- Status: Need to verify data loading
- April 2025 data: To be checked
- ETL process may need to be run

## Next Steps

1. **Load April 2025 Data to HeavyDB**:
   - Check if ETL scripts exist
   - Run data loading process
   - Verify data integrity post-load

2. **Run Backtester Comparison**:
   - Once data is loaded in both systems
   - Use `run_parallel_test_external.py`
   - Compare outputs

3. **Fix Any Data Issues**:
   - Address missing dates
   - Resolve data quality issues
   - Ensure consistency

## Commands Reference

```bash
# Check MySQL connection
python3 scripts/test_mysql_connection.py

# Run data integrity check
python3 scripts/data_integrity_check_fixed.py

# Quick data availability check
python3 scripts/quick_data_check.py

# Verify test period data
python3 scripts/verify_test_data.py
```

## Known Issues

1. MySQL time format needs conversion (seconds → HH:MM:SS)
2. MySQL prices need division by 100
3. HeavyDB may not have April 2025 data loaded yet
4. Some MySQL queries have DISTINCT/ORDER BY compatibility issues

## Data Quality Checks

The scripts check for:
- NULL values in critical fields
- Zero or negative prices
- Missing option symbols
- Data completeness by date
- Consistent strike ranges 