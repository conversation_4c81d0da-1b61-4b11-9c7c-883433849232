#!/usr/bin/env python3
"""
Create test dataset in HeavyDB for development
Uses 1 day of data (April 1, 2024) for fast execution
"""
import sys
import time
from datetime import datetime
from heavydb import connect

def create_test_dataset():
    """Extract 1 day of data for testing"""
    
    print("=" * 80)
    print("CREATING TEST DATASET IN HEAVYDB")
    print("=" * 80)
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Connect to HeavyDB
        print("Connecting to HeavyDB...")
        conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai'
        )
        print("✓ Connected successfully")
        
        # Check if source table exists
        print("\nChecking source table...")
        cursor = conn.execute("SELECT COUNT(*) FROM nifty_option_chain LIMIT 1")
        total_rows = cursor.fetchone()
        print(f"✓ Source table exists with data")
        
        # Get count for test date
        print("\nChecking data for test date (2024-04-01)...")
        cursor = conn.execute("""
            SELECT COUNT(*) 
            FROM nifty_option_chain 
            WHERE trade_date = DATE '2024-04-01'
        """)
        test_date_rows = cursor.fetchone()[0]
        print(f"✓ Found {test_date_rows:,} rows for 2024-04-01")
        
        if test_date_rows == 0:
            print("\n⚠️  WARNING: No data found for 2024-04-01")
            print("Checking available dates...")
            cursor = conn.execute("""
                SELECT trade_date, COUNT(*) as row_count
                FROM nifty_option_chain
                GROUP BY trade_date
                ORDER BY trade_date DESC
                LIMIT 10
            """)
            print("\nAvailable dates:")
            for row in cursor.fetchall():
                print(f"  {row[0]}: {row[1]:,} rows")
            
            # Use the most recent date with data
            cursor = conn.execute("""
                SELECT trade_date, COUNT(*) as row_count
                FROM nifty_option_chain
                GROUP BY trade_date
                ORDER BY row_count DESC
                LIMIT 1
            """)
            best_date, best_count = cursor.fetchone()
            print(f"\nUsing {best_date} with {best_count:,} rows instead")
            test_date = best_date
        else:
            test_date = '2024-04-01'
        
        # Drop test table if exists
        print("\nDropping existing test table if exists...")
        try:
            conn.execute("DROP TABLE IF EXISTS nifty_option_chain_test")
            print("✓ Dropped existing test table")
        except:
            print("✓ No existing test table to drop")
        
        # Create test table with 1 day of data
        print(f"\nCreating test table with data from {test_date}...")
        start_time = time.time()
        
        create_query = f"""
        CREATE TABLE nifty_option_chain_test AS 
        SELECT * FROM nifty_option_chain 
        WHERE trade_date = DATE '{test_date}'
        """
        
        conn.execute(create_query)
        create_time = time.time() - start_time
        print(f"✓ Test table created in {create_time:.2f} seconds")
        
        # Create indexes for performance
        print("\nCreating indexes on test table...")
        indexes = [
            ("idx_test_date_time", "trade_date, trade_time"),
            ("idx_test_strike", "strike, trade_date"),
            ("idx_test_expiry", "expiry_date, strike"),
            ("idx_test_spot", "trade_date, spot")
        ]
        
        for idx_name, idx_cols in indexes:
            try:
                start_time = time.time()
                conn.execute(f"CREATE INDEX {idx_name} ON nifty_option_chain_test({idx_cols})")
                idx_time = time.time() - start_time
                print(f"✓ Created {idx_name} in {idx_time:.2f} seconds")
            except Exception as e:
                print(f"⚠️  Warning: Could not create {idx_name}: {str(e)}")
        
        # Verify final data
        print("\nVerifying test dataset...")
        cursor = conn.execute("SELECT COUNT(*) FROM nifty_option_chain_test")
        final_count = cursor.fetchone()[0]
        
        # Get sample data
        cursor = conn.execute("""
            SELECT 
                MIN(trade_time) as start_time,
                MAX(trade_time) as end_time,
                COUNT(DISTINCT strike) as unique_strikes,
                COUNT(DISTINCT expiry_date) as unique_expiries,
                MIN(spot) as min_spot,
                MAX(spot) as max_spot
            FROM nifty_option_chain_test
        """)
        stats = cursor.fetchone()
        
        print("\n" + "=" * 80)
        print("TEST DATASET CREATED SUCCESSFULLY")
        print("=" * 80)
        print(f"Table name: nifty_option_chain_test")
        print(f"Test date: {test_date}")
        print(f"Total rows: {final_count:,}")
        print(f"Time range: {stats[0]} to {stats[1]}")
        print(f"Unique strikes: {stats[2]}")
        print(f"Unique expiries: {stats[3]}")
        print(f"Spot range: {stats[4]} to {stats[5]}")
        print(f"\nEnd time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        print("\nTroubleshooting:")
        print("1. Ensure HeavyDB is running")
        print("2. Check connection parameters")
        print("3. Verify source table 'nifty_option_chain' exists")
        return False

if __name__ == "__main__":
    success = create_test_dataset()
    sys.exit(0 if success else 1)