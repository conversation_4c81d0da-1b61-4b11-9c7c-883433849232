#!/usr/bin/env python3
"""
Setup network connectivity for legacy backtester
"""

import os
import shutil
import subprocess

def create_updated_config():
    """Create an updated config.py with accessible endpoints"""
    
    config_content = '''
TAXES = 0
LOT_SIZE = {}
VALID_TRADING_WEEKDAYS = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
]
VALID_MONTHS = [
    'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'
]
TRAIL_COST_RE_ENTRY = False
FIXED_VALUE_FOR_DYNAMIC_FACTOR = {}

# Updated to use localhost with SSH tunnels
BT_URII = {
    "tick": "http://127.0.0.1:5000/backtest/start",    # SSH tunnel to ***************:5000
    "minute": "http://127.0.0.1:5001/backtest/start"   # SSH tunnel to ***************:5000
}

VERSION_NO = {
    "TV": "29 Mar 25 (1.0.0)", 
    "PORTFOLIO": "15 Feb 25 (1.0.0)", 
    "FRONTEND": "22 Jan 25 (1.0.0)", 
    "FRONTENDTV": "22 Jan 25 (1.0.0)"
}

INPUT_FILE_FOLDER = "INPUT SHEETS"
PORTFOLIO_FILE_PATH = "INPUT PORTFOLIO.xlsx"
TV_FILE_PATH = "INPUT TV.xlsx"
LOG_FILE_FOLDER = "Logs"
'''
    
    # Backup original config
    original_config = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/config.py"
    backup_config = "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/config.py.bak"
    
    if os.path.exists(original_config) and not os.path.exists(backup_config):
        shutil.copy2(original_config, backup_config)
        print(f"✓ Backed up original config to {backup_config}")
    
    # Write new config
    with open(original_config, 'w') as f:
        f.write(config_content)
    
    print(f"✓ Updated config.py to use localhost endpoints")
    print("\nTo use the legacy backtester, set up SSH tunnels:")
    print("  ssh -L 5000:***************:5000 user@gateway_server")
    print("  ssh -L 5001:***************:5000 user@gateway_server")

def check_mysql_connection():
    """Test MySQL connectivity"""
    print("\nChecking MySQL connectivity...")
    
    try:
        import mysql.connector
        
        conn = mysql.connector.connect(
            host="************",
            user="mahesh",
            password="mahesh_123",
            database="historicaldb",
            connection_timeout=5
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        
        print(f"✓ MySQL connection successful!")
        print(f"  Server version: {version[0]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"✗ MySQL connection failed: {e}")
        print("\nPossible solutions:")
        print("  1. Check if MySQL server allows remote connections")
        print("  2. Verify firewall rules allow port 3306")
        print("  3. Check MySQL user permissions for remote access")
        print("  4. Verify credentials are correct")

def main():
    print("Legacy Backtester Network Setup")
    print("="*50)
    
    # Create updated config
    create_updated_config()
    
    # Test MySQL
    check_mysql_connection()
    
    print("\n" + "="*50)
    print("Setup complete!")
    print("\nNext steps:")
    print("1. Set up SSH tunnels to internal services")
    print("2. Run the legacy backtester")
    print("3. Or use the new HeavyDB backtester which doesn't require external services")

if __name__ == "__main__":
    main() 