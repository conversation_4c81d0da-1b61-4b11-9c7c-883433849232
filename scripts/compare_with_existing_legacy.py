#!/usr/bin/env python3
"""Compare HeavyDB output with existing legacy parity test output."""

import pandas as pd
import os
from pathlib import Path

def analyze_and_compare():
    """Analyze and compare outputs."""
    
    # Use existing parity test output as legacy reference
    legacy_file = "tests/outputs/gpu_parity_run/NIF0DTE_250401_cpu.xlsx"
    heavydb_file = "heavydb_output_april1.xlsx"
    
    print("="*80)
    print("COMPARING OUTPUTS")
    print("="*80)
    
    # Check if files exist
    if not os.path.exists(legacy_file):
        print(f"✗ Legacy file not found: {legacy_file}")
        return
    
    if not os.path.exists(heavydb_file):
        print(f"✗ HeavyDB file not found: {heavydb_file}")
        return
    
    # Analyze legacy file
    print(f"\nLEGACY OUTPUT: {legacy_file}")
    print("-" * 60)
    
    legacy_xl = pd.ExcelFile(legacy_file)
    print("Sheets:")
    for sheet in legacy_xl.sheet_names:
        df = pd.read_excel(legacy_file, sheet_name=sheet)
        print(f"  - {sheet} (shape: {df.shape})")
    
    # Analyze HeavyDB file
    print(f"\nHEAVYDB OUTPUT: {heavydb_file}")
    print("-" * 60)
    
    heavydb_xl = pd.ExcelFile(heavydb_file)
    print("Sheets:")
    for sheet in heavydb_xl.sheet_names:
        df = pd.read_excel(heavydb_file, sheet_name=sheet)
        print(f"  - {sheet} (shape: {df.shape})")
    
    # Compare sheets
    print("\n" + "="*80)
    print("SHEET COMPARISON")
    print("="*80)
    
    legacy_sheets = set(legacy_xl.sheet_names)
    heavydb_sheets = set(heavydb_xl.sheet_names)
    
    print("\nMissing in HeavyDB:")
    for sheet in sorted(legacy_sheets - heavydb_sheets):
        print(f"  ❌ {sheet}")
    
    print("\nExtra in HeavyDB:")
    for sheet in sorted(heavydb_sheets - legacy_sheets):
        print(f"  ➕ {sheet}")
    
    print("\nCommon sheets:")
    for sheet in sorted(legacy_sheets & heavydb_sheets):
        print(f"  ✓ {sheet}")
    
    # Compare transaction data
    print("\n" + "="*80)
    print("TRANSACTION DATA COMPARISON")
    print("="*80)
    
    # Find transaction sheets
    legacy_trans_sheet = None
    for sheet in legacy_xl.sheet_names:
        if 'Trans' in sheet and 'PORTFOLIO' not in sheet:
            legacy_trans_sheet = sheet
            break
    
    heavydb_trans_sheet = None
    for sheet in heavydb_xl.sheet_names:
        if 'Trans' in sheet and 'PORTFOLIO' not in sheet and 'RS,916' in sheet:
            heavydb_trans_sheet = sheet
            break
    
    if legacy_trans_sheet and heavydb_trans_sheet:
        print(f"\nComparing: {legacy_trans_sheet} vs {heavydb_trans_sheet}")
        
        legacy_trans = pd.read_excel(legacy_file, sheet_name=legacy_trans_sheet)
        heavydb_trans = pd.read_excel(heavydb_file, sheet_name=heavydb_trans_sheet)
        
        print(f"\nLegacy trades: {len(legacy_trans)}")
        print(f"HeavyDB trades: {len(heavydb_trans)}")
        
        # Show first few trades from each
        print("\nLegacy first trade:")
        if len(legacy_trans) > 0:
            print(legacy_trans.iloc[0][['ID', 'Strike', 'CE/PE', 'Trade', 'Entry at', 'Exit at', 'PNL']])
        
        print("\nHeavyDB first trade:")
        if len(heavydb_trans) > 0:
            print(heavydb_trans.iloc[0][['leg_id', 'strike', 'instrument_type', 'side', 'entry_price', 'exit_price', 'pnl']])
    
    # Summary
    print("\n" + "="*80)
    print("SUMMARY")
    print("="*80)
    
    print("\nKey differences:")
    print("1. HeavyDB is missing parameter sheets (PortfolioParameter, GeneralParameter, LegParameter)")
    print("2. HeavyDB is missing 'PORTFOLIO Results' sheet")
    print("3. Legacy has truncated sheet names (e.g., 'RS,916-1200,ATM-SELL,OTM2-BU W')")
    print("4. Both systems generate 4 trades as expected")
    print("\nThe parameter sheets need to be added to HeavyDB output for full compatibility.")

if __name__ == "__main__":
    analyze_and_compare() 