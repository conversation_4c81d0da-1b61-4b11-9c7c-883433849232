#!/usr/bin/env python3
"""
Sort the processed April 2025 Nifty data by trade_date, trade_time, and expiry_bucket.
Expiry bucket order: CW, NW, CM, NM
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

# Input and output paths
INPUT_FILE = "/srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_updated.csv"
OUTPUT_FILE = "/srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_sorted.csv"

def main():
    print("="*80)
    print("SORTING APRIL 2025 NIFTY DATA")
    print("="*80)
    print(f"Started at: {datetime.now()}")
    
    # Load the data
    print(f"\nLoading data from {INPUT_FILE}")
    df = pd.read_csv(INPUT_FILE)
    print(f"Loaded {len(df):,} rows")
    
    # Create a custom sort order for expiry_bucket
    expiry_order = {'CW': 1, 'NW': 2, 'CM': 3, 'NM': 4}
    
    # Add a temporary sort column for expiry bucket
    df['expiry_sort'] = df['expiry_bucket'].map(expiry_order)
    
    # Sort by trade_date, trade_time, expiry_bucket order, and then by strike
    print("\nSorting data by:")
    print("  1. trade_date (ascending)")
    print("  2. trade_time (ascending)")
    print("  3. expiry_bucket (CW, NW, CM, NM)")
    print("  4. strike (ascending)")
    
    df_sorted = df.sort_values(
        by=['trade_date', 'trade_time', 'expiry_sort', 'strike'],
        ascending=[True, True, True, True]
    )
    
    # Remove the temporary sort column
    df_sorted = df_sorted.drop('expiry_sort', axis=1)
    
    # Save the sorted data
    print(f"\nSaving sorted data to {OUTPUT_FILE}")
    df_sorted.to_csv(OUTPUT_FILE, index=False)
    
    # Print summary statistics
    print("\n" + "="*80)
    print("SORTING SUMMARY")
    print("="*80)
    
    # Show first few rows of each date
    print("\nFirst few rows by date:")
    for date in df_sorted['trade_date'].unique()[:3]:
        date_data = df_sorted[df_sorted['trade_date'] == date]
        print(f"\nDate: {date}")
        print(f"  Total rows: {len(date_data):,}")
        
        # Show expiry bucket distribution
        bucket_counts = date_data['expiry_bucket'].value_counts().reindex(['CW', 'NW', 'CM', 'NM'], fill_value=0)
        print("  Expiry buckets:")
        for bucket, count in bucket_counts.items():
            print(f"    {bucket}: {count:,}")
        
        # Show first few times
        first_times = date_data['trade_time'].unique()[:5]
        print(f"  First times: {', '.join(first_times)}")
    
    # Verify sorting
    print("\nVerifying sort order...")
    
    # Check if trade_date is sorted
    date_sorted = df_sorted['trade_date'].is_monotonic_increasing
    print(f"  Trade dates sorted: {'✓' if date_sorted else '✗'}")
    
    # Check time sorting within dates
    time_sorted = True
    for date in df_sorted['trade_date'].unique()[:5]:  # Check first 5 dates
        date_data = df_sorted[df_sorted['trade_date'] == date]
        if not date_data['trade_time'].is_monotonic_increasing:
            time_sorted = False
            break
    print(f"  Trade times sorted within dates: {'✓' if time_sorted else '✗'}")
    
    # Check expiry bucket order
    print("\nSample of sorted data (first 10 rows):")
    sample_cols = ['trade_date', 'trade_time', 'expiry_bucket', 'strike', 'spot', 'atm_strike']
    print(df_sorted[sample_cols].head(10).to_string(index=False))
    
    print(f"\nTotal rows in output: {len(df_sorted):,}")
    print(f"Output saved to: {OUTPUT_FILE}")
    print(f"\nCompleted at: {datetime.now()}")
    print("="*80)

if __name__ == "__main__":
    main() 