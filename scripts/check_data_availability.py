#!/usr/bin/env python3
"""
Check data availability in HeavyDB
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bt.backtester_stable.BTRUN.core.heavydb_connection import get_connection, execute_query

def check_data_availability():
    """Check available data years"""
    print("Checking data availability in HeavyDB")
    print("=" * 70)
    
    conn = get_connection()
    
    # Check overall date range
    query = """
    SELECT MIN(trade_date) as min_date, 
           MAX(trade_date) as max_date,
           COUNT(DISTINCT trade_date) as total_days
    FROM nifty_option_chain
    """
    
    try:
        result = execute_query(query, return_gpu_df=False, connection=conn)
        print("\nOverall data range:")
        print(result)
        
        # Check by year
        years = [2023, 2024, 2025]
        for year in years:
            query = f"""
            SELECT COUNT(DISTINCT trade_date) as trading_days
            FROM nifty_option_chain
            WHERE EXTRACT(YEAR FROM trade_date) = {year}
            """
            result = execute_query(query, return_gpu_df=False, connection=conn)
            print(f"\n{year}: {result.iloc[0]['trading_days'] if not result.empty else 0} trading days")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_data_availability() 