#!/usr/bin/env python3
"""Analyze the missing sheets from legacy output."""

import os
import sys
import pandas as pd

# Add project paths
sys.path.append('/srv/samba/shared')

def analyze_legacy_sheets():
    """Analyze what's in the legacy sheets that are missing from HeavyDB."""
    
    legacy_file = "tests/outputs/gpu_parity_run/NIF0DTE_250401_cpu.xlsx"
    
    if not os.path.exists(legacy_file):
        print(f"Legacy file not found: {legacy_file}")
        return
    
    # Missing sheets identified
    missing_sheets = [
        'RS,916-1200,ATM-SELL,OTM2-BU W',
        'RS,916-1200,ATM-SELL,OTM2 ResultsTmp'
    ]
    
    xl = pd.ExcelFile(legacy_file)
    
    print("="*80)
    print("ANALYZING MISSING SHEETS FROM LEGACY OUTPUT")
    print("="*80)
    
    for sheet_name in missing_sheets:
        if sheet_name in xl.sheet_names:
            print(f"\n\nSheet: {sheet_name}")
            print("-"*80)
            
            df = pd.read_excel(legacy_file, sheet_name=sheet_name)
            
            print(f"Shape: {df.shape}")
            print(f"Columns: {list(df.columns)}")
            
            # Show first few rows
            print(f"\nFirst 5 rows:")
            print(df.head())
            
            # Check if it's a duplicate of another sheet
            if sheet_name == 'RS,916-1200,ATM-SELL,OTM2-BU W':
                # This looks like a truncated strategy name
                # Compare with other transaction sheets
                trans_sheets = [s for s in xl.sheet_names if 'Trans' in s and 'PORTFOLIO' not in s]
                if trans_sheets:
                    trans_df = pd.read_excel(legacy_file, sheet_name=trans_sheets[0])
                    if df.shape == trans_df.shape and list(df.columns) == list(trans_df.columns):
                        print("\n⚠️  This appears to be a duplicate of the Trans sheet!")
                        # Check if content is identical
                        if df.equals(trans_df):
                            print("✓ Content is IDENTICAL to Trans sheet - likely a naming issue")
                        else:
                            print("⚠️  Content differs from Trans sheet")
            
            elif 'ResultsTmp' in sheet_name:
                print("\n⚠️  This appears to be a temporary results sheet")
                # Analyze what type of results it contains
                if 'Type' in df.columns:
                    print(f"Result types: {df['Type'].unique()}")
                if 'Value' in df.columns:
                    print(f"Value range: {df['Value'].min()} to {df['Value'].max()}")
    
    # Also check strategy sheets in HeavyDB output
    print("\n\n" + "="*80)
    print("COMPARING STRATEGY SHEET NAMES")
    print("="*80)
    
    # Load HeavyDB output to compare
    heavydb_file = "heavydb_comprehensive_output.xlsx"
    if os.path.exists(heavydb_file):
        hdb_xl = pd.ExcelFile(heavydb_file)
        
        legacy_strategy_sheets = [s for s in xl.sheet_names if any(x in s for x in ['ATM', 'OTM', 'ITM'])]
        heavydb_strategy_sheets = [s for s in hdb_xl.sheet_names if any(x in s for x in ['ATM', 'OTM', 'ITM'])]
        
        print("\nLegacy strategy sheets:")
        for sheet in sorted(legacy_strategy_sheets):
            print(f"  - {sheet} (len={len(sheet)})")
        
        print("\nHeavyDB strategy sheets:")
        for sheet in sorted(heavydb_strategy_sheets):
            print(f"  - {sheet} (len={len(sheet)})")
        
        # Check for truncation patterns
        print("\n\nTruncation Analysis:")
        print("Legacy truncates at 31 chars for sheet names")
        print("'RS,916-1200,ATM-SELL,OTM2-BU W' is exactly 31 chars")
        print("Full name would be: 'RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL'")
        print("\nHeavyDB uses: 'RS,916-1200,ATM-Sell,OTM2-Buy w' (31 chars)")
        print("Note the case differences: SELL→Sell, BU W→Buy w")

def main():
    """Main execution."""
    analyze_legacy_sheets()

if __name__ == "__main__":
    main() 