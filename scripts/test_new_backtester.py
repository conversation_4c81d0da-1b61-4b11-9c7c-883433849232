#!/usr/bin/env python3
"""
Test the new refactored backtester independently
"""

import os
import sys
import subprocess
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def test_new_backtester():
    """Test the new backtester with proper setup"""
    print("="*60)
    print("Testing New Refactored Backtester")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Configuration
    portfolio_excel = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    output_path = "test_new_output.xlsx"
    
    # Check input file
    if not os.path.exists(portfolio_excel):
        print(f"✗ Portfolio file not found: {portfolio_excel}")
        return False
    
    print(f"\n✓ Input file found: {portfolio_excel}")
    
    # Method 1: Run as subprocess with PYTHONPATH
    print("\nRunning new backtester as subprocess...")
    
    env = os.environ.copy()
    env['PYTHONPATH'] = os.path.abspath('.')
    
    cmd = [
        sys.executable,
        "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", portfolio_excel,
        "--output-path", output_path,
        "--start-date", "20250401",
        "--end-date", "20250402"  # Just 2 days for quick test
    ]
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        print(f"\nReturn code: {result.returncode}")
        
        if result.stdout:
            print("\nSTDOUT:")
            print("-" * 40)
            print(result.stdout[-1000:])  # Last 1000 chars
            
        if result.stderr:
            print("\nSTDERR:")
            print("-" * 40)
            print(result.stderr[-1000:])  # Last 1000 chars
            
        # Check if output was created
        if os.path.exists(output_path):
            print(f"\n✓ Output created: {output_path}")
            file_size = os.path.getsize(output_path)
            print(f"  File size: {file_size:,} bytes")
            return True
        else:
            print(f"\n✗ Output not created: {output_path}")
            
            # Check alternative locations
            alt_paths = [
                "bt/backtester_stable/BTRUN/output/test_new_output.xlsx",
                "output/test_new_output.xlsx"
            ]
            
            for path in alt_paths:
                if os.path.exists(path):
                    print(f"  Found at: {path}")
                    return True
                    
            return False
            
    except subprocess.TimeoutExpired:
        print("\n✗ Backtester timed out")
        return False
    except Exception as e:
        print(f"\n✗ Error: {e}")
        return False

def check_heavydb_data():
    """Quick check if HeavyDB has data for the test period"""
    print("\nChecking HeavyDB data availability...")
    
    try:
        from heavydb import connect
    except ImportError:
        import pymapd
        connect = pymapd.connect
    
    try:
        conn = connect(
            host="127.0.0.1",
            port=6274,
            user="admin",
            password="HyperInteractive",
            dbname="heavyai"
        )
        cursor = conn.cursor()
        
        # Check for April 2025 data
        cursor.execute("""
            SELECT COUNT(*), MIN(trade_date), MAX(trade_date)
            FROM nifty_option_chain
            WHERE trade_date >= DATE '2025-04-01'
            AND trade_date <= DATE '2025-04-02'
        """)
        
        result = cursor.fetchone()
        if result and result[0] > 0:
            print(f"  ✓ HeavyDB has {result[0]:,} records for test period")
            print(f"    Date range: {result[1]} to {result[2]}")
        else:
            print("  ✗ No data found in HeavyDB for April 2025")
            print("    The backtester will fail without data")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"  ✗ Error checking HeavyDB: {e}")

if __name__ == "__main__":
    # Check data first
    check_heavydb_data()
    
    # Run test
    success = test_new_backtester()
    
    print("\n" + "="*60)
    if success:
        print("✓ New backtester test PASSED")
    else:
        print("✗ New backtester test FAILED")
        print("\nPossible issues:")
        print("1. HeavyDB may not have data for April 2025")
        print("2. Check if all required Python packages are installed")
        print("3. Verify HeavyDB connection is working")
    print("="*60) 