#!/usr/bin/env python3
"""
Debug input Excel files to understand the configuration
"""

import pandas as pd

portfolio_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
tbs_file = "/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"

print("Debugging Input Files")
print("=" * 60)

# Check PortfolioSetting
try:
    print("\n1. Portfolio Settings:")
    df = pd.read_excel(portfolio_file, sheet_name='PortfolioSetting')
    print(f"   Shape: {df.shape}")
    for idx, row in df.iterrows():
        if str(row.get('Enabled', 'NO')).upper() == 'YES':
            print(f"\n   Portfolio: {row.get('PortfolioName', 'N/A')}")
            print(f"     Start Date: {row.get('StartDate', 'N/A')}")
            print(f"     End Date: {row.get('EndDate', 'N/A')}")
            print(f"     Is Tick BT: {row.get('IsTickBT', 'N/A')}")
except Exception as e:
    print(f"   Error: {e}")

# Check StrategySetting
try:
    print("\n2. Strategy Settings:")
    df = pd.read_excel(portfolio_file, sheet_name='StrategySetting')
    print(f"   Shape: {df.shape}")
    for idx, row in df.iterrows():
        if str(row.get('Enabled', 'NO')).upper() == 'YES':
            print(f"\n   Portfolio: {row.get('PortfolioName', 'N/A')}")
            print(f"     Strategy Type: {row.get('StrategyType', 'N/A')}")
            print(f"     Strategy Excel: {row.get('StrategyExcelFilePath', 'N/A')}")
except Exception as e:
    print(f"   Error: {e}")

# Check TBS GeneralParameter
try:
    print("\n3. TBS General Parameters:")
    df = pd.read_excel(tbs_file, sheet_name='GeneralParameter')
    print(f"   Shape: {df.shape}")
    print(f"   Columns: {list(df.columns)}")
    
    # Show first row details
    if not df.empty:
        row = df.iloc[0]
        print(f"\n   Strategy: {row.get('StrategyName', 'N/A')}")
        print(f"     StartTime: {row.get('StartTime', 'N/A')} (type: {type(row.get('StartTime'))})")
        print(f"     EndTime: {row.get('EndTime', 'N/A')} (type: {type(row.get('EndTime'))})")
        print(f"     LastEntryTime: {row.get('LastEntryTime', 'N/A')} (type: {type(row.get('LastEntryTime'))})")
        print(f"     StrikeSelectionTime: {row.get('StrikeSelectionTime', 'N/A')}")
        print(f"     DTE: {row.get('DTE', 'N/A')}")
        print(f"     Weekdays: {row.get('Weekdays', 'N/A')}")
        
        # Show all columns for debugging
        print(f"\n   All values:")
        for col in df.columns:
            print(f"     {col}: {row.get(col, 'N/A')}")
        
except Exception as e:
    print(f"   Error: {e}")

# Check TBS LegParameter
try:
    print("\n4. TBS Leg Parameters:")
    df = pd.read_excel(tbs_file, sheet_name='LegParameter')
    print(f"   Shape: {df.shape}")
    print(f"   Columns: {list(df.columns)}")
    
    # Show all legs
    for idx, row in df.iterrows():
        print(f"\n   Leg {idx + 1} (ID: {row.get('LegID', 'N/A')}):")
        print(f"     Strategy: {row.get('StrategyName', 'N/A')}")
        print(f"     Instrument: {row.get('Instrument', 'N/A')}")
        print(f"     Transaction: {row.get('Transaction', 'N/A')}")
        print(f"     Strike Method: {row.get('StrikeMethod', 'N/A')}")
        print(f"     Strike Value: {row.get('StrikeValue', 'N/A')}")
        print(f"     StartTime: {row.get('StartTime', 'N/A')} (type: {type(row.get('StartTime'))})")
        print(f"     EndTime: {row.get('EndTime', 'N/A')} (type: {type(row.get('EndTime'))})")
        print(f"     SL: {row.get('SLType', 'N/A')} / {row.get('SLValue', 'N/A')}")
        print(f"     TGT: {row.get('TGTType', 'N/A')} / {row.get('TGTValue', 'N/A')}")
        print(f"     IsIdle: {row.get('IsIdle', 'N/A')}")
        
except Exception as e:
    print(f"   Error: {e}")

print("\n" + "=" * 60) 