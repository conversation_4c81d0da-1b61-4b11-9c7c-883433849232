#!/usr/bin/env python3
"""
Compare TBS output with golden file
"""

import pandas as pd
import numpy as np
import sys

# File paths
output_file = "/srv/samba/shared/tbs_test_output.xlsx"
golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"

def compare_files():
    print("Comparing TBS output with golden file")
    print("=" * 60)
    
    # Read both files
    try:
        output_xl = pd.ExcelFile(output_file, engine='openpyxl')
        golden_xl = pd.ExcelFile(golden_file, engine='openpyxl')
    except Exception as e:
        print(f"Error reading files: {e}")
        return
    
    # Compare sheet names
    print("\n1. Sheet Names:")
    print(f"   Output sheets: {output_xl.sheet_names}")
    print(f"   Golden sheets: {golden_xl.sheet_names}")
    
    # Check common sheets
    common_sheets = set(output_xl.sheet_names) & set(golden_xl.sheet_names)
    print(f"\n   Common sheets: {common_sheets}")
    
    # Compare key sheets
    key_sheets = ['TradeBook', 'Metrics', 'PORTFOLIO Trans']
    
    for sheet in key_sheets:
        print(f"\n2. Comparing {sheet}:")
        
        if sheet not in output_xl.sheet_names:
            print(f"   ❌ {sheet} missing in output")
            continue
            
        if sheet not in golden_xl.sheet_names:
            print(f"   ❌ {sheet} missing in golden file")
            continue
        
        output_df = pd.read_excel(output_file, sheet_name=sheet)
        golden_df = pd.read_excel(golden_file, sheet_name=sheet)
        
        print(f"   Output shape: {output_df.shape}")
        print(f"   Golden shape: {golden_df.shape}")
        
        if sheet == 'TradeBook' and not output_df.empty and not golden_df.empty:
            # Compare key columns
            key_cols = ['entry_date', 'entry_time', 'exit_date', 'exit_time', 
                       'symbol', 'strike', 'instrument_type', 'side', 'pnl']
            
            # Find common columns
            common_cols = [col for col in key_cols if col in output_df.columns and col in golden_df.columns]
            
            if common_cols:
                print(f"\n   Comparing {len(common_cols)} key columns: {common_cols}")
                
                # Show first few rows
                print("\n   Output sample:")
                print(output_df[common_cols].head(3))
                
                print("\n   Golden sample:")
                print(golden_df[common_cols].head(3))
                
                # Compare P&L if available
                if 'pnl' in common_cols:
                    output_pnl = output_df['pnl'].sum()
                    golden_pnl = golden_df['pnl'].sum()
                    print(f"\n   Total P&L comparison:")
                    print(f"     Output: {output_pnl:,.2f}")
                    print(f"     Golden: {golden_pnl:,.2f}")
                    print(f"     Difference: {output_pnl - golden_pnl:,.2f} ({((output_pnl - golden_pnl) / golden_pnl * 100):.2f}%)")
                
                # Compare trade counts
                print(f"\n   Trade count:")
                print(f"     Output: {len(output_df)}")
                print(f"     Golden: {len(golden_df)}")
                
                # Compare date ranges
                if 'entry_date' in common_cols:
                    print(f"\n   Date range:")
                    print(f"     Output: {output_df['entry_date'].min()} to {output_df['entry_date'].max()}")
                    print(f"     Golden: {golden_df['entry_date'].min()} to {golden_df['entry_date'].max()}")
            else:
                print(f"   ❌ No common key columns found")
                print(f"   Output columns: {list(output_df.columns)[:10]}...")
                print(f"   Golden columns: {list(golden_df.columns)[:10]}...")
        
        elif sheet == 'Metrics' and not output_df.empty and not golden_df.empty:
            print("\n   Metrics comparison:")
            # Show key metrics if available
            if 'portfolio' in output_df.columns:
                print(f"   Output metrics sample:")
                print(output_df.head())
            if 'portfolio' in golden_df.columns:
                print(f"   Golden metrics sample:")
                print(golden_df.head())

if __name__ == "__main__":
    compare_files() 