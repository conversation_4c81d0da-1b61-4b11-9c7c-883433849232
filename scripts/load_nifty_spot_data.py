#!/usr/bin/env python3
"""
Load nifty spot OHLC data into HeavyDB
Handles column name mapping from CSV to table
"""

import pandas as pd
from heavydb import connect
from datetime import datetime
import sys
import tempfile
import os

# HeavyDB connection details
HOST = "127.0.0.1"
PORT = 6274
USER = "admin"
PASSWORD = "HyperInteractive"
DATABASE = "heavyai"

# File paths
CSV_FILE = "/srv/samba/shared/market_data/nifty/nifty_cash_merged_rounded_v2.csv"

def main():
    # Connect to HeavyDB
    print("Connecting to HeavyDB...")
    try:
        conn = connect(
            host=HOST,
            port=PORT,
            user=USER,
            password=PASSWORD,
            dbname=DATABASE
        )
        cursor = conn.cursor()
    except Exception as e:
        print(f"Error connecting to HeavyDB: {e}")
        sys.exit(1)
    
    # Create table
    print("Creating nifty_spot table...")
    
    # First drop the table if it exists
    try:
        cursor.execute("DROP TABLE IF EXISTS nifty_spot")
    except Exception as e:
        # Table might not exist, that's ok
        pass
    
    # Then create the new table
    create_table_sql = """
    CREATE TABLE nifty_spot (
        trade_date DATE,
        trade_time TIME,
        symbol TEXT ENCODING DICT,
        "open" DOUBLE,
        "high" DOUBLE,
        "low" DOUBLE,
        "close" DOUBLE
    ) WITH (fragment_size = 32000000)
    """
    
    try:
        cursor.execute(create_table_sql)
    except Exception as e:
        print(f"Error creating table: {e}")
        sys.exit(1)
    
    # Load CSV data
    print(f"Loading data from {CSV_FILE}...")
    try:
        df = pd.read_csv(CSV_FILE)
        print(f"Loaded {len(df)} rows from CSV")
        
        # Rename columns to match table structure
        df.rename(columns={
            'date': 'trade_date',
            'time': 'trade_time'
        }, inplace=True)
        
        # Write to temporary CSV with correct column names
        temp_csv = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        df.to_csv(temp_csv.name, index=False)
        temp_csv.close()
        
        # Use COPY command to load data
        copy_sql = f"COPY nifty_spot FROM '{temp_csv.name}' WITH (header='true', delimiter=',')"
        cursor.execute(copy_sql)
        
        # Clean up temp file
        os.unlink(temp_csv.name)
        
        print("Data load complete!")
        
    except Exception as e:
        print(f"Error loading data: {e}")
        # Try to clean up temp file if it exists
        if 'temp_csv' in locals():
            try:
                os.unlink(temp_csv.name)
            except:
                pass
        sys.exit(1)
    
    # Verify data
    print("\nVerifying data load...")
    try:
        cursor.execute("SELECT COUNT(*) AS total_rows FROM nifty_spot")
        total = cursor.fetchone()[0]
        print(f"Total rows loaded: {total}")
        
        cursor.execute("SELECT MIN(trade_date) AS start_date, MAX(trade_date) AS end_date FROM nifty_spot")
        result = cursor.fetchone()
        print(f"Date range: {result[0]} to {result[1]}")
        
        cursor.execute("SELECT * FROM nifty_spot LIMIT 5")
        print("\nSample data:")
        for row in cursor.fetchall():
            print(row)
        
    except Exception as e:
        print(f"Error verifying data: {e}")
    
    # Close connection
    cursor.close()
    conn.close()
    print("\nDone!")

if __name__ == "__main__":
    main() 