#!/usr/bin/env python3
"""
Test Legacy Backtester with Local MySQL
Verifies the legacy backtester can connect to local MySQL database
"""

import sys
import os
from pathlib import Path

# Add legacy backtester to path
legacy_path = Path(__file__).parent.parent / 'bt' / 'archive' / 'backtester_stable' / 'BTRUN'
sys.path.insert(0, str(legacy_path))

def test_mysql_connection():
    """Test MySQL connection from legacy code"""
    try:
        # Test if we can import the legacy module
        import Util
        print("✓ Successfully imported legacy Util module")
        
        # The legacy code uses config variables
        # Update config.py or use environment variables
        import config
        
        # Override config values for local MySQL
        config.host = 'localhost'
        config.user = 'mahesh'  
        config.password = 'mahesh_123'
        config.database = 'historicaldb'
        
        print("Testing legacy backtester MySQL connection...")
        print(f"Host: {config.host}")
        print(f"Database: {config.database}")
        
        # Import mysql.connector as the legacy code does
        import mysql.connector as mysql
        
        # Try to connect as legacy code would
        conn = mysql.connect(
            host=config.host,
            user=config.user,
            password=config.password,
            database=config.database
        )
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT COUNT(*) FROM nifty_call WHERE date >= 240101 AND date <= 240131")
        count = cursor.fetchone()[0]
        print(f"\n✓ Connection successful!")
        print(f"  nifty_call rows in Jan 2024: {count:,}")
        
        # Check all tables
        tables = ['nifty_call', 'nifty_put', 'nifty_cash', 'nifty_future']
        print("\nTable row counts:")
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  {table}: {count:,} rows")
        
        cursor.close()
        conn.close()
        
        print("\n✓ Legacy backtester can successfully use local MySQL!")
        print("\nNext steps:")
        print("1. Update Excel input files with 2024 dates")
        print("2. Run legacy backtester: cd bt/archive/backtester_stable/BTRUN && python BTRunPortfolio.py")
        print("3. Run GPU backtester with same inputs")
        print("4. Compare outputs")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        print("Make sure you're in the correct directory")
        return False
    except Exception as e:
        print(f"✗ Connection error: {e}")
        print("\nTroubleshooting:")
        print("1. Check MySQL is running: sudo systemctl status mysql")
        print("2. Verify credentials: mysql -u mahesh -pmahesh_123 -h localhost historicaldb")
        print("3. Check tables exist: SHOW TABLES;")
        return False

def test_simple_query():
    """Test a simple options query"""
    try:
        import mysql.connector
        
        conn = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='mahesh',
            password='mahesh_123',
            database='historicaldb'
        )
        cursor = conn.cursor()
        
        # Test ATM strike query
        query = """
        SELECT date, time, spot, strike
        FROM nifty_call
        WHERE date = 240103
        AND time = 91600
        ORDER BY ABS(strike - spot)
        LIMIT 1
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        
        if result:
            date, time, spot, strike = result
            print(f"\nATM Strike Test (Jan 3, 2024 at 9:16 AM):")
            print(f"  Date: {date}")
            print(f"  Time: {time}")
            print(f"  Spot: {spot}")
            print(f"  ATM Strike: {strike}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"Query test error: {e}")

if __name__ == '__main__':
    print("="*60)
    print("Legacy Backtester MySQL Connection Test")
    print("="*60)
    
    # Test connection
    success = test_mysql_connection()
    
    if success:
        # Test a simple query
        test_simple_query()
    
    print("\n" + "="*60) 