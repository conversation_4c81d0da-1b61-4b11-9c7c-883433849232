#!/usr/bin/env python3
"""
Compare sheets between output and golden files
"""

import pandas as pd
import json

output_file = "/srv/samba/shared/tbs_test_golden_dates.xlsx"
golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"
json_file = "/srv/samba/shared/tbs_test_golden_dates.json"

print("Sheet comparison")
print("=" * 60)

try:
    # Read sheet names
    output_xl = pd.ExcelFile(output_file, engine='openpyxl')
    golden_xl = pd.ExcelFile(golden_file, engine='openpyxl')
    
    print(f"\nOutput file sheets: {output_xl.sheet_names}")
    print(f"Golden file sheets: {golden_xl.sheet_names}")
    
    # Check JSON output
    print(f"\nJSON output:")
    with open(json_file, 'r') as f:
        json_data = json.load(f)
        print(f"  Keys: {list(json_data.keys())}")
        if 'summary' in json_data:
            print(f"  Summary: {json_data['summary']}")
        if 'trades' in json_data:
            print(f"  Number of trades: {len(json_data['trades'])}")
            
    # Compare Metrics sheet
    if 'Metrics' in output_xl.sheet_names and 'Metrics' in golden_xl.sheet_names:
        print(f"\nComparing Metrics sheet:")
        
        output_metrics = pd.read_excel(output_file, sheet_name='Metrics', engine='openpyxl')
        golden_metrics = pd.read_excel(golden_file, sheet_name='Metrics', engine='openpyxl')
        
        print(f"\n  Output Metrics (first 10 rows):")
        print(output_metrics.head(10))
        
        print(f"\n  Golden Metrics (first 10 rows):")
        print(golden_metrics.head(10))
        
        # Look for Total PnL row
        if 'Particulars' in output_metrics.columns:
            total_pnl_row = output_metrics[output_metrics['Particulars'].str.contains('Total PnL', case=False, na=False)]
            if not total_pnl_row.empty:
                print(f"\n  Output Total PnL row:")
                print(total_pnl_row)
                
        if 'Particulars' in golden_metrics.columns:
            total_pnl_row = golden_metrics[golden_metrics['Particulars'].str.contains('Total PnL', case=False, na=False)]
            if not total_pnl_row.empty:
                print(f"\n  Golden Total PnL row:")
                print(total_pnl_row)
                
    # Check TradeBook if it exists
    if 'TradeBook' in output_xl.sheet_names:
        print(f"\nTradeBook sheet found in output:")
        trades_df = pd.read_excel(output_file, sheet_name='TradeBook', engine='openpyxl')
        print(f"  Shape: {trades_df.shape}")
        print(f"  Columns: {list(trades_df.columns)[:10]}...")
        print(f"  Date range: {trades_df['entry_date'].min()} to {trades_df['entry_date'].max()}")
        print(f"  Total P&L: {trades_df['pnl'].sum():.2f}")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc() 