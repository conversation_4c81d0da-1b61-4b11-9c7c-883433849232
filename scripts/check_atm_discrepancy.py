#!/usr/bin/env python3
"""
Check why ATM strike varies at different times on April 3, 2025
"""

try:
    from pyheavydb import connect as heavydb_connect
except ImportError:
    from heavydb import connect as heavydb_connect

def check_atm_variations():
    """Check ATM strike at different times"""
    conn = heavydb_connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    print("ATM Strike Variations on April 3, 2025")
    print("="*70)
    
    # Check ATM at different times
    times = ['09:15:00', '09:16:00', '09:30:00', '10:00:00', '11:00:00', '12:00:00']
    
    for time in times:
        query = f"""
        SELECT 
            trade_time,
            spot,
            atm_strike,
            ce_close,
            pe_close,
            strike + ce_close - pe_close as synthetic_future
        FROM nifty_option_chain
        WHERE trade_date = '2025-04-03'
        AND trade_time = '{time}'
        AND strike = atm_strike
        AND expiry_date = '2025-04-03'
        LIMIT 1
        """
        
        result = conn.execute(query).fetchone()
        if result:
            trade_time, spot, atm, ce, pe, syn_fut = result
            print(f"\nTime: {trade_time}")
            print(f"  Spot: {spot:.2f}")
            print(f"  ATM Strike: {int(atm)}")
            print(f"  CE Price: {ce:.2f}, PE Price: {pe:.2f}")
            print(f"  Synthetic Future: {syn_fut:.2f}")
        else:
            print(f"\nTime: {time} - No data")
    
    # Check all unique ATM strikes for the day
    print("\n" + "="*70)
    print("All Unique ATM Strikes on April 3, 2025:")
    print("="*70)
    
    query2 = """
    SELECT DISTINCT
        atm_strike,
        COUNT(*) as occurrences,
        MIN(trade_time) as first_time,
        MAX(trade_time) as last_time
    FROM nifty_option_chain
    WHERE trade_date = '2025-04-03'
    AND expiry_date = '2025-04-03'
    GROUP BY atm_strike
    ORDER BY occurrences DESC
    """
    
    results = conn.execute(query2).fetchall()
    for atm, count, first, last in results:
        print(f"ATM {int(atm)}: {count} times, from {first} to {last}")
    
    conn.close()

def main():
    check_atm_variations()
    
    print("\n" + "="*70)
    print("EXPLANATION:")
    print("ATM strike can change during the day as spot price moves")
    print("The synthetic future method recalculates ATM at each timestamp")
    print("This is why we see different ATM strikes at different times")

if __name__ == "__main__":
    main() 