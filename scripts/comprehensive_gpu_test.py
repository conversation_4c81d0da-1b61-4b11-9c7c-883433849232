#!/usr/bin/env python3
"""
Comprehensive GPU Backtester Testing Script
Since legacy backtester requires external services, we focus on GPU testing only.
"""

import os
import subprocess
import time
import json
import pandas as pd
from datetime import datetime
from pathlib import Path
import shutil

class GPUBacktesterTest:
    def __init__(self):
        self.test_dir = f"gpu_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.test_dir, exist_ok=True)
        self.results = []
        
    def log(self, message):
        """Log message with timestamp."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
        # Also write to log file
        with open(f"{self.test_dir}/test_log.txt", "a") as f:
            f.write(f"[{timestamp}] {message}\n")
    
    def run_gpu_test(self, test_name, portfolio_excel, date_range="1day"):
        """Run a single GPU backtester test."""
        self.log(f"Running test: {test_name} ({date_range})")
        
        output_file = f"{self.test_dir}/{test_name}_{date_range}.xlsx"
        start_time = time.time()
        
        # Build command
        cmd = [
            "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
            "--legacy-excel",
            "--portfolio-excel", portfolio_excel,
            "--output-path", output_file
        ]
        
        # Run the test
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        duration = time.time() - start_time
        
        # Check if output was created
        success = os.path.exists(output_file)
        
        # Store result
        test_result = {
            "test_name": test_name,
            "date_range": date_range,
            "portfolio_excel": portfolio_excel,
            "success": success,
            "duration": duration,
            "output_file": output_file if success else None,
            "error": result.stderr if not success else None
        }
        
        self.results.append(test_result)
        
        if success:
            self.log(f"✅ Success: {test_name} completed in {duration:.2f}s")
            # Analyze the output
            self.analyze_output(output_file, test_name)
        else:
            self.log(f"❌ Failed: {test_name}")
            if result.stderr:
                self.log(f"Error: {result.stderr[:200]}...")
        
        return success
    
    def analyze_output(self, output_file, test_name):
        """Analyze the output Excel file."""
        try:
            # Read key sheets
            portfolio_trans = pd.read_excel(output_file, sheet_name="PORTFOLIO Trans")
            metrics = pd.read_excel(output_file, sheet_name="Metrics")
            
            # Extract key metrics
            if not portfolio_trans.empty:
                total_trades = len(portfolio_trans)
                total_pnl = portfolio_trans['overall_pnl'].sum() if 'overall_pnl' in portfolio_trans.columns else 0
                
                self.log(f"  - Total trades: {total_trades}")
                self.log(f"  - Total P&L: ₹{total_pnl:,.2f}")
                
                # Check if all legs executed
                if 'legId' in portfolio_trans.columns:
                    unique_legs = portfolio_trans['legId'].unique()
                    self.log(f"  - Legs executed: {sorted(unique_legs)}")
            
            # Check metrics sheet
            if not metrics.empty:
                self.log(f"  - Metrics rows: {len(metrics)}")
                # Show some key metrics
                net_profit = metrics[metrics['Particulars'] == 'Net Profit']['NIF0DTE'].values
                if len(net_profit) > 0:
                    self.log(f"  - Net Profit: ₹{net_profit[0]:,.2f}")
                
        except Exception as e:
            self.log(f"  - Error analyzing output: {str(e)}")
    
    def run_all_tests(self):
        """Run all test scenarios."""
        self.log("Starting comprehensive GPU backtester testing")
        self.log("=" * 60)
        
        # Test 1: TBS Strategy - 1 day
        self.run_gpu_test(
            test_name="TBS_strategy",
            portfolio_excel="test_data/tbs/input_portfolio_tbs_1day.xlsx",
            date_range="1day"
        )
        
        # Test 2: TBS Strategy - 30 days (if available)
        if os.path.exists("test_data/tbs/input_portfolio_tbs_30day.xlsx"):
            self.run_gpu_test(
                test_name="TBS_strategy",
                portfolio_excel="test_data/tbs/input_portfolio_tbs_30day.xlsx",
                date_range="30day"
            )
        
        # Test 3: Check multi-worker GPU optimization
        self.log("\nTesting GPU optimization with workers...")
        self.test_gpu_optimization()
        
        # Generate summary report
        self.generate_report()
    
    def test_gpu_optimization(self):
        """Test GPU optimization features."""
        test_file = "test_data/tbs/input_portfolio_tbs_1day.xlsx"
        
        # Test with different worker configurations
        worker_configs = [
            ("sequential", 1, False),
            ("gpu_2_workers", 2, True),
            ("gpu_4_workers", 4, True)
        ]
        
        for config_name, workers, gpu_opt in worker_configs:
            self.log(f"\nTesting {config_name} configuration...")
            
            output_file = f"{self.test_dir}/gpu_opt_{config_name}.xlsx"
            start_time = time.time()
            
            cmd = [
                "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
                "--legacy-excel",
                "--portfolio-excel", test_file,
                "--output-path", output_file,
                "--workers", str(workers)
            ]
            
            if gpu_opt:
                cmd.append("--gpu-optimize")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            duration = time.time() - start_time
            
            success = os.path.exists(output_file)
            
            self.log(f"  - {config_name}: {'Success' if success else 'Failed'} in {duration:.2f}s")
            
            if success and workers == 1:
                baseline_time = duration
            elif success and workers > 1:
                speedup = baseline_time / duration if 'baseline_time' in locals() else 1.0
                self.log(f"  - Speedup: {speedup:.2f}x")
    
    def generate_report(self):
        """Generate a summary report."""
        self.log("\n" + "=" * 60)
        self.log("TEST SUMMARY REPORT")
        self.log("=" * 60)
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r['success'])
        
        self.log(f"Total tests run: {total_tests}")
        self.log(f"Successful: {successful_tests}")
        self.log(f"Failed: {total_tests - successful_tests}")
        self.log(f"Success rate: {(successful_tests/total_tests*100):.1f}%")
        
        # Save detailed results as JSON
        with open(f"{self.test_dir}/test_results.json", "w") as f:
            json.dump(self.results, f, indent=2, default=str)
        
        self.log(f"\nDetailed results saved to: {self.test_dir}/test_results.json")
        
        # Create summary DataFrame
        summary_df = pd.DataFrame(self.results)
        summary_df.to_excel(f"{self.test_dir}/test_summary.xlsx", index=False)
        
        self.log(f"Summary Excel saved to: {self.test_dir}/test_summary.xlsx")
        
        # Print individual test results
        self.log("\nIndividual Test Results:")
        for result in self.results:
            status = "✅" if result['success'] else "❌"
            self.log(f"{status} {result['test_name']} ({result['date_range']}): {result['duration']:.2f}s")

if __name__ == "__main__":
    tester = GPUBacktesterTest()
    tester.run_all_tests() 