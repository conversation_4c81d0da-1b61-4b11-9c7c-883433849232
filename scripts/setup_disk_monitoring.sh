#!/bin/bash
# Setup script for disk monitoring services

echo "Setting up HeavyDB Disk Monitoring Services..."

# Create systemd service for disk monitoring
cat > /tmp/heavydb-disk-monitor.service << EOF
[Unit]
Description=HeavyDB Disk Space Monitor
After=network.target

[Service]
Type=simple
ExecStart=/usr/bin/python3 /srv/samba/shared/scripts/disk_space_monitor.py --daemon
Restart=always
RestartSec=300
User=root
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# Create systemd timer for periodic cleanup
cat > /tmp/heavydb-disk-cleanup.service << EOF
[Unit]
Description=HeavyDB Disk Cleanup Service
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/bin/python3 /srv/samba/shared/scripts/automated_disk_expansion.py
User=root
StandardOutput=journal
StandardError=journal
EOF

cat > /tmp/heavydb-disk-cleanup.timer << EOF
[Unit]
Description=Run HeavyDB Disk Cleanup daily
Requires=heavydb-disk-cleanup.service

[Timer]
OnCalendar=daily
OnCalendar=02:00
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Create cron job for storage check (backup to systemd)
cat > /tmp/heavydb-storage-check.cron << EOF
# Check HeavyDB storage optimization hourly
0 * * * * /usr/bin/python3 /srv/samba/shared/scripts/check_heavydb_storage.py > /srv/samba/shared/logs/storage_check_\$(date +\%Y\%m\%d_\%H\%M\%S).log 2>&1

# Run disk space monitor every 30 minutes
*/30 * * * * /usr/bin/python3 /srv/samba/shared/scripts/disk_space_monitor.py > /srv/samba/shared/logs/disk_monitor_\$(date +\%Y\%m\%d_\%H\%M\%S).log 2>&1

# Daily cleanup at 2 AM
0 2 * * * /usr/bin/python3 /srv/samba/shared/scripts/automated_disk_expansion.py > /srv/samba/shared/logs/disk_cleanup_\$(date +\%Y\%m\%d_\%H\%M\%S).log 2>&1
EOF

# Create default configuration files
mkdir -p /srv/samba/shared/config

# Disk monitor config
if [ ! -f /srv/samba/shared/config/disk_monitor.yaml ]; then
    cat > /srv/samba/shared/config/disk_monitor.yaml << EOF
monitored_paths:
  /nvme0n1-disk:
    name: 'HeavyDB NVMe Storage'
    critical_threshold: 90
    warning_threshold: 80
    info_threshold: 70
  /srv/samba/shared:
    name: 'Shared Data Storage'
    critical_threshold: 85
    warning_threshold: 75
    info_threshold: 65
  /:
    name: 'Root Filesystem'
    critical_threshold: 90
    warning_threshold: 80
    info_threshold: 70

alerts:
  telegram:
    enabled: false
    bot_token: ''  # Set your bot token
    chat_ids: []   # Add chat IDs
  email:
    enabled: false
    smtp_server: 'smtp.gmail.com'
    smtp_port: 587
    from_email: ''
    password: ''
    to_emails: []

monitoring:
  check_interval_minutes: 30
  alert_cooldown_hours: 4
  trend_analysis_days: 7
  predict_full_days: 3
EOF
fi

# Disk expansion config
if [ ! -f /srv/samba/shared/config/disk_expansion.yaml ]; then
    cat > /srv/samba/shared/config/disk_expansion.yaml << EOF
cleanup_policies:
  logs:
    enabled: true
    paths:
      - '/srv/samba/shared/logs'
      - '/nvme0n1-disk/var/lib/heavyai/storage/log'
      - '/srv/samba/shared/bt/backtester_stable/BTRUN/logs'
    retention_days: 30
    patterns: ['*.log', '*.log.*']
  
  backtest_outputs:
    enabled: true
    paths:
      - '/srv/samba/shared/Trades'
      - '/srv/samba/shared/bt/backtester_stable/BTRUN/output'
    retention_days: 7
    patterns: ['*.xlsx', '*.json', '*.csv']
  
  temp_files:
    enabled: true
    paths:
      - '/tmp'
      - '/srv/samba/shared/temp'
      - '/var/lib/heavyai/import'
    retention_days: 1
    patterns: ['*']

heavydb_cleanup:
  enabled: false  # Enable after testing
  retention_days: 365
  tables_to_clean: ['market_option_chain', 'market_futures', 'market_spot']
  archive_before_delete: true
  archive_path: '/sdb-disk/heavydb_archive'

thresholds:
  auto_cleanup_percent: 85
  emergency_cleanup_percent: 95
EOF
fi

# Installation function
install_services() {
    echo "Installing systemd services..."
    
    # Copy service files
    sudo cp /tmp/heavydb-disk-monitor.service /etc/systemd/system/
    sudo cp /tmp/heavydb-disk-cleanup.service /etc/systemd/system/
    sudo cp /tmp/heavydb-disk-cleanup.timer /etc/systemd/system/
    
    # Reload systemd
    sudo systemctl daemon-reload
    
    # Enable services
    sudo systemctl enable heavydb-disk-monitor.service
    sudo systemctl enable heavydb-disk-cleanup.timer
    
    # Start services
    sudo systemctl start heavydb-disk-monitor.service
    sudo systemctl start heavydb-disk-cleanup.timer
    
    echo "Systemd services installed and started."
}

# Installation function for cron
install_cron() {
    echo "Installing cron jobs..."
    
    # Install crontab
    crontab -l > /tmp/current_cron 2>/dev/null || true
    cat /tmp/heavydb-storage-check.cron >> /tmp/current_cron
    crontab /tmp/current_cron
    
    echo "Cron jobs installed."
}

# Main menu
echo ""
echo "HeavyDB Storage Monitoring Setup"
echo "================================"
echo ""
echo "1. Install systemd services (recommended)"
echo "2. Install cron jobs"
echo "3. Install both"
echo "4. Generate configs only"
echo "5. Exit"
echo ""
read -p "Select option (1-5): " choice

case $choice in
    1)
        install_services
        ;;
    2)
        install_cron
        ;;
    3)
        install_services
        install_cron
        ;;
    4)
        echo "Configuration files generated in /srv/samba/shared/config/"
        ;;
    5)
        echo "Exiting..."
        exit 0
        ;;
    *)
        echo "Invalid option"
        exit 1
        ;;
esac

echo ""
echo "Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit /srv/samba/shared/config/disk_monitor.yaml to configure alerts"
echo "2. Edit /srv/samba/shared/config/disk_expansion.yaml to customize cleanup policies"
echo "3. Test the monitoring: python3 /srv/samba/shared/scripts/check_heavydb_storage.py"
echo "4. Check service status: systemctl status heavydb-disk-monitor"
echo ""
echo "Monitoring logs will be in: /srv/samba/shared/logs/"