#!/usr/bin/env python3
"""
Start Legacy Backtester Backend Service
This script starts the Flask-based backend service that the legacy backtester needs
"""

import os
import sys
import logging
from multiprocessing import Process

# Setup logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s %(levelname)s %(name)s %(threadName)s : %(message)s'
)
logging.getLogger('socketio').setLevel(logging.ERROR)
logging.getLogger('engineio').setLevel(logging.ERROR)

# Add legacy path
legacy_path = os.path.abspath('bt/archive/backtester_stable')
sys.path.insert(0, legacy_path)

def start_service(port, name):
    """Start a Flask service on the specified port"""
    try:
        print(f"\n{'='*60}")
        print(f"Starting {name} Backend Service on port {port}")
        print(f"{'='*60}")
        
        # Change to legacy directory
        os.chdir(legacy_path)
        
        # Ensure we're using MySQL config
        import shutil
        config_path = os.path.join(legacy_path, 'app', 'config.py')
        mysql_config_path = os.path.join(legacy_path, 'app', 'config_mysql.py')
        
        if os.path.exists(mysql_config_path):
            shutil.copy2(mysql_config_path, config_path)
            print(f"✓ Using MySQL configuration")
        
        # Import app after config is set
        from app import create_app
        
        app = create_app()
        
        print(f"✓ Flask app created")
        print(f"✓ Starting service on 127.0.0.1:{port}")
        print(f"✓ Endpoint: http://127.0.0.1:{port}/backtest/start")
        
        # Run the Flask app
        app.run(host="127.0.0.1", port=port, debug=False)
        
    except Exception as e:
        print(f"✗ Error starting {name} service: {e}")
        import traceback
        traceback.print_exc()

def test_backend_connection():
    """Test if the backend services are accessible"""
    import time
    import requests
    
    print("\n" + "="*60)
    print("Testing Backend Services")
    print("="*60)
    
    # Wait a bit for services to start
    time.sleep(3)
    
    for port, name in [(5000, "Tick"), (5001, "Minute")]:
        try:
            response = requests.get(f"http://127.0.0.1:{port}/")
            print(f"✓ {name} service on port {port}: Accessible")
        except requests.exceptions.ConnectionError:
            print(f"✗ {name} service on port {port}: Not accessible")
        except Exception as e:
            print(f"✗ {name} service on port {port}: Error - {e}")

def main():
    """Main function to start both backend services"""
    print("Legacy Backtester Backend Service Manager")
    print("="*60)
    
    # Start both services in separate processes
    tick_process = Process(target=start_service, args=(5000, "Tick"))
    minute_process = Process(target=start_service, args=(5001, "Minute"))
    
    # Start the processes
    tick_process.start()
    minute_process.start()
    
    # Test the connections
    test_backend_connection()
    
    print("\n" + "="*60)
    print("Backend Services Status:")
    print("- Tick Service (5000): Running" if tick_process.is_alive() else "- Tick Service (5000): Failed")
    print("- Minute Service (5001): Running" if minute_process.is_alive() else "- Minute Service (5001): Failed")
    print("="*60)
    print("\nPress Ctrl+C to stop the services")
    
    try:
        # Keep the main process running
        tick_process.join()
        minute_process.join()
    except KeyboardInterrupt:
        print("\n\nShutting down services...")
        tick_process.terminate()
        minute_process.terminate()
        print("Services stopped.")

if __name__ == "__main__":
    main() 