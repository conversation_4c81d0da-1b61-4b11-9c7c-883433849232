#!/usr/bin/env python3
"""
Comprehensive fix for new backtester issues:
1. Align ATM calculation with legacy
2. Ensure all 4 trades are generated
3. Fix output format to match legacy
"""

import os
import sys
import pandas as pd
from datetime import datetime
import shutil

def create_atm_alignment_patch():
    """Create a patch to align ATM calculation with legacy method"""
    print("="*60)
    print("Creating ATM Alignment Patch")
    print("="*60)
    
    patch_content = '''
def get_atm_strike_simple(spot_price):
    """Simple ATM calculation matching legacy backtester"""
    return round(spot_price / 50) * 50

def patch_atm_calculation():
    """Patch to use simple ATM calculation instead of synthetic future"""
    # This would need to be applied to the actual HeavyDB query or post-processing
    pass
'''
    
    # Save patch for reference
    with open("scripts/atm_alignment_patch.py", "w") as f:
        f.write(patch_content)
    
    print("✓ Created ATM alignment patch reference")

def analyze_trade_generation_issue():
    """Analyze why only 1 trade is being generated"""
    print("\n" + "="*60)
    print("Analyzing Trade Generation Issue")
    print("="*60)
    
    print("\nPossible causes for missing trades:")
    print("1. <PERSON><PERSON><PERSON> not reading all legs correctly")
    print("2. Strike selection query filtering out some legs")
    print("3. Output formatting only showing first trade")
    print("4. Date/time filtering too restrictive")
    
    # Check the output file structure
    output_file = "comparison_outputs/new_gpu_fixed.xlsx"
    if os.path.exists(output_file):
        xl = pd.ExcelFile(output_file)
        print(f"\nOutput file sheets: {xl.sheet_names}")
        
        for sheet in xl.sheet_names:
            df = pd.read_excel(xl, sheet_name=sheet)
            print(f"\n{sheet}: {len(df)} rows, columns: {list(df.columns)[:5]}...")
            
            if len(df) > 0 and 'Trans' in sheet:
                print(f"First few rows:")
                print(df.head())

def create_test_query():
    """Create a test query to verify data availability"""
    print("\n" + "="*60)
    print("Test Query for Data Verification")
    print("="*60)
    
    test_query = '''
-- Test query to verify all required strikes exist
SELECT 
    strike,
    ce_symbol,
    pe_symbol,
    ce_close,
    pe_close,
    ce_volume,
    pe_volume
FROM nifty_option_chain
WHERE trade_date = DATE '2024-12-31'
AND trade_time = TIME '09:16:00'
AND strike IN (23450, 23500, 23550, 23600, 23650, 23700)
ORDER BY strike;
'''
    
    print("Run this query to verify data exists for all required strikes:")
    print(test_query)
    
    # Save query
    with open("scripts/verify_strikes.sql", "w") as f:
        f.write(test_query)

def create_debug_version():
    """Create a debug version of the input to isolate issues"""
    print("\n" + "="*60)
    print("Creating Debug Input Configuration")
    print("="*60)
    
    # Create a simple single-leg configuration for testing
    general_data = {
        'StrategyName': ['Debug_Single_Leg'],
        'MoveSlToCost': ['no'],
        'Underlying': ['SPOT'],
        'Index': ['NIFTY'],
        'Weekdays': ['1,2,3,4,5'],
        'DTE': [0],
        'StartTime': [91600],
        'LastEntryTime': [120000],
        'EndTime': [120000]
    }
    
    leg_data = {
        'StrategyName': ['Debug_Single_Leg'],
        'IsIdle': ['no'],
        'LegID': [1],
        'Instrument': ['call'],
        'Transaction': ['buy'],
        'Expiry': ['current'],
        'StrikeMethod': ['atm'],
        'StrikeValue': [0],
        'Lots': [1],
        'SLType': ['percentage'],
        'SLValue': [50],
        'TGTType': ['percentage'],
        'TGTValue': [100]
    }
    
    # Save debug strategy
    debug_file = "bt/backtester_stable/BTRUN/input_sheets/debug_single_leg.xlsx"
    with pd.ExcelWriter(debug_file, engine='openpyxl') as writer:
        pd.DataFrame(general_data).to_excel(writer, sheet_name='GeneralParameter', index=False)
        pd.DataFrame(leg_data).to_excel(writer, sheet_name='LegParameter', index=False)
    
    print(f"✓ Created debug strategy: {debug_file}")
    
    return debug_file

def suggest_fixes():
    """Suggest specific fixes for the issues"""
    print("\n" + "="*60)
    print("Recommended Fixes")
    print("="*60)
    
    print("\n1. ATM Calculation Fix:")
    print("   - Option A: Modify HeavyDB query to use simple rounding")
    print("   - Option B: Post-process results to adjust strikes")
    print("   - Option C: Add configuration option for ATM calculation method")
    
    print("\n2. Trade Generation Fix:")
    print("   - Check portfolio parser in bt/backtester_stable/BTRUN/excel_parser/")
    print("   - Verify all legs are being processed in trade execution")
    print("   - Check output generation in io.py")
    
    print("\n3. Output Format Fix:")
    print("   - Ensure column names match legacy format")
    print("   - Include all required fields in output")
    print("   - Format P&L calculations consistently")

def create_manual_test():
    """Create a manual test to verify HeavyDB data"""
    print("\n" + "="*60)
    print("Manual HeavyDB Test")
    print("="*60)
    
    import heavydb
    
    try:
        conn = heavydb.connect(
            host='127.0.0.1', 
            port=6274, 
            user='admin', 
            password='HyperInteractive', 
            dbname='heavyai'
        )
        
        # Test 1: Check if all strikes exist
        query = """
        SELECT COUNT(*) as count, strike
        FROM nifty_option_chain
        WHERE trade_date = DATE '2024-12-31'
        AND trade_time = TIME '09:16:00'
        AND strike IN (23450, 23500, 23550, 23600, 23650, 23700)
        GROUP BY strike
        ORDER BY strike
        """
        
        df = pd.read_sql(query, conn)
        print("\nStrike availability:")
        print(df)
        
        # Test 2: Check exit time data
        query2 = """
        SELECT COUNT(*) as count, strike
        FROM nifty_option_chain
        WHERE trade_date = DATE '2024-12-31'
        AND trade_time = TIME '12:00:00'
        AND strike IN (23450, 23500, 23550, 23600, 23650, 23700)
        GROUP BY strike
        ORDER BY strike
        """
        
        df2 = pd.read_sql(query2, conn)
        print("\nExit time data availability:")
        print(df2)
        
        conn.close()
        
    except Exception as e:
        print(f"Error connecting to HeavyDB: {e}")

if __name__ == "__main__":
    print("="*80)
    print("Comprehensive New Backtester Fix")
    print(f"Time: {datetime.now()}")
    print("="*80)
    
    # Create patches and analysis
    create_atm_alignment_patch()
    analyze_trade_generation_issue()
    create_test_query()
    debug_file = create_debug_version()
    suggest_fixes()
    create_manual_test()
    
    print("\n" + "="*80)
    print("Next Steps")
    print("="*80)
    print("\n1. Check the trade generation logic in:")
    print("   - bt/backtester_stable/BTRUN/heavydb_trade_processing.py")
    print("   - bt/backtester_stable/BTRUN/query_builder/entry_exit_sql.py")
    print("\n2. Run debug configuration to isolate issue:")
    print(f"   {debug_file}")
    print("\n3. Consider implementing ATM alignment option") 