#!/usr/bin/env python3
"""Check the actual date format in Excel files."""

import pandas as pd
import os

def check_excel_dates(excel_path):
    """Check date format in Excel file."""
    
    print(f"\nChecking dates in: {excel_path}")
    
    try:
        # Try to read PortfolioSetting sheet
        df = pd.read_excel(excel_path, sheet_name='PortfolioSetting')
        print(f"  PortfolioSetting sheet found")
        print(f"  Columns: {list(df.columns)}")
        
        # Show first few rows
        print(f"  First 3 rows:")
        print(df.head(3))
        
        # Look for date columns
        for col in df.columns:
            if 'date' in col.lower():
                print(f"\n  Column '{col}':")
                print(f"    Values: {df[col].tolist()}")
                
    except Exception as e:
        print(f"  Could not read PortfolioSetting: {e}")
    
    # List all sheets
    try:
        xl_file = pd.ExcelFile(excel_path)
        print(f"\n  All sheets: {xl_file.sheet_names}")
    except Exception as e:
        print(f"  Error reading file: {e}")

def main():
    """Check dates in legacy Excel files."""
    
    os.chdir("/srv/samba/shared/bt/archive/backtester_stable/BTRUN")
    
    # Check portfolio file
    portfolio_file = "INPUT SHEETS/INPUT PORTFOLIO.xlsx"
    if os.path.exists(portfolio_file):
        check_excel_dates(portfolio_file)
    
    # Also check the test data file
    test_file = "/srv/samba/shared/test_data/tbs/input_portfolio_tbs_1day.xlsx"
    if os.path.exists(test_file):
        check_excel_dates(test_file)

if __name__ == "__main__":
    main() 