#!/usr/bin/env python3
"""
Debug ATM calculation differences between MySQL and HeavyDB
"""

import mysql.connector
try:
    from pyheavydb import connect as heavydb_connect
except ImportError:
    from heavydb import connect as heavydb_connect

# MySQL connection
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def debug_mysql_atm():
    """Debug MySQL ATM calculation"""
    print("MySQL ATM Calculation Debug")
    print("="*60)
    
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    # Get all strikes with CE/PE prices for synthetic future calculation
    query = """
    SELECT 
        c.strike,
        c.close as ce_close,
        p.close as pe_close,
        cash.close as spot_price,
        c.strike + (c.close/100) - (p.close/100) as synthetic_future
    FROM nifty_call c
    JOIN nifty_put p ON c.date = p.date AND c.time = p.time AND c.strike = p.strike
    JOIN nifty_cash cash ON c.date = cash.date AND c.time = cash.time
    WHERE c.date = '250401' AND c.time = 33360
    AND c.close > 0 AND p.close > 0
    AND c.strike BETWEEN 23000 AND 24500
    ORDER BY c.strike
    """
    
    cursor.execute(query)
    rows = cursor.fetchall()
    
    print(f"MySQL Data (April 1, 2025 at 09:16:00):")
    print(f"Strike | CE Price | PE Price | Synthetic Future | Diff from Spot")
    print("-" * 70)
    
    spot_price = None
    min_diff = float('inf')
    best_strike = None
    
    for strike, ce_close, pe_close, spot, syn_future in rows:
        if spot_price is None:
            spot_price = float(spot) / 100
        diff = abs(float(syn_future) - float(spot)/100)
        
        if diff < min_diff:
            min_diff = diff
            best_strike = strike
        
        # Show strikes near ATM
        if strike >= 23400 and strike <= 24000:
            print(f"{strike:6.0f} | {float(ce_close)/100:8.2f} | {float(pe_close)/100:8.2f} | {float(syn_future):15.2f} | {diff:10.2f}")
    
    print(f"\nSpot Price: {spot_price:.2f}")
    print(f"Best ATM Strike (MySQL): {best_strike} (diff: {min_diff:.2f})")
    
    cursor.close()
    conn.close()

def debug_heavydb_atm():
    """Debug HeavyDB ATM calculation"""
    print("\n" + "="*60)
    print("HeavyDB ATM Calculation Debug")
    print("="*60)
    
    conn = heavydb_connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    # Get strikes around ATM with synthetic future calculation
    query = """
    SELECT 
        strike,
        ce_close,
        pe_close,
        spot,
        strike + ce_close - pe_close as synthetic_future,
        ABS(strike + ce_close - pe_close - spot) as diff_from_spot
    FROM nifty_option_chain
    WHERE trade_date = '2025-04-01' 
    AND trade_time = '09:16:00'
    AND strike BETWEEN 23000 AND 24500
    AND ce_close > 0 AND pe_close > 0
    ORDER BY strike
    """
    
    result = conn.execute(query).fetchall()
    
    print(f"HeavyDB Data (April 1, 2025 at 09:16:00):")
    print(f"Strike | CE Price | PE Price | Synthetic Future | Diff from Spot")
    print("-" * 70)
    
    spot_price = None
    for row in result:
        strike, ce_close, pe_close, spot, syn_future, diff = row
        if spot_price is None:
            spot_price = spot
        
        # Show strikes near ATM
        if strike >= 23400 and strike <= 24000:
            print(f"{strike:6.0f} | {ce_close:8.2f} | {pe_close:8.2f} | {syn_future:15.2f} | {diff:10.2f}")
    
    # Get the actual ATM strike
    query2 = "SELECT atm_strike FROM nifty_option_chain WHERE trade_date = '2025-04-01' AND trade_time = '09:16:00' LIMIT 1"
    result2 = conn.execute(query2).fetchone()
    
    print(f"\nSpot Price: {spot_price:.2f}")
    print(f"Best ATM Strike (HeavyDB): {result2[0]}")
    
    conn.close()

def main():
    print("Debugging ATM Calculation Differences")
    print("="*60)
    
    debug_mysql_atm()
    debug_heavydb_atm()
    
    print("\n" + "="*60)
    print("Summary:")
    print("Both systems have the same spot price but calculate different ATM strikes.")
    print("This could be due to:")
    print("1. Different option data (missing strikes, different prices)")
    print("2. Different expiry dates being used")
    print("3. Data quality issues in one of the systems")
    print("="*60)

if __name__ == "__main__":
    main() 