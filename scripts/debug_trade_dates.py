#!/usr/bin/env python3
"""
Debug trade dates and tick P&L calculation
"""

import json
import pandas as pd
from datetime import datetime

def debug_trade_dates():
    """Debug why tick P&L calculation is not working"""
    
    print("Debugging Trade Dates and Tick P&L")
    print("=" * 70)
    
    # Read the Excel output
    excel_file = 'output/tbs_apr3_heavydb.xlsx'
    trans_df = pd.read_excel(excel_file, sheet_name='PORTFOLIO Trans')
    
    print(f"\nTrades found in Excel: {len(trans_df)}")
    if not trans_df.empty:
        print("\nTrade dates and times:")
        for idx, row in trans_df.iterrows():
            print(f"Trade {idx+1}: Entry Date={row.get('Entry Date')}, Entry Time={row.get('Entry Time')}, Exit Time={row.get('Exit Time')}")
        
        # Check date format
        first_entry_date = trans_df['Entry Date'].iloc[0]
        print(f"\nFirst entry date value: {first_entry_date}")
        print(f"First entry date type: {type(first_entry_date)}")
        
        # If it's a pandas Timestamp, convert it
        if hasattr(first_entry_date, 'strftime'):
            yymmdd_str = first_entry_date.strftime('%y%m%d')
            print(f"Converted to YYMMDD: {yymmdd_str}")
        
    # Check the most recent log for tick P&L messages
    import glob
    log_files = sorted(glob.glob('portfolio_backtest_debug_*.log'), key=lambda x: x.split('_')[-1], reverse=True)
    if log_files:
        latest_log = log_files[0]
        print(f"\nChecking latest log: {latest_log}")
        
        # Look for tick P&L related messages
        with open(latest_log, 'r') as f:
            lines = f.readlines()
            
        tick_pnl_lines = [line for line in lines if 'tick' in line.lower() and 'pnl' in line.lower()]
        if tick_pnl_lines:
            print("\nTick P&L related log messages:")
            for line in tick_pnl_lines[-10:]:  # Last 10 messages
                print(line.strip())
        
        # Look for trade processing messages
        trade_lines = [line for line in lines if 'trades_by_date' in line]
        if trade_lines:
            print("\nTrade grouping messages:")
            for line in trade_lines[-5:]:
                print(line.strip())

if __name__ == "__main__":
    debug_trade_dates() 