#!/usr/bin/env python3
"""Deep trace of trade generation to find where 3 legs are being lost."""

import sys
import os
import json
sys.path.append('/srv/samba/shared')

from bt.backtester_stable.BTRUN.excel_parser.portfolio_parser import parse_portfolio_excel
from bt.backtester_stable.BTRUN.query_builder.entry_exit_sql import build_entry_sql
from bt.backtester_stable.BTRUN.core.heavydb_connection import get_connection
from datetime import datetime, date
import pandas as pd

def trace_trade_generation():
    """Trace where trades are being lost in the pipeline."""
    
    print("=== DEEP TRACE: Trade Generation Pipeline ===\n")
    
    # Step 1: Parse Excel
    portfolio_excel = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    portfolios_dict = parse_portfolio_excel(portfolio_excel)
    
    portfolio_name = list(portfolios_dict.keys())[0]
    portfolio_model = portfolios_dict[portfolio_name]
    strategy = portfolio_model.strategies[0]
    
    print(f"Portfolio: {portfolio_name}")
    print(f"Strategy: {strategy.strategy_name}")
    print(f"Total legs in model: {len(strategy.legs)}")
    
    # Step 2: Check each leg's SQL generation
    print("\n=== STEP 2: SQL Generation for Each Leg ===")
    
    test_date = date(2025, 4, 1)
    test_time = "09:16:00"
    
    sql_queries = []
    for i, leg in enumerate(strategy.legs, 1):
        print(f"\nLeg {i} ({leg.leg_id}):")
        print(f"  Instrument: {leg.option_type}")
        print(f"  Transaction: {leg.transaction}")
        print(f"  Strike Rule: {leg.strike_rule}")
        print(f"  Is Idle: {leg.extra_params.get('IsIdle', 'N/A')}")
        
        # Check if leg is idle
        is_idle = str(leg.extra_params.get('IsIdle', 'no')).lower()
        if is_idle == 'yes':
            print(f"  ⚠️ Leg is marked as IDLE - will be skipped!")
            continue
        
        try:
            # Generate SQL for this leg
            sql = build_entry_sql(leg, test_date, test_time)
            sql_queries.append((leg.leg_id, sql))
            print(f"  ✓ SQL generated successfully")
            print(f"  SQL preview: {sql[:200]}...")
        except Exception as e:
            print(f"  ✗ Failed to generate SQL: {e}")
    
    print(f"\n✓ Generated SQL for {len(sql_queries)} legs out of {len(strategy.legs)}")
    
    # Step 3: Execute queries and check results
    print("\n=== STEP 3: Executing SQL Queries ===")
    
    try:
        conn = get_connection()
        
        for leg_id, sql in sql_queries:
            print(f"\nExecuting query for leg {leg_id}:")
            try:
                cursor = conn.execute(sql)
                results = cursor.fetchall()
                print(f"  Results: {len(results)} rows")
                
                if results:
                    # Show first result
                    df = pd.DataFrame(results, columns=[col[0] for col in cursor.description])
                    print(f"  Sample data:")
                    print(f"    Strike: {df['strike'].iloc[0] if 'strike' in df.columns else 'N/A'}")
                    print(f"    Symbol: {df['ce_symbol'].iloc[0] if 'ce_symbol' in df.columns else df.get('pe_symbol', pd.Series()).iloc[0] if 'pe_symbol' in df.columns else 'N/A'}")
                else:
                    print(f"  ⚠️ No data returned!")
                    
            except Exception as e:
                print(f"  ✗ Query execution failed: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
    
    # Step 4: Check trade building logic
    print("\n=== STEP 4: Trade Building Analysis ===")
    
    # Let's check if there's any filtering happening in the trade builder
    print("\nChecking for common filtering issues:")
    
    # Check DTE filter
    dte_filter = strategy.extra_params.get('DTE', '0')
    print(f"1. DTE Filter: {dte_filter}")
    
    # Check date range
    print(f"2. Date Range: {portfolio_model.start_date} to {portfolio_model.end_date}")
    
    # Check for any leg-specific filters
    for i, leg in enumerate(strategy.legs, 1):
        sl_value = leg.extra_params.get('SLValue', 'N/A')
        tgt_value = leg.extra_params.get('TGTValue', 'N/A')
        print(f"3. Leg {i} Risk Rules: SL={sl_value}, TGT={tgt_value}")
    
    # Step 5: Identify the pattern
    print("\n=== STEP 5: Pattern Analysis ===")
    
    print("\nObservations:")
    print("- Only leg 4 (BUY PUT) appears in output")
    print("- This is the last leg in the Excel sheet")
    print("- Strike is 23450 (which matches the ATM strike)")
    
    print("\nPossible causes:")
    print("1. Trade aggregation might be overwriting trades with same strike")
    print("2. Output generation might be taking only the last trade")
    print("3. Risk rule evaluation might be exiting other trades immediately")
    
    return strategy

def check_runtime_processing():
    """Check how trades are processed in the runtime."""
    print("\n=== STEP 6: Runtime Processing Check ===")
    
    # Look for files that handle trade aggregation
    runtime_files = [
        "bt/backtester_stable/BTRUN/core/runtime.py",
        "bt/backtester_stable/BTRUN/heavydb_trade_processing.py",
        "bt/backtester_stable/BTRUN/core/stats.py"
    ]
    
    print("\nKey runtime files to investigate:")
    for file in runtime_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} (not found)")
    
    print("\nLikely issue locations:")
    print("1. Trade deduplication logic")
    print("2. Portfolio aggregation")
    print("3. Transaction DataFrame creation")

if __name__ == "__main__":
    strategy = trace_trade_generation()
    check_runtime_processing()
    
    # Save strategy model for detailed inspection
    if strategy:
        with open('debug_strategy_detailed.json', 'w') as f:
            json.dump(strategy.model_dump(), f, indent=2, default=str)
        print("\n✓ Detailed strategy model saved to debug_strategy_detailed.json") 