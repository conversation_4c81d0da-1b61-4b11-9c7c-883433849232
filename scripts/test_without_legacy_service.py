#!/usr/bin/env python3
"""
Test script that validates the new HeavyDB backtester against golden output
without requiring access to the legacy HTTP service.
"""

import os
import sys
import json
import pandas as pd
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from bt.backtester_stable.BTRUN.BTRunPortfolio_GPU import main as run_new_backtester

def load_golden_output(golden_file):
    """Load the golden output file for comparison"""
    if golden_file.endswith('.xlsx'):
        # Load all sheets from Excel
        xl_file = pd.ExcelFile(golden_file)
        sheets = {}
        for sheet_name in xl_file.sheet_names:
            sheets[sheet_name] = pd.read_excel(xl_file, sheet_name=sheet_name)
        return sheets
    elif golden_file.endswith('.json'):
        with open(golden_file, 'r') as f:
            return json.load(f)
    else:
        raise ValueError(f"Unsupported file format: {golden_file}")

def compare_outputs(new_output, golden_output):
    """Compare new output against golden output"""
    differences = []
    
    if isinstance(golden_output, dict) and all(isinstance(v, pd.DataFrame) for v in golden_output.values()):
        # Excel comparison
        new_xl = pd.ExcelFile(new_output)
        
        # Check sheet names match
        golden_sheets = set(golden_output.keys())
        new_sheets = set(new_xl.sheet_names)
        
        if golden_sheets != new_sheets:
            differences.append(f"Sheet mismatch: Golden has {golden_sheets}, New has {new_sheets}")
            return differences
        
        # Compare each sheet
        for sheet_name in golden_sheets:
            golden_df = golden_output[sheet_name]
            new_df = pd.read_excel(new_xl, sheet_name=sheet_name)
            
            # Check shape
            if golden_df.shape != new_df.shape:
                differences.append(f"Sheet '{sheet_name}' shape mismatch: Golden {golden_df.shape}, New {new_df.shape}")
                continue
            
            # Check columns
            if not golden_df.columns.equals(new_df.columns):
                differences.append(f"Sheet '{sheet_name}' column mismatch")
                continue
            
            # Compare values (with tolerance for floats)
            for col in golden_df.columns:
                if golden_df[col].dtype in ['float64', 'float32']:
                    # Compare with tolerance
                    if not pd.Series(golden_df[col]).round(6).equals(pd.Series(new_df[col]).round(6)):
                        differences.append(f"Sheet '{sheet_name}', column '{col}' has different values")
                else:
                    # Exact comparison
                    if not golden_df[col].equals(new_df[col]):
                        differences.append(f"Sheet '{sheet_name}', column '{col}' has different values")
    
    return differences

def run_test():
    """Run the new backtester and compare against golden output"""
    print("=" * 80)
    print("Testing New HeavyDB Backtester Against Golden Output")
    print("=" * 80)
    
    # Configuration
    portfolio_excel = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    golden_file = "bt/backtester_stable/BTRUN/input_sheets/golden_output.xlsx"
    new_output = "bt/backtester_stable/BTRUN/output/new_test_output.xlsx"
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(new_output), exist_ok=True)
    
    # Load golden output
    print("\n1. Loading golden output...")
    try:
        golden_data = load_golden_output(golden_file)
        print(f"   ✓ Loaded golden output from {golden_file}")
        if isinstance(golden_data, dict):
            for sheet, df in golden_data.items():
                print(f"     - Sheet '{sheet}': {df.shape[0]} rows, {df.shape[1]} columns")
    except Exception as e:
        print(f"   ✗ Failed to load golden output: {e}")
        return
    
    # Run new backtester
    print("\n2. Running new HeavyDB backtester...")
    try:
        # Prepare arguments for the new backtester
        sys.argv = [
            'BTRunPortfolio_GPU.py',
            '--legacy-excel',
            '--portfolio-excel', portfolio_excel,
            '--output-path', new_output,
            '--start-date', '20250401',
            '--end-date', '20250411'
        ]
        
        # Run the backtester
        run_new_backtester()
        print(f"   ✓ New backtester completed")
    except Exception as e:
        print(f"   ✗ New backtester failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Check if output was created
    if not os.path.exists(new_output):
        print(f"\n   ✗ Output file not created: {new_output}")
        print("   Checking for alternative output locations...")
        
        # Check common output patterns
        output_dir = os.path.dirname(new_output)
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            print(f"   Files in output directory: {files}")
        
        return
    
    # Compare outputs
    print("\n3. Comparing outputs...")
    try:
        differences = compare_outputs(new_output, golden_data)
        
        if not differences:
            print("   ✓ New output matches golden output perfectly!")
        else:
            print("   ✗ Differences found:")
            for diff in differences:
                print(f"     - {diff}")
    except Exception as e:
        print(f"   ✗ Comparison failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Summary
    print("\n" + "=" * 80)
    print("Test Summary:")
    print(f"  Golden file: {golden_file}")
    print(f"  New output:  {new_output}")
    print(f"  Status:      {'PASS' if not differences else 'FAIL'}")
    print("=" * 80)

if __name__ == "__main__":
    run_test() 