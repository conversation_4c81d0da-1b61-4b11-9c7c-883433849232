#!/usr/bin/env python3
"""
Test P&L calculation with both ATM strikes to understand the difference
"""

import mysql.connector

# MySQL connection
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def calculate_pnl_for_strike(atm_strike, date='250401', expiry='250409'):
    """Calculate P&L for a given ATM strike"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    entry_time = 33360  # 9:16 AM
    exit_time = 43200   # 12:00 PM
    
    print(f"\nCalculating P&L for ATM Strike: {atm_strike}")
    print("="*60)
    
    # Define trades
    trades = [
        {'strike': atm_strike, 'option': 'CE', 'side': 'SELL', 'qty': 50},
        {'strike': atm_strike, 'option': 'PE', 'side': 'SELL', 'qty': 50},
        {'strike': atm_strike + 100, 'option': 'CE', 'side': 'BUY', 'qty': 50},  # OTM2
        {'strike': atm_strike - 100, 'option': 'PE', 'side': 'BUY', 'qty': 50}   # OTM2
    ]
    
    total_pnl = 0
    trade_details = []
    
    for i, trade in enumerate(trades, 1):
        # Get entry price
        if trade['option'] == 'CE':
            query = "SELECT close FROM nifty_call WHERE date = %s AND time = %s AND strike = %s AND expiry = %s"
        else:
            query = "SELECT close FROM nifty_put WHERE date = %s AND time = %s AND strike = %s AND expiry = %s"
        
        cursor.execute(query, (date, entry_time, trade['strike'], expiry))
        entry_result = cursor.fetchone()
        
        # Get exit price
        cursor.execute(query, (date, exit_time, trade['strike'], expiry))
        exit_result = cursor.fetchone()
        
        if entry_result and exit_result:
            entry_price = entry_result[0] / 100
            exit_price = exit_result[0] / 100
            
            # Calculate P&L
            if trade['side'] == 'SELL':
                pnl = (entry_price - exit_price) * trade['qty']
            else:
                pnl = (exit_price - entry_price) * trade['qty']
            
            total_pnl += pnl
            
            trade_details.append({
                'leg': i,
                'strike': trade['strike'],
                'option': trade['option'],
                'side': trade['side'],
                'entry': entry_price,
                'exit': exit_price,
                'pnl': pnl
            })
            
            print(f"  {i}. NIFTY {trade['strike']} {trade['option']} {trade['side']}")
            print(f"     Entry: 09:16 @ {entry_price:.2f}")
            print(f"     Exit: 12:00 @ {exit_price:.2f}")
            print(f"     P&L: {pnl:.2f}")
        else:
            print(f"  {i}. NIFTY {trade['strike']} {trade['option']} - NO DATA")
    
    print(f"\nTotal P&L: {total_pnl:.2f}")
    
    cursor.close()
    conn.close()
    
    return total_pnl, trade_details

def main():
    print("Testing P&L with Different ATM Strikes")
    print("="*60)
    print("Date: April 1, 2025")
    print("Expiry: April 9, 2025")
    
    # Test with HeavyDB's ATM
    pnl_23450, _ = calculate_pnl_for_strike(23450)
    
    # Test with MySQL's calculated ATM
    pnl_23950, _ = calculate_pnl_for_strike(23950)
    
    print("\n" + "="*60)
    print("Summary:")
    print(f"P&L with ATM 23450 (HeavyDB): {pnl_23450:.2f}")
    print(f"P&L with ATM 23950 (MySQL): {pnl_23950:.2f}")
    print(f"Expected P&L (HeavyDB test): -62.00")
    print("\nConclusion:")
    print("The difference in ATM calculation leads to different P&L results.")
    print("HeavyDB and MySQL have the same algorithm but might have:")
    print("1. Different data quality or completeness")
    print("2. Different handling of edge cases")
    print("3. Different option chain data for some strikes")

if __name__ == "__main__":
    main() 