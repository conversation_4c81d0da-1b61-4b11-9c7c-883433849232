#!/usr/bin/env python3
"""
Run comprehensive comparison between legacy MySQL and HeavyDB backtests
"""

import os
import sys
import time
import subprocess
import pandas as pd
import json
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

class BacktestComparison:
    def __init__(self):
        self.results = {
            'legacy': {},
            'heavydb': {},
            'comparison': {}
        }
        
    def run_legacy_backtest(self, start_date, end_date):
        """Run legacy backtest using local MySQL"""
        print("\n" + "="*70)
        print("Running Legacy Backtest (MySQL)")
        print("="*70)
        
        try:
            # Create config override for local MySQL
            config_override = """
# Legacy config override for local MySQL
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}
"""
            with open('bt/archive/backtester_stable/BTRUN/local_mysql_config.py', 'w') as f:
                f.write(config_override)
            
            # Run legacy backtest
            start_time = time.time()
            
            # Note: This assumes the legacy code structure
            cmd = [
                sys.executable,
                'bt/archive/backtester_stable/BTRUN/BTRunPortfolio.py',
                '--start-date', start_date.replace('_', '-'),
                '--end-date', end_date.replace('_', '-'),
                '--output', 'output/legacy_backtest_output.xlsx'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            duration = time.time() - start_time
            
            self.results['legacy']['duration'] = duration
            self.results['legacy']['success'] = result.returncode == 0
            self.results['legacy']['output_file'] = 'output/legacy_backtest_output.xlsx'
            
            if result.returncode == 0:
                print(f"✓ Legacy backtest completed in {duration:.2f} seconds")
                self._analyze_legacy_results()
            else:
                print(f"✗ Legacy backtest failed: {result.stderr}")
                
        except Exception as e:
            print(f"Error running legacy backtest: {e}")
            self.results['legacy']['success'] = False
            self.results['legacy']['error'] = str(e)
    
    def run_heavydb_backtest(self, start_date, end_date):
        """Run HeavyDB GPU-optimized backtest"""
        print("\n" + "="*70)
        print("Running HeavyDB GPU Backtest")
        print("="*70)
        
        try:
            start_time = time.time()
            
            cmd = [
                sys.executable, '-m', 'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
                '--legacy-excel',
                '--portfolio-excel', 'bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx',
                '--output-path', 'output/heavydb_backtest_output.xlsx',
                '--cpu-only'  # Single-threaded for fair comparison
            ]
            
            env = os.environ.copy()
            env['PYTHONPATH'] = project_root
            
            result = subprocess.run(cmd, capture_output=True, text=True, env=env)
            
            duration = time.time() - start_time
            
            self.results['heavydb']['duration'] = duration
            self.results['heavydb']['success'] = result.returncode == 0
            self.results['heavydb']['output_file'] = 'output/heavydb_backtest_output.xlsx'
            
            if result.returncode == 0:
                print(f"✓ HeavyDB backtest completed in {duration:.2f} seconds")
                self._analyze_heavydb_results()
            else:
                print(f"✗ HeavyDB backtest failed")
                
        except Exception as e:
            print(f"Error running HeavyDB backtest: {e}")
            self.results['heavydb']['success'] = False
            self.results['heavydb']['error'] = str(e)
    
    def _analyze_legacy_results(self):
        """Analyze legacy backtest results"""
        try:
            xl = pd.ExcelFile(self.results['legacy']['output_file'])
            
            # Get metrics
            if 'Metrics' in xl.sheet_names:
                metrics_df = pd.read_excel(xl, sheet_name='Metrics')
                self.results['legacy']['metrics'] = metrics_df.to_dict('records')
            
            # Get trades
            if 'Trades' in xl.sheet_names:
                trades_df = pd.read_excel(xl, sheet_name='Trades')
                self.results['legacy']['total_trades'] = len(trades_df)
                self.results['legacy']['total_pnl'] = trades_df['PnL'].sum() if 'PnL' in trades_df else 0
                
        except Exception as e:
            print(f"Error analyzing legacy results: {e}")
    
    def _analyze_heavydb_results(self):
        """Analyze HeavyDB backtest results"""
        try:
            xl = pd.ExcelFile(self.results['heavydb']['output_file'])
            
            # Get metrics
            if 'Metrics' in xl.sheet_names:
                metrics_df = pd.read_excel(xl, sheet_name='Metrics')
                self.results['heavydb']['metrics'] = metrics_df.to_dict('records')
            
            # Get trades
            if 'PORTFOLIO Trans' in xl.sheet_names:
                trades_df = pd.read_excel(xl, sheet_name='PORTFOLIO Trans')
                self.results['heavydb']['total_trades'] = len(trades_df)
                self.results['heavydb']['total_pnl'] = trades_df['PnL'].sum() if 'PnL' in trades_df else 0
                
        except Exception as e:
            print(f"Error analyzing HeavyDB results: {e}")
    
    def compare_results(self):
        """Compare results between legacy and HeavyDB"""
        print("\n" + "="*70)
        print("Comparison Results")
        print("="*70)
        
        # Performance comparison
        print("\nPerformance Comparison:")
        print("-" * 50)
        
        if self.results['legacy'].get('success') and self.results['heavydb'].get('success'):
            legacy_time = self.results['legacy']['duration']
            heavydb_time = self.results['heavydb']['duration']
            speedup = legacy_time / heavydb_time
            
            print(f"Legacy (MySQL) Duration: {legacy_time:.2f} seconds")
            print(f"HeavyDB (GPU) Duration: {heavydb_time:.2f} seconds")
            print(f"Speedup: {speedup:.2f}x")
            
            self.results['comparison']['speedup'] = speedup
            
            # Trade comparison
            print("\nTrade Comparison:")
            print("-" * 50)
            
            legacy_trades = self.results['legacy'].get('total_trades', 0)
            heavydb_trades = self.results['heavydb'].get('total_trades', 0)
            
            print(f"Legacy Trades: {legacy_trades}")
            print(f"HeavyDB Trades: {heavydb_trades}")
            
            if legacy_trades == heavydb_trades:
                print("✓ Trade count matches")
            else:
                print(f"✗ Trade count mismatch: {abs(legacy_trades - heavydb_trades)} difference")
            
            # P&L comparison
            print("\nP&L Comparison:")
            print("-" * 50)
            
            legacy_pnl = self.results['legacy'].get('total_pnl', 0)
            heavydb_pnl = self.results['heavydb'].get('total_pnl', 0)
            
            print(f"Legacy Total P&L: {legacy_pnl:,.2f}")
            print(f"HeavyDB Total P&L: {heavydb_pnl:,.2f}")
            
            pnl_diff = abs(legacy_pnl - heavydb_pnl)
            if pnl_diff < 100:  # Allow small differences due to rounding
                print("✓ P&L matches (within tolerance)")
            else:
                print(f"✗ P&L mismatch: {pnl_diff:,.2f} difference")
        
        # Save comparison report
        self._save_comparison_report()
    
    def _save_comparison_report(self):
        """Save detailed comparison report"""
        report_file = 'output/comparison_report.json'
        
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\nDetailed report saved to: {report_file}")
        
        # Also save summary CSV
        summary_file = 'output/comparison_summary.csv'
        
        summary_data = {
            'Metric': ['Duration (seconds)', 'Total Trades', 'Total P&L', 'Speedup'],
            'Legacy': [
                self.results['legacy'].get('duration', 'N/A'),
                self.results['legacy'].get('total_trades', 'N/A'),
                self.results['legacy'].get('total_pnl', 'N/A'),
                '1.0x'
            ],
            'HeavyDB': [
                self.results['heavydb'].get('duration', 'N/A'),
                self.results['heavydb'].get('total_trades', 'N/A'),
                self.results['heavydb'].get('total_pnl', 'N/A'),
                f"{self.results['comparison'].get('speedup', 'N/A'):.2f}x"
            ]
        }
        
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_csv(summary_file, index=False)
        print(f"Summary saved to: {summary_file}")

def main():
    """Main function"""
    print("Legacy vs HeavyDB Backtest Comparison Tool")
    print("=" * 70)
    
    # Check if local MySQL is set up
    try:
        import pymysql
        conn = pymysql.connect(
            host='localhost',
            user='mahesh',
            password='mahesh_123',
            database='historicaldb'
        )
        conn.close()
        print("✓ Local MySQL is accessible")
    except Exception as e:
        print("✗ Local MySQL not accessible. Please run setup_local_mysql.sh first")
        return 1
    
    # Get date range
    print("\nSelect test date range:")
    print("1. Single day (April 3, 2024)")
    print("2. One week (April 1-7, 2024)")
    print("3. One month (April 2024)")
    print("4. Custom range")
    
    choice = input("\nEnter choice (1-4): ")
    
    if choice == '1':
        start_date = '03_04_2024'
        end_date = '03_04_2024'
    elif choice == '2':
        start_date = '01_04_2024'
        end_date = '07_04_2024'
    elif choice == '3':
        start_date = '01_04_2024'
        end_date = '30_04_2024'
    elif choice == '4':
        start_date = input("Enter start date (DD_MM_YYYY): ")
        end_date = input("Enter end date (DD_MM_YYYY): ")
    else:
        print("Invalid choice")
        return 1
    
    print(f"\nRunning comparison for: {start_date} to {end_date}")
    
    # Update portfolio dates
    from scripts.run_1year_gpu_backtest_simple import update_portfolio_dates
    update_portfolio_dates(start_date, end_date)
    
    # Run comparison
    comparison = BacktestComparison()
    
    # Run both backtests
    comparison.run_legacy_backtest(start_date, end_date)
    comparison.run_heavydb_backtest(start_date, end_date)
    
    # Compare results
    comparison.compare_results()
    
    print("\n✓ Comparison completed!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 