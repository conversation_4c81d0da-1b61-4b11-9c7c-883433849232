#!/usr/bin/env python3
"""
Data Integrity Check: Legacy MySQL vs New HeavyDB (Fixed)
Compares nifty options data between the two systems
"""

import os
import sys
import mysql.connector
import pandas as pd
from datetime import datetime, date
import numpy as np

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Database configurations
MYSQL_CONFIG = {
    "host": "************",
    "user": "mahesh",
    "password": "mahesh_123",
    "database": "historicaldb"
}

HEAVYDB_CONFIG = {
    "host": "127.0.0.1",
    "port": 6274,
    "user": "admin",
    "password": "HyperInteractive",
    "dbname": "heavyai"
}

def connect_mysql():
    """Connect to legacy MySQL database"""
    try:
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        print("✓ Connected to MySQL (Legacy)")
        return conn
    except Exception as e:
        print(f"✗ MySQL connection failed: {e}")
        return None

def connect_heavydb():
    """Connect to HeavyDB"""
    try:
        # Try heavydb module first
        try:
            from heavydb import connect
        except ImportError:
            # Fallback to pymapd
            import pymapd
            connect = pymapd.connect
            
        conn = connect(
            host=HEAVYDB_CONFIG['host'],
            port=HEAVYDB_CONFIG['port'],
            user=HEAVYDB_CONFIG['user'],
            password=HEAVYDB_CONFIG['password'],
            dbname=HEAVYDB_CONFIG['dbname']
        )
        print("✓ Connected to HeavyDB (New)")
        return conn
    except Exception as e:
        print(f"✗ HeavyDB connection failed: {e}")
        return None

def check_mysql_tables(cursor):
    """Check available tables in MySQL"""
    print("\n" + "="*60)
    print("MySQL Tables Check")
    print("="*60)
    
    cursor.execute("SHOW TABLES LIKE '%nifty%'")
    tables = cursor.fetchall()
    
    nifty_tables = {
        'call': None,
        'put': None,
        'cash': None,
        'future': None
    }
    
    for table in tables:
        table_name = table[0]
        if 'nifty_' in table_name and 'banknifty' not in table_name and 'finnifty' not in table_name and 'midcpnifty' not in table_name and 'niftynxt50' not in table_name:
            print(f"  - {table_name}")
            
            if 'nifty_call' == table_name:
                nifty_tables['call'] = table_name
            elif 'nifty_put' == table_name:
                nifty_tables['put'] = table_name
            elif 'nifty_cash' == table_name:
                nifty_tables['cash'] = table_name
            elif 'nifty_future' == table_name:
                nifty_tables['future'] = table_name
    
    return nifty_tables

def check_heavydb_tables(cursor):
    """Check if nifty_option_chain table exists in HeavyDB"""
    print("\n" + "="*60)
    print("HeavyDB Tables Check")
    print("="*60)
    
    # Try to query the table directly
    try:
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain LIMIT 1")
        count = cursor.fetchone()[0]
        print(f"  - nifty_option_chain (exists)")
        return True
    except Exception as e:
        print(f"  - nifty_option_chain (not found or error: {e})")
        return False

def compare_data_ranges(mysql_cursor, heavydb_cursor):
    """Compare date ranges between databases"""
    print("\n" + "="*60)
    print("Date Range Comparison")
    print("="*60)
    
    # MySQL date range
    print("\nMySQL Data Range:")
    for table_type in ['call', 'put', 'cash']:
        table_name = f'nifty_{table_type}'
        try:
            query = f"SELECT MIN(date), MAX(date), COUNT(DISTINCT date) FROM {table_name}"
            mysql_cursor.execute(query)
            min_date, max_date, count = mysql_cursor.fetchone()
            if min_date:
                print(f"  {table_name}: {min_date} to {max_date} ({count} days)")
            else:
                print(f"  {table_name}: No data found")
        except Exception as e:
            print(f"  {table_name}: Error - {e}")
    
    # HeavyDB date range
    print("\nHeavyDB Data Range:")
    try:
        query = """
        SELECT MIN(trade_date), MAX(trade_date), COUNT(DISTINCT trade_date)
        FROM nifty_option_chain
        """
        heavydb_cursor.execute(query)
        result = heavydb_cursor.fetchone()
        if result and result[0]:
            min_date, max_date, count = result
            print(f"  nifty_option_chain: {min_date} to {max_date} ({count} days)")
        else:
            print(f"  nifty_option_chain: No data found")
    except Exception as e:
        print(f"  nifty_option_chain: Error - {e}")

def compare_specific_date(mysql_cursor, heavydb_cursor, test_date):
    """Compare data for a specific date"""
    print("\n" + "="*60)
    print(f"Data Comparison for {test_date}")
    print("="*60)
    
    # Check if date exists in MySQL
    mysql_cursor.execute(f"SELECT COUNT(*) FROM nifty_call WHERE DATE(date) = '{test_date}'")
    mysql_has_data = mysql_cursor.fetchone()[0] > 0
    
    # Check if date exists in HeavyDB
    try:
        heavydb_cursor.execute(f"SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{test_date}'")
        heavydb_has_data = heavydb_cursor.fetchone()[0] > 0
    except:
        heavydb_has_data = False
    
    if not mysql_has_data and not heavydb_has_data:
        print(f"  ⚠ No data found in either database for {test_date}")
        return
    
    # MySQL data count
    mysql_counts = {}
    for table_type in ['call', 'put']:
        table_name = f'nifty_{table_type}'
        try:
            query = f"SELECT COUNT(*) FROM {table_name} WHERE DATE(date) = '{test_date}'"
            mysql_cursor.execute(query)
            count = mysql_cursor.fetchone()[0]
            mysql_counts[table_type] = count
        except Exception as e:
            mysql_counts[table_type] = f"Error: {e}"
    
    # HeavyDB data count
    if heavydb_has_data:
        try:
            # Count call options
            query = f"""
            SELECT COUNT(*) FROM nifty_option_chain 
            WHERE trade_date = DATE '{test_date}' AND ce_symbol IS NOT NULL
            """
            heavydb_cursor.execute(query)
            heavydb_call_count = heavydb_cursor.fetchone()[0]
            
            # Count put options
            query = f"""
            SELECT COUNT(*) FROM nifty_option_chain 
            WHERE trade_date = DATE '{test_date}' AND pe_symbol IS NOT NULL
            """
            heavydb_cursor.execute(query)
            heavydb_put_count = heavydb_cursor.fetchone()[0]
            
            # Unique strikes
            query = f"""
            SELECT COUNT(DISTINCT strike) FROM nifty_option_chain 
            WHERE trade_date = DATE '{test_date}'
            """
            heavydb_cursor.execute(query)
            unique_strikes = heavydb_cursor.fetchone()[0]
            
        except Exception as e:
            heavydb_call_count = f"Error: {e}"
            heavydb_put_count = f"Error: {e}"
            unique_strikes = "Error"
    else:
        heavydb_call_count = "No data"
        heavydb_put_count = "No data"
        unique_strikes = "N/A"
    
    # Display comparison
    print(f"\nRow Counts:")
    print(f"  MySQL Call Options: {mysql_counts.get('call', 'N/A')}")
    print(f"  HeavyDB Call Options: {heavydb_call_count}")
    print(f"  MySQL Put Options: {mysql_counts.get('put', 'N/A')}")
    print(f"  HeavyDB Put Options: {heavydb_put_count}")
    print(f"  HeavyDB Unique Strikes: {unique_strikes}")

def sample_data_comparison(mysql_cursor, heavydb_cursor, test_date, strike=23000):
    """Compare sample records between databases"""
    print("\n" + "="*60)
    print(f"Sample Data Comparison for {test_date} (Strike: {strike})")
    print("="*60)
    
    # Check if date exists
    mysql_cursor.execute(f"SELECT COUNT(*) FROM nifty_call WHERE DATE(date) = '{test_date}'")
    if mysql_cursor.fetchone()[0] == 0:
        print(f"  ⚠ No data in MySQL for {test_date}")
        return
    
    # Get sample from MySQL
    mysql_query = f"""
    SELECT date, time, strike, close/100.0, oi, volume
    FROM nifty_call
    WHERE DATE(date) = '{test_date}' AND strike = {strike}
    ORDER BY time
    LIMIT 5
    """
    
    try:
        mysql_cursor.execute(mysql_query)
        mysql_data = mysql_cursor.fetchall()
        
        if mysql_data:
            print("\nMySQL Sample (Call Options):")
            print("Date       | Time   | Strike | Close  | OI      | Volume")
            print("-" * 65)
            for row in mysql_data:
                # Convert time from seconds to HH:MM:SS
                hours = row[1] // 3600
                minutes = (row[1] % 3600) // 60
                seconds = row[1] % 60
                time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                print(f"{row[0]} | {time_str} | {row[2]:6} | {row[3]:7.2f} | {row[4]:8} | {row[5]:8}")
        else:
            print(f"  No data found for strike {strike}")
    except Exception as e:
        print(f"MySQL sample error: {e}")
    
    # Get sample from HeavyDB
    heavydb_query = f"""
    SELECT trade_date, trade_time, strike, ce_close, ce_oi, ce_volume
    FROM nifty_option_chain
    WHERE trade_date = DATE '{test_date}' AND strike = {strike}
    AND ce_symbol IS NOT NULL
    ORDER BY trade_time
    LIMIT 5
    """
    
    try:
        heavydb_cursor.execute(heavydb_query)
        heavydb_data = heavydb_cursor.fetchall()
        
        if heavydb_data:
            print("\nHeavyDB Sample (Call Options):")
            print("Date       | Time     | Strike | Close  | OI      | Volume")
            print("-" * 65)
            for row in heavydb_data:
                print(f"{row[0]} | {row[1]} | {row[2]:6.0f} | {row[3]:7.2f} | {row[4]:8} | {row[5]:8}")
        else:
            print(f"  No data found for strike {strike} in HeavyDB")
    except Exception as e:
        print(f"HeavyDB sample error: {e}")

def check_data_quality(mysql_cursor, heavydb_cursor, test_date):
    """Check data quality metrics"""
    print("\n" + "="*60)
    print(f"Data Quality Check for {test_date}")
    print("="*60)
    
    # Check if data exists in HeavyDB
    try:
        heavydb_cursor.execute(f"SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{test_date}'")
        if heavydb_cursor.fetchone()[0] == 0:
            print("  ⚠ No data in HeavyDB for this date")
            return
    except:
        print("  ⚠ Error checking HeavyDB data")
        return
    
    # Check for nulls in HeavyDB
    try:
        quality_checks = [
            ("NULL strikes", f"SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{test_date}' AND strike IS NULL"),
            ("NULL call prices", f"SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{test_date}' AND ce_symbol IS NOT NULL AND ce_close IS NULL"),
            ("NULL put prices", f"SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{test_date}' AND pe_symbol IS NOT NULL AND pe_close IS NULL"),
            ("Zero OI calls", f"SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{test_date}' AND ce_symbol IS NOT NULL AND ce_oi = 0"),
            ("Negative prices", f"SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = DATE '{test_date}' AND (ce_close < 0 OR pe_close < 0)")
        ]
        
        print("\nHeavyDB Data Quality:")
        for check_name, query in quality_checks:
            heavydb_cursor.execute(query)
            count = heavydb_cursor.fetchone()[0]
            status = "✓" if count == 0 else "⚠"
            print(f"  {status} {check_name}: {count}")
            
    except Exception as e:
        print(f"  Error in quality check: {e}")

def main():
    """Main execution"""
    print("="*60)
    print("Nifty Options Data Integrity Check")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Connect to databases
    mysql_conn = connect_mysql()
    heavydb_conn = connect_heavydb()
    
    if not mysql_conn or not heavydb_conn:
        print("\n✗ Cannot proceed without both database connections")
        return
    
    mysql_cursor = mysql_conn.cursor()
    heavydb_cursor = heavydb_conn.cursor()
    
    try:
        # Check tables
        mysql_tables = check_mysql_tables(mysql_cursor)
        has_heavydb_table = check_heavydb_tables(heavydb_cursor)
        
        # Compare date ranges
        compare_data_ranges(mysql_cursor, heavydb_cursor)
        
        # Test specific dates - using dates that likely exist
        test_dates = ['2023-04-03', '2023-04-04', '2024-04-01', '2025-04-01']
        
        for test_date in test_dates:
            compare_specific_date(mysql_cursor, heavydb_cursor, test_date)
            sample_data_comparison(mysql_cursor, heavydb_cursor, test_date, strike=23000)
            check_data_quality(mysql_cursor, heavydb_cursor, test_date)
        
        print("\n" + "="*60)
        print("Data Integrity Check Complete")
        print("="*60)
        
    except Exception as e:
        print(f"\n✗ Error during integrity check: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        mysql_cursor.close()
        mysql_conn.close()
        heavydb_cursor.close()
        heavydb_conn.close()

if __name__ == "__main__":
    main() 