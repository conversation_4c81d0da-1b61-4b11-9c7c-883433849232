#!/usr/bin/env python3
"""
Final comparison of TBS output with golden file
"""

import pandas as pd

output_file = "/srv/samba/shared/tbs_test_final.xlsx"
golden_file = "/srv/samba/shared/Nifty_Golden_Ouput.xlsx"

print("Final TBS Output vs Golden File Comparison")
print("=" * 60)

try:
    # Read PORTFOLIO Trans sheets
    output_trans = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans', engine='openpyxl')
    golden_trans = pd.read_excel(golden_file, sheet_name='PORTFOLIO Trans', engine='openpyxl')
    
    print(f"\nTotal trades:")
    print(f"  Output: {output_trans.shape[0]}")
    print(f"  Golden: {golden_trans.shape[0]}")
    
    # Check date distribution
    output_dates = output_trans['entry_date'].value_counts().sort_index()
    golden_dates = golden_trans['Entry Date'].value_counts().sort_index()
    
    print(f"\nTrades per date:")
    print(f"  Output: {dict(output_dates)}")
    print(f"  Golden: {dict(golden_dates)}")
    
    # Check April 3 trades
    april_3_output = output_trans[output_trans['entry_date'] == '2025-04-03']
    april_3_golden = golden_trans[golden_trans['Entry Date'] == '2025-04-03T00:00:00.000000000']
    
    print(f"\nApril 3 trades:")
    print(f"  Output: {len(april_3_output)} trades")
    print(f"  Golden: {len(april_3_golden)} trades")
    
    if len(april_3_output) == 4:
        print(f"\n  Output trades detail:")
        for idx, row in april_3_output.iterrows():
            print(f"    {row['instrument_type']} {row['side']} @ {row['strike']}: {row['entry_price']} -> {row['exit_price']} = {row['pnl']:.2f}")
            
    # Total P&L comparison
    print(f"\nTotal P&L:")
    print(f"  Output: {output_trans['pnl'].sum():.2f}")
    print(f"  Golden: {golden_trans['PNL'].sum():.2f}")
    print(f"  Difference: {abs(output_trans['pnl'].sum() - golden_trans['PNL'].sum()):.2f}")
    
    # Check exit times
    print(f"\nExit times check:")
    unique_exit_times = output_trans['exit_time'].unique()
    print(f"  Output exit times: {unique_exit_times}")
    golden_exit_times = golden_trans['Exit at'].unique()
    print(f"  Golden exit times: {golden_exit_times}")
    
    # Verify April 3 trades in detail
    if len(april_3_output) == 4 and len(april_3_golden) == 4:
        print(f"\nDetailed April 3 comparison:")
        # Sort both dataframes for comparison
        output_sorted = april_3_output.sort_values(['instrument_type', 'side'])
        golden_sorted = april_3_golden.sort_values(['CE/PE', 'Trade'])
        
        output_pnls = output_sorted['pnl'].tolist()
        golden_pnls = golden_sorted['PNL'].tolist()
        
        print(f"  Output PnLs: {[f'{x:.2f}' for x in output_pnls]}")
        print(f"  Golden PnLs: {[f'{x:.2f}' for x in golden_pnls]}")
        
        # Check if individual trades match
        pnl_match = True
        for i, (o_pnl, g_pnl) in enumerate(zip(output_pnls, golden_pnls)):
            if abs(o_pnl - g_pnl) > 0.01:  # Allow 1 paisa tolerance
                pnl_match = False
                print(f"    Trade {i+1} mismatch: {o_pnl:.2f} vs {g_pnl:.2f}")
        
        if pnl_match:
            print(f"  ✓ All individual trade PnLs match!")
        else:
            print(f"  ✗ Individual trade PnLs do not match")
    
    # Check Metrics sheet
    output_metrics = pd.read_excel(output_file, sheet_name='Metrics', engine='openpyxl')
    golden_metrics = pd.read_excel(golden_file, sheet_name='Metrics', engine='openpyxl')
    
    output_total_pnl_row = output_metrics[output_metrics['Particulars'] == 'Total PnL']
    golden_total_pnl_row = golden_metrics[golden_metrics['Particulars'] == 'Total PnL']
    
    print(f"\nMetrics sheet Total PnL:")
    if not output_total_pnl_row.empty:
        print(f"  Output: {output_total_pnl_row.iloc[0]['Value']}")
    if not golden_total_pnl_row.empty:
        print(f"  Golden: {golden_total_pnl_row.iloc[0]['Combined']}")
        
    print(f"\n{'='*20} SUMMARY {'='*20}")
    if len(april_3_output) == 4 and abs(output_trans['pnl'].sum() - golden_trans['PNL'].sum()) < 100:
        print("✓ Output appears to match golden file structure!")
        print("  - Correct number of trades per day")
        print("  - Exit times are correct (12:00:00)")
        print("  - Total P&L is close to golden value")
    else:
        print("✗ Output does not match golden file")
        print("  Issues found:")
        if len(april_3_output) != 4:
            print(f"  - Wrong number of trades on April 3: {len(april_3_output)} vs 4")
        if abs(output_trans['pnl'].sum() - golden_trans['PNL'].sum()) >= 100:
            print(f"  - Large P&L difference: {abs(output_trans['pnl'].sum() - golden_trans['PNL'].sum()):.2f}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc() 