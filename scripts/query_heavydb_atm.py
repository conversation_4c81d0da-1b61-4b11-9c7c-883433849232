#!/usr/bin/env python3
"""
Simple script to query HeavyDB for ATM strikes directly.
This script uses the project's connection utilities for reliable HeavyDB access.
"""

import sys
import os
import argparse
import datetime
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def query_heavydb(date_str, time_str):
    """Query HeavyDB for ATM strike information using project utilities."""
    
    # Parse date and time
    if len(date_str) == 8:  # YYYYMMDD format
        date_obj = datetime.datetime.strptime(date_str, "%Y%m%d").date()
    else:
        raise ValueError(f"Invalid date format: {date_str}. Expected YYYYMMDD")
    
    # Format time (HHMM or HHMMSS)
    if len(time_str) == 4:  # HHMM format
        time_obj = datetime.datetime.strptime(time_str, "%H%M").time()
    elif len(time_str) == 6:  # HHMMSS format
        time_obj = datetime.datetime.strptime(time_str, "%H%M%S").time()
    else:
        raise ValueError(f"Invalid time format: {time_str}. Expected HHMM or HHMMSS")
    
    # Format for SQL
    sql_date = date_obj.strftime("%Y-%m-%d")
    sql_time = time_obj.strftime("%H:%M:%S")
    
    print(f"Querying HeavyDB for data on {sql_date} at {sql_time}...")
    
    # Try to use project connection utilities
    try:
        from bt.backtester_stable.BTRUN.dal.heavydb_connection import get_connection
        print("Using project's HeavyDB connection utilities")
        conn = get_connection()
    except ImportError as e:
        print(f"Could not import project utilities: {e}")
        print("Falling back to direct connection...")
        
        # Fallback to direct connection
        try:
            from heavydb import connect
            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            print("Connected using heavydb library")
        except ImportError:
            try:
                import pymapd
                conn = pymapd.connect(
                    host='127.0.0.1',
                    port=6274,
                    user='admin',
                    password='HyperInteractive',
                    db_name='heavyai'
                )
                print("Connected using pymapd library")
            except ImportError:
                print("ERROR: No HeavyDB library available. Install with:")
                print("  pip install heavydb importlib_metadata")
                print("  OR")
                print("  pip install pymapd")
                return None
    
    # Test connection first
    try:
        test_result = conn.execute("SELECT 1 as test")
        print("Connection test successful")
    except Exception as e:
        print(f"Connection test failed: {e}")
        return None
    
    # Query for ATM strike and other data
    query = f"""
    SELECT DISTINCT 
        trade_date, 
        trade_time, 
        spot, 
        atm_strike,
        strike
    FROM nifty_option_chain
    WHERE trade_date = DATE '{sql_date}'
      AND trade_time = TIME '{sql_time}'
      AND strike = atm_strike
    LIMIT 1
    """
    
    print(f"Executing query:\n{query}")
    
    try:
        # Execute query
        result = conn.execute(query)
        row = result.fetchone()
        
        if not row:
            print(f"No ATM data found for {sql_date} {sql_time}")
            
            # Check if any data exists for this date/time
            check_query = f"""
            SELECT COUNT(*) as count
            FROM nifty_option_chain
            WHERE trade_date = DATE '{sql_date}'
              AND trade_time = TIME '{sql_time}'
            """
            check_result = conn.execute(check_query)
            count = check_result.fetchone()[0]
            print(f"Total records for this date/time: {count}")
            
            if count == 0:
                # Check available dates
                date_query = """
                SELECT DISTINCT trade_date
                FROM nifty_option_chain
                ORDER BY trade_date DESC
                LIMIT 5
                """
                date_result = conn.execute(date_query)
                dates = [row[0] for row in date_result.fetchall()]
                print(f"Available recent dates: {dates}")
            
            return None
        
        # Process result
        columns = [col[0] for col in result.description]
        data = dict(zip(columns, row))
        
        # Format dates for JSON
        if hasattr(data['trade_date'], 'strftime'):
            data['trade_date'] = data['trade_date'].strftime("%Y-%m-%d")
        if hasattr(data['trade_time'], 'strftime'):
            data['trade_time'] = data['trade_time'].strftime("%H:%M:%S")
        
        # Query for available strikes around ATM
        strike_query = f"""
        SELECT DISTINCT strike, call_strike_type, put_strike_type
        FROM nifty_option_chain
        WHERE trade_date = DATE '{sql_date}'
          AND trade_time = TIME '{sql_time}'
          AND strike BETWEEN {data['atm_strike'] - 200} AND {data['atm_strike'] + 200}
        ORDER BY strike
        """
        
        strike_result = conn.execute(strike_query)
        strikes = []
        for row in strike_result.fetchall():
            strikes.append({
                'strike': row[0],
                'call_type': row[1],
                'put_type': row[2]
            })
        
        data['nearby_strikes'] = strikes
        
        return data
        
    except Exception as e:
        print(f"Query execution failed: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Query HeavyDB for ATM strike')
    parser.add_argument('--date', required=True, help='Date in YYYYMMDD format')
    parser.add_argument('--time', required=True, help='Time in HHMM or HHMMSS format')
    parser.add_argument('--output', help='Output JSON file (default: stdout)')
    
    args = parser.parse_args()
    
    # Query HeavyDB
    result = query_heavydb(args.date, args.time)
    
    if result:
        # Output result
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2)
            print(f"Result saved to {args.output}")
        else:
            print("\nQuery Result:")
            print(json.dumps(result, indent=2))
    else:
        print("No data returned")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 