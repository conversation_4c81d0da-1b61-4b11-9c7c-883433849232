#!/usr/bin/env python3
"""
Compare MySQL and HeavyDB data for the same dates
This ensures we're comparing apples to apples
"""

import mysql.connector
import subprocess
import sys
import os
from datetime import datetime, timedelta

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# MySQL connection details
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def check_mysql_dates():
    """Check available dates in MySQL"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    # Check April 2025 dates
    query = """
    SELECT DISTINCT date 
    FROM nifty_cash 
    WHERE date >= '250401' AND date <= '250411'
    ORDER BY date
    """
    
    cursor.execute(query)
    dates = cursor.fetchall()
    
    print("MySQL Dates Available (April 2025):")
    mysql_dates = []
    for date in dates:
        mysql_dates.append(str(date[0]))
        # Convert to readable format
        date_str = str(date[0])
        if len(date_str) == 6:  # YYMMDD format
            formatted = f"20{date_str[:2]}-{date_str[2:4]}-{date_str[4:6]}"
            print(f"  {date_str} ({formatted})")
    
    cursor.close()
    conn.close()
    
    return mysql_dates

def check_heavydb_dates():
    """Check available dates in HeavyDB"""
    print("\nHeavyDB Dates Available (April 2025):")
    
    # Run heavysql command to check dates
    cmd = [
        "/opt/heavyai/bin/heavysql",
        "-s", "127.0.0.1",
        "-u", "admin",
        "-p", "HyperInteractive",
        "-d", "heavyai",
        "-q",
        "SELECT DISTINCT trade_date FROM nifty_option_chain WHERE trade_date >= '2025-04-01' AND trade_date <= '2025-04-11' ORDER BY trade_date"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            heavydb_dates = []
            for line in lines[1:]:  # Skip header
                if line.strip():
                    date = line.strip()
                    heavydb_dates.append(date)
                    print(f"  {date}")
            return heavydb_dates
        else:
            print(f"Error querying HeavyDB: {result.stderr}")
            return []
    except Exception as e:
        print(f"Failed to query HeavyDB: {e}")
        return []

def compare_data_for_date(mysql_date, heavydb_date):
    """Compare ATM and prices for a specific date"""
    print(f"\n{'='*60}")
    print(f"Comparing data for {mysql_date} (MySQL) vs {heavydb_date} (HeavyDB)")
    print(f"{'='*60}")
    
    # MySQL query
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    # Get spot price from MySQL at 9:16 AM
    cursor.execute("""
        SELECT close FROM nifty_cash 
        WHERE date = %s AND time = 33360
    """, (mysql_date,))
    
    mysql_spot = cursor.fetchone()
    if mysql_spot:
        mysql_spot_price = mysql_spot[0] / 100  # Convert from paisa
        print(f"MySQL Spot Price at 9:16 AM: {mysql_spot_price:.2f}")
    
    # Get ATM strike options using synthetic future method
    cursor.execute("""
        SELECT 
            c.strike,
            c.close as ce_close,
            p.close as pe_close,
            ABS(c.strike + (c.close/100) - (p.close/100) - (cash.close/100)) as syn_diff
        FROM nifty_call c
        JOIN nifty_put p ON c.date = p.date AND c.time = p.time AND c.strike = p.strike
        JOIN nifty_cash cash ON c.date = cash.date AND c.time = cash.time
        WHERE c.date = %s AND c.time = 33360
        AND c.close > 0 AND p.close > 0
        ORDER BY syn_diff
        LIMIT 5
    """, (mysql_date,))
    
    print("\nMySQL Top 5 ATM Candidates (by synthetic future):")
    rows = cursor.fetchall()
    for strike, ce_close, pe_close, syn_diff in rows:
        print(f"  Strike {strike}: CE={ce_close/100:.2f}, PE={pe_close/100:.2f}, Diff={syn_diff:.2f}")
    
    cursor.close()
    conn.close()
    
    # HeavyDB query
    print("\nHeavyDB ATM Data:")
    cmd = [
        "/opt/heavyai/bin/heavysql",
        "-s", "127.0.0.1",
        "-u", "admin",
        "-p", "HyperInteractive",
        "-d", "heavyai",
        "-q",
        f"""SELECT 
            spot as spot_price,
            atm_strike,
            strike,
            ce_close,
            pe_close
        FROM nifty_option_chain 
        WHERE trade_date = '{heavydb_date}' 
        AND trade_time = '09:16:00'
        AND strike = atm_strike
        LIMIT 1"""
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"Error: {result.stderr}")
    except Exception as e:
        print(f"Failed to query HeavyDB: {e}")

def main():
    print("MySQL vs HeavyDB Date Comparison")
    print("="*60)
    
    # Check dates in both systems
    mysql_dates = check_mysql_dates()
    heavydb_dates = check_heavydb_dates()
    
    # Find common dates
    print(f"\nMySQL has {len(mysql_dates)} dates in April 2025")
    print(f"HeavyDB has {len(heavydb_dates)} dates in April 2025")
    
    # Compare specific dates
    test_dates = [
        ('250401', '2025-04-01'),  # April 1
        ('250402', '2025-04-02'),  # April 2
    ]
    
    for mysql_date, heavydb_date in test_dates:
        if mysql_date in mysql_dates:
            compare_data_for_date(mysql_date, heavydb_date)
        else:
            print(f"\nDate {mysql_date} not found in MySQL")
    
    print("\n" + "="*60)
    print("Summary:")
    print("1. MySQL uses YYMMDD format (e.g., 250401)")
    print("2. HeavyDB uses YYYY-MM-DD format (e.g., 2025-04-01)")
    print("3. MySQL stores prices in paisa (1/100 rupee)")
    print("4. HeavyDB stores prices in rupees")
    print("5. Both systems should use synthetic future ATM calculation")
    print("="*60)

if __name__ == "__main__":
    main() 