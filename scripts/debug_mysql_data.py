#!/usr/bin/env python3
"""
Debug MySQL data to understand why ATM calculation differs from HeavyDB
"""

import mysql.connector

# MySQL connection
MYSQL_CONFIG = {
    'host': '************',
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

def debug_mysql_data():
    """Debug MySQL data for April 1, 2025"""
    conn = mysql.connector.connect(**MYSQL_CONFIG)
    cursor = conn.cursor()
    
    date = '250401'
    time = 33360  # 9:16 AM
    
    print("MySQL Data Debug for April 1, 2025 at 09:16 AM")
    print("="*60)
    
    # Check available expiry dates
    print("\n1. Available Expiry Dates:")
    cursor.execute("""
        SELECT DISTINCT expiry, COUNT(*) as count
        FROM nifty_call
        WHERE date = %s AND time = %s
        GROUP BY expiry
        ORDER BY expiry
    """, (date, time))
    
    for expiry, count in cursor.fetchall():
        print(f"   Expiry: {expiry}, Options: {count}")
    
    # Check spot price
    print("\n2. Spot Price:")
    cursor.execute("""
        SELECT close FROM nifty_cash
        WHERE date = %s AND time = %s
    """, (date, time))
    spot = cursor.fetchone()
    if spot:
        print(f"   Spot: {spot[0]/100:.2f}")
    
    # Check strikes around 23450 (HeavyDB's ATM)
    print("\n3. Options around strike 23450 (HeavyDB's ATM):")
    print("   Strike | Expiry | CE Price | PE Price | Has Both")
    print("   " + "-"*50)
    
    cursor.execute("""
        SELECT 
            c.strike,
            c.expiry,
            c.close as ce_close,
            p.close as pe_close
        FROM nifty_call c
        LEFT JOIN nifty_put p ON c.date = p.date AND c.time = p.time 
                                AND c.strike = p.strike AND c.expiry = p.expiry
        WHERE c.date = %s AND c.time = %s
        AND c.strike BETWEEN 23300 AND 23600
        ORDER BY c.strike, c.expiry
    """, (date, time))
    
    for strike, expiry, ce_close, pe_close in cursor.fetchall():
        has_both = "YES" if ce_close and pe_close else "NO"
        ce_price = f"{ce_close/100:.2f}" if ce_close else "N/A"
        pe_price = f"{pe_close/100:.2f}" if pe_close else "N/A"
        print(f"   {strike:6.0f} | {expiry} | {ce_price:>8} | {pe_price:>8} | {has_both}")
    
    # Check synthetic future calculation for each expiry
    print("\n4. Synthetic Future ATM by Expiry:")
    
    cursor.execute("SELECT DISTINCT expiry FROM nifty_call WHERE date = %s AND time = %s ORDER BY expiry", (date, time))
    expiries = [row[0] for row in cursor.fetchall()]
    
    for expiry in expiries[:3]:  # Check first 3 expiries
        print(f"\n   Expiry: {expiry}")
        
        cursor.execute("""
            SELECT 
                c.strike,
                c.close as ce_close,
                p.close as pe_close,
                cash.close as spot_price,
                ABS(c.strike + (c.close/100) - (p.close/100) - (cash.close/100)) as syn_diff
            FROM nifty_call c
            JOIN nifty_put p ON c.date = p.date AND c.time = p.time 
                              AND c.strike = p.strike AND c.expiry = p.expiry
            JOIN nifty_cash cash ON c.date = cash.date AND c.time = cash.time
            WHERE c.date = %s AND c.time = %s AND c.expiry = %s
            AND c.close > 0 AND p.close > 0
            ORDER BY syn_diff
            LIMIT 5
        """, (date, time, expiry))
        
        print("   Strike | CE Price | PE Price | Syn Diff")
        print("   " + "-"*40)
        
        rows = cursor.fetchall()
        if rows:
            for strike, ce_close, pe_close, spot, syn_diff in rows:
                print(f"   {strike:6.0f} | {ce_close/100:8.2f} | {pe_close/100:8.2f} | {syn_diff:8.2f}")
        else:
            print("   No complete option pairs found")
    
    cursor.close()
    conn.close()

if __name__ == "__main__":
    debug_mysql_data() 