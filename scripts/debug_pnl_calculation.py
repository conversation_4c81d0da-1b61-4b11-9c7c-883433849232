#!/usr/bin/env python3
"""
Debug script to trace P&L calculation step-by-step.

Shows how P&L is calculated in both systems to identify
where differences occur.
"""

import os
import sys
import json
import argparse
from pathlib import Path

# Add project to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def calculate_legacy_pnl(trade_params):
    """Show legacy P&L calculation logic."""
    print("\n=== Legacy P&L Calculation ===")
    print("Based on analysis of Util.py (lines 2069-2090)")
    
    # Extract parameters
    entry_price = trade_params['entry_price']
    exit_price = trade_params['exit_price']
    side = trade_params['side']
    quantity = trade_params['quantity']
    slippage = trade_params.get('slippage_percent', 0.1) / 100
    taxes = trade_params.get('taxes', 0.0005)  # Example: 0.05%
    
    print(f"\nInput Parameters:")
    print(f"  Entry Price: {entry_price}")
    print(f"  Exit Price: {exit_price}")
    print(f"  Side: {side}")
    print(f"  Quantity: {quantity}")
    print(f"  Slippage: {slippage * 100}%")
    print(f"  Taxes: {taxes * 100}%")
    
    # Step 1: Apply slippage
    print("\nStep 1: Apply Slippage")
    if side == "SELL":
        entry_price_slip = entry_price * (1 - slippage)
        exit_price_slip = exit_price * (1 + slippage)
        print(f"  SELL: Entry with slippage = {entry_price} * (1 - {slippage}) = {entry_price_slip:.2f}")
        print(f"  SELL: Exit with slippage = {exit_price} * (1 + {slippage}) = {exit_price_slip:.2f}")
    else:  # BUY
        entry_price_slip = entry_price * (1 + slippage)
        exit_price_slip = exit_price * (1 - slippage)
        print(f"  BUY: Entry with slippage = {entry_price} * (1 + {slippage}) = {entry_price_slip:.2f}")
        print(f"  BUY: Exit with slippage = {exit_price} * (1 - {slippage}) = {exit_price_slip:.2f}")
    
    # Step 2: Calculate points
    print("\nStep 2: Calculate Points")
    if side == "SELL":
        points = entry_price - exit_price
        points_slip = entry_price_slip - exit_price_slip
    else:  # BUY
        points = exit_price - entry_price
        points_slip = exit_price_slip - entry_price_slip
    
    print(f"  Points (no slippage): {points:.2f}")
    print(f"  Points (with slippage): {points_slip:.2f}")
    
    # Step 3: Calculate gross P&L
    print("\nStep 3: Calculate Gross P&L")
    gross_pnl = points * quantity
    gross_pnl_slip = points_slip * quantity
    print(f"  Gross P&L (no slippage): {points:.2f} * {quantity} = {gross_pnl:.2f}")
    print(f"  Gross P&L (with slippage): {points_slip:.2f} * {quantity} = {gross_pnl_slip:.2f}")
    
    # Step 4: Calculate expenses
    print("\nStep 4: Calculate Expenses (Taxes)")
    if taxes != 0:
        expenses = (entry_price_slip + exit_price_slip) * quantity * taxes
        print(f"  Expenses = ({entry_price_slip:.2f} + {exit_price_slip:.2f}) * {quantity} * {taxes}")
        print(f"  Expenses = {expenses:.2f}")
    else:
        expenses = 0
        print(f"  Expenses = 0 (no taxes)")
    
    # Step 5: Calculate net P&L
    print("\nStep 5: Calculate Net P&L")
    net_pnl = gross_pnl_slip - expenses
    print(f"  Net P&L = {gross_pnl_slip:.2f} - {expenses:.2f} = {net_pnl:.2f}")
    
    # Step 6: Rounding (legacy rounds at each step)
    print("\nStep 6: Rounding")
    print("  Legacy rounds at each calculation step")
    print(f"  Final Net P&L: {round(net_pnl, 2)}")
    
    return {
        'gross_pnl': round(gross_pnl, 2),
        'gross_pnl_with_slippage': round(gross_pnl_slip, 2),
        'expenses': round(expenses, 2),
        'net_pnl': round(net_pnl, 2)
    }

def calculate_gpu_pnl(trade_params):
    """Show GPU P&L calculation logic."""
    print("\n=== GPU P&L Calculation ===")
    print("Based on HeavyDB backtester implementation")
    
    # Extract parameters
    entry_price = trade_params['entry_price']
    exit_price = trade_params['exit_price']
    side = trade_params['side']
    quantity = trade_params['quantity']
    slippage = trade_params.get('slippage_percent', 0.1) / 100
    
    print(f"\nInput Parameters:")
    print(f"  Entry Price: {entry_price}")
    print(f"  Exit Price: {exit_price}")
    print(f"  Side: {side}")
    print(f"  Quantity: {quantity}")
    print(f"  Slippage: {slippage * 100}%")
    
    # GPU calculation (should match legacy)
    if side == "SELL":
        entry_value = entry_price * quantity * (1 - slippage)
        exit_value = exit_price * quantity * (1 + slippage)
        gross_pnl = entry_value - exit_value
    else:  # BUY
        entry_value = entry_price * quantity * (1 + slippage)
        exit_value = exit_price * quantity * (1 - slippage)
        gross_pnl = exit_value - entry_value
    
    print(f"\nCalculation:")
    print(f"  Entry Value: {entry_value:.2f}")
    print(f"  Exit Value: {exit_value:.2f}")
    print(f"  Gross P&L: {gross_pnl:.2f}")
    
    # Brokerage/taxes (simplified)
    brokerage = abs(entry_value + exit_value) * 0.0005  # Example
    net_pnl = gross_pnl - brokerage
    
    print(f"  Brokerage: {brokerage:.2f}")
    print(f"  Net P&L: {net_pnl:.2f}")
    
    return {
        'gross_pnl': round(gross_pnl, 2),
        'brokerage': round(brokerage, 2),
        'net_pnl': round(net_pnl, 2)
    }

def compare_calculations(trade_params):
    """Compare P&L calculations between systems."""
    print("\n" + "="*60)
    print("P&L CALCULATION COMPARISON")
    print("="*60)
    
    # Calculate both
    legacy_result = calculate_legacy_pnl(trade_params)
    gpu_result = calculate_gpu_pnl(trade_params)
    
    # Compare results
    print("\n=== COMPARISON ===")
    print(f"Legacy Gross P&L: {legacy_result['gross_pnl_with_slippage']}")
    print(f"GPU Gross P&L: {gpu_result['gross_pnl']}")
    print(f"Difference: {gpu_result['gross_pnl'] - legacy_result['gross_pnl_with_slippage']:.2f}")
    
    print(f"\nLegacy Net P&L: {legacy_result['net_pnl']}")
    print(f"GPU Net P&L: {gpu_result['net_pnl']}")
    print(f"Difference: {gpu_result['net_pnl'] - legacy_result['net_pnl']:.2f}")
    
    # Identify issues
    if abs(gpu_result['net_pnl'] - legacy_result['net_pnl']) > 0.01:
        print("\n⚠️  P&L MISMATCH DETECTED!")
        print("Possible causes:")
        print("1. Different rounding approaches")
        print("2. Different tax/brokerage calculation")
        print("3. Slippage applied differently")
        print("4. Different order of operations")

def debug_from_trade_file(trade_file):
    """Debug P&L from actual trade data."""
    print(f"\nLoading trades from: {trade_file}")
    
    try:
        with open(trade_file, 'r') as f:
            trades = json.load(f)
        
        for i, trade in enumerate(trades[:5]):  # First 5 trades
            print(f"\n\n{'='*60}")
            print(f"TRADE {i+1}")
            print('='*60)
            
            trade_params = {
                'entry_price': trade.get('entry_price', 100),
                'exit_price': trade.get('exit_price', 105),
                'side': trade.get('side', 'BUY'),
                'quantity': trade.get('quantity', 50),
                'slippage_percent': trade.get('slippage_percent', 0.1),
                'taxes': trade.get('taxes', 0.0005)
            }
            
            compare_calculations(trade_params)
            
    except Exception as e:
        print(f"Error loading trade file: {e}")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description='Debug P&L calculation differences')
    parser.add_argument('--trade-file', help='JSON file with trade data')
    parser.add_argument('--entry-price', type=float, help='Entry price')
    parser.add_argument('--exit-price', type=float, help='Exit price')
    parser.add_argument('--side', choices=['BUY', 'SELL'], help='Trade side')
    parser.add_argument('--quantity', type=int, default=50, help='Quantity')
    parser.add_argument('--slippage', type=float, default=0.1, help='Slippage percentage')
    parser.add_argument('--show-rounding', action='store_true', help='Show rounding details')
    parser.add_argument('--show-charges', action='store_true', help='Show all charges breakdown')
    
    args = parser.parse_args()
    
    if args.trade_file:
        debug_from_trade_file(args.trade_file)
    elif args.entry_price and args.exit_price and args.side:
        trade_params = {
            'entry_price': args.entry_price,
            'exit_price': args.exit_price,
            'side': args.side,
            'quantity': args.quantity,
            'slippage_percent': args.slippage
        }
        compare_calculations(trade_params)
    else:
        # Default example
        print("Running with example trade...")
        trade_params = {
            'entry_price': 100.0,
            'exit_price': 105.0,
            'side': 'SELL',
            'quantity': 50,
            'slippage_percent': 0.1
        }
        compare_calculations(trade_params)

if __name__ == '__main__':
    main() 