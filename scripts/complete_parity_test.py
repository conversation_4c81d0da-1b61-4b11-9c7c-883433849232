#!/usr/bin/env python3
"""Complete parity test between legacy (MySQL) and new (HeavyDB) backtesting systems."""

import os
import sys
import pandas as pd
import numpy as np
import subprocess
import json
import shutil
from datetime import datetime
from pathlib import Path
import openpyxl
import mysql.connector as mysql

# Add project paths
sys.path.append('/srv/samba/shared')
sys.path.append('/srv/samba/shared/bt/archive/backtester_stable/BTRUN')

def update_portfolio_dates(portfolio_file, start_date="01_04_2025", end_date="01_04_2025"):
    """Update dates in portfolio Excel file."""
    print(f"\n1. Updating dates in {portfolio_file}")
    
    # Read and update PortfolioSetting sheet
    wb = openpyxl.load_workbook(portfolio_file)
    ws = wb['PortfolioSetting']
    
    # Find StartDate and EndDate columns
    header_row = 1
    start_col = end_col = None
    
    for col in range(1, ws.max_column + 1):
        cell_value = ws.cell(row=header_row, column=col).value
        if cell_value == 'StartDate':
            start_col = col
        elif cell_value == 'EndDate':
            end_col = col
    
    # Update dates
    for row in range(2, ws.max_row + 1):
        if ws.cell(row=row, column=1).value:  # If row has data
            ws.cell(row=row, column=start_col).value = start_date
            ws.cell(row=row, column=end_col).value = end_date
    
    # Save the workbook
    wb.save(portfolio_file)
    print(f"   Updated dates to {start_date} - {end_date}")

def get_synthetic_future_atm(date_str, time_str, strike_range):
    """Calculate ATM using synthetic future method from MySQL data."""
    try:
        # Connect to MySQL
        mydb = mysql.connect(
            host="************",
            user="mahesh", 
            password="mahesh_123",
            database="historicaldb"
        )
        cursor = mydb.cursor()
        
        # Convert date format
        date_obj = datetime.strptime(date_str, '%d_%m_%Y')
        mysql_date = date_obj.strftime('%Y-%m-%d')
        
        # Convert time to seconds
        time_parts = time_str.split(':')
        time_seconds = int(time_parts[0]) * 3600 + int(time_parts[1]) * 60 + int(time_parts[2])
        
        # Get underlying price
        query = f"""
            SELECT close/100.0 as price
            FROM nifty_cash
            WHERE DATE(date) = '{mysql_date}'
            AND time >= {time_seconds}
            ORDER BY time ASC
            LIMIT 1
        """
        cursor.execute(query)
        result = cursor.fetchone()
        
        if not result:
            print(f"   Warning: No underlying price found for {mysql_date} at {time_str}")
            return None
            
        underlying_price = result[0]
        print(f"   Underlying price: {underlying_price}")
        
        # Get all strikes with both call and put prices
        strikes_data = []
        for strike in strike_range:
            # Get call price
            query = f"""
                SELECT close/100.0 as price
                FROM nifty_call
                WHERE DATE(date) = '{mysql_date}'
                AND strike = {strike}
                AND time >= {time_seconds}
                ORDER BY time ASC
                LIMIT 1
            """
            cursor.execute(query)
            call_result = cursor.fetchone()
            
            # Get put price
            query = f"""
                SELECT close/100.0 as price
                FROM nifty_put
                WHERE DATE(date) = '{mysql_date}'
                AND strike = {strike}
                AND time >= {time_seconds}
                ORDER BY time ASC
                LIMIT 1
            """
            cursor.execute(query)
            put_result = cursor.fetchone()
            
            if call_result and put_result:
                call_price = call_result[0]
                put_price = put_result[0]
                synthetic_future = strike + call_price - put_price
                diff = abs(synthetic_future - underlying_price)
                strikes_data.append({
                    'strike': strike,
                    'call_price': call_price,
                    'put_price': put_price,
                    'synthetic_future': synthetic_future,
                    'diff': diff
                })
        
        cursor.close()
        mydb.close()
        
        if not strikes_data:
            print("   Warning: No valid strike pairs found")
            return None
            
        # Find strike with minimum difference
        best_strike = min(strikes_data, key=lambda x: x['diff'])
        print(f"   Synthetic future ATM: {best_strike['strike']} (diff: {best_strike['diff']:.2f})")
        
        return best_strike['strike']
        
    except Exception as e:
        print(f"   Error calculating synthetic future ATM: {e}")
        return None

def patch_legacy_atm_calculation():
    """Patch the legacy Util.py to use synthetic future ATM calculation."""
    util_file = "bt/archive/backtester_stable/BTRUN/Util.py"
    
    print(f"\n2. Patching legacy ATM calculation in {util_file}")
    
    # Read the file
    with open(util_file, 'r') as f:
        content = f.read()
    
    # Check if already patched
    if "SYNTHETIC_FUTURE_ATM_PATCH" in content:
        print("   Already patched for synthetic future ATM")
        return
    
    # Find the line with simple ATM calculation and replace it
    old_atm_line = "atm_strike = round(underlying_price / 50) * 50"
    
    if old_atm_line in content:
        # Create replacement code
        new_atm_code = """# SYNTHETIC_FUTURE_ATM_PATCH - Calculate ATM using synthetic future
                                # Generate strike range
                                base_strike = round(underlying_price / 50) * 50
                                strike_range = [base_strike + (i * 50) for i in range(-10, 11)]
                                
                                # Get synthetic future ATM
                                atm_strike = base_strike  # Default fallback
                                
                                try:
                                    # Calculate synthetic future for each strike
                                    strikes_data = []
                                    for test_strike in strike_range:
                                        # Get call price
                                        query = f'''
                                            SELECT close/100.0 as price
                                            FROM nifty_call
                                            WHERE DATE(date) = '{start_date}'
                                            AND strike = {test_strike}
                                            AND time >= (TIME_TO_SEC('{entry_time}'))
                                            ORDER BY time ASC
                                            LIMIT 1
                                        '''
                                        cursor.execute(query)
                                        call_result = cursor.fetchone()
                                        
                                        # Get put price  
                                        query = f'''
                                            SELECT close/100.0 as price
                                            FROM nifty_put
                                            WHERE DATE(date) = '{start_date}'
                                            AND strike = {test_strike}
                                            AND time >= (TIME_TO_SEC('{entry_time}'))
                                            ORDER BY time ASC
                                            LIMIT 1
                                        '''
                                        cursor.execute(query)
                                        put_result = cursor.fetchone()
                                        
                                        if call_result and put_result:
                                            call_price = call_result[0]
                                            put_price = put_result[0]
                                            synthetic_future = test_strike + call_price - put_price
                                            diff = abs(synthetic_future - underlying_price)
                                            strikes_data.append({
                                                'strike': test_strike,
                                                'diff': diff
                                            })
                                    
                                    if strikes_data:
                                        # Find strike with minimum difference
                                        best_strike = min(strikes_data, key=lambda x: x['diff'])
                                        atm_strike = best_strike['strike']
                                        print(f"Using synthetic future ATM: {atm_strike}")
                                except Exception as e:
                                    print(f"Error in synthetic future ATM calculation: {e}")
                                    atm_strike = round(underlying_price / 50) * 50"""
        
        # Replace the line
        content = content.replace(old_atm_line, new_atm_code)
        print("   Patched ATM calculation in MySQL section to use synthetic future")
    else:
        print("   Warning: Could not find ATM calculation line to patch")
    
    # Write back the patched content
    with open(util_file, 'w') as f:
        f.write(content)

def run_legacy_backtest():
    """Run the legacy backtester with MySQL."""
    print("\n3. Running legacy backtester with MySQL...")
    
    output_file = "legacy_output_synthetic_atm.xlsx"
    
    cmd = [
        "python3", "bt/archive/backtester_stable/BTRUN/BTRunPortfolio.py",
        "-i", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "-sd", "01_04_2025",
        "-ed", "01_04_2025",
        "-o", output_file
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"   Error running legacy backtester: {result.stderr}")
        return None
    
    print(f"   Legacy output saved to: {output_file}")
    return output_file

def run_heavydb_backtest():
    """Run the new HeavyDB backtester."""
    print("\n4. Running HeavyDB backtester...")
    
    output_file = "heavydb_output_synthetic_atm.xlsx"
    
    cmd = [
        "python3", "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
        "--output-path", output_file,
        "--start-date", "20250401",
        "--end-date", "20250401"
    ]
    
    env = os.environ.copy()
    env['PYTHONPATH'] = '/srv/samba/shared'
    
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    
    if result.returncode != 0:
        print(f"   Error running HeavyDB backtester: {result.stderr}")
        return None
    
    print(f"   HeavyDB output saved to: {output_file}")
    return output_file

def compare_outputs(legacy_file, heavydb_file):
    """Compare outputs from both systems."""
    print("\n5. Comparing outputs...")
    
    # Read transaction sheets
    legacy_xl = pd.ExcelFile(legacy_file)
    heavydb_xl = pd.ExcelFile(heavydb_file)
    
    # Find transaction sheet names
    legacy_trans_sheet = [s for s in legacy_xl.sheet_names if 'Trans' in s and 'PORTFOLIO' not in s][0]
    heavydb_trans_sheet = [s for s in heavydb_xl.sheet_names if 'Trans' in s and 'PORTFOLIO' not in s and 'RS,916' in s][0]
    
    legacy_trans = pd.read_excel(legacy_file, sheet_name=legacy_trans_sheet)
    heavydb_trans = pd.read_excel(heavydb_file, sheet_name=heavydb_trans_sheet)
    
    print(f"\n   Legacy trades: {len(legacy_trans)}")
    print(f"   HeavyDB trades: {len(heavydb_trans)}")
    
    # Compare key fields
    print("\n   Trade Comparison:")
    print("   " + "-" * 80)
    
    # Map column names
    col_mapping = {
        'ID': 'leg_id',
        'Strike': 'strike',
        'CE/PE': 'instrument_type',
        'Trade': 'side',
        'Entry at': 'entry_price',
        'Exit at': 'exit_price',
        'PNL': 'pnl',
        'Net PNL': 'netPnlAfterExpenses'
    }
    
    for i in range(min(len(legacy_trans), len(heavydb_trans))):
        print(f"\n   Trade {i+1}:")
        
        # Legacy trade
        l_trade = legacy_trans.iloc[i]
        print(f"   Legacy:  {l_trade.get('ID', 'N/A')} | Strike: {l_trade.get('Strike', 'N/A')} | "
              f"{l_trade.get('CE/PE', 'N/A')} {l_trade.get('Trade', 'N/A')} | "
              f"Entry: {l_trade.get('Entry at', 'N/A'):.2f} | Exit: {l_trade.get('Exit at', 'N/A'):.2f} | "
              f"P&L: {l_trade.get('PNL', 0):.2f}")
        
        # HeavyDB trade
        h_trade = heavydb_trans.iloc[i]
        print(f"   HeavyDB: {h_trade.get('leg_id', 'N/A')} | Strike: {h_trade.get('strike', 'N/A')} | "
              f"{h_trade.get('instrument_type', 'N/A')} {h_trade.get('side', 'N/A')} | "
              f"Entry: {h_trade.get('entry_price', 'N/A'):.2f} | Exit: {h_trade.get('exit_price', 'N/A'):.2f} | "
              f"P&L: {h_trade.get('pnl', 0):.2f}")
    
    # Total P&L comparison
    legacy_total_pnl = legacy_trans['PNL'].sum() if 'PNL' in legacy_trans else 0
    heavydb_total_pnl = heavydb_trans['pnl'].sum() if 'pnl' in heavydb_trans else 0
    
    print(f"\n   Total P&L:")
    print(f"   Legacy:  {legacy_total_pnl:.2f}")
    print(f"   HeavyDB: {heavydb_total_pnl:.2f}")
    print(f"   Difference: {abs(legacy_total_pnl - heavydb_total_pnl):.2f}")

def main():
    """Main execution."""
    print("="*80)
    print("Complete Parity Test: Legacy (MySQL) vs HeavyDB")
    print("="*80)
    
    # Step 1: Update portfolio dates
    portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
    update_portfolio_dates(portfolio_file, "01_04_2025", "01_04_2025")
    
    # Step 2: Patch legacy ATM calculation
    patch_legacy_atm_calculation()
    
    # Step 3: Run legacy backtest
    legacy_output = run_legacy_backtest()
    if not legacy_output:
        print("\nFailed to run legacy backtest")
        return
    
    # Step 4: Run HeavyDB backtest  
    heavydb_output = run_heavydb_backtest()
    if not heavydb_output:
        print("\nFailed to run HeavyDB backtest")
        return
    
    # Step 5: Compare outputs
    if os.path.exists(legacy_output) and os.path.exists(heavydb_output):
        compare_outputs(legacy_output, heavydb_output)
    else:
        print("\nError: Output files not found for comparison")

if __name__ == "__main__":
    main() 