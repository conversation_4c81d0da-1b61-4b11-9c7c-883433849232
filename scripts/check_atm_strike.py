#!/usr/bin/env python3
"""Check what the ATM strike should be for a given date/time."""

import mysql.connector
from datetime import datetime
from heavydb import connect

def check_atm_strike_mysql(date_str="240103", time_str="91600"):
    """Check ATM strike using MySQL data."""
    
    conn = mysql.connector.connect(
        host='localhost',
        user='mahesh',
        password='mahesh_123',
        database='historicaldb'
    )
    cursor = conn.cursor()
    
    # Get spot price
    cursor.execute("""
        SELECT close FROM nifty_cash 
        WHERE date = %s AND time = %s
    """, (date_str, time_str))
    spot_result = cursor.fetchone()
    
    if spot_result:
        spot = spot_result[0]
        print(f"Spot price at {date_str} {time_str}: {spot}")
        
        # Calculate simple ATM
        simple_atm = round(spot / 50) * 50
        print(f"Simple ATM (round to 50): {simple_atm}")
        
        # Check for synthetic future ATM
        cursor.execute("""
            SELECT nc.strike, nc.close as ce_close, np.close as pe_close,
                   nc.strike + nc.close - np.close as synthetic_future
            FROM nifty_call nc
            INNER JOIN nifty_put np ON nc.strike = np.strike 
                AND nc.date = np.date AND nc.time = np.time
            WHERE nc.date = %s AND nc.time = %s
                AND nc.close IS NOT NULL AND np.close IS NOT NULL
            ORDER BY ABS(nc.strike + nc.close - np.close - %s)
            LIMIT 5
        """, (date_str, time_str, spot))
        
        print("\nSynthetic future calculations (top 5):")
        print("Strike | CE Close | PE Close | Synthetic Future | Diff from Spot")
        print("-" * 70)
        
        results = cursor.fetchall()
        for strike, ce_close, pe_close, synthetic_future in results:
            diff = abs(synthetic_future - spot)
            print(f"{strike:6} | {ce_close:8.2f} | {pe_close:8.2f} | {synthetic_future:16.2f} | {diff:14.2f}")
        
        if results:
            best_strike = results[0][0]
            print(f"\nSynthetic Future ATM: {best_strike}")
    else:
        print(f"No spot data found for {date_str} {time_str}")
    
    conn.close()

def check_atm_strike_heavydb():
    """Check ATM strike in HeavyDB."""
    try:
        conn = connect(host='127.0.0.1', port=6274, user='admin', password='HyperInteractive', dbname='heavyai')
        cursor = conn.cursor()
        
        # Get data for the test date
        query = """
        SELECT 
            trade_date,
            trade_time,
            expiry_date,
            strike,
            ce_close,
            pe_close,
            ce_volume,
            pe_volume
        FROM nifty_greeks 
        WHERE trade_date = DATE '2024-01-03' 
          AND trade_time = TIME '09:16:00'
        ORDER BY strike
        LIMIT 10
        """
        cursor.execute(query)
        results = cursor.fetchall()
        
        if results:
            print("\nHeavyDB Option Chain (first 10 strikes):")
            print("Strike | CE Close | PE Close | CE Volume | PE Volume")
            print("-" * 60)
            
            for row in results:
                trade_date, trade_time, expiry_date, strike, ce_close, pe_close, ce_volume, pe_volume = row
                print(f"{strike:6} | {ce_close:8.2f} | {pe_close:8.2f} | {ce_volume:9.0f} | {pe_volume:9.0f}")
            
            # Calculate synthetic future ATM
            print("\nCalculating synthetic future ATM...")
            cursor.execute("""
                WITH option_data AS (
                    SELECT 
                        strike,
                        ce_close,
                        pe_close,
                        strike + ce_close - pe_close as synthetic_future
                    FROM nifty_greeks
                    WHERE trade_date = DATE '2024-01-03'
                      AND trade_time = TIME '09:16:00'
                      AND ce_close IS NOT NULL 
                      AND pe_close IS NOT NULL
                )
                SELECT 
                    strike,
                    ce_close,
                    pe_close,
                    synthetic_future,
                    ABS(synthetic_future - (
                        SELECT AVG(synthetic_future) 
                        FROM option_data
                    )) as diff_from_avg
                FROM option_data
                ORDER BY diff_from_avg
                LIMIT 5
            """)
            
            print("\nSynthetic future calculations (top 5):")
            print("Strike | CE Close | PE Close | Synthetic Future | Diff from Avg")
            print("-" * 70)
            
            for row in cursor.fetchall():
                strike, ce_close, pe_close, synthetic_future, diff = row
                print(f"{strike:6} | {ce_close:8.2f} | {pe_close:8.2f} | {synthetic_future:16.2f} | {diff:14.2f}")
            
            if cursor.rowcount > 0:
                best_strike = cursor.fetchone()[0]
                print(f"\nSynthetic Future ATM: {best_strike}")
        else:
            print("\nNo data found in HeavyDB for 2024-01-03 09:16:00")
            
    except Exception as e:
        print(f"\nError connecting to HeavyDB: {e}")
        print("Please check if the nifty_greeks table exists and has the correct schema")
        print("Expected columns: trade_date, trade_time, expiry_date, strike, ce_close, pe_close, ce_volume, pe_volume")

if __name__ == "__main__":
    print("Checking ATM strike for January 3, 2024 at 09:16:00")
    print("="*70)
    
    # Check MySQL
    check_atm_strike_mysql()
    
    # Check HeavyDB
    check_atm_strike_heavydb() 