#!/bin/bash

# SSH Tunnel to access legacy backtesting service
# This creates a local port forwarding to the remote internal service

echo "==================================="
echo "Legacy Service SSH Tunnel Setup"
echo "==================================="

# Configuration
REMOTE_HOST="***************"  # Windows machine with the service
REMOTE_PORT="5000"              # Service port
LOCAL_PORT="5001"               # Local port to forward to
SSH_USER="your_username"        # Your Windows username
SSH_HOST="your_gateway_host"    # Public-facing SSH gateway

# Option 1: Direct SSH tunnel (if you have SSH access to the Windows machine)
echo "Option 1: Direct SSH tunnel to Windows machine"
echo "ssh -L ${LOCAL_PORT}:localhost:${REMOTE_PORT} ${SSH_USER}@${REMOTE_HOST}"

# Option 2: Jump through a gateway server
echo ""
echo "Option 2: SSH tunnel through gateway"
echo "ssh -L ${LOCAL_PORT}:${REMOTE_HOST}:${REMOTE_PORT} ${SSH_USER}@${SSH_HOST}"

# Option 3: Using autossh for persistent tunnel
echo ""
echo "Option 3: Persistent tunnel with autossh"
echo "autossh -M 0 -f -N -L ${LOCAL_PORT}:${REMOTE_HOST}:${REMOTE_PORT} ${SSH_USER}@${SSH_HOST}"

echo ""
echo "After establishing tunnel, access service at: http://localhost:${LOCAL_PORT}" 