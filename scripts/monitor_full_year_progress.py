#!/usr/bin/env python3
"""Monitor the full year 2024 TBS backtest progress"""
import os
import time
import psutil
from datetime import datetime

def monitor_progress():
    """Monitor the full year backtest progress"""
    output_file = "bt/backtester_stable/BTRUN/output/tbs_2024_full_year_sequential.xlsx"
    
    print("🚀 GPU-Optimized TBS Backtesting System - Full Year 2024")
    print("=" * 60)
    print(f"📊 Strategy: Time-Based Strategy (TBS)")
    print(f"📅 Period: January 1, 2024 - December 31, 2024 (365 days)")
    print(f"⚡ Mode: Sequential (1 worker) - Avoiding pickling issues")
    print(f"📁 Output: {output_file}")
    print("=" * 60)
    print()
    
    start_time = datetime.now()
    last_size = 0
    
    while True:
        try:
            # Check for running processes
            gpu_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_info']):
                try:
                    if 'BTRunPortfolio_GPU' in ' '.join(proc.info['cmdline'] or []):
                        gpu_processes.append({
                            'pid': proc.info['pid'],
                            'cpu': proc.info['cpu_percent'],
                            'memory': proc.info['memory_info'].rss / 1024 / 1024,  # MB
                            'cmdline': ' '.join(proc.info['cmdline'])
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Check output file
            current_time = datetime.now()
            elapsed = current_time - start_time
            
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                mod_time = datetime.fromtimestamp(os.path.getmtime(output_file))
                
                # Calculate progress indicators
                size_change = file_size - last_size
                last_size = file_size
                
                print(f"✅ [{current_time.strftime('%H:%M:%S')}] Output file found!")
                print(f"   📁 Size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
                print(f"   📈 Size change: +{size_change:,} bytes")
                print(f"   🕒 Modified: {mod_time.strftime('%H:%M:%S')}")
                print(f"   ⏱️  Elapsed: {elapsed}")
                
                if gpu_processes:
                    for proc in gpu_processes:
                        print(f"   🖥️  Process {proc['pid']}: CPU {proc['cpu']:.1f}%, Memory {proc['memory']:.1f} MB")
                
                print("   🎉 BACKTEST COMPLETED!")
                break
                
            else:
                print(f"⏳ [{current_time.strftime('%H:%M:%S')}] Processing... | Elapsed: {elapsed}")
                
                if gpu_processes:
                    print("✅ GPU processes found:")
                    for proc in gpu_processes:
                        print(f"   🖥️  PID {proc['pid']}: CPU {proc['cpu']:.1f}%, Memory {proc['memory']:.1f} MB")
                        # Show truncated command line
                        cmd_short = proc['cmdline'][:100] + "..." if len(proc['cmdline']) > 100 else proc['cmdline']
                        print(f"      Command: {cmd_short}")
                else:
                    print("❌ No GPU processes found - backtest may have completed or failed")
                
                print("-" * 60)
            
            time.sleep(10)  # Check every 10 seconds
            
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"❌ Error during monitoring: {e}")
            time.sleep(5)

if __name__ == "__main__":
    monitor_progress() 