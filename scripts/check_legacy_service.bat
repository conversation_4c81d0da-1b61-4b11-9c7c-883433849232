@echo off
echo ========================================
echo Checking Backtesting Service Status
echo ========================================
echo.

echo 1. Checking if port 5000 is listening:
netstat -an | findstr :5000
echo.

echo 2. Checking for Python processes:
tasklist | findstr python
echo.

echo 3. Checking for specific service:
sc query | findstr -i "backtest"
echo.

echo 4. Checking Windows Firewall rules:
netsh advfirewall firewall show rule name=all | findstr "5000"
echo.

echo ========================================
echo If port 5000 is not LISTENING, the service needs to be started.
echo ========================================
pause 