#!/usr/bin/env python3
"""Check sheet names in Excel file"""

import pandas as pd
import sys

if len(sys.argv) < 2:
    print("Usage: python3 check_excel_sheets.py <excel_file>")
    sys.exit(1)

excel_file = sys.argv[1]

try:
    # Get all sheet names
    xl_file = pd.ExcelFile(excel_file)
    sheet_names = xl_file.sheet_names
    
    print(f"Sheets in {excel_file}:")
    for i, sheet in enumerate(sheet_names):
        print(f"  {i+1}. {sheet}")
        
    # Try to read the first few rows of each sheet
    print("\nSheet previews:")
    for sheet in sheet_names:
        print(f"\n{sheet}:")
        try:
            df = pd.read_excel(excel_file, sheet_name=sheet)
            print(f"  Shape: {df.shape}")
            if not df.empty:
                print(f"  Columns: {list(df.columns)}")
                print(f"  First row:")
                print(f"    {df.iloc[0].to_dict()}")
        except Exception as e:
            print(f"  Error reading sheet: {e}")
            
except Exception as e:
    print(f"Error: {e}") 