#!/usr/bin/env python3
"""Quick data check for April 2025 backtesting period"""

import mysql.connector
from datetime import datetime

# Database configurations
MYSQL_CONFIG = {
    "host": "************",
    "user": "mahesh", 
    "password": "mahesh_123",
    "database": "historicaldb"
}

HEAVYDB_CONFIG = {
    "host": "127.0.0.1",
    "port": 6274,
    "user": "admin",
    "password": "HyperInteractive",
    "dbname": "heavyai"
}

def main():
    print("="*60)
    print("Quick Data Check - April 2025")
    print("="*60)
    
    # Check MySQL
    print("\n1. MySQL Data (Legacy):")
    try:
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()
        
        # Check April 2025 data
        cursor.execute("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT strike) as unique_strikes,
                MIN(close/100.0) as min_price,
                MAX(close/100.0) as max_price
            FROM nifty_call
            WHERE date >= '2025-04-01' AND date <= '2025-04-11'
        """)
        
        result = cursor.fetchone()
        print(f"   Call Options (Apr 1-11, 2025):")
        print(f"   - Records: {result[0]:,}")
        print(f"   - Unique strikes: {result[1]}")
        print(f"   - Price range: {result[2]:.2f} to {result[3]:.2f}")
        
        # Check specific date
        cursor.execute("""
            SELECT COUNT(*), MIN(time), MAX(time)
            FROM nifty_call
            WHERE DATE(date) = '2025-04-01'
        """)
        
        result = cursor.fetchone()
        if result[0] > 0:
            print(f"\n   April 1, 2025:")
            print(f"   - Records: {result[0]:,}")
            print(f"   - Time range: {result[1]} to {result[2]} (in seconds)")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"   Error: {e}")
    
    # Check HeavyDB
    print("\n2. HeavyDB Data (New):")
    try:
        from heavydb import connect
    except ImportError:
        import pymapd
        connect = pymapd.connect
    
    try:
        conn = connect(**HEAVYDB_CONFIG)
        cursor = conn.cursor()
        
        # Check if table exists and has data
        cursor.execute("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(DISTINCT strike) as unique_strikes,
                MIN(ce_close) as min_price,
                MAX(ce_close) as max_price
            FROM nifty_option_chain
            WHERE trade_date >= DATE '2025-04-01' 
            AND trade_date <= DATE '2025-04-11'
            AND ce_symbol IS NOT NULL
        """)
        
        result = cursor.fetchone()
        if result and result[0] > 0:
            print(f"   Call Options (Apr 1-11, 2025):")
            print(f"   - Records: {result[0]:,}")
            print(f"   - Unique strikes: {result[1]}")
            print(f"   - Price range: {result[2]:.2f} to {result[3]:.2f}")
        else:
            print("   ⚠ No data found for April 2025")
            
            # Check overall data availability
            cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM nifty_option_chain")
            result = cursor.fetchone()
            if result and result[0]:
                print(f"   Available date range: {result[0]} to {result[1]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n" + "="*60)
    print("Summary:")
    print("- MySQL has data for April 2025")
    print("- Check if HeavyDB has been loaded with April 2025 data")
    print("- If not, ETL process may need to be run")
    print("="*60)

if __name__ == "__main__":
    main() 