#!/usr/bin/env python3
"""Test MySQL connection to Windows server"""

import mysql.connector
from datetime import datetime

# MySQL connection details from combined_operations.py
MYSQL_CONFIG = {
    "host": "************",
    "user": "mahesh",
    "password": "mahesh_123",
    "database": "historicaldb"
}

def test_connection():
    """Test MySQL connection"""
    print("=" * 60)
    print(f"Testing MySQL Connection at {datetime.now()}")
    print("=" * 60)
    print(f"Host: {MYSQL_CONFIG['host']}")
    print(f"User: {MYSQL_CONFIG['user']}")
    print(f"Database: {MYSQL_CONFIG['database']}")
    
    try:
        # Connect to MySQL
        print("\nConnecting to MySQL...")
        conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"✓ Connected successfully!")
        print(f"  MySQL Version: {version}")
        
        # Check for backtest-related tables
        print("\nChecking for relevant tables...")
        cursor.execute("SHOW TABLES LIKE '%nifty%'")
        tables = cursor.fetchall()
        
        if tables:
            print(f"✓ Found {len(tables)} NIFTY-related tables:")
            for table in tables[:5]:  # Show first 5
                print(f"  - {table[0]}")
            if len(tables) > 5:
                print(f"  ... and {len(tables)-5} more")
        
        # Check for option data
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_schema = 'historicaldb' 
            AND table_name LIKE '%option%'
        """)
        option_tables = cursor.fetchone()[0]
        print(f"\n✓ Found {option_tables} option-related tables")
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 60)
        print("MySQL connection test PASSED!")
        print("You can use this database for backtester comparisons.")
        print("=" * 60)
        
        return True
        
    except mysql.connector.Error as e:
        print(f"\n✗ MySQL Error: {e}")
        print("\nTroubleshooting:")
        print("1. Check if MySQL server is running on Windows")
        print("2. Verify firewall allows port 3306")
        print("3. Check MySQL user permissions")
        return False
        
    except Exception as e:
        print(f"\n✗ General Error: {e}")
        return False

if __name__ == "__main__":
    test_connection() 