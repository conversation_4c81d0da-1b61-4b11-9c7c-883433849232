#!/usr/bin/env python3
"""Check the actual results and date range in the backtest output"""

import pandas as pd
import os
from datetime import datetime

def check_backtest_results():
    """Check the backtest results and validate date range"""
    
    output_file = "bt/backtester_stable/BTRUN/output/tbs_2024_full_year_corrected.xlsx"
    
    print("🔍 Analyzing Backtest Results")
    print("=" * 50)
    
    if not os.path.exists(output_file):
        print(f"❌ Output file not found: {output_file}")
        return
    
    file_size = os.path.getsize(output_file)
    print(f"📁 File: {output_file}")
    print(f"📦 Size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
    print()
    
    try:
        # Read Excel file and check sheets
        with pd.ExcelFile(output_file) as xls:
            sheet_names = xls.sheet_names
            print(f"📋 Sheets found: {len(sheet_names)}")
            for sheet in sheet_names:
                print(f"   - {sheet}")
            print()
            
            # Check Metrics sheet for date range
            if 'Metrics' in sheet_names:
                metrics_df = pd.read_excel(xls, sheet_name='Metrics')
                print("📊 Metrics Sheet Analysis:")
                print(f"   Rows: {len(metrics_df)}")
                
                # Look for date-related metrics
                for idx, row in metrics_df.iterrows():
                    if 'Date' in str(row.iloc[0]):
                        print(f"   {row.iloc[0]}: {row.iloc[1]}")
                    elif 'Trading Days' in str(row.iloc[0]):
                        print(f"   {row.iloc[0]}: {row.iloc[1]}")
                print()
            
            # Check Portfolio Trans sheet for actual trades
            portfolio_trans_sheets = [s for s in sheet_names if 'Trans' in s and 'PORTFOLIO' in s]
            if portfolio_trans_sheets:
                trans_sheet = portfolio_trans_sheets[0]
                trans_df = pd.read_excel(xls, sheet_name=trans_sheet)
                print(f"📈 {trans_sheet} Analysis:")
                print(f"   Total trades: {len(trans_df)}")
                
                if len(trans_df) > 0:
                    # Check date columns
                    date_columns = [col for col in trans_df.columns if 'date' in col.lower() or 'time' in col.lower()]
                    print(f"   Date columns: {date_columns}")
                    
                    # Show first few trades
                    print("   First 5 trades:")
                    for idx, row in trans_df.head().iterrows():
                        trade_info = []
                        for col in trans_df.columns[:6]:  # First 6 columns
                            trade_info.append(f"{col}: {row[col]}")
                        print(f"     Trade {idx+1}: {', '.join(trade_info[:3])}")
                    
                    # Check date range
                    if date_columns:
                        date_col = date_columns[0]
                        dates = pd.to_datetime(trans_df[date_col], errors='coerce').dropna()
                        if len(dates) > 0:
                            min_date = dates.min()
                            max_date = dates.max()
                            print(f"   Date range: {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}")
                            
                            # Calculate actual trading days
                            unique_dates = dates.dt.date.nunique()
                            print(f"   Unique trading days: {unique_dates}")
                print()
            
            # Check if we have the expected full year data
            expected_trading_days = 250  # Approximate trading days in a year
            if 'Metrics' in sheet_names:
                # Try to find trading days metric
                trading_days_found = False
                for idx, row in metrics_df.iterrows():
                    if 'Trading Days' in str(row.iloc[0]):
                        actual_days = row.iloc[1]
                        print(f"🎯 Trading Days Analysis:")
                        print(f"   Expected (full year): ~{expected_trading_days} days")
                        print(f"   Actual: {actual_days} days")
                        
                        if actual_days < 10:
                            print("   ⚠️ Very few trading days - likely wrong date range!")
                        elif actual_days < 100:
                            print("   ⚠️ Partial year data - check input file dates")
                        else:
                            print("   ✅ Full year data detected!")
                        trading_days_found = True
                        break
                
                if not trading_days_found:
                    print("   ❓ Could not find trading days metric")
    
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        print("   This might indicate a corrupted or incomplete file")

def check_input_file_dates():
    """Check the dates in the input file"""
    
    input_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_full_year.xlsx"
    
    print("\n🔍 Checking Input File Dates")
    print("=" * 50)
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return
    
    try:
        with pd.ExcelFile(input_file) as xls:
            if 'PortfolioSetting' in xls.sheet_names:
                portfolio_df = pd.read_excel(xls, sheet_name='PortfolioSetting')
                print("📅 PortfolioSetting dates:")
                
                for idx, row in portfolio_df.iterrows():
                    if 'Date' in str(row.iloc[0]):
                        print(f"   {row.iloc[0]}: {row.iloc[1]}")
    
    except Exception as e:
        print(f"❌ Error reading input file: {e}")

if __name__ == "__main__":
    check_backtest_results()
    check_input_file_dates() 