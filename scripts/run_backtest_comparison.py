#!/usr/bin/env python3
"""
Run both legacy and refactored backtesters for comparison
Handles issues and provides detailed output
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import shutil

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configuration
TEST_CONFIG = {
    "portfolio_excel": "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx",
    "strategy_excel": "bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx",
    "start_date": "20250401",
    "end_date": "20250411",
    "legacy_output": "legacy_backtest_output.xlsx",
    "new_output": "new_backtest_output.xlsx",
    "golden_output": "bt/backtester_stable/BTRUN/input_sheets/golden_output.xlsx"
}

def check_input_files():
    """Verify all input files exist"""
    print("\n1. Checking Input Files:")
    print("-" * 60)
    
    files_ok = True
    for key, path in TEST_CONFIG.items():
        if key.endswith('_excel') or key == 'golden_output':
            if os.path.exists(path):
                print(f"  ✓ {key}: {path}")
            else:
                print(f"  ✗ {key}: {path} NOT FOUND")
                files_ok = False
    
    return files_ok

def run_legacy_backtester():
    """Run the legacy backtester locally (not via HTTP service)"""
    print("\n2. Running Legacy Backtester (Archive Code):")
    print("-" * 60)
    
    # Change to legacy directory
    legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
    
    if not os.path.exists(legacy_dir):
        print("  ✗ Legacy directory not found")
        return False
    
    # Prepare legacy command
    cmd = [
        sys.executable,  # Use current Python interpreter
        "BTRunPortfolio.py",
        "--portfolio", os.path.abspath(TEST_CONFIG["portfolio_excel"]),
        "--strategy", os.path.abspath(TEST_CONFIG["strategy_excel"]),
        "--start", TEST_CONFIG["start_date"],
        "--end", TEST_CONFIG["end_date"],
        "--output", os.path.abspath(TEST_CONFIG["legacy_output"])
    ]
    
    print(f"  Running from: {legacy_dir}")
    print(f"  Command: {' '.join(cmd)}")
    
    try:
        # Run legacy backtester
        result = subprocess.run(
            cmd,
            cwd=legacy_dir,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            print("  ✓ Legacy backtester completed successfully")
            if os.path.exists(TEST_CONFIG["legacy_output"]):
                print(f"  ✓ Output saved to: {TEST_CONFIG['legacy_output']}")
                return True
            else:
                print("  ⚠ Output file not created")
                # Check if output was created in legacy directory
                legacy_output = os.path.join(legacy_dir, os.path.basename(TEST_CONFIG["legacy_output"]))
                if os.path.exists(legacy_output):
                    shutil.copy2(legacy_output, TEST_CONFIG["legacy_output"])
                    print(f"  ✓ Output copied from legacy directory")
                    return True
                return False
        else:
            print(f"  ✗ Legacy backtester failed with return code: {result.returncode}")
            print("  STDOUT:", result.stdout[:500] if result.stdout else "None")
            print("  STDERR:", result.stderr[:500] if result.stderr else "None")
            
            # Try to fix common issues
            if "ModuleNotFoundError" in result.stderr:
                print("\n  Attempting to fix module imports...")
                return run_legacy_with_pythonpath()
            
            return False
            
    except subprocess.TimeoutExpired:
        print("  ✗ Legacy backtester timed out")
        return False
    except Exception as e:
        print(f"  ✗ Error running legacy backtester: {e}")
        return False

def run_legacy_with_pythonpath():
    """Run legacy backtester with proper PYTHONPATH"""
    print("\n  Retrying with PYTHONPATH fix:")
    
    legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
    
    # Set up environment with PYTHONPATH
    env = os.environ.copy()
    env['PYTHONPATH'] = f"{os.path.abspath('bt/archive/backtester_stable')}:{os.path.abspath('.')}"
    
    cmd = [
        sys.executable,
        "-m", "BTRUN.BTRunPortfolio",  # Run as module
        "--portfolio", os.path.abspath(TEST_CONFIG["portfolio_excel"]),
        "--strategy", os.path.abspath(TEST_CONFIG["strategy_excel"]),
        "--start", TEST_CONFIG["start_date"],
        "--end", TEST_CONFIG["end_date"],
        "--output", os.path.abspath(TEST_CONFIG["legacy_output"])
    ]
    
    try:
        result = subprocess.run(
            cmd,
            cwd=os.path.abspath("bt/archive/backtester_stable"),
            env=env,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("  ✓ Legacy backtester completed with PYTHONPATH fix")
            return True
        else:
            print(f"  ✗ Still failed: {result.stderr[:500]}")
            return False
            
    except Exception as e:
        print(f"  ✗ Error with PYTHONPATH fix: {e}")
        return False

def run_new_backtester():
    """Run the new refactored backtester"""
    print("\n3. Running New Backtester (Refactored Code):")
    print("-" * 60)
    
    # Import and run directly
    try:
        from bt.backtester_stable.BTRUN.BTRunPortfolio_GPU import main as run_gpu_backtester
        
        # Set up arguments
        sys.argv = [
            'BTRunPortfolio_GPU.py',
            '--legacy-excel',
            '--portfolio-excel', TEST_CONFIG["portfolio_excel"],
            '--output-path', TEST_CONFIG["new_output"],
            '--start-date', TEST_CONFIG["start_date"],
            '--end-date', TEST_CONFIG["end_date"]
        ]
        
        print(f"  Arguments: {' '.join(sys.argv[1:])}")
        
        # Run the backtester
        run_gpu_backtester()
        
        if os.path.exists(TEST_CONFIG["new_output"]):
            print(f"  ✓ New backtester completed")
            print(f"  ✓ Output saved to: {TEST_CONFIG['new_output']}")
            return True
        else:
            print("  ⚠ Output file not created")
            
            # Check common output locations
            possible_outputs = [
                "bt/backtester_stable/BTRUN/output/new_test_output.xlsx",
                "output/new_backtest_output.xlsx",
                "new_test_output.xlsx"
            ]
            
            for path in possible_outputs:
                if os.path.exists(path):
                    shutil.copy2(path, TEST_CONFIG["new_output"])
                    print(f"  ✓ Output found at {path} and copied")
                    return True
                    
            return False
            
    except ImportError as e:
        print(f"  ✗ Import error: {e}")
        print("  Attempting to run as subprocess...")
        return run_new_as_subprocess()
    except Exception as e:
        print(f"  ✗ Error running new backtester: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_new_as_subprocess():
    """Run new backtester as subprocess"""
    print("\n  Running as subprocess:")
    
    cmd = [
        sys.executable,
        "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", TEST_CONFIG["portfolio_excel"],
        "--output-path", TEST_CONFIG["new_output"],
        "--start-date", TEST_CONFIG["start_date"],
        "--end-date", TEST_CONFIG["end_date"]
    ]
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("  ✓ New backtester completed as subprocess")
            return os.path.exists(TEST_CONFIG["new_output"])
        else:
            print(f"  ✗ Failed with return code: {result.returncode}")
            print("  STDERR:", result.stderr[:500] if result.stderr else "None")
            return False
            
    except Exception as e:
        print(f"  ✗ Subprocess error: {e}")
        return False

def compare_outputs():
    """Compare outputs from both backtesters"""
    print("\n4. Comparing Outputs:")
    print("-" * 60)
    
    outputs = {
        "Legacy": TEST_CONFIG["legacy_output"],
        "New": TEST_CONFIG["new_output"],
        "Golden": TEST_CONFIG["golden_output"]
    }
    
    # Check which outputs exist
    available = {}
    for name, path in outputs.items():
        if os.path.exists(path):
            available[name] = path
            print(f"  ✓ {name} output found: {path}")
        else:
            print(f"  ✗ {name} output not found: {path}")
    
    if len(available) < 2:
        print("\n  ⚠ Need at least 2 outputs to compare")
        return
    
    # Load and compare
    print("\n  Loading Excel files...")
    dataframes = {}
    
    for name, path in available.items():
        try:
            xl_file = pd.ExcelFile(path)
            dataframes[name] = {
                'sheets': xl_file.sheet_names,
                'data': {sheet: pd.read_excel(xl_file, sheet_name=sheet) 
                        for sheet in xl_file.sheet_names[:3]}  # First 3 sheets
            }
            print(f"  ✓ Loaded {name}: {len(xl_file.sheet_names)} sheets")
        except Exception as e:
            print(f"  ✗ Error loading {name}: {e}")
    
    # Basic comparison
    if len(dataframes) >= 2:
        names = list(dataframes.keys())
        print(f"\n  Comparing {names[0]} vs {names[1]}:")
        
        # Compare sheet names
        sheets1 = set(dataframes[names[0]]['sheets'])
        sheets2 = set(dataframes[names[1]]['sheets'])
        
        if sheets1 == sheets2:
            print(f"  ✓ Same sheets in both outputs")
        else:
            print(f"  ⚠ Different sheets:")
            print(f"    Only in {names[0]}: {sheets1 - sheets2}")
            print(f"    Only in {names[1]}: {sheets2 - sheets1}")
        
        # Compare first sheet data
        common_sheets = sheets1.intersection(sheets2)
        if common_sheets:
            first_sheet = list(common_sheets)[0]
            df1 = dataframes[names[0]]['data'].get(first_sheet)
            df2 = dataframes[names[1]]['data'].get(first_sheet)
            
            if df1 is not None and df2 is not None:
                print(f"\n  Sheet '{first_sheet}' comparison:")
                print(f"    {names[0]}: {df1.shape[0]} rows, {df1.shape[1]} columns")
                print(f"    {names[1]}: {df2.shape[0]} rows, {df2.shape[1]} columns")

def main():
    """Main execution"""
    print("="*80)
    print("Backtester Comparison: Legacy vs Refactored")
    print(f"Time: {datetime.now()}")
    print("="*80)
    
    # Check input files
    if not check_input_files():
        print("\n✗ Cannot proceed without all input files")
        return
    
    # Run both backtesters
    legacy_success = run_legacy_backtester()
    new_success = run_new_backtester()
    
    # Compare outputs
    compare_outputs()
    
    # Summary
    print("\n" + "="*80)
    print("Summary:")
    print("="*80)
    print(f"Legacy Backtester: {'✓ Success' if legacy_success else '✗ Failed'}")
    print(f"New Backtester: {'✓ Success' if new_success else '✗ Failed'}")
    
    if legacy_success and new_success:
        print("\n✓ Both backtesters completed - ready for detailed comparison")
    elif new_success:
        print("\n⚠ Only new backtester succeeded - compare with golden output")
    elif legacy_success:
        print("\n⚠ Only legacy backtester succeeded")
    else:
        print("\n✗ Both backtesters failed - check error messages above")

if __name__ == "__main__":
    main() 