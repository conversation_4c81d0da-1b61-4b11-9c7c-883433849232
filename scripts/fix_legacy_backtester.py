#!/usr/bin/env python3
"""
Fix the legacy backtester by creating a mock service locally
"""

import os
import sys
import subprocess
import json
from datetime import datetime
import threading
from flask import Flask, request, jsonify
import time

# Add legacy path to import from it
sys.path.insert(0, os.path.abspath('bt/archive/backtester_stable'))

def create_mock_backtest_service():
    """Create a simple mock service that responds to backtest requests"""
    app = Flask(__name__)
    
    @app.route('/backtest/start', methods=['POST'])
    def mock_backtest():
        """Mock endpoint that simulates a backtest response"""
        data = request.json
        
        # Create a mock response that matches what the legacy code expects
        mock_response = {
            "strategies": {
                "orders": [
                    {
                        "entry_time": "Mon, 01 Apr 2025 09:16:00 GMT",
                        "exit_time": "Mon, 01 Apr 2025 12:00:00 GMT",
                        "option_type": "PUT",
                        "qty": 50,
                        "entry_number": 1,
                        "strategy_name": "Test Strategy",
                        "side": "SELL",
                        "entry_price": 100.0,
                        "exit_price": 80.0,
                        "symbol": "NIFTY",
                        "strike": 23000,
                        "expiry": "250403",
                        "leg_id": "1",
                        "index_entry_price": 23050,
                        "index_exit_price": 23100,
                        "reason": "EndTime",
                        "stop_loss_entry_number": 0,
                        "take_profit_entry_number": 0,
                        "strategy_entry_number": 1,
                        "pnl": 1000.0
                    },
                    {
                        "entry_time": "Mon, 01 Apr 2025 09:16:00 GMT", 
                        "exit_time": "Mon, 01 Apr 2025 12:00:00 GMT",
                        "option_type": "CALL",
                        "qty": 50,
                        "entry_number": 1,
                        "strategy_name": "Test Strategy",
                        "side": "SELL", 
                        "entry_price": 100.0,
                        "exit_price": 90.0,
                        "symbol": "NIFTY",
                        "strike": 23000,
                        "expiry": "250403",
                        "leg_id": "2",
                        "index_entry_price": 23050,
                        "index_exit_price": 23100,
                        "reason": "EndTime",
                        "stop_loss_entry_number": 0,
                        "take_profit_entry_number": 0,
                        "strategy_entry_number": 1,
                        "pnl": 500.0
                    }
                ],
                "strategy_profits": {
                    "250401": {
                        "34560": 1500.0,  # 09:36:00 in seconds
                        "43200": 1500.0   # 12:00:00 in seconds
                    }
                },
                "strategy_losses": {
                    "250401": {}
                }
            }
        }
        
        return jsonify(mock_response)
    
    @app.route('/healthcheck', methods=['GET'])
    def healthcheck():
        return jsonify({"status": "ok", "service": "mock_backtest"})
    
    return app

def run_mock_service(port):
    """Run the mock service in a thread"""
    app = create_mock_backtest_service()
    app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)

def patch_legacy_config():
    """Patch the legacy config to use local mock service"""
    config_path = "bt/archive/backtester_stable/BTRUN/config.py"
    
    # Read current config
    with open(config_path, 'r') as f:
        content = f.read()
    
    # Check if already patched
    if "# PATCHED" in content:
        print("  ✓ Config already patched")
        return
    
    # Backup original
    backup_path = config_path + ".original"
    if not os.path.exists(backup_path):
        with open(backup_path, 'w') as f:
            f.write(content)
        print(f"  ✓ Backed up original config to: {backup_path}")
    
    # Patch the BT_URII to use local service
    patched_content = content.replace(
        'BT_URII = {',
        '# PATCHED\nBT_URII = {'
    ).replace(
        '"tick": "http://127.0.0.1:5000/backtest/start"',
        '"tick": "http://127.0.0.1:5555/backtest/start"'
    ).replace(
        '"minute": "http://127.0.0.1:5001/backtest/start"',
        '"minute": "http://127.0.0.1:5555/backtest/start"'
    )
    
    # Write patched config
    with open(config_path, 'w') as f:
        f.write(patched_content)
    
    print("  ✓ Patched config to use local mock service")

def run_legacy_with_mock():
    """Run legacy backtester with mock service"""
    print("\nStarting mock service...")
    
    # Start mock service in background thread
    service_thread = threading.Thread(target=run_mock_service, args=(5555,))
    service_thread.daemon = True
    service_thread.start()
    
    # Give service time to start
    time.sleep(2)
    
    # Test service
    import requests
    try:
        r = requests.get("http://127.0.0.1:5555/healthcheck")
        if r.status_code == 200:
            print("  ✓ Mock service running on port 5555")
        else:
            print("  ✗ Mock service not responding")
            return False
    except:
        print("  ✗ Failed to connect to mock service")
        return False
    
    # Now run the legacy backtester
    print("\nRunning legacy backtester with mock service...")
    
    legacy_dir = os.path.abspath("bt/archive/backtester_stable/BTRUN")
    
    # Setup input files
    input_sheets_dir = os.path.join(legacy_dir, "INPUT SHEETS")
    os.makedirs(input_sheets_dir, exist_ok=True)
    
    # Copy input file
    import shutil
    source_file = os.path.abspath("bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx")
    dest_file = os.path.join(input_sheets_dir, "INPUT PORTFOLIO.xlsx")
    
    if os.path.exists(source_file):
        shutil.copy2(source_file, dest_file)
        print(f"  ✓ Copied input file")
        
        # Also copy strategy files
        source_dir = os.path.dirname(source_file)
        for f in os.listdir(source_dir):
            if f.endswith('.xlsx') and 'tbs' in f.lower():
                shutil.copy2(os.path.join(source_dir, f), os.path.join(input_sheets_dir, f))
    
    # Run legacy backtester
    env = os.environ.copy()
    env['PYTHONPATH'] = f"{os.path.abspath('bt/archive/backtester_stable')}:{os.path.abspath('.')}"
    
    cmd = [sys.executable, os.path.join(legacy_dir, "BTRunPortfolio.py")]
    
    try:
        result = subprocess.run(
            cmd,
            cwd=legacy_dir,
            env=env,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print(f"  Return code: {result.returncode}")
        
        if result.stderr:
            print(f"  Errors: {result.stderr[:500]}")
        
        # Check for output
        trades_dir = os.path.join(legacy_dir, "Trades")
        if os.path.exists(trades_dir):
            output_files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')]
            if output_files:
                print(f"\n  ✓ Legacy backtester created {len(output_files)} output file(s):")
                for f in output_files:
                    print(f"    - {f}")
                return True
        
        return False
        
    except Exception as e:
        print(f"  ✗ Error: {e}")
        return False

def main():
    print("="*60)
    print("Fixing Legacy Backtester")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Check if Flask is available
    try:
        import flask
        print("  ✓ Flask is available")
    except ImportError:
        print("  ✗ Flask not installed. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "flask"])
        print("  ✓ Flask installed")
    
    # Patch the config
    print("\nPatching legacy config...")
    patch_legacy_config()
    
    # Run with mock service
    success = run_legacy_with_mock()
    
    print("\n" + "="*60)
    if success:
        print("✓ Legacy backtester successfully fixed and running!")
        print("  Output files are in: bt/archive/backtester_stable/BTRUN/Trades/")
    else:
        print("✗ Failed to fix legacy backtester")
        print("\nNote: The legacy backtester depends on external services.")
        print("This fix creates a mock service to simulate those dependencies.")
    print("="*60)

if __name__ == "__main__":
    main() 