#!/usr/bin/env python3
"""
Fix the input Excel configuration to have correct strike methods
"""

import pandas as pd
import openpyxl
from datetime import datetime
import shutil

def fix_leg_configuration():
    """Fix the leg parameter configuration"""
    print("="*60)
    print("Fixing Input Configuration")
    print(f"Time: {datetime.now()}")
    print("="*60)
    
    # Create backup
    strategy_file = "bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"
    backup_file = strategy_file.replace('.xlsx', '_backup.xlsx')
    shutil.copy2(strategy_file, backup_file)
    print(f"\n✓ Created backup: {backup_file}")
    
    # Load the Excel file
    wb = openpyxl.load_workbook(strategy_file)
    
    # Update LegParameter sheet
    ws = wb['LegParameter']
    
    print("\nUpdating LegParameter configuration:")
    
    # Find the header row
    header_row = 1
    headers = {}
    for col in range(1, ws.max_column + 1):
        cell_value = ws.cell(row=header_row, column=col).value
        if cell_value:
            headers[cell_value] = col
    
    # Get column indices
    strike_method_col = headers.get('StrikeMethod')
    strike_value_col = headers.get('StrikeValue')
    
    # Update rows (starting from row 2)
    for row in range(2, ws.max_row + 1):
        leg_id = ws.cell(row=row, column=headers['LegID']).value
        if leg_id in [3, 4]:  # Legs 3 and 4
            old_method = ws.cell(row=row, column=strike_method_col).value
            old_value = ws.cell(row=row, column=strike_value_col).value
            
            # Fix the configuration
            ws.cell(row=row, column=strike_method_col).value = 'otm2'
            ws.cell(row=row, column=strike_value_col).value = 0
            
            print(f"  Leg {leg_id}: Changed from '{old_method}' (value={old_value}) to 'otm2' (value=0)")
    
    # Save the file
    wb.save(strategy_file)
    print(f"\n✓ Updated strategy file: {strategy_file}")
    
def verify_configuration():
    """Verify the updated configuration"""
    print("\n" + "="*60)
    print("Verifying Configuration")
    print("="*60)
    
    strategy_file = "bt/backtester_stable/BTRUN/input_sheets/input_tbs_multi_legs.xlsx"
    leg_df = pd.read_excel(strategy_file, sheet_name='LegParameter')
    
    print("\nUpdated Leg Configuration:")
    for i, row in leg_df.iterrows():
        print(f"  Leg {row['LegID']}: {row['Instrument'].upper()} {row['Transaction'].upper()} {row['StrikeMethod'].upper()} (value={row['StrikeValue']})")
    
    # Check if configuration is correct
    issues = []
    if leg_df.iloc[0]['StrikeMethod'] != 'atm':
        issues.append("Leg 1 should be ATM")
    if leg_df.iloc[1]['StrikeMethod'] != 'atm':
        issues.append("Leg 2 should be ATM")
    if leg_df.iloc[2]['StrikeMethod'] != 'otm2':
        issues.append("Leg 3 should be OTM2")
    if leg_df.iloc[3]['StrikeMethod'] != 'otm2':
        issues.append("Leg 4 should be OTM2")
    
    if issues:
        print("\n✗ Issues found:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("\n✓ Configuration is correct!")

def create_atm_test_query():
    """Create a test query to check ATM calculation"""
    print("\n" + "="*60)
    print("ATM Calculation Test Query")
    print("="*60)
    
    query = """
    -- Test ATM calculation for 2024-12-31 at 9:16
    SELECT 
        trade_time,
        spot,
        atm_strike,
        strike,
        ce_close,
        pe_close,
        strike + ce_close - pe_close AS synthetic_future
    FROM nifty_option_chain
    WHERE trade_date = DATE '2024-12-31'
    AND trade_time = TIME '09:16:00'
    AND strike IN (23450, 23500, 23550, 23600, 23650)
    ORDER BY strike;
    """
    
    print("\nTest this query in HeavyDB to understand ATM calculation:")
    print(query)

if __name__ == "__main__":
    fix_leg_configuration()
    verify_configuration()
    create_atm_test_query() 