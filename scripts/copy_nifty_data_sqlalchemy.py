#!/usr/bin/env python3
"""
Copy all NIFTY data from 2022 onwards using SQLAlchemy
"""

import pandas as pd
from sqlalchemy import create_engine, text
import time
import sys
from datetime import datetime

# Connection URLs
REMOTE_URL = 'mysql+pymysql://mahesh:mahesh_123@************:3306/historicaldb'
LOCAL_URL = 'mysql+pymysql://mahesh:mahesh_123@localhost:3306/historicaldb'

# NIFTY-related tables to copy
NIFTY_TABLES = [
    'nifty_call',
    'nifty_put', 
    'nifty_cash',
    'nifty_future'
]

def copy_table_data(remote_engine, local_engine, table_name, start_date='220101', batch_size=100000):
    """Copy table data from remote to local with date filtering"""
    print(f"\nCopying table: {table_name} (from 2022 onwards)")
    print("-" * 50)
    
    try:
        # First, get the table structure
        with remote_engine.connect() as conn:
            result = conn.execute(text(f"SHOW CREATE TABLE {table_name}"))
            create_stmt = result.fetchone()[1]
        
        # Create table in local database
        with local_engine.connect() as conn:
            conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
            conn.execute(text(create_stmt))
            conn.commit()
        print("Table structure created")
        
        # Get total row count
        with remote_engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name} WHERE date >= :start_date"), {"start_date": start_date})
            total_rows = result.scalar()
        
        print(f"Total rows to copy: {total_rows:,}")
        
        if total_rows == 0:
            print("No data to copy")
            return True
        
        # Copy data in batches
        offset = 0
        start_time = time.time()
        
        while offset < total_rows:
            # Read batch
            query = f"""
            SELECT * FROM {table_name} 
            WHERE date >= :start_date
            ORDER BY date, time
            LIMIT :limit OFFSET :offset
            """
            
            df = pd.read_sql_query(
                text(query),
                remote_engine,
                params={"start_date": start_date, "limit": batch_size, "offset": offset}
            )
            
            if df.empty:
                break
            
            # Write to local
            df.to_sql(table_name, local_engine, if_exists='append', index=False, method='multi', chunksize=10000)
            
            offset += len(df)
            progress = min(100, (offset / total_rows) * 100)
            elapsed = time.time() - start_time
            eta = (elapsed / offset) * (total_rows - offset) if offset > 0 else 0
            
            print(f"\rProgress: {progress:.1f}% ({offset:,}/{total_rows:,} rows) - ETA: {eta:.0f}s", end='', flush=True)
        
        print(f"\nCompleted in {time.time() - start_time:.1f} seconds")
        
        # Verify
        with local_engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            local_rows = result.scalar()
            
            result = conn.execute(text(f"SELECT MIN(date), MAX(date) FROM {table_name}"))
            min_date, max_date = result.fetchone()
        
        print(f"✓ Verification: {local_rows:,} rows copied")
        print(f"Date range: {min_date} to {max_date}")
        
        return True
        
    except Exception as e:
        print(f"\nError copying {table_name}: {e}")
        return False

def main():
    """Main function"""
    print("NIFTY Data Copy Tool (2022 onwards) - SQLAlchemy Version")
    print("=" * 70)
    print(f"Source: {REMOTE_URL}")
    print(f"Destination: {LOCAL_URL}")
    print("=" * 70)
    
    try:
        # Create engines
        print("\nConnecting to databases...")
        remote_engine = create_engine(REMOTE_URL, pool_pre_ping=True)
        local_engine = create_engine(LOCAL_URL, pool_pre_ping=True)
        
        # Test connections
        with remote_engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        with local_engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print("✓ Connected successfully")
        
        # Check which tables exist
        with remote_engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES"))
            all_tables = [row[0] for row in result]
        
        tables_to_copy = [t for t in NIFTY_TABLES if t in all_tables]
        missing_tables = [t for t in NIFTY_TABLES if t not in all_tables]
        
        if missing_tables:
            print(f"\nWarning: These tables don't exist: {', '.join(missing_tables)}")
        
        print(f"\nTables to copy: {', '.join(tables_to_copy)}")
        
        # Confirm
        response = input("\nProceed with copying NIFTY data from 2022 onwards? (yes/no): ")
        if response.lower() != 'yes':
            print("Aborted.")
            return 0
        
        # Copy each table
        success_count = 0
        failed_tables = []
        
        for table in tables_to_copy:
            if copy_table_data(remote_engine, local_engine, table):
                success_count += 1
            else:
                failed_tables.append(table)
        
        # Summary
        print(f"\n{'='*70}")
        print(f"Summary:")
        print(f"  Successfully copied: {success_count}/{len(tables_to_copy)} tables")
        if failed_tables:
            print(f"  Failed tables: {', '.join(failed_tables)}")
        
        # Show statistics
        print(f"\nData Statistics:")
        with local_engine.connect() as conn:
            for table in tables_to_copy:
                if table not in failed_tables:
                    result = conn.execute(text(f"SELECT COUNT(*) as count, MIN(date) as min_date, MAX(date) as max_date FROM {table}"))
                    row = result.fetchone()
                    print(f"  {table}: {row.count:,} rows ({row.min_date} to {row.max_date})")
        
        # Dispose engines
        remote_engine.dispose()
        local_engine.dispose()
        
        print("\n✓ NIFTY data copy completed!")
        print("\nNext steps:")
        print("1. Run legacy backtester against local MySQL")
        print("2. Run HeavyDB backtester")
        print("3. Compare results using: python3 scripts/run_legacy_vs_heavydb_comparison.py")
        
    except Exception as e:
        print(f"\nError: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 