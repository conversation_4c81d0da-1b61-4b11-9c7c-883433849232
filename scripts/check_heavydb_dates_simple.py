#!/usr/bin/env python3
"""Check HeavyDB date range"""

try:
    from pyheavydb import connect
except ImportError:
    from heavydb import connect

def check_dates():
    # Direct connection to HeavyDB
    conn = connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    # Check date range
    query = "SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date FROM nifty_option_chain"
    result = conn.execute(query).fetchone()
    print(f"HeavyDB date range: {result[0]} to {result[1]}")
    
    # Check April 2025 data
    query = "SELECT COUNT(*) FROM nifty_option_chain WHERE trade_date = '2025-04-01'"
    result = conn.execute(query).fetchone()
    print(f"Rows for April 1, 2025: {result[0]}")
    
    # Check ATM for April 1
    if result[0] > 0:
        query = """
        SELECT spot, atm_strike, strike, ce_close, pe_close
        FROM nifty_option_chain 
        WHERE trade_date = '2025-04-01' 
        AND trade_time = '09:16:00' 
        AND strike = atm_strike
        LIMIT 1
        """
        result = conn.execute(query).fetchone()
        if result:
            print(f"\nApril 1, 2025 at 09:16:00:")
            print(f"  Spot: {result[0]}")
            print(f"  ATM Strike: {result[1]}")
            print(f"  CE Close: {result[3]}")
            print(f"  PE Close: {result[4]}")
    else:
        # Check what dates we do have
        query = """
        SELECT DISTINCT trade_date 
        FROM nifty_option_chain 
        WHERE trade_date LIKE '2025-04%'
        ORDER BY trade_date
        """
        result = conn.execute(query).fetchall()
        print(f"\nApril 2025 dates in HeavyDB:")
        for row in result:
            print(f"  {row[0]}")
    
    conn.close()

if __name__ == "__main__":
    check_dates() 