#!/usr/bin/env python3
"""Test the legacy backtester in standalone mode with debugging."""

import os
import sys
import shutil
import json
import pandas as pd

# Add the legacy BTRUN directory to Python path
sys.path.insert(0, '/srv/samba/shared/bt/archive/backtester_stable/BTRUN')

# Import legacy modules
import config
from Util import Util
from LocalBacktestEngine import LocalBacktestEngine

def test_local_engine():
    """Test the local backtesting engine directly."""
    
    print("=" * 60)
    print("TESTING LOCAL BACKTESTING ENGINE")
    print("=" * 60)
    
    # Change to legacy directory
    os.chdir('/srv/samba/shared/bt/archive/backtester_stable/BTRUN')
    
    # Load the btpara.json file that was created
    with open('btpara.json', 'r') as f:
        btPara = json.load(f)
    
    print(f"\nLoaded btPara with dates: {btPara['start_date']} to {btPara['end_date']}")
    print(f"Portfolio: {btPara['portfolio']['name']}")
    print(f"Number of strategies: {len(btPara.get('portfolio', {}).get('strategies', []))}")
    
    # Initialize local engine
    mysql_config = {
        'host': 'localhost',
        'user': 'mahesh',
        'password': 'mahesh_123',
        'database': 'historicaldb'
    }
    
    print("\nInitializing LocalBacktestEngine...")
    engine = LocalBacktestEngine(mysql_config)
    
    # Test MySQL connection
    print("\nTesting MySQL connection...")
    try:
        underlying_price = engine.get_underlying_price('NIFTY', '2024-01-03', '09:16:00')
        print(f"✅ MySQL connected. NIFTY spot price on 2024-01-03 09:16:00: {underlying_price}")
    except Exception as e:
        print(f"❌ MySQL error: {e}")
        return
    
    # Test ATM strike calculation
    print("\nTesting ATM strike calculation...")
    try:
        atm_strike = engine.get_atm_strike('NIFTY', '2024-01-03', '09:16:00')
        print(f"✅ ATM strike calculated: {atm_strike}")
    except Exception as e:
        print(f"❌ ATM calculation error: {e}")
        return
    
    # Execute trade
    print("\nExecuting backtest...")
    try:
        result = engine.execute_trade(btPara)
        
        print(f"\n✅ Backtest executed successfully!")
        print(f"Number of orders: {len(result.get('strategies', {}).get('orders', []))}")
        
        # Show orders
        orders = result.get('strategies', {}).get('orders', [])
        if orders:
            print("\nGenerated orders:")
            for i, order in enumerate(orders):
                print(f"\n  Order {i+1}:")
                print(f"    Leg: {order['leg_id']}")
                print(f"    Strike: {order['strike']}")
                print(f"    Type: {order['option_type']}")
                print(f"    Side: {order['side']}")
                print(f"    Entry: {order['entry_price']} @ {order['entry_time']}")
                print(f"    Exit: {order['exit_price']} @ {order['exit_time']}")
                print(f"    P&L: {order['pnl']}")
        else:
            print("\n❌ No orders generated!")
            
            # Debug information
            if btPara.get('portfolio', {}).get('strategies'):
                strategy = btPara['portfolio']['strategies'][0]
                print(f"\nDebug - Strategy info:")
                print(f"  Name: {strategy.get('name')}")
                print(f"  Entry start: {strategy.get('entry_start')}")
                print(f"  Entry end: {strategy.get('entry_end')}")
                print(f"  Number of legs: {len(strategy.get('legs', []))}")
                
                if strategy.get('legs'):
                    leg = strategy['legs'][0]
                    print(f"\nDebug - First leg info:")
                    print(f"  ID: {leg.get('id')}")
                    print(f"  Option type: {leg.get('option_type')}")
                    print(f"  Side: {leg.get('side')}")
                    print(f"  Strike selection: {leg.get('strike_selection')}")
                    print(f"  Is idle: {leg.get('is_idle')}")
        
        # Save result to file
        with open('local_engine_result.json', 'w') as f:
            json.dump(result, f, indent=2)
            print(f"\nResult saved to local_engine_result.json")
        
    except Exception as e:
        print(f"\n❌ Backtest failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        engine.close()
        print("\nClosed database connection")

def main():
    """Main function."""
    test_local_engine()

if __name__ == "__main__":
    main() 