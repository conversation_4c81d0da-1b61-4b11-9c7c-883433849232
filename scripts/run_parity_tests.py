#!/usr/bin/env python3
"""
Comprehensive Parity Testing Script for HeavyDB Backtester
Phase 2.D: Systematic testing of all strategy types against legacy system
"""

import os
import sys
import subprocess
import pandas as pd
import json
from datetime import datetime, timedelta
import argparse
import time
from typing import Dict, List, Tuple

# Add project paths
sys.path.append('/srv/samba/shared')

# Test configuration
TEST_STRATEGIES = ['TBS', 'ORB', 'OI', 'INDICATOR', 'TV']
TEST_DATA_DIR = '/srv/samba/shared/test_data'
OUTPUT_DIR = '/srv/samba/shared/test_results'
LEGACY_DIR = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN'
GPU_MODULE = 'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU'
TV_GPU_MODULE = 'bt.backtester_stable.BTRUN.BT_TV_GPU'

class ParityTester:
    """Handles parity testing between legacy and HeavyDB backtesters."""
    
    def __init__(self, strategy: str, test_type: str):
        self.strategy = strategy.upper()
        self.test_type = test_type  # '1day' or '30day'
        self.results = {
            'strategy': self.strategy,
            'test_type': self.test_type,
            'start_time': datetime.now().isoformat(),
            'legacy_output': None,
            'gpu_output': None,
            'comparison': {},
            'issues': []
        }
    
    def setup_test_files(self) -> Tuple[str, str]:
        """Setup test input files for the strategy."""
        # Create test data directory structure
        strategy_dir = os.path.join(TEST_DATA_DIR, self.strategy.lower())
        os.makedirs(strategy_dir, exist_ok=True)
        
        # Define file paths
        portfolio_file = f"input_portfolio_{self.strategy.lower()}_{self.test_type}.xlsx"
        strategy_file = f"input_{self.strategy.lower()}_strategy.xlsx"
        
        portfolio_path = os.path.join(strategy_dir, portfolio_file)
        strategy_path = os.path.join(strategy_dir, strategy_file)
        
        # Check if files exist, if not, create them from templates
        if not os.path.exists(portfolio_path):
            self._create_test_portfolio(portfolio_path)
        
        if not os.path.exists(strategy_path):
            self._create_test_strategy(strategy_path)
        
        return portfolio_path, strategy_path
    
    def _create_test_portfolio(self, path: str):
        """Create a test portfolio file based on strategy type."""
        # Define date ranges
        if self.test_type == '1day':
            start_date = '01_04_2025'
            end_date = '01_04_2025'
        else:  # 30day
            start_date = '01_04_2025'
            end_date = '30_04_2025'
        
        # Portfolio settings
        portfolio_name = 'NIF0DTE' if self.strategy == 'TBS' else f'{self.strategy}_TEST'
        portfolio_settings = pd.DataFrame({
            'StartDate': [start_date],
            'EndDate': [end_date],
            'IsTickBT': ['no'],
            'Enabled': ['YES'],
            'PortfolioName': [portfolio_name],
            'Multiplier': [1.0],
            'SlippagePercent': [0.1],
            'PortfolioTarget': [0],
            'PortfolioStoploss': [0]
        })
        
        # Strategy settings
        strategy_file = os.path.join(strategy_dir, f'input_{self.strategy.lower()}_strategy.xlsx')
        strategy_settings = pd.DataFrame({
            'Enabled': ['YES'],
            'PortfolioName': [portfolio_name],
            'StrategyType': [self.strategy],
            'StrategyExcelFilePath': [strategy_file]
        })
        
        # Write to Excel
        with pd.ExcelWriter(path, engine='openpyxl') as writer:
            portfolio_settings.to_excel(writer, sheet_name='PortfolioSetting', index=False)
            strategy_settings.to_excel(writer, sheet_name='StrategySetting', index=False)
        
        print(f"Created test portfolio: {path}")
    
    def _create_test_strategy(self, path: str):
        """Create a test strategy file based on strategy type."""
        # For TBS, use the known working configuration
        if self.strategy == 'TBS':
            # Use the exact configuration from our working input files
            general_params = {
                'StrategyName': ['RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL'],
                'MoveSlToCost': ['no'],
                'Underlying': ['SPOT'],
                'Index': ['NIFTY'],
                'Weekdays': ['1,2,3,4,5'],
                'DTE': [0],
                'StartTime': [91600],
                'LastEntryTime': [91600],
                'EndTime': [120000],  # 12:00 PM exit
                'StrikeSelectionTime': [91600],
                'CheckPremiumDiffCondition': ['no'],
                'ConsiderHedgePnLForStgyPnL': [''],
                'OnExpiryDayTradeNextExpiry': [''],
                'StrategyProfit': [''],
                'StrategyLoss': [''],
                'StrategyProfitReExecuteNo': [''],
                'StrategyLossReExecuteNo': [''],
                'StrategyTrailingType': [''],
                'PnLCalTime': [''],
                'PnLCalculationFrom': [''],
                'LockPercent': [''],
                'TrailPercent': [''],
                'SqOff1Time': [''],
                'SqOff1Percent': [''],
                'SqOff2Time': [''],
                'SqOff2Percent': [''],
                'ProfitReaches': [''],
                'LockMinProfitAt': [''],
                'IncreaseInProfit': [''],
                'TrailMinProfitBy': ['']
            }
            
            # Leg parameters matching the working configuration
            leg_params = pd.DataFrame({
                'StrategyName': ['RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL'] * 4,
                'IsIdle': ['no'] * 4,
                'LegID': [1, 2, 3, 4],
                'Instrument': ['call', 'put', 'call', 'put'],
                'Transaction': ['sell', 'sell', 'buy', 'buy'],
                'Expiry': [''] * 4,
                'StrikeMethod': ['atm', 'atm', 'otm2', 'otm2'],
                'StrikeValue': [0] * 4,
                'Lots': [1] * 4,
                'MatchPremium': [''] * 4,
                'StrikePremiumCondition': [''] * 4,
                'SLType': ['percentage'] * 4,
                'SLValue': [100, 100, 0, 0],  # 100% SL for sell legs, 0 for buy legs
                'TGTType': ['percentage'] * 4,
                'TGTValue': [0] * 4,  # No target
                'TrailSLType': [''] * 4,
                'SL_TrailAt': [''] * 4,
                'SL_TrailBy': [''] * 4,
                'OpenHedge': [''] * 4,
                'HedgeStrikeMethod': [''] * 4,
                'HedgeStrikeValue': [''] * 4,
                'HedgeStrikePremiumCondition': [''] * 4
            })
        else:
            # For other strategies, use basic configuration
            general_params = {
                'StrategyName': [f'{self.strategy}_Test_Strategy'],
                'Underlying': ['SPOT'],
                'Index': ['NIFTY'],
                'Weekdays': ['1,2,3,4,5'],
                'DTE': [0],
                'StartTime': [91600],
                'EndTime': [120000],  # 12:00 PM exit
            }
            
            # Add strategy-specific parameters
            if self.strategy == 'ORB':
                general_params.update({
                    'OrbRangeStart': [91500],
                    'OrbRangeEnd': [92000],
                    'LastEntryTime': [91600]
                })
            elif self.strategy == 'OI':
                general_params.update({
                    'Timeframe': [3],
                    'MaxOpenPositions': [4],
                    'OiThreshold': [800000]
                })
            elif self.strategy == 'INDICATOR':
                general_params.update({
                    'Timeframe': [5],
                    'ConsiderVwapForEntry': ['YES'],
                    'ConsiderEmaForEntry': ['YES'],
                    'EntryCombination': ['AND']
                })
            
            # Leg parameters
            leg_params = pd.DataFrame({
                'StrategyName': [f'{self.strategy}_Test_Strategy'] * 4,
                'IsIdle': ['no'] * 4,
                'LegID': [1, 2, 3, 4],
                'Instrument': ['call', 'put', 'call', 'put'],
                'Transaction': ['sell', 'sell', 'buy', 'buy'],
                'StrikeMethod': ['atm', 'atm', 'otm2', 'otm2'],
                'StrikeValue': [0] * 4,
                'Lots': [1] * 4,
                'SLType': ['percentage'] * 4,
                'SLValue': [100, 100, 0, 0],  # Match TBS configuration
                'TGTType': ['percentage'] * 4,
                'TGTValue': [0] * 4
            })
        
        general_df = pd.DataFrame(general_params)
        
        # Write to Excel
        with pd.ExcelWriter(path, engine='openpyxl') as writer:
            general_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
            leg_params.to_excel(writer, sheet_name='LegParameter', index=False)
        
        print(f"Created test strategy: {path}")
    
    def run_legacy_test(self, portfolio_path: str) -> str:
        """Run the legacy backtester."""
        print(f"\n{'='*60}")
        print(f"Running LEGACY {self.strategy} {self.test_type} test...")
        print(f"{'='*60}")
        
        output_file = os.path.join(OUTPUT_DIR, f'legacy_{self.strategy}_{self.test_type}.xlsx')
        
        # Copy portfolio file to legacy directory
        legacy_portfolio = os.path.join(LEGACY_DIR, 'INPUT SHEETS', 'INPUT PORTFOLIO.xlsx')
        subprocess.run(['cp', portfolio_path, legacy_portfolio], check=True)
        
        # Also copy the strategy file to legacy directory
        strategy_path = portfolio_path.replace('input_portfolio_', 'input_').replace(f'_{self.test_type}.xlsx', '_strategy.xlsx')
        if os.path.exists(strategy_path):
            # Determine strategy file name based on strategy type
            if self.strategy == 'TBS':
                legacy_strategy = os.path.join(LEGACY_DIR, 'INPUT SHEETS', 'INPUT TBS MULTI LEGS.xlsx')
            elif self.strategy == 'ORB':
                legacy_strategy = os.path.join(LEGACY_DIR, 'INPUT SHEETS', 'INPUT ORB.xlsx')
            elif self.strategy == 'OI':
                legacy_strategy = os.path.join(LEGACY_DIR, 'INPUT SHEETS', 'INPUT OI.xlsx')
            elif self.strategy == 'INDICATOR':
                legacy_strategy = os.path.join(LEGACY_DIR, 'INPUT SHEETS', 'INPUT INDICATOR.xlsx')
            elif self.strategy == 'TV':
                legacy_strategy = os.path.join(LEGACY_DIR, 'INPUT SHEETS', 'INPUT TV.xlsx')
            else:
                legacy_strategy = os.path.join(LEGACY_DIR, 'INPUT SHEETS', f'INPUT {self.strategy}.xlsx')
            subprocess.run(['cp', strategy_path, legacy_strategy], check=True)
        
        # Ensure output directory exists
        output_dir = os.path.join(LEGACY_DIR, 'output')
        os.makedirs(output_dir, exist_ok=True)
        
        # Run legacy backtester
        start_time = time.time()
        try:
            result = subprocess.run(
                ['python3', 'BTRunPortfolio.py'],
                cwd=LEGACY_DIR,
                capture_output=True,
                text=True,
                check=True
            )
            duration = time.time() - start_time
            
            # Show output for debugging
            print(f"Legacy stdout:\n{result.stdout}")
            print(f"Legacy stderr:\n{result.stderr}")
            
            # Copy output - legacy writes to Trades folder with timestamp
            trades_dir = os.path.join(LEGACY_DIR, 'Trades')
            if os.path.exists(trades_dir):
                # Find the most recent file in Trades directory
                portfolio_name = 'NIF0DTE' if self.strategy == 'TBS' else f'{self.strategy}_TEST'
                files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx') and portfolio_name in f]
                if files:
                    # Sort by modification time to get the most recent
                    files.sort(key=lambda x: os.path.getmtime(os.path.join(trades_dir, x)), reverse=True)
                    legacy_output = os.path.join(trades_dir, files[0])
                    subprocess.run(['cp', legacy_output, output_file], check=True)
                    self.results['legacy_duration'] = duration
                    self.results['legacy_output'] = output_file
                    print(f"✓ Legacy test completed in {duration:.2f}s")
                    print(f"  Output: {output_file}")
                else:
                    raise Exception(f"No output files found in {trades_dir}")
            else:
                raise Exception(f"Trades directory not found: {trades_dir}")
                
            # Show output for debugging
            print(f"Legacy stdout:\n{result.stdout}")
            print(f"Legacy stderr:\n{result.stderr}")
        except subprocess.CalledProcessError as e:
            print(f"✗ Legacy test failed: {e}")
            print(f"  stdout: {e.stdout}")
            print(f"  stderr: {e.stderr}")
            self.results['issues'].append({
                'type': 'legacy_failure',
                'error': str(e),
                'stdout': e.stdout,
                'stderr': e.stderr
            })
            return None
        
        return output_file
    
    def run_gpu_test(self, portfolio_path: str) -> str:
        """Run the HeavyDB GPU backtester."""
        print(f"\n{'='*60}")
        print(f"Running GPU {self.strategy} {self.test_type} test...")
        print(f"{'='*60}")
        
        output_file = os.path.join(OUTPUT_DIR, f'gpu_{self.strategy}_{self.test_type}.xlsx')
        
        # Prepare command
        if self.strategy == 'TV':
            # TV uses different module and parameters
            cmd = [
                'python3', '-m', TV_GPU_MODULE,
                '--legacy-excel',
                '--tv-excel', portfolio_path,
                '--output-dir', os.path.dirname(output_file)
            ]
        else:
            cmd = [
                'python3', '-m', GPU_MODULE,
                '--legacy-excel',
                '--portfolio-excel', portfolio_path,
                '--output-path', output_file
            ]
        
        # Add GPU optimization for 30-day tests
        if self.test_type == '30day':
            cmd.extend(['--workers', '4', '--gpu-optimize'])
        
        # Run GPU backtester
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd,
                cwd='/srv/samba/shared',
                capture_output=True,
                text=True,
                check=True
            )
            duration = time.time() - start_time
            
            self.results['gpu_duration'] = duration
            self.results['gpu_output'] = output_file
            print(f"✓ GPU test completed in {duration:.2f}s")
            print(f"  Output: {output_file}")
            
            # Log speedup for 30-day tests
            if self.test_type == '30day' and 'legacy_duration' in self.results:
                speedup = self.results['legacy_duration'] / duration
                print(f"  Speedup: {speedup:.2f}x")
                self.results['speedup'] = speedup
                
        except subprocess.CalledProcessError as e:
            print(f"✗ GPU test failed: {e}")
            print(f"  stdout: {e.stdout}")
            print(f"  stderr: {e.stderr}")
            self.results['issues'].append({
                'type': 'gpu_failure',
                'error': str(e),
                'stdout': e.stdout,
                'stderr': e.stderr
            })
            return None
        
        return output_file
    
    def compare_outputs(self, legacy_file: str, gpu_file: str):
        """Compare outputs between legacy and GPU versions."""
        print(f"\n{'='*60}")
        print(f"Comparing outputs...")
        print(f"{'='*60}")
        
        try:
            # Get available sheets
            legacy_sheets = pd.ExcelFile(legacy_file).sheet_names
            gpu_sheets = pd.ExcelFile(gpu_file).sheet_names
            
            print(f"Legacy sheets: {legacy_sheets}")
            print(f"GPU sheets: {gpu_sheets}")
            
            # Read key sheets that should exist in both
            legacy_metrics = pd.read_excel(legacy_file, sheet_name='Metrics')
            gpu_metrics = pd.read_excel(gpu_file, sheet_name='Metrics')
            
            # Try to read transaction sheets if they exist
            legacy_trans = None
            gpu_trans = None
            
            # Check for transaction sheets in legacy
            legacy_trans_sheets = [s for s in legacy_sheets if 'Trans' in s]
            if legacy_trans_sheets:
                legacy_trans = pd.read_excel(legacy_file, sheet_name=legacy_trans_sheets[0])
                print(f"Found legacy transactions in: {legacy_trans_sheets[0]}")
            
            # Check for transaction sheets in GPU
            gpu_trans_sheets = [s for s in gpu_sheets if 'Trans' in s and 'PORTFOLIO' in s]
            if gpu_trans_sheets:
                gpu_trans = pd.read_excel(gpu_file, sheet_name=gpu_trans_sheets[0])
                print(f"Found GPU transactions in: {gpu_trans_sheets[0]}")
            
            # Compare trade counts
            legacy_trades = len(legacy_trans) if legacy_trans is not None else 0
            gpu_trades = len(gpu_trans) if gpu_trans is not None else 0
            
            self.results['comparison']['trade_count'] = {
                'legacy': legacy_trades,
                'gpu': gpu_trades,
                'match': bool(legacy_trades == gpu_trades)
            }
            print(f"Trade Count - Legacy: {legacy_trades}, GPU: {gpu_trades} {'✓' if legacy_trades == gpu_trades else '✗'}")
            
            # Compare total P&L
            legacy_pnl = 0
            gpu_pnl = 0
            
            if legacy_trans is not None and 'PnL' in legacy_trans.columns:
                legacy_pnl = legacy_trans['PnL'].sum()
            elif 'Total PnL' in legacy_metrics['Particulars'].values:
                # Try to get P&L from metrics
                pnl_row = legacy_metrics[legacy_metrics['Particulars'] == 'Total PnL']
                if not pnl_row.empty:
                    legacy_pnl = float(pnl_row['Combined'].iloc[0])
            
            if gpu_trans is not None and 'PnL' in gpu_trans.columns:
                gpu_pnl = gpu_trans['PnL'].sum()
            elif 'Total PnL' in gpu_metrics['Particulars'].values:
                # Try to get P&L from metrics
                pnl_row = gpu_metrics[gpu_metrics['Particulars'] == 'Total PnL']
                if not pnl_row.empty:
                    gpu_pnl = float(pnl_row['Combined'].iloc[0])
            
            pnl_diff = abs(legacy_pnl - gpu_pnl)
            pnl_match = pnl_diff < 0.01  # Within 0.01 tolerance
            
            self.results['comparison']['total_pnl'] = {
                'legacy': float(legacy_pnl),
                'gpu': float(gpu_pnl),
                'difference': float(pnl_diff),
                'match': bool(pnl_match)
            }
            print(f"Total P&L - Legacy: {legacy_pnl:.2f}, GPU: {gpu_pnl:.2f}, Diff: {pnl_diff:.2f} {'✓' if pnl_match else '✗'}")
            
            # Compare sheet counts
            missing_in_gpu = set(legacy_sheets) - set(gpu_sheets)
            extra_in_gpu = set(gpu_sheets) - set(legacy_sheets)
            
            self.results['comparison']['sheets'] = {
                'legacy_count': len(legacy_sheets),
                'gpu_count': len(gpu_sheets),
                'missing_in_gpu': list(missing_in_gpu),
                'extra_in_gpu': list(extra_in_gpu)
            }
            
            if missing_in_gpu:
                print(f"Missing sheets in GPU: {missing_in_gpu}")
            if extra_in_gpu:
                print(f"Extra sheets in GPU: {extra_in_gpu}")
            
            # Compare metrics if available
            if 'Metrics' in legacy_sheets and 'Metrics' in gpu_sheets:
                # Compare key metrics
                legacy_margin = self._extract_metric(legacy_metrics, 'Margin Required')
                gpu_margin = self._extract_metric(gpu_metrics, 'Margin Required')
                
                if legacy_margin is not None and gpu_margin is not None:
                    margin_diff = abs(legacy_margin - gpu_margin)
                    margin_match = margin_diff < 1.0  # Within 1.0 tolerance
                    print(f"Margin Required - Legacy: {legacy_margin:.2f}, GPU: {gpu_margin:.2f} {'✓' if margin_match else '✗'}")
            
            # Overall verdict - focus on what we can actually compare
            comparable_items = []
            
            # Only compare trade count if both have transaction data
            if legacy_trans is not None and gpu_trans is not None:
                comparable_items.append(self.results['comparison']['trade_count']['match'])
            
            # Always compare P&L if we can extract it
            comparable_items.append(self.results['comparison']['total_pnl']['match'])
            
            # Don't fail on missing sheets if legacy has fewer sheets (expected with mock service)
            all_match = all(comparable_items) if comparable_items else False
            
            self.results['comparison']['overall_match'] = all_match
            print(f"\nOverall Parity: {'✓ PASS' if all_match else '✗ FAIL'}")
            
            # Document issues
            if not all_match:
                if legacy_trans is not None and gpu_trans is not None and not self.results['comparison']['trade_count']['match']:
                    self.results['issues'].append({
                        'type': 'trade_count_mismatch',
                        'legacy': legacy_trades,
                        'gpu': gpu_trades
                    })
                if not self.results['comparison']['total_pnl']['match']:
                    self.results['issues'].append({
                        'type': 'pnl_mismatch',
                        'legacy': legacy_pnl,
                        'gpu': gpu_pnl,
                        'difference': pnl_diff
                    })
                    
        except Exception as e:
            print(f"Error comparing outputs: {e}")
            self.results['issues'].append({
                'type': 'comparison_error',
                'error': str(e)
            })
    
    def _extract_metric(self, metrics_df, metric_name):
        """Extract a metric value from the metrics dataframe."""
        try:
            row = metrics_df[metrics_df['Particulars'] == metric_name]
            if not row.empty:
                return float(row['Combined'].iloc[0])
        except:
            pass
        return None
    
    def save_results(self):
        """Save test results to JSON."""
        self.results['end_time'] = datetime.now().isoformat()
        
        # Create results directory
        results_dir = os.path.join(OUTPUT_DIR, 'comparisons')
        os.makedirs(results_dir, exist_ok=True)
        
        # Save results
        results_file = os.path.join(
            results_dir, 
            f'{self.strategy}_{self.test_type}_comparison.json'
        )
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\nResults saved to: {results_file}")
        
        # Create markdown report if issues found
        if self.results['issues']:
            self._create_issue_report()
    
    def _create_issue_report(self):
        """Create a markdown report for issues found."""
        issues_dir = os.path.join(OUTPUT_DIR, 'issues')
        os.makedirs(issues_dir, exist_ok=True)
        
        report_file = os.path.join(
            issues_dir,
            f'{self.strategy}_{self.test_type}_issues.md'
        )
        
        with open(report_file, 'w') as f:
            f.write(f"# {self.strategy} {self.test_type} Test Issues\n\n")
            f.write(f"**Test Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for idx, issue in enumerate(self.results['issues'], 1):
                f.write(f"## Issue {idx}: {issue['type']}\n\n")
                f.write(f"- **Strategy**: {self.strategy}\n")
                f.write(f"- **Test Case**: {self.test_type}\n")
                f.write(f"- **Severity**: High\n")
                f.write(f"- **Description**: {json.dumps(issue, indent=2)}\n")
                f.write(f"- **Status**: Open\n\n")
        
        print(f"Issue report created: {report_file}")


def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description='Run parity tests for HeavyDB backtester')
    parser.add_argument('--strategy', choices=['ALL'] + TEST_STRATEGIES, default='ALL',
                       help='Strategy to test (default: ALL)')
    parser.add_argument('--test-type', choices=['1day', '30day', 'both'], default='both',
                       help='Test type to run (default: both)')
    parser.add_argument('--skip-legacy', action='store_true',
                       help='Skip legacy tests (useful for debugging GPU)')
    
    args = parser.parse_args()
    
    # Determine strategies to test
    strategies = TEST_STRATEGIES if args.strategy == 'ALL' else [args.strategy]
    test_types = ['1day', '30day'] if args.test_type == 'both' else [args.test_type]
    
    # Create output directories
    os.makedirs(TEST_DATA_DIR, exist_ok=True)
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Summary results
    summary = {
        'test_date': datetime.now().isoformat(),
        'strategies_tested': strategies,
        'test_types': test_types,
        'results': []
    }
    
    # Run tests
    for strategy in strategies:
        for test_type in test_types:
            print(f"\n{'#'*80}")
            print(f"# Testing {strategy} - {test_type}")
            print(f"{'#'*80}")
            
            tester = ParityTester(strategy, test_type)
            
            # Setup test files
            portfolio_path, strategy_path = tester.setup_test_files()
            
            # Run tests
            legacy_output = None
            if not args.skip_legacy:
                legacy_output = tester.run_legacy_test(portfolio_path)
            
            gpu_output = tester.run_gpu_test(portfolio_path)
            
            # Compare outputs
            if legacy_output and gpu_output:
                tester.compare_outputs(legacy_output, gpu_output)
            
            # Save results
            tester.save_results()
            
            # Add to summary
            summary['results'].append({
                'strategy': strategy,
                'test_type': test_type,
                'passed': tester.results.get('comparison', {}).get('overall_match', False),
                'issues_count': len(tester.results['issues'])
            })
    
    # Save summary
    summary_file = os.path.join(OUTPUT_DIR, 'test_summary.json')
    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    # Print summary
    print(f"\n{'='*80}")
    print("TEST SUMMARY")
    print(f"{'='*80}")
    
    total_tests = len(summary['results'])
    passed_tests = sum(1 for r in summary['results'] if r['passed'])
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    print("\nDetailed Results:")
    for result in summary['results']:
        status = '✓ PASS' if result['passed'] else f"✗ FAIL ({result['issues_count']} issues)"
        print(f"  {result['strategy']} {result['test_type']}: {status}")
    
    print(f"\nFull results saved to: {OUTPUT_DIR}")


if __name__ == "__main__":
    main() 