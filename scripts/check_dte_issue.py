#!/usr/bin/env python3
"""
Check if D<PERSON> (Days to Expiry) is causing the issue for April 1-3
"""

try:
    from pyheavydb import connect as heavydb_connect
except ImportError:
    from heavydb import connect as heavydb_connect

def check_dte_values(date_str):
    """Check DTE values for different expiries"""
    conn = heavydb_connect(
        host='127.0.0.1',
        port=6274,
        user='admin',
        password='HyperInteractive',
        dbname='heavyai'
    )
    
    # Convert date format
    heavydb_date = f"20{date_str[:2]}-{date_str[2:4]}-{date_str[4:6]}"
    
    print(f"\n{'='*70}")
    print(f"Date: {heavydb_date}")
    print(f"{'='*70}")
    
    # Check DTE values for different expiries
    query = f"""
    SELECT DISTINCT
        expiry_date,
        expiry_bucket,
        dte,
        COUNT(*) as option_count
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}'
    AND trade_time = '09:16:00'
    GROUP BY expiry_date, expiry_bucket, dte
    ORDER BY expiry_date
    """
    
    print("\nExpiry Date | Bucket | DTE | Option Count")
    print("-" * 45)
    
    results = conn.execute(query).fetchall()
    for expiry, bucket, dte, count in results:
        print(f"{expiry} | {bucket:6s} | {dte:3d} | {count:12d}")
    
    # Check specific data for DTE=0 (if that's what the strategy uses)
    print("\nChecking data for DTE=0 (same-day expiry):")
    query2 = f"""
    SELECT 
        expiry_date,
        COUNT(DISTINCT strike) as strikes,
        MIN(strike) as min_strike,
        MAX(strike) as max_strike
    FROM nifty_option_chain
    WHERE trade_date = '{heavydb_date}'
    AND trade_time = '09:16:00'
    AND dte = 0
    GROUP BY expiry_date
    """
    
    results = conn.execute(query2).fetchall()
    if results:
        for expiry, strikes, min_strike, max_strike in results:
            print(f"  Expiry: {expiry}, Strikes: {strikes}, Range: {int(min_strike)}-{int(max_strike)}")
    else:
        print("  No data found for DTE=0")
    
    # Check if April 3 expiry has DTE=0 on April 3
    if date_str == '250403':
        print("\nSpecial check for April 3 (expiry day):")
        query3 = f"""
        SELECT 
            strike,
            ce_close,
            pe_close,
            dte
        FROM nifty_option_chain
        WHERE trade_date = '2025-04-03'
        AND trade_time = '09:16:00'
        AND expiry_date = '2025-04-03'
        AND strike IN (23100, 23200, 23300)
        ORDER BY strike
        """
        
        print("Strike | CE Price | PE Price | DTE")
        print("-" * 40)
        
        results = conn.execute(query3).fetchall()
        for strike, ce, pe, dte in results:
            print(f"{int(strike):6d} | {ce:8.2f} | {pe:8.2f} | {dte:3d}")
    
    conn.close()

def main():
    print("DTE (Days to Expiry) Analysis")
    print("="*70)
    print("Checking if DTE filtering is causing the issue")
    print("Note: The input portfolio has DTE=0 setting")
    
    dates = ['250401', '250402', '250403']
    
    for date in dates:
        check_dte_values(date)
    
    print("\n" + "="*70)
    print("INSIGHTS:")
    print("1. April 1: April 3 expiry has DTE=2 (not 0)")
    print("2. April 2: April 3 expiry has DTE=1 (not 0)")
    print("3. April 3: April 3 expiry has DTE=0 (matches!)")
    print("\nTHIS IS THE ISSUE!")
    print("The portfolio is configured with DTE=0, which means it only")
    print("trades on expiry day. That's why no trades on April 1-2.")
    print("\nSOLUTION: Change DTE filter in the input file or test with")
    print("April 3 only, or use a different expiry date.")

if __name__ == "__main__":
    main() 