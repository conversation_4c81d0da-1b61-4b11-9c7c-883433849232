#!/usr/bin/env python3
"""Fix the Excel structure for legacy backtester to match expected format."""

import pandas as pd
from openpyxl import Workbook, load_workbook
import os
import shutil

def create_correct_portfolio_excel():
    """Create a correctly formatted portfolio Excel file for legacy backtester."""
    
    print("Creating correctly formatted Excel files for legacy backtester...")
    
    # Change to legacy directory
    os.chdir("/srv/samba/shared/bt/archive/backtester_stable/BTRUN")
    
    # Portfolio data with 2024 dates
    portfolio_data = {
        'StartDate': ['03_01_2024'],
        'EndDate': ['03_01_2024'],
        'IsTickBT': ['no'],
        'Enabled': ['YES'],
        'PortfolioName': ['NIF0DTE'],
        'PortfolioTarget': [0],
        'PortfolioStoploss': [0],
        'PortfolioTrailingType': ['None'],
        'PnLCalTime': ['0'],
        'LockPercent': [0],
        'TrailPercent': [0],
        'SqOff1Time': ['0'],
        'SqOff1Percent': [0],
        'SqOff2Time': ['0'],
        'SqOff2Percent': [0],
        'ProfitReaches': [0],
        'LockMinProfitAt': [0],
        'IncreaseInProfit': [0],
        'TrailMinProfitBy': [0],
        'Multiplier': [1],
        'SlippagePercent': [0.1]
    }
    
    # Strategy settings
    strategy_data = {
        'Enabled': ['YES'],
        'PortfolioName': ['NIF0DTE'],
        'StrategyType': ['TBS'],
        'StrategyExcelFilePath': ['/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/input_tbs_multi_legs.xlsx']
    }
    
    # Create Excel file
    with pd.ExcelWriter('INPUT SHEETS/INPUT PORTFOLIO.xlsx', engine='openpyxl') as writer:
        pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
        pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
    
    print("Created INPUT PORTFOLIO.xlsx with correct format")
    
    # Verify the file
    wb = load_workbook('INPUT SHEETS/INPUT PORTFOLIO.xlsx')
    ws = wb['PortfolioSetting']
    print(f"First row: {[cell.value for cell in ws[1]]}")
    print(f"Second row: {[cell.value for cell in ws[2]]}")
    
    return True

def verify_strategy_file():
    """Verify the strategy file exists."""
    
    strategy_file = "INPUT SHEETS/input_tbs_multi_legs.xlsx"
    
    if not os.path.exists(strategy_file):
        # Copy from test data
        source = "/srv/samba/shared/test_data/tbs/input_tbs_multi_legs.xlsx"
        if os.path.exists(source):
            shutil.copy(source, strategy_file)
            print(f"Copied strategy file to {strategy_file}")
        else:
            print(f"Strategy file not found: {source}")
            return False
    else:
        print(f"Strategy file exists: {strategy_file}")
    
    return True

def main():
    """Main function."""
    
    # Create correct portfolio Excel
    if create_correct_portfolio_excel():
        print("\n✅ Portfolio Excel created successfully")
    else:
        print("\n❌ Failed to create portfolio Excel")
        return
    
    # Verify strategy file
    if verify_strategy_file():
        print("✅ Strategy Excel verified")
    else:
        print("❌ Strategy Excel missing")
        return
    
    print("\nRunning legacy backtester with corrected files...")
    print("-" * 60)
    
    # Run the backtester
    os.system("export MYSQL_HOST=localhost && export DEBUG_MODE=true && python3 BTRunPortfolio.py")

if __name__ == "__main__":
    main() 