#!/usr/bin/env python3
"""
Comprehensive Parity Validation for HeavyDB Backtester
This script validates that the GPU system produces correct results using:
1. Synthetic future-based ATM selection (same as legacy)
2. Real MySQL data for validation
3. Comprehensive output validation
"""

import os
import sys
import subprocess
import pandas as pd
import mysql.connector
from datetime import datetime
import json
import time

# Add project paths
sys.path.append('/srv/samba/shared')

# MySQL configuration
MYSQL_CONFIG = {
    'host': '************',
    'port': 3306,
    'user': 'mahesh',
    'password': 'mahesh_123',
    'database': 'historicaldb'
}

class ParityValidator:
    """Comprehensive parity validation for GPU backtester"""
    
    def __init__(self):
        self.test_date = '2025-04-01'
        self.test_date_mysql = '250401'  # YYMMDD format for MySQL
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'mysql_validation': {},
            'gpu_validation': {},
            'parity_check': {},
            'issues': []
        }
    
    def validate_mysql_data(self):
        """Validate MySQL data is available for test date"""
        print("="*60)
        print("1. MySQL Data Validation")
        print("="*60)
        
        try:
            conn = mysql.connector.connect(**MYSQL_CONFIG)
            cursor = conn.cursor()
            
            # Check data availability
            tables = ['nifty_call', 'nifty_put', 'nifty_cash']
            data_counts = {}
            
            for table in tables:
                query = f"SELECT COUNT(*) FROM {table} WHERE date = %s"
                cursor.execute(query, (self.test_date_mysql,))
                count = cursor.fetchone()[0]
                data_counts[table] = count
                print(f"  {table}: {count} records")
            
            # Get spot price for ATM validation
            cursor.execute("SELECT close FROM nifty_cash WHERE date = %s ORDER BY time DESC LIMIT 1", 
                          (self.test_date_mysql,))
            spot_result = cursor.fetchone()
            spot_price = spot_result[0] if spot_result else None
            
            # Calculate expected ATM using synthetic future method
            if spot_price:
                # Get call and put prices for ATM calculation
                cursor.execute("""
                    SELECT strike, close as ce_close 
                    FROM nifty_call 
                    WHERE date = %s AND time = '091600' 
                    ORDER BY ABS(strike - %s) 
                    LIMIT 5
                """, (self.test_date_mysql, spot_price))
                call_data = cursor.fetchall()
                
                cursor.execute("""
                    SELECT strike, close as pe_close 
                    FROM nifty_put 
                    WHERE date = %s AND time = '091600' 
                    ORDER BY ABS(strike - %s) 
                    LIMIT 5
                """, (self.test_date_mysql, spot_price))
                put_data = cursor.fetchall()
                
                # Calculate synthetic future for each strike
                atm_candidates = []
                for call_row in call_data:
                    strike, ce_close = call_row
                    # Find matching put
                    for put_row in put_data:
                        if put_row[0] == strike:
                            pe_close = put_row[1]
                            synthetic_future = strike + ce_close - pe_close
                            diff = abs(synthetic_future - spot_price)
                            atm_candidates.append((strike, diff, synthetic_future))
                            break
                
                if atm_candidates:
                    # Find strike with minimum difference
                    expected_atm = min(atm_candidates, key=lambda x: x[1])[0]
                    print(f"  Expected ATM (synthetic future method): {expected_atm}")
                    print(f"  Spot price: {spot_price}")
                else:
                    expected_atm = round(spot_price / 50) * 50  # Fallback
                    print(f"  Expected ATM (fallback): {expected_atm}")
            else:
                expected_atm = None
                print("  ✗ No spot price found")
            
            self.results['mysql_validation'] = {
                'connected': True,
                'data_counts': data_counts,
                'spot_price': spot_price,
                'expected_atm': expected_atm,
                'total_records': sum(data_counts.values())
            }
            
            cursor.close()
            conn.close()
            
            print(f"✓ MySQL validation completed")
            print(f"  Total records: {sum(data_counts.values())}")
            return True
            
        except Exception as e:
            print(f"✗ MySQL validation failed: {e}")
            self.results['mysql_validation'] = {'connected': False, 'error': str(e)}
            return False
    
    def run_gpu_backtester(self):
        """Run the GPU backtester and capture results"""
        print("\n" + "="*60)
        print("2. GPU Backtester Execution")
        print("="*60)
        
        # Prepare input files
        portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
        output_file = "test_results/comprehensive_validation_output.xlsx"
        
        # Ensure output directory exists
        os.makedirs("test_results", exist_ok=True)
        
        # Run GPU backtester
        print("Running GPU backtester...")
        cmd = [
            'python3', '-m', 'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
            '--legacy-excel',
            '--portfolio-excel', portfolio_file,
            '--output-path', output_file
        ]
        
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd,
                cwd='/srv/samba/shared',
                capture_output=True,
                text=True,
                check=True
            )
            execution_time = time.time() - start_time
            
            print(f"✓ GPU backtester completed in {execution_time:.2f}s")
            
            # Analyze output
            return self.analyze_gpu_output(output_file, execution_time)
            
        except subprocess.CalledProcessError as e:
            print(f"✗ GPU backtester failed: {e}")
            print(f"  stdout: {e.stdout}")
            print(f"  stderr: {e.stderr}")
            self.results['gpu_validation'] = {'success': False, 'error': str(e)}
            return False
    
    def analyze_gpu_output(self, output_file, execution_time):
        """Analyze GPU backtester output"""
        try:
            # Read output file
            excel_file = pd.ExcelFile(output_file)
            sheets = excel_file.sheet_names
            
            # Read key sheets
            trans_df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
            metrics_df = pd.read_excel(output_file, sheet_name='Metrics')
            
            # Extract key metrics
            trade_count = len(trans_df)
            total_pnl = trans_df['PnL'].sum()
            
            # Get ATM strike from trades
            atm_strikes = trans_df[trans_df['Instrument Type'].isin(['CE', 'PE'])]['Strike'].unique()
            actual_atm = atm_strikes[0] if len(atm_strikes) > 0 else None
            
            # Get margin from metrics
            margin_row = metrics_df[metrics_df['Particulars'] == 'Margin Required']
            margin_required = float(margin_row['Value'].iloc[0]) if not margin_row.empty else None
            
            # Analyze trade details
            trade_details = []
            for idx, row in trans_df.iterrows():
                trade_details.append({
                    'symbol': row['symbol'],
                    'strike': row['Strike'],
                    'instrument': row['Instrument Type'],
                    'side': row['Side'],
                    'entry_price': row['Entry Price'],
                    'exit_price': row['Exit Price'],
                    'pnl': row['PnL'],
                    'entry_time': row['Entry Time'],
                    'exit_time': row['Exit Time'],
                    'reason': row['Reason']
                })
            
            self.results['gpu_validation'] = {
                'success': True,
                'execution_time': execution_time,
                'sheets_count': len(sheets),
                'trade_count': trade_count,
                'total_pnl': float(total_pnl),
                'actual_atm': actual_atm,
                'margin_required': margin_required,
                'trade_details': trade_details,
                'all_sheets': sheets
            }
            
            print(f"  Sheets: {len(sheets)}")
            print(f"  Trades: {trade_count}")
            print(f"  Total P&L: {total_pnl:.2f}")
            print(f"  ATM Strike: {actual_atm}")
            print(f"  Margin Required: {margin_required:.2f}")
            
            return True
            
        except Exception as e:
            print(f"✗ GPU output analysis failed: {e}")
            self.results['gpu_validation'] = {'success': False, 'error': str(e)}
            return False
    
    def validate_parity(self):
        """Validate parity between expected and actual results"""
        print("\n" + "="*60)
        print("3. Parity Validation")
        print("="*60)
        
        mysql_data = self.results.get('mysql_validation', {})
        gpu_data = self.results.get('gpu_validation', {})
        
        if not mysql_data.get('connected') or not gpu_data.get('success'):
            print("✗ Cannot validate parity - missing data")
            return False
        
        # Validate ATM calculation
        expected_atm = mysql_data.get('expected_atm')
        actual_atm = gpu_data.get('actual_atm')
        
        atm_match = expected_atm == actual_atm if expected_atm and actual_atm else False
        print(f"ATM Strike - Expected: {expected_atm}, Actual: {actual_atm} {'✓' if atm_match else '✗'}")
        
        # Validate trade count (expected 4 for the test strategy)
        expected_trades = 4
        actual_trades = gpu_data.get('trade_count', 0)
        trades_match = actual_trades == expected_trades
        print(f"Trade Count - Expected: {expected_trades}, Actual: {actual_trades} {'✓' if trades_match else '✗'}")
        
        # Validate exit times (should be 12:00:00)
        expected_exit_time = '12:00:00'
        trade_details = gpu_data.get('trade_details', [])
        exit_times_correct = all(trade['exit_time'] == expected_exit_time for trade in trade_details)
        print(f"Exit Times - Expected: {expected_exit_time}, All Correct: {'✓' if exit_times_correct else '✗'}")
        
        # Validate exit reasons (should be "Exit Time Hit")
        expected_exit_reason = 'Exit Time Hit'
        exit_reasons_correct = all(trade['reason'] == expected_exit_reason for trade in trade_details)
        print(f"Exit Reasons - Expected: {expected_exit_reason}, All Correct: {'✓' if exit_reasons_correct else '✗'}")
        
        # Validate P&L is reasonable (not zero, within expected range)
        total_pnl = gpu_data.get('total_pnl', 0)
        pnl_reasonable = -200 <= total_pnl <= 200  # Reasonable range for 1-day test
        print(f"P&L Range - Actual: {total_pnl:.2f}, Reasonable: {'✓' if pnl_reasonable else '✗'}")
        
        # Overall parity assessment
        parity_checks = [atm_match, trades_match, exit_times_correct, exit_reasons_correct, pnl_reasonable]
        overall_parity = all(parity_checks)
        
        self.results['parity_check'] = {
            'atm_match': atm_match,
            'trades_match': trades_match,
            'exit_times_correct': exit_times_correct,
            'exit_reasons_correct': exit_reasons_correct,
            'pnl_reasonable': pnl_reasonable,
            'overall_parity': overall_parity,
            'checks_passed': sum(parity_checks),
            'total_checks': len(parity_checks)
        }
        
        print(f"\nOverall Parity: {'✓ PASS' if overall_parity else '✗ FAIL'}")
        print(f"Checks Passed: {sum(parity_checks)}/{len(parity_checks)}")
        
        return overall_parity
    
    def run_30day_performance_test(self):
        """Run 30-day test with GPU optimization"""
        print("\n" + "="*60)
        print("4. 30-Day Performance Test")
        print("="*60)
        
        # Update portfolio file for 30-day test
        portfolio_file = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
        output_file = "test_results/30day_performance_output.xlsx"
        
        # Read and update portfolio file
        try:
            df = pd.read_excel(portfolio_file, sheet_name='PortfolioSetting')
            df.loc[0, 'StartDate'] = '01_04_2025'
            df.loc[0, 'EndDate'] = '30_04_2025'
            
            with pd.ExcelWriter(portfolio_file, mode='a', if_sheet_exists='replace') as writer:
                df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
            
            print("✓ Updated portfolio for 30-day test")
        except Exception as e:
            print(f"✗ Failed to update portfolio: {e}")
            return False
        
        # Run 30-day test with GPU optimization
        print("Running 30-day GPU-optimized backtest...")
        cmd = [
            'python3', '-m', 'bt.backtester_stable.BTRUN.BTRunPortfolio_GPU',
            '--legacy-excel',
            '--portfolio-excel', portfolio_file,
            '--output-path', output_file,
            '--workers', '4',
            '--gpu-optimize'
        ]
        
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd,
                cwd='/srv/samba/shared',
                capture_output=True,
                text=True,
                check=True
            )
            execution_time = time.time() - start_time
            
            # Analyze 30-day results
            trans_df = pd.read_excel(output_file, sheet_name='PORTFOLIO Trans')
            total_trades = len(trans_df)
            total_pnl = trans_df['PnL'].sum()
            
            print(f"✓ 30-day test completed in {execution_time:.2f}s")
            print(f"  Total trades: {total_trades}")
            print(f"  Total P&L: {total_pnl:.2f}")
            print(f"  Performance: {total_trades/execution_time:.1f} trades/second")
            
            # Expected performance targets
            performance_targets = {
                'max_execution_time': 60,  # 60 seconds max
                'min_trades_per_day': 3,   # At least 3 trades per day on average
                'trades_per_second': 1     # At least 1 trade per second processing
            }
            
            avg_trades_per_day = total_trades / 30
            trades_per_second = total_trades / execution_time
            
            performance_pass = (
                execution_time <= performance_targets['max_execution_time'] and
                avg_trades_per_day >= performance_targets['min_trades_per_day'] and
                trades_per_second >= performance_targets['trades_per_second']
            )
            
            self.results['30day_performance'] = {
                'execution_time': execution_time,
                'total_trades': total_trades,
                'total_pnl': float(total_pnl),
                'avg_trades_per_day': avg_trades_per_day,
                'trades_per_second': trades_per_second,
                'performance_pass': performance_pass,
                'targets': performance_targets
            }
            
            print(f"Performance Test: {'✓ PASS' if performance_pass else '✗ FAIL'}")
            
            # Restore original portfolio file
            df.loc[0, 'EndDate'] = '01_04_2025'
            with pd.ExcelWriter(portfolio_file, mode='a', if_sheet_exists='replace') as writer:
                df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
            
            return performance_pass
            
        except subprocess.CalledProcessError as e:
            print(f"✗ 30-day test failed: {e}")
            self.results['30day_performance'] = {'success': False, 'error': str(e)}
            return False
    
    def generate_report(self):
        """Generate comprehensive validation report"""
        print("\n" + "="*60)
        print("5. Validation Report")
        print("="*60)
        
        # Save detailed results
        results_file = "test_results/comprehensive_validation_results.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Generate summary
        mysql_ok = self.results.get('mysql_validation', {}).get('connected', False)
        gpu_ok = self.results.get('gpu_validation', {}).get('success', False)
        parity_ok = self.results.get('parity_check', {}).get('overall_parity', False)
        performance_ok = self.results.get('30day_performance', {}).get('performance_pass', False)
        
        print(f"MySQL Data Validation: {'✓ PASS' if mysql_ok else '✗ FAIL'}")
        print(f"GPU Backtester: {'✓ PASS' if gpu_ok else '✗ FAIL'}")
        print(f"Parity Validation: {'✓ PASS' if parity_ok else '✗ FAIL'}")
        print(f"30-Day Performance: {'✓ PASS' if performance_ok else '✗ FAIL'}")
        
        overall_success = mysql_ok and gpu_ok and parity_ok and performance_ok
        
        print(f"\nOVERALL VALIDATION: {'✓ PASS' if overall_success else '✗ FAIL'}")
        print(f"Detailed results saved to: {results_file}")
        
        return overall_success

def main():
    """Main validation function"""
    print("="*80)
    print("COMPREHENSIVE PARITY VALIDATION")
    print("HeavyDB GPU Backtester vs Legacy MySQL System")
    print(f"Time: {datetime.now()}")
    print("="*80)
    
    validator = ParityValidator()
    
    # Run validation steps
    steps = [
        ("MySQL Data Validation", validator.validate_mysql_data),
        ("GPU Backtester Test", validator.run_gpu_backtester),
        ("Parity Validation", validator.validate_parity),
        ("30-Day Performance Test", validator.run_30day_performance_test)
    ]
    
    results = []
    for step_name, step_func in steps:
        try:
            result = step_func()
            results.append(result)
        except Exception as e:
            print(f"✗ {step_name} failed with exception: {e}")
            results.append(False)
    
    # Generate final report
    overall_success = validator.generate_report()
    
    print("\n" + "="*80)
    if overall_success:
        print("🎉 COMPREHENSIVE VALIDATION PASSED!")
        print("The HeavyDB GPU backtester is ready for production use.")
        print("✓ Synthetic future-based ATM selection working correctly")
        print("✓ Real MySQL data validation successful")
        print("✓ GPU optimization performance targets met")
    else:
        print("❌ VALIDATION FAILED")
        print("Issues found that need to be addressed before production use.")
        failed_steps = [steps[i][0] for i, result in enumerate(results) if not result]
        print(f"Failed steps: {', '.join(failed_steps)}")
    print("="*80)
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 