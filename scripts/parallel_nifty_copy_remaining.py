#!/usr/bin/env python3
"""
Copy remaining NIFTY data in parallel
- nifty_put: from Nov 14, 2024 onwards
- nifty_cash: all 2024 data
- nifty_future: all 2024 data
"""

import pandas as pd
from sqlalchemy import create_engine, text
import time
import sys
from datetime import datetime
import concurrent.futures
import threading

# Connection URLs
REMOTE_URL = 'mysql+pymysql://mahesh:mahesh_123@************:3306/historicaldb'
LOCAL_URL = 'mysql+pymysql://mahesh:mahesh_123@localhost:3306/historicaldb'

# Progress tracking
progress_lock = threading.Lock()
progress_data = {}

def copy_table_data(table_name, start_date='240101', end_date='241231', 
                   date_filter=None, batch_size=50000):
    """Copy table data from remote to local with date filtering"""
    
    try:
        # Create engines
        remote_engine = create_engine(REMOTE_URL, pool_pre_ping=True)
        local_engine = create_engine(LOCAL_URL, pool_pre_ping=True)
        
        print(f"\n[{table_name}] Starting copy...")
        
        # For nifty_put, we only need to append new data
        if table_name == 'nifty_put' and date_filter:
            # Just append new data
            print(f"[{table_name}] Appending data from {date_filter} onwards")
            create_table = False
        else:
            # For other tables, recreate
            with remote_engine.connect() as conn:
                result = conn.execute(text(f"SHOW CREATE TABLE {table_name}"))
                create_stmt = result.fetchone()[1]
            
            with local_engine.connect() as conn:
                conn.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                conn.execute(text(create_stmt))
                conn.commit()
            print(f"[{table_name}] Table structure created")
            create_table = True
        
        # Build query
        if date_filter:
            query_text = f"""
            SELECT COUNT(*) FROM {table_name} 
            WHERE date > :date_filter AND date <= :end_date
            """
            params = {"date_filter": date_filter, "end_date": end_date}
        else:
            query_text = f"""
            SELECT COUNT(*) FROM {table_name} 
            WHERE date >= :start_date AND date <= :end_date
            """
            params = {"start_date": start_date, "end_date": end_date}
        
        # Get total row count
        with remote_engine.connect() as conn:
            result = conn.execute(text(query_text), params)
            total_rows = result.scalar()
        
        print(f"[{table_name}] Total rows to copy: {total_rows:,}")
        
        if total_rows == 0:
            print(f"[{table_name}] No data to copy")
            return True
        
        # Initialize progress
        with progress_lock:
            progress_data[table_name] = {
                'total': total_rows,
                'copied': 0,
                'start_time': time.time()
            }
        
        # Copy data in batches
        offset = 0
        
        while offset < total_rows:
            # Build select query
            if date_filter:
                select_query = text(f"""
                SELECT * FROM {table_name} 
                WHERE date > :date_filter AND date <= :end_date
                ORDER BY date, time
                LIMIT :limit OFFSET :offset
                """)
                select_params = {
                    "date_filter": date_filter, 
                    "end_date": end_date,
                    "limit": batch_size, 
                    "offset": offset
                }
            else:
                select_query = text(f"""
                SELECT * FROM {table_name} 
                WHERE date >= :start_date AND date <= :end_date
                ORDER BY date, time
                LIMIT :limit OFFSET :offset
                """)
                select_params = {
                    "start_date": start_date,
                    "end_date": end_date,
                    "limit": batch_size,
                    "offset": offset
                }
            
            # Read batch
            with remote_engine.connect() as conn:
                df = pd.read_sql_query(select_query, conn, params=select_params)
            
            if df.empty:
                break
            
            # Write to local
            with local_engine.begin() as conn:
                df.to_sql(table_name, conn, if_exists='append', index=False, 
                         method='multi', chunksize=10000)
            
            offset += len(df)
            
            # Update progress
            with progress_lock:
                progress_data[table_name]['copied'] = offset
            
            # Print progress every 10 batches
            if (offset // batch_size) % 10 == 0:
                elapsed = time.time() - progress_data[table_name]['start_time']
                rate = offset / elapsed if elapsed > 0 else 0
                eta = (total_rows - offset) / rate if rate > 0 else 0
                print(f"[{table_name}] Progress: {offset:,}/{total_rows:,} "
                      f"({offset/total_rows*100:.1f}%) - ETA: {eta:.0f}s")
        
        # Final verification
        with local_engine.connect() as conn:
            if table_name == 'nifty_put':
                # Check total for nifty_put
                result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            else:
                result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            local_rows = result.scalar()
            
            result = conn.execute(text(f"SELECT MIN(date), MAX(date) FROM {table_name}"))
            min_date, max_date = result.fetchone()
        
        elapsed = time.time() - progress_data[table_name]['start_time']
        print(f"[{table_name}] ✓ Completed in {elapsed:.1f}s")
        print(f"[{table_name}] Total rows in local DB: {local_rows:,}")
        print(f"[{table_name}] Date range: {min_date} to {max_date}")
        
        # Dispose engines
        remote_engine.dispose()
        local_engine.dispose()
        
        return True
        
    except Exception as e:
        print(f"\n[{table_name}] Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_overall_progress():
    """Print overall progress periodically"""
    while True:
        time.sleep(30)  # Update every 30 seconds
        with progress_lock:
            if not progress_data:
                continue
            
            print("\n" + "="*70)
            print(f"Overall Progress Update - {datetime.now().strftime('%H:%M:%S')}")
            print("="*70)
            
            total_copied = 0
            total_target = 0
            
            for table, data in progress_data.items():
                total_copied += data['copied']
                total_target += data['total']
                progress = (data['copied'] / data['total'] * 100) if data['total'] > 0 else 0
                elapsed = time.time() - data['start_time']
                rate = data['copied'] / elapsed if elapsed > 0 else 0
                
                print(f"{table:15} {data['copied']:>10,} / {data['total']:>10,} "
                      f"({progress:5.1f}%) - {rate:>7,.0f} rows/s")
            
            if total_target > 0:
                overall_progress = (total_copied / total_target * 100)
                print(f"\nOVERALL:        {total_copied:>10,} / {total_target:>10,} "
                      f"({overall_progress:5.1f}%)")
            print("="*70 + "\n")

def main():
    """Main function"""
    print("Parallel NIFTY Data Copy Tool - Remaining Data")
    print("=" * 70)
    print(f"Started at: {datetime.now()}")
    print("=" * 70)
    
    # Define copy tasks
    copy_tasks = [
        # Copy remaining nifty_put data (after Nov 13, 2024)
        ('nifty_put', '240101', '241231', '241113'),  # date_filter = 241113
        # Copy all nifty_cash data
        ('nifty_cash', '240101', '241231', None),
        # Copy all nifty_future data  
        ('nifty_future', '240101', '241231', None),
    ]
    
    # Start progress monitoring thread
    progress_thread = threading.Thread(target=print_overall_progress, daemon=True)
    progress_thread.start()
    
    # Execute copies in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        
        for table, start_date, end_date, date_filter in copy_tasks:
            future = executor.submit(copy_table_data, table, start_date, end_date, date_filter)
            futures.append((table, future))
        
        # Wait for all to complete
        success_count = 0
        failed_tables = []
        
        for table, future in futures:
            try:
                if future.result():
                    success_count += 1
                else:
                    failed_tables.append(table)
            except Exception as e:
                print(f"[{table}] Exception: {e}")
                failed_tables.append(table)
    
    # Final summary
    print(f"\n{'='*70}")
    print(f"Copy completed at: {datetime.now()}")
    print(f"Successfully copied: {success_count}/{len(copy_tasks)} tables")
    if failed_tables:
        print(f"Failed tables: {', '.join(failed_tables)}")
    
    print(f"\n✓ All remaining NIFTY data copy completed!")
    print("\nNext steps:")
    print("1. Run legacy backtester comparison")
    print("2. Compare results between legacy and HeavyDB systems")
    
    return 0 if not failed_tables else 1

if __name__ == "__main__":
    sys.exit(main()) 