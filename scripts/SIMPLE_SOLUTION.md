# Simple Solution: Expose Legacy Service (***************:5000) to External Access

## The Problem
- Legacy service runs on internal IP: ***************:5000
- You need to access it from Linux server (outside the network)
- You have RDP access to Windows server at ************

## The Solution: Port Forwarding

Just 3 simple steps on the Windows server:

### Step 1: RDP to Windows Server
```bash
# From Linux terminal (or use any RDP client)
xfreerdp /u:MahaInvest /p:Maruth@123 /v:************:33898
```

### Step 2: Run Commands on Windows (as Administrator)
Open Command Prompt as Administrator and run:

```cmd
# Forward external port 5000 to internal service
netsh interface portproxy add v4tov4 listenport=5000 listenaddress=0.0.0.0 connectport=5000 connectaddress=***************

# Open Windows Firewall
netsh advfirewall firewall add rule name="Legacy Backtest Service" dir=in action=allow protocol=TCP localport=5000

# Verify it worked
netsh interface portproxy show all
```

### Step 3: Test from Linux
```bash
# Now the service is accessible from outside!
curl http://************:5000/healthcheck
```

## That's It! 

The legacy service at ***************:5000 is now accessible from anywhere as:
**http://************:5000**

## To Use in Your Scripts

Update any script that needs the legacy service:
```python
# Before (internal only):
# LEGACY_SERVICE_URL = "http://***************:5000"

# After (accessible from anywhere):
LEGACY_SERVICE_URL = "http://************:5000"
```

## Run the Comparison Test
```bash
cd /srv/samba/shared
python scripts/run_parallel_test_external.py
```

## To Remove Port Forwarding (if needed)
```cmd
# On Windows, run as Administrator:
netsh interface portproxy delete v4tov4 listenport=5000 listenaddress=0.0.0.0
netsh advfirewall firewall delete rule name="Legacy Backtest Service"
```

## Alternative: Use the Batch File
I've created `expose_legacy_service.bat` that does all this automatically:
1. Copy it to Windows server
2. Run as Administrator
3. Done! 