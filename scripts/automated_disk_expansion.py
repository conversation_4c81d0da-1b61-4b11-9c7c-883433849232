#!/usr/bin/env python3
"""
Automated Disk Expansion System
Handles automatic cleanup and expansion when disk space is low
"""

import os
import sys
import subprocess
import shutil
import psutil
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import argparse
import yaml

class AutomatedDiskExpander:
    def __init__(self, config_file: str = "/srv/samba/shared/config/disk_expansion.yaml"):
        self.config = self.load_config(config_file)
        self.setup_logging()
        self.dry_run = False
        
    def load_config(self, config_file: str) -> Dict:
        """Load configuration from YAML file"""
        default_config = {
            'cleanup_policies': {
                'logs': {
                    'enabled': True,
                    'paths': [
                        '/srv/samba/shared/logs',
                        '/nvme0n1-disk/var/lib/heavyai/storage/log',
                        '/srv/samba/shared/bt/backtester_stable/BTRUN/logs'
                    ],
                    'retention_days': 30,
                    'patterns': ['*.log', '*.log.*']
                },
                'backtest_outputs': {
                    'enabled': True,
                    'paths': [
                        '/srv/samba/shared/Trades',
                        '/srv/samba/shared/bt/backtester_stable/BTRUN/output'
                    ],
                    'retention_days': 7,
                    'patterns': ['*.xlsx', '*.json', '*.csv']
                },
                'temp_files': {
                    'enabled': True,
                    'paths': [
                        '/tmp',
                        '/srv/samba/shared/temp',
                        '/var/lib/heavyai/import'
                    ],
                    'retention_days': 1,
                    'patterns': ['*']
                },
                'archive_old_data': {
                    'enabled': True,
                    'source_path': '/srv/samba/shared/market_data',
                    'archive_path': '/sdb-disk/archive',
                    'retention_days': 90,
                    'compress': True
                }
            },
            'heavydb_cleanup': {
                'enabled': True,
                'retention_days': 365,
                'tables_to_clean': ['market_option_chain', 'market_futures', 'market_spot'],
                'archive_before_delete': True,
                'archive_path': '/sdb-disk/heavydb_archive'
            },
            'expansion_options': {
                'lvm_enabled': False,
                'lvm_volume_group': 'vg_data',
                'cloud_storage': {
                    'enabled': False,
                    'provider': 's3',
                    'bucket': 'heavydb-archive'
                }
            },
            'thresholds': {
                'auto_cleanup_percent': 85,
                'emergency_cleanup_percent': 95
            },
            'notifications': {
                'log_file': '/srv/samba/shared/logs/disk_expansion.log',
                'webhook_url': ''
            }
        }
        
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                loaded_config = yaml.safe_load(f) or {}
                return self._deep_merge(default_config, loaded_config)
        else:
            # Save default config
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            with open(config_file, 'w') as f:
                yaml.dump(default_config, f, default_flow_style=False)
            return default_config
    
    def _deep_merge(self, base: Dict, update: Dict) -> Dict:
        """Deep merge two dictionaries"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                base[key] = self._deep_merge(base[key], value)
            else:
                base[key] = value
        return base
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_file = self.config['notifications']['log_file']
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_disk_usage(self, path: str) -> float:
        """Get disk usage percentage for a path"""
        try:
            usage = psutil.disk_usage(path)
            return usage.percent
        except:
            return 0
    
    def cleanup_old_files(self, policy_name: str, policy: Dict) -> Dict:
        """Clean up old files based on policy"""
        self.logger.info(f"Running cleanup policy: {policy_name}")
        
        results = {
            'policy': policy_name,
            'files_deleted': 0,
            'space_freed_gb': 0,
            'errors': []
        }
        
        if not policy.get('enabled', False):
            self.logger.info(f"Policy {policy_name} is disabled")
            return results
        
        retention_days = policy.get('retention_days', 30)
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        for base_path in policy.get('paths', []):
            if not os.path.exists(base_path):
                continue
            
            try:
                path = Path(base_path)
                
                for pattern in policy.get('patterns', ['*']):
                    for file_path in path.rglob(pattern):
                        if file_path.is_file():
                            try:
                                # Check file age
                                file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                                if file_mtime < cutoff_date:
                                    file_size = file_path.stat().st_size
                                    
                                    if self.dry_run:
                                        self.logger.info(f"DRY RUN: Would delete {file_path}")
                                    else:
                                        file_path.unlink()
                                        self.logger.debug(f"Deleted: {file_path}")
                                    
                                    results['files_deleted'] += 1
                                    results['space_freed_gb'] += file_size / (1024**3)
                                    
                            except Exception as e:
                                results['errors'].append(f"Error deleting {file_path}: {str(e)}")
                                
            except Exception as e:
                results['errors'].append(f"Error processing path {base_path}: {str(e)}")
        
        self.logger.info(
            f"Cleanup {policy_name} completed: "
            f"{results['files_deleted']} files deleted, "
            f"{results['space_freed_gb']:.2f} GB freed"
        )
        
        return results
    
    def archive_old_data(self) -> Dict:
        """Archive old data to secondary storage"""
        policy = self.config['cleanup_policies'].get('archive_old_data', {})
        
        if not policy.get('enabled', False):
            return {'archived': False, 'reason': 'Policy disabled'}
        
        results = {
            'files_archived': 0,
            'space_freed_gb': 0,
            'archive_size_gb': 0,
            'errors': []
        }
        
        source_path = Path(policy['source_path'])
        archive_path = Path(policy['archive_path'])
        retention_days = policy['retention_days']
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        # Create archive directory
        archive_path.mkdir(parents=True, exist_ok=True)
        
        # Find old files
        files_to_archive = []
        for file_path in source_path.rglob('*'):
            if file_path.is_file():
                try:
                    file_mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_mtime < cutoff_date:
                        files_to_archive.append(file_path)
                except:
                    pass
        
        if not files_to_archive:
            return results
        
        # Create archive with date
        archive_date = datetime.now().strftime('%Y%m%d_%H%M%S')
        archive_name = f"market_data_archive_{archive_date}"
        
        if policy.get('compress', True):
            # Create tar.gz archive
            archive_file = archive_path / f"{archive_name}.tar.gz"
            
            if not self.dry_run:
                import tarfile
                
                with tarfile.open(archive_file, 'w:gz') as tar:
                    for file_path in files_to_archive:
                        # Preserve directory structure
                        arcname = file_path.relative_to(source_path.parent)
                        tar.add(file_path, arcname=str(arcname))
                        
                        file_size = file_path.stat().st_size
                        results['files_archived'] += 1
                        results['space_freed_gb'] += file_size / (1024**3)
                
                # Remove original files
                for file_path in files_to_archive:
                    file_path.unlink()
                
                # Get archive size
                results['archive_size_gb'] = archive_file.stat().st_size / (1024**3)
                
                self.logger.info(
                    f"Archived {results['files_archived']} files "
                    f"({results['space_freed_gb']:.2f} GB) to "
                    f"{archive_file} ({results['archive_size_gb']:.2f} GB)"
                )
            else:
                self.logger.info(f"DRY RUN: Would archive {len(files_to_archive)} files")
        
        return results
    
    def cleanup_heavydb_data(self) -> Dict:
        """Clean up old HeavyDB data"""
        config = self.config.get('heavydb_cleanup', {})
        
        if not config.get('enabled', False):
            return {'cleaned': False, 'reason': 'HeavyDB cleanup disabled'}
        
        results = {
            'tables_cleaned': [],
            'rows_deleted': 0,
            'space_freed_gb': 0,
            'errors': []
        }
        
        retention_days = config['retention_days']
        cutoff_date = (datetime.now() - timedelta(days=retention_days)).strftime('%Y-%m-%d')
        
        try:
            from heavydb import connect
            
            # Connect to HeavyDB
            conn = connect(
                host='localhost',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            cursor = conn.cursor()
            
            for table in config['tables_to_clean']:
                try:
                    # Get count before deletion
                    cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE trade_date < '{cutoff_date}'")
                    count_before = cursor.fetchone()[0]
                    
                    if count_before == 0:
                        continue
                    
                    # Archive if enabled
                    if config.get('archive_before_delete', True):
                        archive_path = Path(config['archive_path']) / f"{table}_{cutoff_date.replace('-', '')}.parquet"
                        
                        if not self.dry_run:
                            # Export to parquet
                            export_query = f"""
                            COPY (SELECT * FROM {table} WHERE trade_date < '{cutoff_date}') 
                            TO '{archive_path}' WITH (format='parquet')
                            """
                            cursor.execute(export_query)
                    
                    # Delete old data
                    if not self.dry_run:
                        delete_query = f"DELETE FROM {table} WHERE trade_date < '{cutoff_date}'"
                        cursor.execute(delete_query)
                        
                        results['tables_cleaned'].append(table)
                        results['rows_deleted'] += count_before
                    else:
                        self.logger.info(f"DRY RUN: Would delete {count_before} rows from {table}")
                    
                except Exception as e:
                    results['errors'].append(f"Error cleaning {table}: {str(e)}")
            
            # Optimize tables after deletion
            if not self.dry_run and results['tables_cleaned']:
                for table in results['tables_cleaned']:
                    try:
                        cursor.execute(f"OPTIMIZE TABLE {table}")
                    except:
                        pass
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            results['errors'].append(f"HeavyDB connection error: {str(e)}")
        
        return results
    
    def expand_lvm_volume(self) -> Dict:
        """Expand LVM volume if possible"""
        config = self.config['expansion_options']
        
        if not config.get('lvm_enabled', False):
            return {'expanded': False, 'reason': 'LVM expansion disabled'}
        
        results = {
            'expanded': False,
            'size_added_gb': 0,
            'errors': []
        }
        
        vg_name = config['lvm_volume_group']
        
        try:
            # Check for free space in volume group
            cmd = ['vgs', '--noheadings', '--units', 'g', '-o', 'vg_free', vg_name]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                free_space = float(result.stdout.strip().rstrip('g'))
                
                if free_space > 10:  # At least 10GB free
                    # Find logical volume
                    cmd = ['lvs', '--noheadings', '-o', 'lv_name', vg_name]
                    result = subprocess.run(cmd, capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        lv_name = result.stdout.strip()
                        
                        # Extend logical volume
                        extend_size = min(free_space - 1, 100)  # Leave 1GB free, max 100GB
                        
                        if not self.dry_run:
                            cmd = ['lvextend', '-L', f'+{extend_size}G', f'/dev/{vg_name}/{lv_name}']
                            result = subprocess.run(cmd)
                            
                            if result.returncode == 0:
                                # Resize filesystem
                                cmd = ['resize2fs', f'/dev/{vg_name}/{lv_name}']
                                result = subprocess.run(cmd)
                                
                                if result.returncode == 0:
                                    results['expanded'] = True
                                    results['size_added_gb'] = extend_size
                                    self.logger.info(f"Expanded LVM volume by {extend_size} GB")
                        else:
                            self.logger.info(f"DRY RUN: Would expand LVM by {extend_size} GB")
                            
        except Exception as e:
            results['errors'].append(f"LVM expansion error: {str(e)}")
        
        return results
    
    def check_and_expand(self, path: str) -> Dict:
        """Check disk usage and run appropriate expansion/cleanup"""
        usage_percent = self.get_disk_usage(path)
        
        self.logger.info(f"Checking {path}: {usage_percent:.1f}% used")
        
        results = {
            'path': path,
            'initial_usage': usage_percent,
            'actions_taken': [],
            'final_usage': usage_percent,
            'success': True
        }
        
        # Determine action based on usage
        if usage_percent >= self.config['thresholds']['emergency_cleanup_percent']:
            action_level = 'emergency'
        elif usage_percent >= self.config['thresholds']['auto_cleanup_percent']:
            action_level = 'normal'
        else:
            action_level = 'none'
        
        if action_level == 'none':
            self.logger.info(f"No action needed for {path}")
            return results
        
        self.logger.info(f"Running {action_level} cleanup for {path}")
        
        # Run cleanup policies
        for policy_name, policy in self.config['cleanup_policies'].items():
            if policy_name == 'archive_old_data':
                continue  # Handle separately
            
            # Run more aggressive cleanup in emergency mode
            if action_level == 'emergency':
                # Temporarily reduce retention
                original_retention = policy.get('retention_days', 30)
                policy['retention_days'] = max(1, original_retention // 4)
            
            cleanup_result = self.cleanup_old_files(policy_name, policy)
            results['actions_taken'].append(cleanup_result)
            
            # Restore original retention
            if action_level == 'emergency':
                policy['retention_days'] = original_retention
        
        # Archive old data if enabled
        if self.config['cleanup_policies']['archive_old_data']['enabled']:
            archive_result = self.archive_old_data()
            results['actions_taken'].append(archive_result)
        
        # Clean HeavyDB data if needed
        if '/heavyai' in path or '/nvme' in path:
            heavydb_result = self.cleanup_heavydb_data()
            results['actions_taken'].append(heavydb_result)
        
        # Try LVM expansion if still needed
        final_usage = self.get_disk_usage(path)
        if final_usage >= self.config['thresholds']['auto_cleanup_percent']:
            lvm_result = self.expand_lvm_volume()
            results['actions_taken'].append(lvm_result)
        
        # Get final usage
        results['final_usage'] = self.get_disk_usage(path)
        
        # Calculate total space freed
        total_freed = sum(
            action.get('space_freed_gb', 0) 
            for action in results['actions_taken']
        )
        
        self.logger.info(
            f"Cleanup completed for {path}: "
            f"{results['initial_usage']:.1f}% -> {results['final_usage']:.1f}% "
            f"({total_freed:.2f} GB freed)"
        )
        
        # Send notification if webhook configured
        if self.config['notifications'].get('webhook_url'):
            self.send_notification(results)
        
        return results
    
    def send_notification(self, results: Dict):
        """Send notification about cleanup actions"""
        webhook_url = self.config['notifications'].get('webhook_url')
        
        if not webhook_url:
            return
        
        try:
            import requests
            
            payload = {
                'timestamp': datetime.now().isoformat(),
                'event': 'disk_cleanup',
                'results': results
            }
            
            response = requests.post(webhook_url, json=payload, timeout=10)
            
            if response.status_code != 200:
                self.logger.warning(f"Webhook notification failed: {response.status_code}")
                
        except Exception as e:
            self.logger.error(f"Error sending notification: {e}")
    
    def generate_cleanup_script(self) -> str:
        """Generate a shell script for manual cleanup"""
        script_lines = [
            "#!/bin/bash",
            "# Automated Disk Cleanup Script",
            f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "echo 'Starting disk cleanup...'",
            ""
        ]
        
        # Add cleanup commands for each policy
        for policy_name, policy in self.config['cleanup_policies'].items():
            if not policy.get('enabled', False):
                continue
            
            script_lines.append(f"# {policy_name}")
            
            for path in policy.get('paths', []):
                for pattern in policy.get('patterns', ['*']):
                    days = policy.get('retention_days', 30)
                    cmd = f"find {path} -name '{pattern}' -type f -mtime +{days} -delete"
                    script_lines.append(cmd)
            
            script_lines.append("")
        
        # Add HeavyDB cleanup
        if self.config['heavydb_cleanup']['enabled']:
            script_lines.extend([
                "# HeavyDB Cleanup",
                "echo 'Cleaning HeavyDB old data...'",
                f"heavysql -p HyperInteractive -c \"DELETE FROM market_option_chain WHERE trade_date < DATE_SUB(CURRENT_DATE, INTERVAL {self.config['heavydb_cleanup']['retention_days']} DAY)\"",
                ""
            ])
        
        script_lines.extend([
            "echo 'Cleanup completed!'",
            "df -h"
        ])
        
        return '\n'.join(script_lines)

def main():
    parser = argparse.ArgumentParser(description="Automated disk expansion and cleanup")
    parser.add_argument(
        '--config',
        default='/srv/samba/shared/config/disk_expansion.yaml',
        help='Configuration file path'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be done without making changes'
    )
    parser.add_argument(
        '--path',
        help='Specific path to check and clean'
    )
    parser.add_argument(
        '--generate-script',
        action='store_true',
        help='Generate cleanup shell script'
    )
    parser.add_argument(
        '--force-cleanup',
        action='store_true',
        help='Force cleanup regardless of disk usage'
    )
    
    args = parser.parse_args()
    
    # Create expander
    expander = AutomatedDiskExpander(args.config)
    expander.dry_run = args.dry_run
    
    if args.generate_script:
        # Generate and save cleanup script
        script = expander.generate_cleanup_script()
        script_path = "/srv/samba/shared/scripts/manual_cleanup.sh"
        
        with open(script_path, 'w') as f:
            f.write(script)
        
        os.chmod(script_path, 0o755)
        print(f"Cleanup script generated: {script_path}")
        
    elif args.force_cleanup:
        # Run all cleanup policies regardless of usage
        results = {
            'forced_cleanup': True,
            'actions': []
        }
        
        for policy_name, policy in expander.config['cleanup_policies'].items():
            if policy_name == 'archive_old_data':
                result = expander.archive_old_data()
            else:
                result = expander.cleanup_old_files(policy_name, policy)
            results['actions'].append(result)
        
        # Also run HeavyDB cleanup
        heavydb_result = expander.cleanup_heavydb_data()
        results['actions'].append(heavydb_result)
        
        # Print summary
        total_freed = sum(
            action.get('space_freed_gb', 0) 
            for action in results['actions']
        )
        
        print(f"\nForced cleanup completed:")
        print(f"Total space freed: {total_freed:.2f} GB")
        
    else:
        # Normal operation - check and expand specific path or all monitored paths
        if args.path:
            results = expander.check_and_expand(args.path)
        else:
            # Check all critical paths
            critical_paths = [
                '/nvme0n1-disk',
                '/srv/samba/shared',
                '/'
            ]
            
            for path in critical_paths:
                if os.path.exists(path):
                    results = expander.check_and_expand(path)

if __name__ == "__main__":
    main()