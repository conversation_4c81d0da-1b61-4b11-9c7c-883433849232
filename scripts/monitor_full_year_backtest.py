#!/usr/bin/env python3
"""Enhanced monitoring script for full year GPU backtesting"""
import os
import time
import psutil
from datetime import datetime, <PERSON><PERSON><PERSON>

def monitor_full_year_backtest():
    """Monitor the full year GPU backtest with enhanced metrics"""
    output_file = "bt/backtester_stable/BTRUN/output/tbs_2024_full_year.xlsx"
    
    print("=== GPU Full Year Backtesting Monitor ===")
    print(f"Monitoring: {output_file}")
    print(f"Date Range: 2024-01-01 to 2024-12-31 (365 days)")
    print("Press Ctrl+C to stop monitoring\n")
    
    start_time = datetime.now()
    last_size = 0
    last_mod_time = None
    
    while True:
        try:
            # Check GPU processes
            gpu_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'BTRunPortfolio_GPU' in ' '.join(proc.info['cmdline'] or []):
                        gpu_processes.append(proc.info['pid'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Check if output file exists
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                mod_time = datetime.fromtimestamp(os.path.getmtime(output_file))
                elapsed = datetime.now() - start_time
                
                # Calculate progress indicators
                size_change = file_size - last_size
                time_since_mod = (datetime.now() - mod_time).seconds if mod_time else 0
                
                # Estimate progress (rough estimate based on file size)
                estimated_final_size = 50 * 1024 * 1024  # 50MB estimate for full year
                progress_pct = min(100, (file_size / estimated_final_size) * 100)
                
                print(f"\r[{datetime.now().strftime('%H:%M:%S')}] "
                      f"Size: {file_size/1024/1024:.1f}MB "
                      f"(+{size_change/1024:.1f}KB) | "
                      f"Progress: ~{progress_pct:.1f}% | "
                      f"Processes: {len(gpu_processes)} | "
                      f"Elapsed: {elapsed} | "
                      f"Last update: {time_since_mod}s ago", end='', flush=True)
                
                last_size = file_size
                last_mod_time = mod_time
                
                # Check if likely complete
                if time_since_mod > 60 and len(gpu_processes) == 0:
                    print(f"\n\n🎉 Backtest appears to be COMPLETE!")
                    print(f"📊 Final file size: {file_size/1024/1024:.1f}MB")
                    print(f"⏱️  Total time: {elapsed}")
                    print(f"📈 Processing rate: ~{365 / (elapsed.total_seconds() / 3600):.1f} days/hour")
                    break
                    
            else:
                elapsed = datetime.now() - start_time
                print(f"\r[{datetime.now().strftime('%H:%M:%S')}] "
                      f"Waiting for output... | "
                      f"Processes: {len(gpu_processes)} | "
                      f"Elapsed: {elapsed}", end='', flush=True)
            
            time.sleep(10)  # Check every 10 seconds
            
        except KeyboardInterrupt:
            print("\n\n⏹️  Monitoring stopped by user.")
            print(f"⏱️  Total monitoring time: {datetime.now() - start_time}")
            break
        except Exception as e:
            print(f"\n\n❌ Error: {e}")
            break

if __name__ == "__main__":
    monitor_full_year_backtest() 