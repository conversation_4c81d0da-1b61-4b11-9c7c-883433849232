# Local MySQL Setup for Legacy Backtester Comparison

This guide explains how to set up a local MySQL environment to replicate the legacy backtester data and run comparisons with the HeavyDB GPU-optimized version.

## Overview

The setup allows you to:
- Copy production MySQL data to a local instance
- Run the legacy backtester against local data
- Compare performance and results between legacy (MySQL) and new (HeavyDB) systems
- Benchmark GPU acceleration benefits

## Prerequisites

- Ubuntu/Debian Linux system
- Python 3.8+
- sudo access (for MySQL installation)
- Network access to remote MySQL server (************)

## Setup Steps

### 1. Install MySQL Server (if not already installed)

```bash
chmod +x scripts/setup_local_mysql.sh
./scripts/setup_local_mysql.sh
```

This will:
- Install MySQL server and client
- Create the `historicaldb` database
- Create user `mahesh` with password `mahesh_123`
- Grant necessary permissions

### 2. Check Remote MySQL Schema

```bash
python3 scripts/check_remote_mysql_schema.py
```

This will:
- Connect to the remote MySQL server
- List all tables and their structures
- Show row counts and date ranges
- Save schema information to `remote_mysql_schema.txt`

### 3. Copy Data from Remote to Local

```bash
python3 scripts/copy_mysql_data.py
```

Options:
1. **Copy priority tables only** (recommended for testing)
   - Copies essential tables: nifty_spot, nifty_cash, nifty_call, nifty_put, nifty_future, holidays, lot_size
   - Fastest option for initial testing

2. **Copy all tables**
   - Copies entire database
   - May take longer depending on data size

3. **Copy specific date range**
   - Allows filtering data by date range
   - Useful for specific period testing

### 4. Run Comparison Tests

```bash
python3 scripts/run_legacy_vs_heavydb_comparison.py
```

This will:
- Run both legacy (MySQL) and HeavyDB backtests
- Compare execution times and results
- Generate comparison reports

## File Structure

```
scripts/
├── setup_local_mysql.sh              # MySQL installation script
├── check_remote_mysql_schema.py      # Schema inspection tool
├── copy_mysql_data.py               # Data migration tool
├── run_legacy_vs_heavydb_comparison.py  # Comparison runner
└── LOCAL_MYSQL_SETUP_README.md      # This file

output/
├── legacy_backtest_output.xlsx      # Legacy system output
├── heavydb_backtest_output.xlsx     # HeavyDB system output
├── comparison_report.json           # Detailed comparison
└── comparison_summary.csv           # Summary metrics
```

## Connection Details

### Remote MySQL (Production)
- Host: ************
- Port: 3306
- User: mahesh
- Password: mahesh_123
- Database: historicaldb

### Local MySQL (For Testing)
- Host: localhost
- Port: 3306
- User: mahesh
- Password: mahesh_123
- Database: historicaldb

### HeavyDB (GPU Database)
- Host: 127.0.0.1
- Port: 6274
- User: admin
- Password: HyperInteractive
- Database: heavyai

## Troubleshooting

### MySQL Connection Issues

1. Check MySQL service status:
   ```bash
   sudo systemctl status mysql
   ```

2. Verify user can connect:
   ```bash
   mysql -u mahesh -p -h localhost historicaldb
   ```

3. Check firewall:
   ```bash
   sudo ufw status
   ```

### Data Copy Issues

1. Check network connectivity to remote server:
   ```bash
   ping ************
   ```

2. Verify remote MySQL access:
   ```bash
   mysql -h ************ -u mahesh -p historicaldb
   ```

3. For large tables, increase batch size in `copy_mysql_data.py`

### Legacy Code Issues

1. Ensure legacy code is in `bt/archive/backtester_stable/BTRUN/`
2. Check Python dependencies are installed
3. Verify local MySQL config override is created

## Performance Expectations

Typical speedup with GPU acceleration:
- Small datasets (1 day): 2-5x faster
- Medium datasets (1 month): 10-20x faster
- Large datasets (1 year): 50-100x faster

The speedup depends on:
- Data size
- Query complexity
- GPU specifications (A100 provides best performance)
- Number of concurrent queries

## Next Steps

1. Run single-day comparisons first to verify setup
2. Gradually increase date ranges for performance testing
3. Compare results to ensure accuracy
4. Use findings to optimize HeavyDB queries further 