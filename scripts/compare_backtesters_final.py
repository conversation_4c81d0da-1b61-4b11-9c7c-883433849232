#!/usr/bin/env python3
"""
Final comparison of legacy and new backtesters
Legacy uses mock data since external services aren't available
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import json

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

class BacktesterFinalComparison:
    def __init__(self):
        self.portfolio_excel = "bt/backtester_stable/BTRUN/input_sheets/input_portfolio.xlsx"
        self.start_date = "20250401"
        self.end_date = "20250401"  # Single day test
        
    def run_new_backtester(self):
        """Run the new GPU-accelerated backtester"""
        print("\n" + "="*60)
        print("Running New GPU Backtester")
        print("="*60)
        
        output_path = "comparison_outputs/new_gpu_output.xlsx"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        env = os.environ.copy()
        env['PYTHONPATH'] = os.path.abspath('.')
        
        cmd = [
            sys.executable,
            "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
            "--legacy-excel",
            "--portfolio-excel", self.portfolio_excel,
            "--output-path", output_path,
            "--start-date", self.start_date,
            "--end-date", self.end_date
        ]
        
        print(f"  Running BTRunPortfolio_GPU")
        print(f"  Date range: {self.start_date} to {self.end_date}")
        
        try:
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True,
                timeout=180
            )
            
            if result.returncode == 0 and os.path.exists(output_path):
                print(f"  ✓ Output created: {output_path}")
                
                # Also check for JSON output
                json_path = output_path.replace('.xlsx', '.json')
                if os.path.exists(json_path):
                    print(f"  ✓ JSON output: {json_path}")
                    
                return output_path
            else:
                print("  ✗ New backtester failed")
                if result.stderr:
                    print(f"  Error: {result.stderr[-500:]}")
                return None
                
        except Exception as e:
            print(f"  ✗ Error: {e}")
            return None
    
    def check_legacy_output(self):
        """Check if legacy output exists from previous run"""
        print("\n" + "="*60)
        print("Checking Legacy Backtester Output")
        print("="*60)
        
        legacy_output = "comparison_outputs/legacy_output_patched.xlsx"
        
        if os.path.exists(legacy_output):
            print(f"  ✓ Found existing legacy output: {legacy_output}")
            print("  Note: Legacy uses mock data (external services not available)")
            return legacy_output
        else:
            print("  ✗ No legacy output found")
            print("  Run 'python3 scripts/patch_legacy_backtester.py' first")
            return None
    
    def analyze_outputs(self, new_output, legacy_output):
        """Analyze and compare the outputs"""
        print("\n" + "="*60)
        print("Output Analysis")
        print("="*60)
        
        outputs = {}
        
        # Load both Excel files
        for name, path in [("New", new_output), ("Legacy", legacy_output)]:
            if path and os.path.exists(path):
                try:
                    xl_file = pd.ExcelFile(path)
                    outputs[name] = {
                        'sheets': xl_file.sheet_names,
                        'data': {}
                    }
                    
                    # Load key sheets
                    for sheet in xl_file.sheet_names[:5]:  # First 5 sheets
                        try:
                            outputs[name]['data'][sheet] = pd.read_excel(xl_file, sheet_name=sheet)
                        except:
                            pass
                            
                    print(f"\n{name} Backtester Output:")
                    print(f"  Sheets: {', '.join(xl_file.sheet_names[:5])}")
                    
                    # Look for metrics/summary sheet
                    for sheet in ['Metrics', 'Summary', 'PORTFOLIO Trans']:
                        if sheet in outputs[name]['data']:
                            df = outputs[name]['data'][sheet]
                            print(f"\n  {sheet} Sheet:")
                            print(f"    Rows: {df.shape[0]}, Columns: {df.shape[1]}")
                            
                            # Show P&L if available
                            pnl_cols = [col for col in df.columns if 'pnl' in col.lower() or 'p&l' in col.lower()]
                            if pnl_cols:
                                print(f"    P&L Columns: {pnl_cols}")
                                for col in pnl_cols[:3]:  # First 3 P&L columns
                                    if not df[col].empty:
                                        total = df[col].sum() if df[col].dtype in ['float64', 'int64'] else 'N/A'
                                        print(f"      {col}: {total}")
                            break
                            
                except Exception as e:
                    print(f"  Error loading {name}: {e}")
        
        # Compare structures
        if len(outputs) == 2:
            print("\n" + "-"*60)
            print("Structural Comparison:")
            print("-"*60)
            
            new_sheets = set(outputs["New"]['sheets'])
            legacy_sheets = set(outputs["Legacy"]['sheets'])
            
            common_sheets = new_sheets.intersection(legacy_sheets)
            print(f"  Common sheets: {len(common_sheets)}")
            print(f"  New only: {new_sheets - legacy_sheets}")
            print(f"  Legacy only: {legacy_sheets - new_sheets}")
            
            # Compare data in common sheets
            for sheet in list(common_sheets)[:3]:  # First 3 common sheets
                if sheet in outputs["New"]['data'] and sheet in outputs["Legacy"]['data']:
                    new_df = outputs["New"]['data'][sheet]
                    legacy_df = outputs["Legacy"]['data'][sheet]
                    
                    print(f"\n  Sheet '{sheet}':")
                    print(f"    New: {new_df.shape}")
                    print(f"    Legacy: {legacy_df.shape}")
                    
                    # Compare columns
                    new_cols = set(new_df.columns)
                    legacy_cols = set(legacy_df.columns)
                    common_cols = new_cols.intersection(legacy_cols)
                    
                    if len(common_cols) > 0:
                        print(f"    Common columns: {len(common_cols)}/{len(new_cols)}")
    
    def run_comparison(self):
        """Run the full comparison"""
        print("="*80)
        print("Final Backtester Comparison")
        print(f"Time: {datetime.now()}")
        print("="*80)
        
        # Check input file
        if not os.path.exists(self.portfolio_excel):
            print(f"\n✗ Input file not found: {self.portfolio_excel}")
            return
        
        print(f"\n✓ Input file: {self.portfolio_excel}")
        
        # Run new backtester
        new_output = self.run_new_backtester()
        
        # Check legacy output
        legacy_output = self.check_legacy_output()
        
        # Analyze outputs
        if new_output or legacy_output:
            self.analyze_outputs(new_output, legacy_output)
        
        # Summary
        print("\n" + "="*80)
        print("Summary")
        print("="*80)
        print(f"New GPU Backtester: {'✓ Success' if new_output else '✗ Failed'}")
        print(f"Legacy Backtester: {'✓ Available (mock data)' if legacy_output else '✗ Not available'}")
        
        print("\nKey Differences:")
        print("1. New backtester uses HeavyDB for GPU acceleration")
        print("2. Legacy backtester requires external services (using mock data)")
        print("3. New backtester has improved data models and validation")
        print("4. Output formats may differ but core metrics should align")
        
        if new_output:
            print(f"\n✓ New backtester output saved to: {new_output}")
            print("  This is the recommended backtester for production use")

if __name__ == "__main__":
    comparison = BacktesterFinalComparison()
    comparison.run_comparison() 