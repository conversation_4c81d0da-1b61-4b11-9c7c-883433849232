import os
import csv

# Define paths
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
FIXTURES_DIR = os.path.join(BASE_DIR, 'tests', 'fixtures')
EXCEL_PARSER_DIR = os.path.join(BASE_DIR, 'excel_parser')

PORTFOLIO_HEADERS_CSV = os.path.join(FIXTURES_DIR, 'portfolio_headers.csv')
GENERAL_HEADERS_CSV = os.path.join(FIXTURES_DIR, 'general_headers.csv')
LEG_HEADERS_CSV = os.path.join(FIXTURES_DIR, 'leg_headers.csv')

HEADER_MAP_PY = os.path.join(EXCEL_PARSER_DIR, 'header_map.py')

def load_headers_from_csv(csv_path):
    """Loads headers from a CSV file."""
    try:
        with open(csv_path, 'r', newline='') as csvfile:
            reader = csv.reader(csvfile)
            headers = next(reader)
            return headers
    except FileNotFoundError:
        print(f"Error: CSV file not found at {csv_path}. Run scripts/extract_headers.py first.")
        return None
    except StopIteration:
        print(f"Error: CSV file is empty: {csv_path}.")
        return None
    except Exception as e:
        print(f"An unexpected error occurred while reading {csv_path}: {e}")
        return None

def generate_mapping_dict_string(headers, dict_name):
    """Generates a string representation of a dictionary for the header map."""
    if headers is None:
        return f"{dict_name} = {{}}\n"
    
    dict_items = []
    for header in sorted(headers):
        dict_items.append(f"    '{header}': 'TODO',  # TODO: Map this column")
    
    return f"{dict_name} = {{\n" + '\n'.join(dict_items) + "\n}}\n"

def main():
    portfolio_headers = load_headers_from_csv(PORTFOLIO_HEADERS_CSV)
    general_headers = load_headers_from_csv(GENERAL_HEADERS_CSV)
    leg_headers = load_headers_from_csv(LEG_HEADERS_CSV)

    if not all([portfolio_headers, general_headers, leg_headers]):
        print("One or more header CSV files could not be read. Aborting.")
        return

    # Ensure excel_parser directory exists
    if not os.path.exists(EXCEL_PARSER_DIR):
        os.makedirs(EXCEL_PARSER_DIR)
        print(f"Created directory: {EXCEL_PARSER_DIR}")

    content = "# excel_parser/header_map.py\n\n"
    content += "# This file is auto-generated by scripts/make_initial_mappings.py\n"
    content += "# Please review and update the 'TODO' placeholders with actual model field names or None.\n\n"
    
    content += "# Mapping for columns from PortfolioSetting and StrategySetting sheets\n"
    content += generate_mapping_dict_string(portfolio_headers, "PORTFOLIO_COLUMNS")
    content += "\n"
    content += "# Mapping for columns from GeneralParameter sheet\n"
    content += generate_mapping_dict_string(general_headers, "GENERAL_COLUMNS")
    content += "\n"
    content += "# Mapping for columns from LegParameter sheet\n"
    content += generate_mapping_dict_string(leg_headers, "LEG_COLUMNS")

    try:
        with open(HEADER_MAP_PY, 'w') as f:
            f.write(content)
        print(f"Successfully updated {HEADER_MAP_PY} with initial mappings.")
        print("Please review the 'TODO' placeholders in the file.")
    except IOError as e:
        print(f"Error writing to {HEADER_MAP_PY}: {e}")
    except Exception as e:
        print(f"An unexpected error occurred while writing to {HEADER_MAP_PY}: {e}")

if __name__ == '__main__':
    main() 