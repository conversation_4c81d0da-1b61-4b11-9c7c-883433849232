#!/bin/bash
# Load nifty spot OHLC data into HeavyDB

# HeavyDB connection details
HOST="127.0.0.1"
PORT="6274"
USER="admin"
PASSWORD="HyperInteractive"
DATABASE="heavyai"

# File paths
CSV_FILE="/srv/samba/shared/market_data/nifty/nifty_cash_merged_rounded_v2.csv"
SQL_FILE="/srv/samba/shared/scripts/create_nifty_spot_table.sql"

echo "Creating nifty_spot table..."
/opt/heavyai/bin/heavysql -s $HOST --port $PORT -u $USER -p $PASSWORD --db $DATABASE -q < $SQL_FILE

if [ $? -ne 0 ]; then
    echo "Error creating table. Exiting."
    exit 1
fi

echo "Loading data from CSV..."
# Use COPY command to load data
# Note: HeavyDB expects the date and time columns to match the table format
/opt/heavyai/bin/heavysql -s $HOST --port $PORT -u $USER -p $PASSWORD --db $DATABASE << EOF
COPY nifty_spot FROM '$CSV_FILE' WITH (header='true', delimiter=',');
EOF

if [ $? -ne 0 ]; then
    echo "Error loading data. Exiting."
    exit 1
fi

echo "Verifying data load..."
/opt/heavyai/bin/heavysql -s $HOST --port $PORT -u $USER -p $PASSWORD --db $DATABASE << EOF
SELECT COUNT(*) AS total_rows FROM nifty_spot;
SELECT MIN(trade_date) AS start_date, MAX(trade_date) AS end_date FROM nifty_spot;
SELECT * FROM nifty_spot LIMIT 5;
EOF

echo "Data load complete!" 