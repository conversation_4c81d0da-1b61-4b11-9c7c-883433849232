#!/usr/bin/env python3
"""Compare legacy and GPU Excel outputs."""

import pandas as pd
import sys
import os
from openpyxl import load_workbook

def compare_sheets(legacy_file, gpu_file):
    """Compare sheets between legacy and GPU outputs."""
    
    print("="*80)
    print("Comparing Excel outputs")
    print("="*80)
    
    # Load workbooks to get sheet names
    legacy_wb = load_workbook(legacy_file, read_only=True)
    gpu_wb = load_workbook(gpu_file, read_only=True)
    
    legacy_sheets = set(legacy_wb.sheetnames)
    gpu_sheets = set(gpu_wb.sheetnames)
    
    print(f"\nLegacy sheets: {sorted(legacy_sheets)}")
    print(f"GPU sheets: {sorted(gpu_sheets)}")
    
    # Check sheet differences
    only_legacy = legacy_sheets - gpu_sheets
    only_gpu = gpu_sheets - legacy_sheets
    common_sheets = legacy_sheets & gpu_sheets
    
    if only_legacy:
        print(f"\n⚠️  Sheets only in legacy: {sorted(only_legacy)}")
    if only_gpu:
        print(f"\n⚠️  Sheets only in GPU: {sorted(only_gpu)}")
    
    print(f"\nCommon sheets to compare: {sorted(common_sheets)}")
    
    # Compare common sheets
    for sheet in sorted(common_sheets):
        print(f"\n{'='*60}")
        print(f"Comparing sheet: {sheet}")
        print(f"{'='*60}")
        
        # Read data
        legacy_df = pd.read_excel(legacy_file, sheet_name=sheet)
        gpu_df = pd.read_excel(gpu_file, sheet_name=sheet)
        
        print(f"Legacy shape: {legacy_df.shape}")
        print(f"GPU shape: {gpu_df.shape}")
        
        # Compare columns
        legacy_cols = set(legacy_df.columns)
        gpu_cols = set(gpu_df.columns)
        
        if legacy_cols != gpu_cols:
            print(f"\n⚠️  Column differences:")
            only_legacy_cols = legacy_cols - gpu_cols
            only_gpu_cols = gpu_cols - legacy_cols
            if only_legacy_cols:
                print(f"  Only in legacy: {sorted(only_legacy_cols)}")
            if only_gpu_cols:
                print(f"  Only in GPU: {sorted(only_gpu_cols)}")
        
        # For transaction sheets, compare key metrics
        if "Trans" in sheet:
            print("\nComparing transactions:")
            # Count of trades
            print(f"  Legacy trades: {len(legacy_df)}")
            print(f"  GPU trades: {len(gpu_df)}")
            
            # Check if we have Net PNL column
            if 'Net PNL' in legacy_df.columns and 'Net PNL' in gpu_df.columns:
                legacy_total_pnl = legacy_df['Net PNL'].sum()
                gpu_total_pnl = gpu_df['Net PNL'].sum()
                print(f"  Legacy total PNL: {legacy_total_pnl:.2f}")
                print(f"  GPU total PNL: {gpu_total_pnl:.2f}")
                print(f"  Difference: {abs(legacy_total_pnl - gpu_total_pnl):.2f}")
            
            # Check strikes
            if 'Strike' in legacy_df.columns and 'Strike' in gpu_df.columns:
                legacy_strikes = sorted(legacy_df['Strike'].unique())
                gpu_strikes = sorted(gpu_df['Strike'].unique())
                print(f"  Legacy strikes: {legacy_strikes}")
                print(f"  GPU strikes: {gpu_strikes}")
                
            # Check entry/exit times
            if 'Enter On' in legacy_df.columns and 'Enter On' in gpu_df.columns:
                print(f"  Legacy entry times: {sorted(legacy_df['Enter On'].unique())}")
                print(f"  GPU entry times: {sorted(gpu_df['Enter On'].unique())}")
            
            if 'Exit at' in legacy_df.columns and 'Exit at' in gpu_df.columns:
                print(f"  Legacy exit times: {sorted(legacy_df['Exit at'].unique())}")
                print(f"  GPU exit times: {sorted(gpu_df['Exit at'].unique())}")
        
        # For metrics sheet
        if sheet == "Metrics":
            print("\nComparing metrics:")
            # Show first few rows
            print("\nLegacy metrics (first 5):")
            print(legacy_df.head())
            print("\nGPU metrics (first 5):")
            print(gpu_df.head())
    
    print("\n" + "="*80)
    print("Comparison complete")
    print("="*80)

def main():
    if len(sys.argv) != 3:
        print("Usage: python compare_excel_outputs.py <legacy_file> <gpu_file>")
        sys.exit(1)
    
    legacy_file = sys.argv[1]
    gpu_file = sys.argv[2]
    
    if not os.path.exists(legacy_file):
        print(f"❌ Legacy file not found: {legacy_file}")
        sys.exit(1)
    
    if not os.path.exists(gpu_file):
        print(f"❌ GPU file not found: {gpu_file}")
        sys.exit(1)
    
    compare_sheets(legacy_file, gpu_file)

if __name__ == "__main__":
    main() 