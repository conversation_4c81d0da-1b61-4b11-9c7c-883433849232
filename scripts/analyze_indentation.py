#!/usr/bin/env python3
"""Analyze indentation to find why only 1 trade is generated."""

with open('bt/backtester_stable/BTRUN/strategies/heavydb_trade_processing.py', 'r') as f:
    lines = f.readlines()

# Find key loop lines
leg_loop_line = None
append_line = None

for i, line in enumerate(lines):
    if 'for leg in strategy.legs:' in line:
        leg_loop_line = i
        print(f'Line {i+1}: Found leg loop with {len(line) - len(line.lstrip())} spaces indentation')
        print(f'  Content: {line.strip()}')
    
    if 'trade_records.append(record)' in line:
        append_line = i
        print(f'\nLine {i+1}: Found append with {len(line) - len(line.lstrip())} spaces indentation')
        print(f'  Content: {line.strip()}')

if leg_loop_line is not None and append_line is not None:
    print(f'\n=== Analysis ===')
    leg_indent = len(lines[leg_loop_line]) - len(lines[leg_loop_line].lstrip())
    append_indent = len(lines[append_line]) - len(lines[append_line].lstrip())
    
    print(f'Leg loop indentation: {leg_indent} spaces')
    print(f'Append indentation: {append_indent} spaces')
    print(f'Difference: {append_indent - leg_indent} spaces')
    
    if append_indent == leg_indent + 4:
        print('✓ Append is correctly inside the leg loop (first level)')
    elif append_indent > leg_indent:
        print(f'✓ Append is inside the leg loop ({(append_indent - leg_indent)//4} levels deep)')
    else:
        print('✗ ERROR: Append is OUTSIDE the leg loop!')
        
    # Check what's between leg loop and append
    print(f'\n=== Code structure between leg loop and append ===')
    for i in range(leg_loop_line, min(leg_loop_line + 10, append_line)):
        indent = len(lines[i]) - len(lines[i].lstrip())
        print(f'Line {i+1} ({indent:2d} spaces): {lines[i].strip()[:60]}...') 