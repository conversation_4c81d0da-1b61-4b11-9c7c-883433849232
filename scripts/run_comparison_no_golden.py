#!/usr/bin/env python3
"""
Run both legacy and refactored backtesters for comparison
Works without golden output file
"""

import os
import sys
import subprocess
import pandas as pd
from datetime import datetime
import shutil

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configuration - using actual TBS input file
TEST_CONFIG = {
    "portfolio_excel": "bt/backtester_stable/BTRUN/input_sheets/INPUT TBS.xlsx",
    "start_date": "20250401",
    "end_date": "20250402",  # Short test period
    "legacy_output": "legacy_tbs_output.xlsx",
    "new_output": "new_tbs_output.xlsx"
}

def check_input_files():
    """Verify input files exist"""
    print("\n1. Checking Input Files:")
    print("-" * 60)
    
    if os.path.exists(TEST_CONFIG["portfolio_excel"]):
        print(f"  ✓ Portfolio Excel: {TEST_CONFIG['portfolio_excel']}")
        return True
    else:
        print(f"  ✗ Portfolio Excel NOT FOUND: {TEST_CONFIG['portfolio_excel']}")
        return False

def run_legacy_backtester():
    """Run the legacy backtester"""
    print("\n2. Running Legacy Backtester (Archive Code):")
    print("-" * 60)
    
    # Check if legacy code exists
    legacy_main = "bt/archive/backtester_stable/BTRUN/BTRunPortfolio.py"
    
    if not os.path.exists(legacy_main):
        print(f"  ✗ Legacy code not found at: {legacy_main}")
        print("  ⚠ Skipping legacy test")
        return False
    
    print("  ⚠ Legacy backtester may have compatibility issues")
    print("  ⚠ Skipping for now - focus on new backtester")
    return False

def run_new_backtester():
    """Run the new refactored backtester"""
    print("\n3. Running New Backtester (Refactored Code):")
    print("-" * 60)
    
    env = os.environ.copy()
    env['PYTHONPATH'] = os.path.abspath('.')
    
    cmd = [
        sys.executable,
        "-m", "bt.backtester_stable.BTRUN.BTRunPortfolio_GPU",
        "--legacy-excel",
        "--portfolio-excel", TEST_CONFIG["portfolio_excel"],
        "--output-path", TEST_CONFIG["new_output"],
        "--start-date", TEST_CONFIG["start_date"],
        "--end-date", TEST_CONFIG["end_date"]
    ]
    
    print(f"  Command: {' '.join(cmd[2:])}")  # Skip python path
    print(f"  Date range: {TEST_CONFIG['start_date']} to {TEST_CONFIG['end_date']}")
    
    try:
        result = subprocess.run(
            cmd,
            env=env,
            capture_output=True,
            text=True,
            timeout=180
        )
        
        print(f"\n  Return code: {result.returncode}")
        
        if result.returncode == 0:
            print("  ✓ New backtester completed successfully")
            
            # Check output
            if os.path.exists(TEST_CONFIG["new_output"]):
                file_size = os.path.getsize(TEST_CONFIG["new_output"])
                print(f"  ✓ Output created: {TEST_CONFIG['new_output']} ({file_size:,} bytes)")
                return True
            else:
                print(f"  ⚠ Output file not found at expected location")
                return False
        else:
            print("  ✗ New backtester failed")
            if result.stderr:
                print("\n  Error output (last 500 chars):")
                print("  " + result.stderr[-500:].replace("\n", "\n  "))
            return False
            
    except subprocess.TimeoutExpired:
        print("  ✗ Backtester timed out (>3 minutes)")
        return False
    except Exception as e:
        print(f"  ✗ Error: {e}")
        return False

def analyze_output():
    """Analyze the output from new backtester"""
    print("\n4. Analyzing Output:")
    print("-" * 60)
    
    if not os.path.exists(TEST_CONFIG["new_output"]):
        print("  ✗ No output file to analyze")
        return
    
    try:
        # Load Excel file
        xl_file = pd.ExcelFile(TEST_CONFIG["new_output"])
        print(f"  ✓ Loaded output file with {len(xl_file.sheet_names)} sheets")
        
        # Show sheet names
        print("\n  Sheet names:")
        for sheet in xl_file.sheet_names:
            df = pd.read_excel(xl_file, sheet_name=sheet)
            print(f"    - {sheet}: {df.shape[0]} rows, {df.shape[1]} columns")
        
        # Check for summary sheet
        if 'Summary' in xl_file.sheet_names:
            summary_df = pd.read_excel(xl_file, sheet_name='Summary')
            print("\n  Summary metrics:")
            for col in summary_df.columns:
                if 'P&L' in col or 'PnL' in col:
                    print(f"    - {col}: {summary_df[col].iloc[0] if len(summary_df) > 0 else 'N/A'}")
                    
    except Exception as e:
        print(f"  ✗ Error analyzing output: {e}")

def check_data_availability():
    """Check if HeavyDB has data for the test period"""
    print("\n5. Data Availability Check:")
    print("-" * 60)
    
    try:
        from heavydb import connect
    except ImportError:
        import pymapd
        connect = pymapd.connect
    
    try:
        conn = connect(
            host="127.0.0.1",
            port=6274,
            user="admin",
            password="HyperInteractive",
            dbname="heavyai"
        )
        cursor = conn.cursor()
        
        # Check data for test period
        cursor.execute(f"""
            SELECT 
                COUNT(*) as records,
                COUNT(DISTINCT trade_date) as days,
                COUNT(DISTINCT strike) as strikes,
                COUNT(DISTINCT expiry_date) as expiries
            FROM nifty_option_chain
            WHERE trade_date >= DATE '{TEST_CONFIG["start_date"][:4]}-{TEST_CONFIG["start_date"][4:6]}-{TEST_CONFIG["start_date"][6:]}'
            AND trade_date <= DATE '{TEST_CONFIG["end_date"][:4]}-{TEST_CONFIG["end_date"][4:6]}-{TEST_CONFIG["end_date"][6:]}'
        """)
        
        result = cursor.fetchone()
        if result and result[0] > 0:
            print(f"  ✓ HeavyDB has data for test period:")
            print(f"    - Records: {result[0]:,}")
            print(f"    - Trading days: {result[1]}")
            print(f"    - Unique strikes: {result[2]}")
            print(f"    - Unique expiries: {result[3]}")
        else:
            print("  ✗ No data in HeavyDB for test period")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"  ✗ Error checking HeavyDB: {e}")

def main():
    """Main execution"""
    print("="*80)
    print("Backtester Test - TBS Strategy")
    print(f"Time: {datetime.now()}")
    print("="*80)
    
    # Check input files
    if not check_input_files():
        print("\n✗ Cannot proceed without input files")
        return
    
    # Check data availability
    check_data_availability()
    
    # Run backtesters
    legacy_success = run_legacy_backtester()
    new_success = run_new_backtester()
    
    # Analyze results
    if new_success:
        analyze_output()
    
    # Summary
    print("\n" + "="*80)
    print("Summary:")
    print("="*80)
    print(f"Legacy Backtester: {'✓ Success' if legacy_success else '✗ Skipped/Failed'}")
    print(f"New Backtester: {'✓ Success' if new_success else '✗ Failed'}")
    
    if new_success:
        print(f"\n✓ New backtester output saved to: {TEST_CONFIG['new_output']}")
        print("  You can open this Excel file to review the results")
    else:
        print("\n✗ Test failed - check error messages above")

if __name__ == "__main__":
    main() 