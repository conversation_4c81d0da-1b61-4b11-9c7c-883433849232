#!/usr/bin/env python3
"""Prepare legacy backtester configuration for local MySQL testing."""

import os
import shutil
from datetime import datetime

def prepare_legacy_config():
    """Update legacy config.py for local MySQL testing."""
    
    config_path = "bt/archive/backtester_stable/BTRUN/config.py"
    backup_path = f"{config_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    print("Preparing legacy configuration for local testing...")
    print(f"Config file: {config_path}")
    
    # Check if config exists
    if not os.path.exists(config_path):
        print(f"ERROR: Config file not found at {config_path}")
        return False
    
    # Create backup
    shutil.copy2(config_path, backup_path)
    print(f"Created backup: {backup_path}")
    
    # Read current config
    with open(config_path, 'r') as f:
        lines = f.readlines()
    
    # Update configuration
    updated_lines = []
    changes_made = []
    
    for line in lines:
        original_line = line
        
        # Update host to localhost
        if 'host' in line and '************' in line:
            line = line.replace('************', 'localhost')
            changes_made.append("Updated host from ************ to localhost")
        elif 'host' in line and '=' in line and not line.strip().startswith('#'):
            # Generic host update
            if 'localhost' not in line and '127.0.0.1' not in line:
                parts = line.split('=')
                if len(parts) == 2:
                    line = f"{parts[0]}= 'localhost'\n"
                    changes_made.append(f"Updated host to localhost")
        
        # Add or update USE_SYNTHETIC_FUTURE_ATM
        if 'USE_SYNTHETIC_FUTURE_ATM' in line:
            if 'False' in line:
                line = line.replace('False', 'True')
                changes_made.append("Updated USE_SYNTHETIC_FUTURE_ATM from False to True")
            elif '= True' not in line:
                line = "USE_SYNTHETIC_FUTURE_ATM = True\n"
                changes_made.append("Set USE_SYNTHETIC_FUTURE_ATM = True")
        
        updated_lines.append(line)
    
    # If USE_SYNTHETIC_FUTURE_ATM not found, add it
    if not any('USE_SYNTHETIC_FUTURE_ATM' in line for line in updated_lines):
        updated_lines.append("\n# ATM calculation method for parity with HeavyDB\n")
        updated_lines.append("USE_SYNTHETIC_FUTURE_ATM = True\n")
        changes_made.append("Added USE_SYNTHETIC_FUTURE_ATM = True")
    
    # Write updated config
    with open(config_path, 'w') as f:
        f.writelines(updated_lines)
    
    print("\nChanges made:")
    for change in changes_made:
        print(f"  - {change}")
    
    if not changes_made:
        print("  - No changes needed (config already correct)")
    
    # Verify the changes
    print("\nVerifying configuration...")
    with open(config_path, 'r') as f:
        content = f.read()
        
    localhost_found = 'localhost' in content or '127.0.0.1' in content
    synthetic_atm_found = 'USE_SYNTHETIC_FUTURE_ATM = True' in content
    
    if localhost_found and synthetic_atm_found:
        print("✅ Configuration verified successfully!")
        print("  - Host is set to localhost")
        print("  - USE_SYNTHETIC_FUTURE_ATM is True")
        return True
    else:
        print("❌ Configuration verification failed!")
        if not localhost_found:
            print("  - Host is NOT set to localhost")
        if not synthetic_atm_found:
            print("  - USE_SYNTHETIC_FUTURE_ATM is NOT True")
        return False

def check_legacy_environment():
    """Check if legacy environment is ready for testing."""
    
    print("\nChecking legacy environment...")
    
    # Check if archive exists
    archive_path = "bt/archive/backtester_stable/BTRUN"
    if not os.path.exists(archive_path):
        print(f"❌ Archive path not found: {archive_path}")
        return False
    
    # Check key files
    required_files = [
        "BTRunPortfolio.py",
        "Util.py",
        "config.py"
    ]
    
    all_found = True
    for file in required_files:
        file_path = os.path.join(archive_path, file)
        if os.path.exists(file_path):
            print(f"✅ Found: {file}")
        else:
            print(f"❌ Missing: {file}")
            all_found = False
    
    # Check input files
    input_path = "bt/backtester_stable/BTRUN/input_sheets"
    if os.path.exists(input_path):
        print(f"✅ Input sheets directory found")
        
        # List available input files
        xlsx_files = [f for f in os.listdir(input_path) if f.endswith('.xlsx')]
        if xlsx_files:
            print(f"  Available input files: {len(xlsx_files)}")
            for f in xlsx_files[:5]:  # Show first 5
                print(f"    - {f}")
    else:
        print(f"❌ Input sheets directory not found: {input_path}")
        all_found = False
    
    return all_found

def main():
    """Main function."""
    print("="*60)
    print("Legacy Backtester Configuration Preparation")
    print("="*60)
    
    # Check environment
    if not check_legacy_environment():
        print("\n❌ Legacy environment check failed. Please verify paths.")
        return 1
    
    # Update configuration
    if prepare_legacy_config():
        print("\n✅ Legacy configuration prepared successfully!")
        print("\nNext steps:")
        print("1. Run: python3 scripts/check_mysql_data_status.py")
        print("2. If data is ready, run legacy test:")
        print("   cd bt/archive/backtester_stable/BTRUN")
        print("   export MYSQL_HOST=localhost")
        print("   export DEBUG_MODE=true")
        print("   python BTRunPortfolio.py")
        return 0
    else:
        print("\n❌ Failed to prepare legacy configuration")
        return 1

if __name__ == "__main__":
    exit(main()) 