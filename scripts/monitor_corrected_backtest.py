#!/usr/bin/env python3
"""Monitor the corrected full year 2024 TBS backtest"""

import os
import time
import psutil
from datetime import datetime

def monitor_corrected_backtest():
    """Monitor the corrected full year backtest progress"""
    
    output_file = "bt/backtester_stable/BTRUN/output/tbs_2024_full_year_corrected.xlsx"
    
    print("🚀 GPU-Optimized TBS Backtesting System - CORRECTED Full Year 2024")
    print("=" * 70)
    print(f"📊 Strategy: Time-Based Strategy (TBS)")
    print(f"📅 Period: January 1, 2024 - December 31, 2024 (365 days)")
    print(f"⚡ Workers: Auto-detected optimal workers")
    print(f"🔧 GPU Processing: Fixed pickling issues")
    print(f"📁 Output: {output_file}")
    print("=" * 70)
    print()
    
    start_time = datetime.now()
    last_size = 0
    
    while True:
        try:
            # Check for running processes
            gpu_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'BTRunPortfolio_GPU' in ' '.join(proc.info['cmdline'] or []):
                        gpu_processes.append({
                            'pid': proc.info['pid'],
                            'cpu_percent': proc.cpu_percent(),
                            'memory_mb': proc.memory_info().rss / 1024 / 1024
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Check output file
            current_time = datetime.now()
            elapsed = current_time - start_time
            
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                mod_time = datetime.fromtimestamp(os.path.getmtime(output_file))
                
                # Calculate progress indicators
                size_change = file_size - last_size
                last_size = file_size
                
                print(f"✅ [{current_time.strftime('%H:%M:%S')}] Output file found!")
                print(f"   📁 Size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
                print(f"   🕒 Modified: {mod_time.strftime('%H:%M:%S')}")
                print(f"   📈 Size change: +{size_change:,} bytes")
                print(f"   ⏱️ Elapsed: {elapsed}")
                
                if gpu_processes:
                    print(f"   🔄 Active processes: {len(gpu_processes)}")
                    for proc in gpu_processes:
                        print(f"      PID {proc['pid']}: CPU {proc['cpu_percent']:.1f}%, Memory {proc['memory_mb']:.1f}MB")
                else:
                    print("   ✅ Process completed!")
                    break
                    
            else:
                print(f"⏳ [{current_time.strftime('%H:%M:%S')}] Waiting for output file... | Elapsed: {elapsed}")
                
                if gpu_processes:
                    print(f"   🔄 Active processes: {len(gpu_processes)}")
                    for proc in gpu_processes:
                        print(f"      PID {proc['pid']}: CPU {proc['cpu_percent']:.1f}%, Memory {proc['memory_mb']:.1f}MB")
                else:
                    print("   ❌ No GPU processes found - backtest may have completed or failed")
            
            print("-" * 70)
            time.sleep(10)  # Check every 10 seconds
            
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"❌ Monitor error: {e}")
            time.sleep(5)
    
    # Final status
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file)
        print(f"\n🎉 BACKTEST COMPLETED!")
        print(f"📁 Final output: {output_file}")
        print(f"📦 Final size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
        print(f"⏱️ Total time: {datetime.now() - start_time}")
        
        # Quick validation
        if file_size > 10000:  # At least 10KB
            print("✅ Output file size looks good!")
        else:
            print("⚠️ Output file seems small - check for errors")
    else:
        print(f"\n❌ BACKTEST FAILED - No output file generated")

if __name__ == "__main__":
    monitor_corrected_backtest() 