# Enterprise Integration Plan – HeavyDB GPU Back-Tester

## Update: 2025-05-13 21:30 - Implementation Progress

### Completed Components
1. **Excel Parser & Input Processing**
   - ✅ `header_map.py`: Complete mapping of all Excel columns to model fields
   - ✅ `validators.py`: Comprehensive field validation with type checking
   - ✅ `parser.py`: Full Excel parsing with error handling
   - ✅ Tests for all validation and parsing functions

2. **HeavyDB Integration**
   - ✅ `heavydb_runner.py`: Core integration with HeavyDB
   - ✅ Query generation for option chain data
   - ✅ Trade processing and P&L calculation
   - ✅ Connection pooling and error handling
   - ✅ Tests with mock data

3. **Main Runner**
   - ✅ `run_backtest.py`: Command-line interface
   - ✅ JSON and Excel output generation
   - ✅ Logging configuration
   - ✅ Error handling and reporting

### In Progress
1. **Performance Optimization**
   - 🚧 Batch SQL queries for multiple legs
   - 🚧 GPU memory optimization
   - 🚧 Connection pooling improvements

2. **Documentation**
   - ✅ README.md with installation and usage instructions
   - ✅ Excel format specifications
   - 🚧 Performance tuning guide
   - 🚧 Troubleshooting guide

### Next Steps
1. **Risk Rules Engine**
   - Implement tick-scan SL/TP/Trail logic
   - Integrate with runtime loop
   - Add tests for risk rule evaluation

2. **Parallel Execution**
   - Implement worker pool
   - Add date slice scheduling
   - Add GPU/CPU fallback
   - Add memory monitoring

3. **Quality Assurance**
   - Create test matrix
   - Add golden-file tests
   - Add performance benchmarks

### Technical Achievements
1. Successfully mapped all Excel columns to model fields with validation
2. Implemented GPU-accelerated option chain queries
3. Created modular architecture for easy extension
4. Added comprehensive test coverage
5. Established error handling patterns

### Known Issues
1. Need to optimize batch SQL for large portfolios
2. Need to implement lot size configuration
3. Need to add more detailed logging for debugging

---

_Last updated: 2025-05-13_

> **Note**: The standalone "## Plan Update: 2025-05-12" section appearing later in this document is now *deprecated* and kept only for historical reference. The canonical, up-to-date tasks and checklists are the ones listed above in section 3. A future cleanup will excise the obsolete block once all merge requests referencing it are closed.

This document expands on the high-level roadmap recorded in progress.md and activeContext.md.  It provides an execution-grade work-breakdown that engineering, QA, and DevOps can follow to deliver the fully-featured HeavyDB + GPU portfolio back-tester.

---
## 0  Scope & Objectives
1. Replace the legacy REST engine with a local HeavyDB/GPU pipeline while preserving functional parity (Excel inputs → metrics & trade output).
2. Maintain backward compatibility with existing Excel templates (`input_portfolio.xlsx`, `input_tbs_multi_legs.xlsx`).
3. Produce deterministic, auditable trade generation from HeavyDB option-chain views for all supported indices (NIFTY, BANKNIFTY, FINNIFTY …).
4. Enable extensibility (new indices, new strike rules, alternative data sources) through clean architecture and typed models.
5. Meet enterprise NFRs: code readability, unit-test coverage ≥ 80 %, logging/observability, GPU/CPU fallback.

---
## 1  Architecture Overview
```
┌──────────────┐  Excel  ┌───────────────┐   models   ┌──────────────────┐   SQL   ┌────────────┐
│  input_xlsx  ├────────►│ excel_parser  ├───────────►│  models.*        ├───────►│ query_sql  ├─────► HeavyDB
└──────────────┘          └───────────────┘            └──────────────────┘         └────────────┘
     ▲                                                              │                        │
     │                                      TradeRecords            │                        │
     │                                      (DataFrame)             ▼                        ▼
┌──────────────┐                                        ┌──────────────────┐      ┌────────────────┐
│  runtime/    │◄─────────────  builders  ──────────────┤ gpu_helpers      │◄────┤  Output (xls,  │
│ portfolio_   │                                         └──────────────────┘      │  json, charts) │
│ runner.py    │                                                                   └────────────────┘
└──────────────┘
```

**Critical Note (2025-05-13):**
> The Excel file loading logic in the HeavyDB GPU pipeline must fully replicate the archive (legacy) engine's behavior:
> - Support multiple strategies per Excel file (e.g., input_tbs_multi_legs.xlsx), not just one strategy per file.
> - Correctly map each enabled row in StrategySetting (input_portfolio.xlsx) to the appropriate rows in GeneralParameter and LegParameter, using StrategyType and/or StrategyName as in the archive code.
> - All parameter extraction, filtering, and transformation must match the archive Util.py logic for backward compatibility.

Key packages:
* **models**      Typed pydantic representations of portfolio, strategy, leg, risk rules.
* **excel_parser** Transforms the four Excel sheets into models.
* **query_builder** Converts a `LegModel` + trade-date into HeavyDB SQL (entry & exit).
* **runtime**      Orchestrates date iteration, executes queries, feeds results into existing stats/export stack.

---
## 2  Work-Breakdown Structure

| Phase | Owner | Duration | Deliverables |
|-------|-------|----------|--------------|
|0  Model Scaffolding|Backend|0.5 day|`models/` package, unit tests, CI lint pass|
|1  Excel Parser & Input Processing Overhaul|Backend|0.5 day|`excel_parser/` with full column coverage, fixtures test|
|2a Query Resolver|Backend|✅ Done|`query_builder/resolver.py` implemented (TableMapper, ExpiryPredicate, StrikeOrderClause)|
|2b Leg SQL Builder|Backend|✅ Done|`query_builder/leg_sql.py` + `strategy_sql.py` generate CTE SQL for legs & full strategy; smoke-tested|
|3  HeavyDB Integration|Backend|✅ Done|Entry/Exit snapshot SQL, `trade_builder` assembler, writers hardened|
|4  Risk Rules Engine|Backend|1 day|Apply SL/TP/Trail intraday using tick scans (post-MVP)|
|5  Perf Optimisation|Backend|🚧 In-progress|UNION-ALL batch SQL & connection pooling|
|6  Parallel Engine|Backend|0.5 day|GPU workers, date-slice scheduling, CPU fallback|
|7  QA & Docs|QA/Docs|1 day|Test matrix, README, example notebooks|

*Total MVP ETA*: **3 development days** (Phases 0-3).

---
## 3  Detailed Tasks
### Phase 0 – Model Scaffolding
1. `models.common` – enums `ExpiryRule`, `StrikeRule`, etc.
2. `models.time_window` – `EntryWindow`, `ExitWindow` (validate HHMMSS).
3. `models.risk` – `RiskRule` + nested validation.
4. `models.leg`, `models.strategy`, `models.portfolio`.
5. pytest covering happy-path & edge-cases.

### Phase 0 – Model Scaffolding (Super-micro checklist)
* [x] Create package `models/` with `__init__.py` exporting public classes.
* [x] Implement `models.common` with Enum stubs (`StrikeRule`, `ExpiryRule`, `OptionType`, `TransactionType`). Write pytest verifying Enum values.
* [x] Implement `models.time_window.EntryWindow` & `ExitWindow` (dataclass validation) – validate HHMMSS string ↔ int seconds conversion; add tests for edge cases (00:00:00, 23:59:59).
* [x] Implement `models.risk.RiskRule` dataclass with union types for SL / TP / Trail; unit-test JSON schema round-trip.
* [x] Implement `models.leg.LegModel` (fields: id, index, option_type, transaction, quantity, strike_rule, expiry_rule, entry_window, exit_window, risk) – 100 % mypy coverage.
* [x] Implement `models.strategy.StrategyModel` (id, name, list[LegModel], meta flags).
* [x] Implement `models.portfolio.PortfolioModel` (name, list[StrategyModel], risk, meta).
* [x] CI job `pytest -q models` passes; coverage ≥ 85 %.

### Phase 1 – Excel Parser & Input Processing Overhaul
*Goal: Replicate archive's comprehensive Excel input parsing logic to build a rich `bt_params` request object suitable for the HeavyDB-native pipeline. This involves correctly interpreting all columns from `PortfolioSetting`, `StrategySetting`, strategy-specific `GeneralParameter` & `LegParameter` sheets, and `lotsize.csv`.*

1.  **Modify `BTRunPortfolio_GPU.load_legacy_portfolio_config()`**:
    *   Reads `PortfolioSetting` and `StrategySetting` from `input_portfolio.xlsx`.
    *   Removes direct loading of `input_tbs_multi_legs.xlsx`; strategy-specific files are loaded based on `StrategySetting.StrategyExcelFilePath`.

2.  **Modify `BTRunPortfolio_GPU._row_build_request()`**:
    *   Orchestrates parsing: filters `StrategySetting` for the current portfolio.
    *   For each strategy row:
        *   Dynamically resolves `StrategyExcelFilePath` (from `config.INPUT_FILE_FOLDER` or absolute).
        *   Extracts `strategy_type`, `portfolio_multiplier`, `is_tick_bt`.
        *   **(CRITICAL: Legacy Compatibility)**
            *   If the referenced Excel file contains multiple strategies (multi-strategy-per-file), select the correct rows from GeneralParameter and LegParameter using the mapping logic from the archive (Util.py):
                - Use `StrategyType` and/or `StrategyName` as the join key, as in the archive.
                - Do not assume one strategy per file.
            *   All parameter extraction, filtering, and transformation must match the archive Util.py logic for backward compatibility.
        *   Calls `Util.getStategyJson()` to parse the specific strategy Excel file (or equivalent parser).
    *   Populates `request['portfolio']['strategies']` with the fully parsed strategy objects.

3.  **Call `Util.runNeccesaryFunctionsBeforeStartingBT()`**:
    *   Ensure this is invoked at the start of `_row_run_backtest` in `BTRunPortfolio_GPU.py` to load `lotsize.csv` into `config.LOT_SIZE` and `Util.MARGIN_INFO`.

4.  **Verify and Enhance `Util.getStategyJson()` and its callees (`getBackendStrategyJson`, `getLegJson`, `getBackendLegJson`)**:
    *   **Full Archive Logic Replication:** Ensure these functions meticulously replicate the archive `Util.py` logic for *every column* in `GeneralParameter` and `LegParameter` (as per user-provided image schemas and archive code).
    *   **Portfolio Multiplier:** Apply `portfolio_row["Multiplier"]` to all relevant monetary fields in `GeneralParameter` and to `Lots` in `LegParameter` *before* final quantity calculation.
    *   **Quantity Calculation:** `quantity = config.LOT_SIZE[index_name] * int(LegParameter_Lots_after_multiplier)`.
    *   **Rule Transformation:** Accurately convert all Excel string values for strike selection (`StrikeMethod`, `StrikeValue`, etc.), expiry rules (`Expiry`), SL/TGT parameters, re-entry conditions (`SL_ReEntryType`, `TGT_ReEntryType`, etc.), Wait & Trade rules (`W&Type`, `W&TValue`), hedging (`OpenHedge`, `HedgeStrikeMethod`), and `OnEntry`/`OnExit` event handlers into structured semantic parameters within the generated leg/strategy JSON. These parameters are for consumption by `heavydb_helpers.LegModel`.
    *   **Indicator Logic:** Correctly construct `entry_indicators` and `exit_indicators` dictionaries in `getBackendStrategyJson` based on `Consider...ForEntry/Exit` flags and associated indicator parameter columns (`EMAPeriod`, `RsiPeriod`, `STPeriod`, etc.) from `GeneralParameter`.

5.  **Refine `Util.getStrikeValueAndMethod()`**:
    *   This function (called by `getBackendLegJson`) parses Excel strike rules (`StrikeMethod`, `StrikeValue`, `StrikePremiumCondition`, `MatchPremium`).
    *   It must produce structured *semantic rules* and parameters (e.g., `rule_name_for_legmodel: "ATM"`, `rule_params: {{}}`; or `rule_name_for_legmodel: "DELTA_TARGET"`, `rule_params: {{ 'delta_value': 0.7 }}`).
    *   These structured rules will be stored in the leg's JSON representation and later used by `heavydb_helpers.LegModel` and the `query_builder` for HeavyDB-native strike selection.

**[NEW – 2025-05-13] Phase 1 Checklist Addition:**
* [ ] **Legacy Excel Multi-Strategy Support:** Confirm that the parser and loader logic in `BTRunPortfolio_GPU.py` and helpers fully support the archive pattern where multiple strategies are defined in a single Excel file, and the correct rows are selected using `StrategyType`/`StrategyName` mapping as in the archive Util.py. This is a critical requirement for backward compatibility and must be covered by tests.

### Phase 1 – Excel Parser & Input Processing (Super-micro checklist)
*Parser core*
* [x] New package `excel_parser/` with `__init__.py`.
* [x] Implement `excel_parser.portfolio.load_portfolio_settings(path) -> pd.DataFrame` – strict column existence, dtype coercion.
* [x] Implement `excel_parser.portfolio.load_strategy_settings(path) -> pd.DataFrame`.
* [x] Implement `excel_parser.strategy.load_general_parameter(path) -> pd.DataFrame` and `.load_leg_parameter(path)`.

*Mapping logic*
* [x] Column-to-field mapping table in `excel_parser.mapping.py`; pytest verifies every column in the 4 sheets appears exactly once (partial – core table created, test TBD).
* [x] Function `excel_parser.to_models.build_leg(df_row, gen_row, lot_size_dict) -> LegModel`.
* [x] Function `excel_parser.to_models.build_strategy(general_df, leg_df) -> StrategyModel`.
* [x] Function `excel_parser.to_models.build_portfolio(port_df_row, strat_df, _general_cache) -> PortfolioModel`.

*Integration with runner*
* [x] Replace direct Util.getStategyJson call in `_row_build_request` with `excel_parser.to_models.build_strategy` output until full migration ready.
* [x] Add compatibility adapter `portfolio_model` dict attached to request for downstream phases.

*Data fixtures*
* [x] Create mocked Excel sheets on-the-fly in pytest; full fixture files pending.

*DAL presence check*
* [ ] Add assert in test-env that `import dal` succeeds; if not, fail CI with helpful message.

### Phase 2 – Query Builder
*2a resolver.py*
• `TableMapper.resolve(index) -> table_name`
• `ExpiryResolver.resolve(rule, trade_date, index) -> SQL predicate`
• `StrikeResolver.resolve(rule, df_alias) -> SQL predicate (moneyness/premium)`

*2b leg_sql.py*
• `build_entry_sql(leg_model, date)` – SELECT first tick ≥ EntryWindow.start.
• `build_exit_sql(leg_model, date)`  – SELECT last  tick ≤ ExitWindow.end.
• Utilises HeavyDB `QUALIFY` & window fns. (Note: `leg_model` here is the `LegModel` instance).

### Phase 2 – Query Builder (Super-micro checklist)
*2a resolver.py*
* [x] `TableMapper` – map index → option_chain view; pytest with NIFTY/BANKNIFTY.
* [x] `ExpiryResolver.resolve(rule, trade_date, index)` – unit-test CURRENT, NEXT, MONTHLY.
* [x] `StrikeResolver.resolve(rule, df_alias)` – test ATM/OTM±n predicates.

*2b leg_sql.py / strategy_sql.py*
* [x] Implement `build_entry_sql(leg_model, date)` – earliest snapshot ≥ entry_time.
* [x] Implement `build_exit_sql(leg_model, date)`  – latest snapshot ≤ exit_time.
* [x] Helper `_select_chain_columns(leg_model)` – selects CE/PE columns only.
* [x] pytest using HeavyDB docker stub; validate generated SQL contains expected WHERE + QUALIFY.

### Phase 3 – HeavyDB Integration
1. Update `heavydb_helpers.get_trades_for_portfolio`:
   *   Input `bt_params` will now contain strategies with fully parsed `legs` (from Phase 1), where each `leg_dict` includes detailed semantic rules for strike, expiry, etc., derived from Excel.
   *   When creating `LegModel` instances:
        *   Extract strike selection method (e.g., "ATM", "CLOSEST_PREMIUM", "DELTA_TARGET"), expiry rule (e.g., "WEEKLY", "MONTHLY", "DTE_TARGET"), and associated parameters (target premium, strike value, DTE value, conditions) directly *from each `leg_dict`*.
        *   Map these parsed semantic string rules/values to the `StrikeRule` (enum), `ExpiryRule` (enum) and correctly populate the `LegModel` fields (e.g., `leg_model.strike_rule_enum`, `leg_model.strike_param_value`, `leg_model.expiry_rule_enum`, `leg_model.dte_target`).
   *   The `query_builder` modules (e.g., `entry_exit_sql.py`, `strategy_sql.py`) will then use these fully populated `LegModel` properties to generate precise HeavyDB SQL for fetching entry/exit snapshots.
2. Convert result to GPU (if enabled) then return schema identical to legacy engine.
3. Validate with builders.parse_backtest_response end-to-end.

### Phase 3 – HeavyDB Integration (Super-micro checklist)
* [x] Modify `heavydb_helpers.get_trades_for_portfolio` to consume `PortfolioModel` (or legacy-json adapter) directly.
* [x] `LegFactory.from_dict(leg_dict) -> LegModel` with full enum mapping; unit-test bad inputs raise.
* [x] Integrate `query_builder` into `_build_union_sql` – remove placeholder strike/expiry logic.
* [x] `trade_builder.build_trade_record` – ensure slippage & taxes params forwarded; pytest golden row.
* [x] End-to-end smoke: one portfolio, one day, four legs; assert ≥4 TradeRecords returned.
* [x] Fixed legacy time string normalization (e.g., 091500 → 09:15:00) for EntryWindow/ExitWindow in LegModel construction.

**Phase 3 status:** All checklist items complete. Model-driven and legacy paths are now robust to time string formats. 

**Next steps:**
- Proceed to Phase 4 (Risk Rules Engine): implement tick-scan SL/TP/Trail logic and integrate with runtime loop.
- Continue Phase 5 (Performance Optimisation): batch UNION-ALL SQL, connection pooling, and GPU memory telemetry.

### Phase 4 – Risk Rules Engine
* [x] Risk rule mapping, model, and evaluation logic implemented and unit tested (all rule types).
* [x] Runtime integration: risk rules evaluated during backtest, early exits applied to TradeRecords if triggered.
* [x] Pipeline runs with real Excel input at canonical path, using PortfolioName as unique identifier.
* [>] Validate end-to-end with real backtest run and confirm risk-triggered exits in output/logs.  
    - Phase 1 complete, DAL presence check implemented, project is now in Phase 4: Risk Rules Engine.

### Phase 5 – Performance Optimisation
1. Build UNION-ALL query grouping legs by (index, date), leveraging the detailed `LegModel` information for common rule batching. Attach `leg_id` column.
2. Split evaluation of strike predicates into CASE blocks within the SQL, or use dynamic filtering based on `LegModel` parameters.
3. Benchmarks vs SIMPLE mode, switch automatically when legs×days > threshold.

### Phase 5 – Performance Optimisation (Super-micro checklist)
* [ ] Batch UNION-ALL generator groups 100 legs per SQL call; benchmark vs single.
* [ ] Add `config.HEAVYDB_QUERY_BATCH` env var; unit test value respected.
* [ ] Implement HeavyDB connection pool (size = workers×2) in `heavydb_helpers` with contextmanager.
* [ ] GPU memory telemetry: `gpu_helpers.log_usage()` every 60 s.
* [ ] Load-test: 90 days × 50 k legs < 30 min wall-clock on A100.

### Phase 6 – Parallel Execution Engine
1. Multiprocessing pool (`--workers N`, default 8 for A100-40GB).
2. Worker receives a slice of `bt_params` (which is now correctly structured and fully parsed by the main process due to Phase 1 improvements).
3. Worker lifecycle: obtain pooled HeavyDB conn → execute batched SQL (using `heavydb_helpers` which consumes the rich `LegModel`) → build TradeRecords on GPU → Parquet chunk output.
4. Retry path: worker slice rerun on CPU when GPU OOM or cuDF error.
5. GPU memory watchdog logs utilisation every 60 s; triggers graceful drain when free < 1 GB.

### Phase 6 – Parallel Execution Engine (Super-micro checklist)
* [ ] Refactor `_backtest_worker` to accept `PortfolioModel` and date slice tuple.
* [ ] Implement slicer `scheduler.split_dates(trade_dates, max_workers)`.
* [ ] Shared read-only `heavydb_helpers.ConnectionPool` injected via multiprocessing `initializer`.
* [ ] Worker graceful shutdown writes partial parquet to `/tmp/bt_chunks/`.
* [ ] Parent merges parquet → final DataFrame; pytest that merge is order-independent.

### Phase 7 – QA & Docs
1. Test matrix, README, example notebooks.

### Phase 7 – QA & Docs (Super-micro checklist)
* [ ] Generate pytest parameter grid covering 3 indices × 4 strike methods × SL vs no-SL.
* [ ] Write golden-file comparator `tests/parity/compare_archive_vs_heavydb.py` (tolerance 0.5 %).
* [ ] GitHub Actions workflow matrix (py3.10 + HeavyDB docker) – run unit, lint, mypy.
* [ ] Sphinx docs build on CI; includes architecture diagram & API docs.
* [ ] Final README with quick-start, performance benchmark table, FAQ.

---
## 4  Naming & Coding Standards
* PEP-8 with Black auto-format.
* Public functions + classes include doctring, Google style.
* Logging via stdlib `logging`, module-level loggers.
* Exceptions use custom hierarchy `BTRunError`, `DataValidationError`, etc.
* SQL text built with `jinja2` templates for readability.

---
## 5  Testing Strategy
* pytest marker `heavydb_required` – skipped if DB not reachable.
* Fixtures: mini option-chain parquet for unit tests.
* Golden-file tests comparing outputs vs known legacy results.
* CI pipeline (GitHub Actions) runs unit + style + mypy.

---
## 6  Deployment & Ops
* Env vars: `BT_HEAVYDB_BATCH`, `BT_USE_GPU`, `BT_LOG_LEVEL`.
* Connection pool in `heavydb_helpers` with contextmanager.
* Alembic-style migration script seeds LOT_SIZE, MARGIN_INFO fixtures.
* README → architecture diagram + quick-start (docker-compose with HeavyDB).

---
## 7  Plan Update: 2025-05-12

This section details an expanded and refined plan based on deeper analysis of archive code and input Excel structures. It supersedes previous implied task details for relevant phases but maintains overall phase goals.

### Phase 0 – Model Scaffolding (No Change to Original Plan)
1. `models.common` – enums `ExpiryRule`, `StrikeRule`, etc.
2. `models.time_window` – `EntryWindow`, `ExitWindow` (validate HHMMSS).
3. `models.risk` – `RiskRule` + nested validation.
4. `models.leg`, `models.strategy`, `models.portfolio`.
5. pytest covering happy-path & edge-cases.

### Phase 1 – Excel Parser & Input Processing Overhaul (Refined)
*Goal: Replicate archive's comprehensive Excel input parsing logic to build a rich `bt_params` request object suitable for the HeavyDB-native pipeline. This involves correctly interpreting all columns from `PortfolioSetting`, `StrategySetting`, strategy-specific `GeneralParameter` & `LegParameter` sheets, and `lotsize.csv`.*

1.  **Modify `BTRunPortfolio_GPU.load_legacy_portfolio_config()`**:
    *   Reads `PortfolioSetting` and `StrategySetting` from `input_portfolio.xlsx`.
    *   Removes direct loading of `input_tbs_multi_legs.xlsx`; strategy-specific files are loaded based on `StrategySetting.StrategyExcelFilePath`.

2.  **Modify `BTRunPortfolio_GPU._row_build_request()`**:
    *   Orchestrates parsing: filters `StrategySetting` for the current portfolio.
    *   For each strategy row:
        *   Dynamically resolves `StrategyExcelFilePath` (from `config.INPUT_FILE_FOLDER` or absolute).
        *   Extracts `strategy_type`, `portfolio_multiplier`, `is_tick_bt`.
        *   **(CRITICAL: Legacy Compatibility)**
            *   If the referenced Excel file contains multiple strategies (multi-strategy-per-file), select the correct rows from GeneralParameter and LegParameter using the mapping logic from the archive (Util.py):
                - Use `StrategyType` and/or `StrategyName` as the join key, as in the archive.
                - Do not assume one strategy per file.
            *   All parameter extraction, filtering, and transformation must match the archive Util.py logic for backward compatibility.
        *   Calls `Util.getStategyJson()` to parse the specific strategy Excel file (or equivalent parser).
    *   Populates `request['portfolio']['strategies']` with the fully parsed strategy objects.

3.  **Call `Util.runNeccesaryFunctionsBeforeStartingBT()`**:
    *   Ensure this is invoked at the start of `_row_run_backtest` in `BTRunPortfolio_GPU.py` to load `lotsize.csv` into `config.LOT_SIZE` and `Util.MARGIN_INFO`.

4.  **Verify and Enhance `Util.getStategyJson()` and its callees (`getBackendStrategyJson`, `getLegJson`, `getBackendLegJson`)**:
    *   **Full Archive Logic Replication:** Ensure these functions meticulously replicate the archive `Util.py` logic for *every column* in `GeneralParameter` and `LegParameter` (as per user-provided image schemas and archive code).
    *   **Portfolio Multiplier:** Apply `portfolio_row["Multiplier"]` to all relevant monetary fields in `GeneralParameter` and to `Lots` in `LegParameter` *before* final quantity calculation.
    *   **Quantity Calculation:** `quantity = config.LOT_SIZE[index_name] * int(LegParameter_Lots_after_multiplier)`.
    *   **Rule Transformation:** Accurately convert all Excel string values for strike selection (`StrikeMethod`, `StrikeValue`, etc.), expiry rules (`Expiry`), SL/TGT parameters, re-entry conditions (`SL_ReEntryType`, `TGT_ReEntryType`, etc.), Wait & Trade rules (`W&Type`, `W&TValue`), hedging (`OpenHedge`, `HedgeStrikeMethod`), and `OnEntry`/`OnExit` event handlers into structured semantic parameters within the generated leg/strategy JSON. These parameters are for consumption by `heavydb_helpers.LegModel`.
    *   **Indicator Logic:** Correctly construct `entry_indicators` and `exit_indicators` dictionaries in `getBackendStrategyJson` based on `Consider...ForEntry/Exit` flags and associated indicator parameter columns (`EMAPeriod`, `RsiPeriod`, `STPeriod`, etc.) from `GeneralParameter`.

5.  **Refine `Util.getStrikeValueAndMethod()`**:
    *   This function (called by `getBackendLegJson`) parses Excel strike rules (`StrikeMethod`, `StrikeValue`, `StrikePremiumCondition`, `MatchPremium`).
    *   It must produce structured *semantic rules* and parameters (e.g., `rule_name_for_legmodel: "ATM"`, `rule_params: {{}}`; or `rule_name_for_legmodel: "DELTA_TARGET"`, `rule_params: {{ 'delta_value': 0.7 }}`).
    *   These structured rules will be stored in the leg's JSON representation and later used by `heavydb_helpers.LegModel` and the `query_builder` for HeavyDB-native strike selection.

### Phase 2 – Query Builder (No Change to Original Plan for 2a, 2b)
*2a resolver.py*
• `TableMapper.resolve(index) -> table_name`
• `ExpiryResolver.resolve(rule, trade_date, index) -> SQL predicate`
• `StrikeResolver.resolve(rule, df_alias) -> SQL predicate (moneyness/premium)`

*2b leg_sql.py*
• `build_entry_sql(leg_model, date)` – SELECT first tick ≥ EntryWindow.start.
• `build_exit_sql(leg_model, date)`  – SELECT last  tick ≤ ExitWindow.end.
• Utilises HeavyDB `QUALIFY` & window fns. (Note: `leg_model` here is the `LegModel` instance).

### Phase 3 – HeavyDB Integration (Refined)
*Goal: Adapt `heavydb_helpers` to consume the richly populated `bt_params` (from Phase 1) and use the `query_builder` to fetch data from HeavyDB.*

1.  **Modify `heavydb_helpers.get_trades_for_portfolio()`**:
    *   The input `bt_params['portfolio']['strategies'][n]['legs']` will now be a list of rich `leg_dict` objects containing detailed semantic rules and parameters from Phase 1 parsing (e.g., `leg_dict['parsed_strike_rule'] = {{ 'method': 'DELTA', 'value': 0.5 }}`, `leg_dict['parsed_expiry_rule'] = {{ 'type': 'WEEKLY' }}`).
    *   When creating `LegModel` instances:
        *   Extract these structured semantic rules and parameters from each `leg_dict`.
        *   Map these to the correct `StrikeRule` enum, `ExpiryRule` enum, and populate corresponding fields in the `LegModel` instance.
    *   The `query_builder` functions will then use these fully populated `LegModel` properties to generate precise HeavyDB SQL queries.
2.  **Data Fetching & TradeRecord Assembly:** Ensure batch SQL, connection pooling, and TradeRecord assembly leverage GPU paths when available.
3.  **Output Schema:** Final DataFrame returned must match legacy engine expectations so downstream writers remain unchanged.

### Phase 4 – Risk Rules Engine (No Change to Original Plan)
1. During date loop collect intra-day tick dataframe for each leg.
2. Evaluate SL/TP/Trails at `check_frequency` (Strategy.general.checking_interval).
3. Generate early exit TradeRecord where rule triggers.

### Phase 5 – Performance Optimisation (Refined)
1. Batch UNION-ALL SQL grouping legs by common predicates.
2. Connection pooling & GPU DataFrame optimisation.
3. Benchmarks and auto-switch between SIMPLE and BATCH modes.

### Phase 6 – Parallel Execution Engine (Refined)
1. Multiprocessing pool (`--workers N`).
2. Worker receives slice of fully-parsed `bt_params`.
3. GPU/CPU fallback with watchdog.
4. Parquet chunk merge guarantees order independence.

### Phase 7 – QA & Docs (Unchanged)
1. Test matrix, notebooks, README & Sphinx docs.

**Status update (2025-05-13):**
- Model-driven HeavyDB integration is live and tested (Phase 3 complete).
- Batch SQL optimization and risk rule engine (SL/TP/Trail) are next (Phases 4/5 in progress).
- Documentation, test matrix, and golden-file tests are being updated (Phase 7 ongoing).

## 3  Adapter Path (Fast-Parity Track) — added 2025-05-14

> Goal: ship HeavyDB / GPU back-tester with functional parity this week while the full modular Excel-parser continues in parallel.

### 3.1  Overview
1. Keep the trusted archive Excel parser (`Util.getStategyJson()` and helpers) but isolate it behind a thin adapter.
2. Convert the JSON produced by Util into the modern `PortfolioModel → StrategyModel → LegModel` hierarchy.
3. Feed those models into the HeavyDB-native query-builder, risk engine, and GPU pipeline.
4. Remove the adapter once the new `excel_parser` reaches 100 % column coverage and passes golden-file diff.

### 3.2  Adapter Mechanics
```
Excel → Util.getStategyJson() ──► util_adapter.translate() ──► HeavyDB pipeline
```

### 3.3  Repo layout changes
```
bt/backtester_stable/BTRUN/
├ legacy_archive/Util_archive.py   (exact copy of archive Util.py)
├ util_adapter.py                 (JSON → models translator)
├ heavydb_helpers.py              (unchanged – HeavyDB only)
└ BTRunPortfolio_GPU.py           (calls util_adapter in _row_build_request)
```

### 3.4  Execution checklist
| # | Task | ETA |
|---|------|-----|
|1|Copy archive Util.py to `legacy_archive/Util_archive.py`|½ h|
|2|Write `util_adapter.py` with `util_json_to_portfolio_model()`|4 h|
|3|Patch runner to use adapter|1 h|
|4|Unit-tests (adapter parity)|2 h|
|5|One-day HeavyDB back-test (parity proof)|1 h|

### 3.5  Parallel full-parser track
Continues Phase-1 mapping work; adapter removed when golden diff passes.

---
## 8  Parity Analysis (HeavyDB/GPU vs. Archive) - 2025-05-14

This section summarizes the current functional parity between the new `BTRunPortfolio_GPU.py` and the legacy `BTRunPortfolio.py` (from the archive).

Legend:
*   ✅ = Fully replicated / tested
*   ◑ = Partially replicated – behaviour differs or only MVP subset
*   ❌ = Missing / Stubbed

### 8.1  Outer Script Workflow (`BTRunPortfolio_GPU.py`)
*   Command-line & logging: ✅
*   Excel input discovery (`PortfolioSetting`/`StrategySetting`): ✅
*   Loop over Portfolio rows: ✅
*   Pre-BT fixtures (`Util.runNeccesaryFunctions...`): ✅
*   Worker fan-out / CPU fallback (`--workers`, `--retry-cpu`): ✅
*   TTS & Merge-folder copy: ❌ (Warnings emitted)

### 8.2  Excel → Strategy JSON Parsing (via `util_adapter`)
*   Multi-strategy per Excel file support: ✅
*   Column-by-column fidelity (Strike, Hedge, W&T, etc.): ◑ (Adapter maps MVP subset only)
*   `getStrikeValueAndMethod()` semantic translation: ❌
*   Portfolio multiplier application: ◑ (Lots: ✅, Monetary fields: ❌)

### 8.3  Trade Generation (`heavydb_helpers`)
*   Model-driven path (`portfolio_model`): ✅
*   Legacy-dict path (fallback): ✅
*   Risk-rule early-exit (SL/TP/Trail): ◑ (Basic logic present, lacks tick optimization & trailing stops)
*   Batch UNION-ALL SQL (Phase-5): ❌ (Per-leg fallback used)

### 8.4  Post-processing / Metrics / Output
*   Slippage + expenses recalculation: ✅
*   `stats.get_backtest_stats()` parity: ◑ (Missing Margin % PnL, Outlier Adj PF; added Sharpe/Sortino)
*   Strategy-wise segregation (Excel sheets, day/month stats): ❌ (`runtime` only processes 'portfolio' key)
*   Margin-percentage-wise stats: ❌ (Placeholder)
*   Excel writer column ordering/renames: ✅
*   JSON writer structure: ✅

### 8.5  High-Risk Gaps for Production Parity
1.  **Strategy-wise Reporting:** Missing per-strategy breakdown in Excel/JSON outputs.
2.  **Strike/Expiry Rules:** Limited mapping via adapter (defaults to ATM/Weekly).
3.  **Margin Calculation:** Placeholder logic affects dependent stats (CAGR, Sharpe, etc.).
4.  **Legacy CLI Features:** Missing Merge-folder copy and Text-to-Speech.

### 8.6  Recommended Next Steps (to achieve parity)
1.  **Implement Strategy Grouping:** Update `runtime.process_backtest_results` to group by strategy and populate all `stgy_*` dicts for output writers.
2.  **Implement Margin Calculation:** Update `builders.parse_backtest_response` to calculate `margin_req_for_each_stgy` using `Util.MARGIN_INFO`.
3.  **Enhance Adapter Mapping:** Complete `util_adapter._map_strike_rule` and related mappings based on archive `Util.getStrikeValueAndMethod` logic.
4.  **Port Strike Rule Logic:** Move `Util.getStrikeValueAndMethod` logic to `excel_parser/strike_rules.py` and integrate with adapter.
5.  **Restore Legacy Utils:** Add `io.copy_files_to_merge_folder` and re-introduce Text-to-Speech.
6.  **Continue Core Phases:** Progress on Risk Rule refinement (Phase 4) and Performance Optimisation (Phase 5 - Batch SQL).

### 8.7  Acceptance Testing for Parity
*   Define specific test cases (e.g., NIFTY short straddle, BANKNIFTY delta-target) with known archive outputs.
*   Compare Excel/JSON outputs from the new pipeline against archive results, aiming for ≤ 0.5% difference in key metrics.
*   Verify correct handling of portfolio multiplier.

### [NEW – 2025-05-15] Phase 1 – Column-Coverage Parity Audit
Gap: Confirm that **ALL** columns from
1. PortfolioSetting (input_portfolio.xlsx)
2. GeneralParameter (input_tbs_multi_legs.xlsx and any strategy-specific workbook)
3. LegParameter    (ditto)
are consumed and translated into the modern model / JSON paths.

Deliverables:
1. `docs/column_mapping.xlsx` – authoritative mapping table (Excel → JSON/Model field).
2. `excel_parser/mappings.py` – updated dicts `PORTFOLIO_COLUMNS`, `GENERAL_COLUMNS`, `LEG_COLUMNS` with 1-to-1 mapping.
3. `util_adapter.py` – mapping extended to include every field listed in the table (fallback default when archive field unused by MVP).
4. Unit test `tests/test_column_coverage.py` – asserts that every header in the sample sheets appears **exactly once** in mapping dicts and that adapter output contains keys for them.
5. Runtime validation in `BTRunPortfolio_GPU._row_build_request` that logs WARNING if any unmapped column remains after adapter translation.

Tasks:
- [ ] Extract headers from latest reference sheets (screenshots provided 2025-05-15) into CSV fixture.
- [ ] Update mapping tables.
- [ ] Extend adapter conversion logic: portfolio-level trailing/locking fields, strategy-level indicator flags, leg-level re-entry flags, hedge fields.
- [ ] Backfill default values for legacy paths when field missing.
- [ ] Add CI test to fail if new columns are introduced without mapping.
- [ ] Update docs & README with column coverage status.

Owner: Backend  |  Status: **Not Started**

#### Super-Micro Task Checklist – Updated

#### 1. Excel Parser & Input Processing
* [x] **Header Mapping**
  - Created `header_map.py` with complete column mappings
  - Added detailed comments for each field's format and usage
  - Organized fields by category (date/time, boolean, numeric, etc.)
  - Added validation rules in comments

* [x] **Field Validation**
  - Created `validators.py` with type-specific validation functions
  - Added support for multiple date/time formats
  - Added support for YES/NO boolean fields
  - Added enum validation for strike methods, expiry types, etc.
  - Added comprehensive error messages

* [x] **Excel Parsing**
  - Created `parser.py` with sheet-specific parsing functions
  - Added column presence validation
  - Added row-level validation with error collection
  - Added support for multiple sheets in strategy files
  - Added logging for parsing errors

* [x] **Test Coverage**
  - Added unit tests for all validators
  - Added test data fixtures
  - Added error case tests
  - Added integration tests for full parsing flow

#### 2. HeavyDB Integration
* [x] **Connection Management**
  - Implemented connection pooling
  - Added connection error handling
  - Added automatic reconnection
  - Added connection cleanup

* [x] **Query Generation**
  - Added option chain query builder
  - Added strike selection logic
  - Added expiry date handling
  - Added price fetching queries
  - Added trade date validation

* [x] **Trade Processing**
  - Added P&L calculation
  - Added position tracking
  - Added multi-leg support
  - Added transaction cost handling

* [x] **GPU Acceleration**
  - Added GPU DataFrame support
  - Added memory management
  - Added CPU fallback
  - Added batch processing

#### 3. Risk Rules Engine (In Progress)
* [ ] **SL/TP Implementation**
  - [ ] Add stop loss calculation
  - [ ] Add take profit calculation
  - [ ] Add trailing stop logic
  - [ ] Add re-entry logic
  - [ ] Add risk metrics calculation

* [ ] **Tick Processing**
  - [ ] Add tick data fetching
  - [ ] Add tick-level price updates
  - [ ] Add tick-level position tracking
  - [ ] Add tick-level P&L calculation

* [ ] **Testing**
  - [ ] Add unit tests for risk rules
  - [ ] Add integration tests with tick data
  - [ ] Add performance tests
  - [ ] Add edge case tests

#### 4. Performance Optimization (In Progress)
* [ ] **SQL Optimization**
  - [ ] Add batch query generation
  - [ ] Add query result caching
  - [ ] Add index optimization
  - [ ] Add query plan analysis

* [ ] **GPU Memory**
  - [ ] Add memory usage monitoring
  - [ ] Add memory cleanup
  - [ ] Add memory pooling
  - [ ] Add out-of-memory handling

* [ ] **Connection Pool**
  - [ ] Add dynamic pool sizing
  - [ ] Add connection reuse
  - [ ] Add connection health checks
  - [ ] Add pool metrics

#### 5. Parallel Execution (Planned)
* [ ] **Worker Pool**
  - [ ] Add worker process management
  - [ ] Add task distribution
  - [ ] Add result collection
  - [ ] Add error handling

* [ ] **Date Slicing**
  - [ ] Add date range partitioning
  - [ ] Add workload balancing
  - [ ] Add progress tracking
  - [ ] Add partial result handling

* [ ] **GPU/CPU Fallback**
  - [ ] Add resource monitoring
  - [ ] Add automatic switching
  - [ ] Add performance comparison
  - [ ] Add failure recovery

#### 6. Quality Assurance (Planned)
* [ ] **Test Matrix**
  - [ ] Define test scenarios
  - [ ] Create test data
  - [ ] Add automated test runs
  - [ ] Add test reporting

* [ ] **Golden Files**
  - [ ] Create reference outputs
  - [ ] Add comparison logic
  - [ ] Add tolerance handling
  - [ ] Add difference reporting

* [ ] **Performance Benchmarks**
  - [ ] Define benchmark scenarios
  - [ ] Create benchmark data
  - [ ] Add measurement tools
  - [ ] Add benchmark reporting

#### 7. Documentation (In Progress)
* [x] **README**
  - Added installation guide
  - Added usage examples
  - Added configuration guide
  - Added troubleshooting tips

* [x] **Excel Format**
  - Added sheet descriptions
  - Added column descriptions
  - Added validation rules
  - Added example files

* [ ] **Performance Guide**
  - [ ] Add optimization tips
  - [ ] Add resource requirements
  - [ ] Add scaling guidelines
  - [ ] Add monitoring guide

* [ ] **Troubleshooting**
  - [ ] Add common issues
  - [ ] Add solutions
  - [ ] Add debugging guide
  - [ ] Add support contacts

[Previous content follows...]