# Nifty Option Chain Materialized View

## Overview

The Nifty Option Chain Materialized View is a GPU-accelerated analytical solution for option chain data in HeavyDB. It transforms raw options data from the `nifty_greeks` table into a structured format with accurate DTE calculation, ATM strike determination, and standardized strike classification.

## Implementation Status

Status: **Complete**

- ✅ Base view creation and deployment
- ✅ ATM strike determination using closest-to-underlying method
- ✅ Strike classification (ATM, ITM1-n, OTM1-n)
- ✅ Holiday-aware DTE calculation
- ✅ NSE holiday calendar (2019-2026)
- ✅ Comprehensive documentation

## Technical Implementation

### View Structure

The materialized view is implemented as a series of CTEs that transform the raw data:

1. **ATM Selection**: Identifies the ATM strike as closest to the underlying price
2. **Holiday Count**: Pre-calculates holidays between trade and expiry dates
3. **Main Query**: Combines transformed data with strike classification and DTE calculation

### DTE Calculation

The Days to Expiry calculation accounts for both weekends and holidays:

```sql
CASE
    WHEN DATEDIFF('day', trade_date, expiry_date) = 0 
        THEN 0
    ELSE
        DATEDIFF('day', trade_date, expiry_date)
        -- Subtract weekends
        - (FLOOR((EXTRACT(DOW FROM trade_date) + DATEDIFF('day', trade_date, expiry_date)) / 7)
           + FLOOR((EXTRACT(DOW FROM trade_date) + DATEDIFF('day', trade_date, expiry_date) + 1) / 7))
        -- Subtract holidays (excluding weekends)
        - COALESCE(holiday_count, 0)
END AS dte
```

### Strike Classification

Strikes are classified relative to the ATM strike with numeric indicators for distance:

- **ATM**: Strike equals the ATM strike
- **ITMn**: For calls when strike < ATM, for puts when strike > ATM
- **OTMn**: For calls when strike > ATM, for puts when strike < ATM

The numeric suffix (n) indicates the number of strike increments away from ATM.

### Holiday Calendar

The solution includes a comprehensive NSE holiday calendar:
- 123 official NSE holidays from 2019-2026
- Stored in the `nse_holidays` table
- Used for accurate DTE calculation

## Usage Guidelines

### Query Best Practices

1. **Always Use Date Filters**:
   ```sql
   SELECT * FROM nifty_option_chain 
   WHERE trade_date = '2023-04-01' AND expiry_date = '2023-04-27'
   ```

2. **Select Only Needed Columns**:
   ```sql
   SELECT trade_date, expiry_date, strike, dte, call_strike_type, ce_close, pe_close
   FROM nifty_option_chain
   WHERE trade_date = '2023-04-01'
   ```

3. **Use Strike Type for Filtering**:
   ```sql
   SELECT * FROM nifty_option_chain
   WHERE trade_date = '2023-04-01' 
     AND call_strike_type IN ('ATM', 'ITM1', 'OTM1')
   ```

### Performance Considerations

1. **Row Limit**: HeavyDB has a 128 million row limit; always use appropriate filters
2. **Column Selection**: Select only needed columns to reduce GPU memory usage
3. **Date Range**: Use narrow date ranges to improve performance
4. **View Refreshes**: Be aware that materialized views need explicit refreshes

## Maintenance

### Holiday Calendar Updates

The NSE holiday calendar should be updated annually as new official holidays are announced:

1. Create SQL scripts for each new year
2. Add both standard holidays and any specific test dates needed
3. Update documentation to reflect the new range of covered years

### View Refresh Strategy

When new data is loaded into the `nifty_greeks` table, the materialized view must be refreshed:

```sql
REFRESH MATERIALIZED VIEW nifty_option_chain;
```

For large datasets, consider incremental refresh strategies or scheduled refreshes during off-peak hours.

## Future Enhancements

1. **Optimization**: Further performance optimization for extremely large datasets
2. **Analytics Extensions**: Additional derived metrics for option analysis
3. **Visualization Integration**: Integration with data visualization tools
4. **Refresh Automation**: Automated refresh scheduling and monitoring

## Related Documentation

- Technical implementation details in `nifty_option_chain_implementation.md`
- Visual structure and data flow in `nifty_option_chain_visualization.md`
- Test scenarios and validation in `nifty_option_chain_testing.md` 