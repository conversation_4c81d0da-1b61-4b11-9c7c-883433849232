# Technical Context: Nifty Option Chain

## Technologies Used

### 1. HeavyDB (OmniSci Core)
- **Description**: GPU-accelerated, SQL-based analytical database
- **Version**: Latest available for server deployment
- **Key Features**:
  - GPU-accelerated query execution
  - ANSI SQL compatibility with extensions
  - Materialized view support
  - Geospatial capabilities (not used in this project)
  - High-performance data ingestion

### 2. SQL
- **Dialect**: HeavyDB SQL (based on PostgreSQL with extensions)
- **Features Used**:
  - Common Table Expressions (CTEs)
  - User-defined functions
  - Materialized views
  - Window functions
  - LATERAL joins

### 3. Python (Support Scripts)
- **Version**: 3.8+
- **Libraries**:
  - pyHeavyDB (for database connection and management)
  - pandas (for data manipulation)
  - numpy (for numerical operations)

## Development Setup

### Database Environment
- **Server**: HeavyDB instance running on GPU-enabled hardware
- **Connection Parameters**:
  - Host: 127.0.0.1
  - Port: 6274
  - User: admin
  - Database: heavyai

### Data Sources
- **Main Table**: `nifty_greeks`
- **Schema**:
  ```
  trade_date DATE,
  trade_time TIME,
  expiry_date DATE,
  strike DOUBLE,
  underlying_price DOUBLE,
  trading_day_dte INTEGER,
  ce_symbol TEXT,
  ce_open DOUBLE,
  ce_high DOUBLE,
  ce_low DOUBLE,
  ce_close DOUBLE,
  ce_volume DOUBLE,
  ce_oi DOUBLE,
  ce_coi DOUBLE,
  ce_iv DOUBLE,
  ce_delta DOUBLE,
  ce_gamma DOUBLE,
  ce_theta DOUBLE,
  ce_vega DOUBLE,
  ce_rho DOUBLE,
  pe_symbol TEXT,
  pe_open DOUBLE,
  pe_high DOUBLE,
  pe_low DOUBLE,
  pe_close DOUBLE,
  pe_volume DOUBLE,
  pe_oi DOUBLE,
  pe_coi DOUBLE,
  pe_iv DOUBLE,
  pe_delta DOUBLE,
  pe_gamma DOUBLE,
  pe_theta DOUBLE,
  pe_vega DOUBLE,
  pe_rho DOUBLE
  ```

### Development Tools
- **SQL Editor**: HeavyDB's built-in SQL editor or compatible tools
- **Version Control**: Git
- **Documentation**: Markdown

## Technical Constraints

### 1. HeavyDB SQL Dialect Limitations
- Certain PostgreSQL features may not be available
- Limited stored procedure capabilities compared to traditional RDBMS
- Window function optimizations may differ from standard PostgreSQL

### 2. Performance Considerations
- GPU memory limitations can impact very large query performance
- Complex queries should be designed with GPU execution in mind
- Materialized view refresh operations can be resource-intensive

### 3. Data Type Constraints
- Optimal data types should be used for GPU acceleration
- TEXT fields should use ENCODING DICT where possible for performance
- DATE/TIME fields should use appropriate encodings for GPU optimization

### 4. Hardware Dependencies
- Performance depends on available GPU resources
- Concurrent queries may compete for GPU memory

## Dependencies

### 1. NSE Trading Calendar
- Needed for accurate DTE calculation
- Maintained in separate module/table
- Must account for NSE holidays and trading days

### 2. Data Loading Process
- Data must be pre-loaded into the `nifty_greeks` table
- Assumes proper data quality and consistency

### 3. Schema Stability
- Materialized view depends on stable schema of source table
- Changes to source table may require view updates

### 4. Refresh Mechanism
- Regular refresh needed for up-to-date analysis
- Refresh scheduling handled by external process

## Hardware Requirements

- NVIDIA GPU with CUDA support (preferably 16GB+ memory)
- Sufficient CPU and RAM for database operations
- SSD storage for optimal I/O performance

### HeavyDB Integration (2025-05-10)
• Table map based on index symbol ⇒  `{NIFTY: nifty_option_chain, BANKNIFTY: banknifty_option_chain, …}` stored in config.
• Views already include moneyness + greeks ⇒ strike rules use SQL only (no Python loops).
• All domain objects validated by pydantic models before hitting query builder.

## Technical Context (Updated 2025-05-12)

### Core Technologies

*   **Python 3.10** – primary language for backtesting engine.
*   **Pandas** – used for reading Excel files (`pd.read_excel`) and DataFrame operations.
*   **HeavyDB 6.x** – columnar GPU database storing option chain data (`nifty_greeks`) and materialized views (`nifty_option_chain`).
*   **cuDF / RAPIDS** – leveraged for GPU DataFrame operations in trade assembly (optional, controlled via `BT_USE_GPU`).
*   **Pydantic (Optional)** – data validation layer for models (`models.*`). Lightweight shims provided when unavailable on target GPU servers.
*   **Jinja2** – templating SQL snippets in `query_builder` for readability.
*   **pytest 7.x** – unit test framework with coverage reporting.

### Excel Parsing Workflow (Phase-1)

1.  **Input Files**
    *   `input_portfolio.xlsx`
        *   `PortfolioSetting` – portfolio-level config (dates, capital, multipliers, risk parameters).
        *   `StrategySetting` – list of strategies for each portfolio, including `StrategyExcelFilePath`.
    *   Strategy-specific files (e.g., `input_tbs_multi_legs.xlsx`)
        *   `GeneralParameter` – high-level strategy configs (entry/exit windows, profit/loss targets, indicator flags).
        *   `LegParameter` – row per option leg with strike/expiry selection rules and SL/TGT/W&T/hedge settings.
    *   `lotsize.csv` – mapping of index symbol → lot size (used for quantity calc).

2.  **Parsing Libraries / Functions**
    *   `Util.getStategyJson(...)` → reads `GeneralParameter`/`LegParameter`, applies `portfolio_multiplier`, returns fully structured strategy dict(s).
    *   `Util.getBackendLegJson(...)` → attaches semantic strike/expiry rules & risk logic per leg.
    *   `BTRunPortfolio_GPU._row_build_request(...)` → orchestrates entire workflow, building a rich `bt_params` object.

3.  **Semantic Strike Rule Format** (produced by `Util.getStrikeValueAndMethod`)
    ```jsonc
    {
      "strike_rule": "DELTA_TARGET",       // enum name consumed by LegModel
      "params": { "delta": 0.25 },         // rule-specific parameters
      "expiry_rule": "CURRENT_WEEK",       // enum for ExpiryRule
      "dte_target": null                    // optional int when rule = DTE_TARGET
    }
    ```

### HeavyDB Integration (Phase-3)

*   `heavydb_helpers.get_trades_for_portfolio(bt_params, ...)` will:
    1.  Loop portfolios → strategies → legs.
    2.  Convert each leg dict into `LegModel`, mapping `strike_rule`/`expiry_rule` strings to enums defined in `models.common`.
    3.  Call `query_builder.build_entry_sql(leg_model, trade_date)` and `build_exit_sql(...)` to generate per-leg CTE SQL.
    4.  Execute the UNION-ALL query, fetch to cuDF (GPU) or Pandas (CPU fallback).
    5.  Build `TradeRecord` objects with entry/exit prices, timestamps, quantity, direction, fees/slippage.

### GPU Usage

*   HeavyDB executes SQL natively on GPU, returning result buffers in host memory.
*   Large result sets are optionally transferred into cuDF for vectorised Python-side operations (risk rules, trailing stop evaluation) – controlled by environment variable `BT_USE_GPU`.
*   Multiprocessing workers (Phase-6) will each obtain a HeavyDB connection (`heavydb_helpers.get_connection()`), run batched SQL, and process results on GPU.

### Parallel Execution

*   Master process performs Excel parsing once → produces `bt_params`.
*   Workload split by date-slice or strategy.
*   Workers receive `bt_params` slice + date range, run HeavyDB queries, produce Parquet chunks (gpu-optimised path writes Arrow device buffers -> disk when available).
*   Parent merges chunks into final DataFrame → writers output Excel/JSON summarised reports.

### Testing Strategy

*   `pytest -m heavydb_required` — runs HeavyDB integration tests (skipped on CI runners lacking DB).
*   Fixtures include: mini option-chain Parquet dataset, tiny Excel portfolios, YAML golden-files with expected TradeRecords.
*   Coverage target ≥ 80 % for key modules (`Util`, `query_builder`, `heavydb_helpers`).

### Deployment & Configuration

*   Docker Compose with HeavyDB image + GPU runtime for local dev.
*   Env vars:
    *   `BT_HEAVYDB_URI` – connection string (default `heavydb://admin:HyperInteractive@127.0.0.1:6274/heavyai`).
    *   `BT_USE_GPU` – `1` to enable GPU DataFrame path, `0` CPU fallback.
    *   `BT_HEAVYDB_BATCH` – max legs per UNION query slice.
*   Alembic-style migration script seeds `LOT_SIZE` and `MARGIN_INFO` tables.

### Technical Constraints & Considerations

*   HeavyDB SQL limitations (no leading comments, limited window func support) handled in `query_builder`.
*   Option-chain view (`nifty_option_chain`) must have indexes on (`trade_date`, `expiry_date`, `strike`) for performance.
*   GPU memory management critical for very large portfolios × date ranges → batching strategy implemented in Phase-5. 