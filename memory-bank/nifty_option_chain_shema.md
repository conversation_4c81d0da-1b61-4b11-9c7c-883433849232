# Nifty Option Chain – Final CSV Schema
*Maintainer: Cursor AI – last updated 2025-05-14*

This document is the canonical reference for every **CSV** generated inside
`/srv/samba/shared/market_data/nifty/oc_with_futures/`.
It captures the column order, data types, and derivation logic implemented
by the processing script.

---
## Column Order & Meaning

| # | Column | CSV Type | HeavyDB Type | Description | Derivation |
|---|--------|----------|--------------|-------------|------------|
| 1 | trade_date | YYYY-MM-DD | DATE | Trading day | Parsed from YYMMDD in source file |
| 2 | trade_time | HH:MM:SS | TIME | Trading time (seconds set to 00) | Source value + `:00` suffix |
| 3 | expiry_date | YYYY-MM-DD | DATE | Option expiry date | Parsed from YYMMDD |
| 4 | index_name | text | TEXT ENCODING DICT | Always **`NIFTY`** | Constant |
| 5 | spot | double | DOUBLE | Underlying index price | Copy of `underlying_price` |
| 6 | atm_strike | double | DOUBLE | Per-tick ATM strike | Synthetic ATM algorithm |
| 7 | strike | double | DOUBLE | Option strike price | Source |
| 8 | dte | int | INT | Trading-day DTE (excl. trade date, incl. expiry) | Holiday-aware counter |
| 9 | expiry_bucket | text | TEXT ENCODING DICT | CW / NW / CM / NM | Rank logic |
|10 | zone_id | smallint | SMALLINT | Intraday zone id (1-5) | Time mapping |
|11 | zone_name | text | TEXT ENCODING DICT | OPEN / MID_MORN / LUNCH / AFTERNOON / CLOSE | Time mapping |
|12 | call_strike_type | text | TEXT ENCODING DICT | Moneyness label for Call | 50-pt ITMn / OTMn |
|13 | put_strike_type | text | TEXT ENCODING DICT | Moneyness label for Put | 50-pt ITMn / OTMn |
|14 | ce_symbol | text | TEXT ENCODING DICT | Call option symbol | Source |
|15 | ce_open | double | DOUBLE | CE open price | Source |
|16 | ce_high | double | DOUBLE | CE high price | Source |
|17 | ce_low | double | DOUBLE | CE low price | Source |
|18 | ce_close | double | DOUBLE | CE close price | Source |
|19 | ce_volume | bigint | BIGINT | CE traded volume | Source |
|20 | ce_oi | bigint | BIGINT | CE open interest | Source |
|21 | ce_coi | bigint | BIGINT | CE change in OI | Source |
|22 | ce_iv | double | DOUBLE | CE implied volatility | Source |
|23 | ce_delta | double | DOUBLE | CE delta | Source |
|24 | ce_gamma | double | DOUBLE | CE gamma | Source |
|25 | ce_theta | double | DOUBLE | CE theta | Source |
|26 | ce_vega | double | DOUBLE | CE vega | Source |
|27 | ce_rho | double | DOUBLE | CE rho | Source |
|28 | pe_symbol | text | TEXT ENCODING DICT | Put option symbol | Source |
|29 | pe_open | double | DOUBLE | PE open price | Source |
|30 | pe_high | double | DOUBLE | PE high price | Source |
|31 | pe_low | double | DOUBLE | PE low price | Source |
|32 | pe_close | double | DOUBLE | PE close price | Source |
|33 | pe_volume | bigint | BIGINT | PE traded volume | Source |
|34 | pe_oi | bigint | BIGINT | PE open interest | Source |
|35 | pe_coi | bigint | BIGINT | PE change in OI | Source |
|36 | pe_iv | double | DOUBLE | PE implied volatility | Source |
|37 | pe_delta | double | DOUBLE | PE delta | Source |
|38 | pe_gamma | double | DOUBLE | PE gamma | Source |
|39 | pe_theta | double | DOUBLE | PE theta | Source |
|40 | pe_vega | double | DOUBLE | PE vega | Source |
|41 | pe_rho | double | DOUBLE | PE rho | Source |
|42 | future_open | double | DOUBLE | Futures open price | Joined from `nifty_future.csv` |
|43 | future_high | double | DOUBLE | Futures high price | Joined |
|44 | future_low | double | DOUBLE | Futures low price | Joined |
|45 | future_close | double | DOUBLE | Futures close price | Joined |
|46 | future_volume | bigint | BIGINT | Futures traded volume | Joined |
|47 | future_oi | bigint | BIGINT | Futures open interest | Joined |
|48 | future_coi | bigint | BIGINT | Futures change in OI | Joined |

Total columns: **48**.

---
### HeavyDB CREATE TABLE Skeleton
```sql
DROP TABLE IF EXISTS nifty_option_chain_csv;
CREATE TABLE nifty_option_chain_csv (
    trade_date       DATE,
    trade_time       TIME,
    expiry_date      DATE,
    index_name       TEXT ENCODING DICT,
    spot             DOUBLE,
    atm_strike       DOUBLE,
    strike           DOUBLE,
    dte              INT,
    expiry_bucket    TEXT ENCODING DICT,
    zone_id          SMALLINT,
    zone_name        TEXT ENCODING DICT,
    call_strike_type TEXT ENCODING DICT,
    put_strike_type  TEXT ENCODING DICT,

    -- Call metrics
    ce_symbol TEXT ENCODING DICT,
    ce_open   DOUBLE,
    ce_high   DOUBLE,
    ce_low    DOUBLE,
    ce_close  DOUBLE,
    ce_volume BIGINT,
    ce_oi     BIGINT,
    ce_coi    BIGINT,
    ce_iv     DOUBLE,
    ce_delta  DOUBLE,
    ce_gamma  DOUBLE,
    ce_theta  DOUBLE,
    ce_vega   DOUBLE,
    ce_rho    DOUBLE,

    -- Put metrics
    pe_symbol TEXT ENCODING DICT,
    pe_open   DOUBLE,
    pe_high   DOUBLE,
    pe_low    DOUBLE,
    pe_close  DOUBLE,
    pe_volume BIGINT,
    pe_oi     BIGINT,
    pe_coi    BIGINT,
    pe_iv     DOUBLE,
    pe_delta  DOUBLE,
    pe_gamma  DOUBLE,
    pe_theta  DOUBLE,
    pe_vega   DOUBLE,
    pe_rho    DOUBLE,

    -- Futures join
    future_open   DOUBLE,
    future_high   DOUBLE,
    future_low    DOUBLE,
    future_close  DOUBLE,
    future_volume BIGINT,
    future_oi     BIGINT,
    future_coi    BIGINT
)
WITH (fragment_size = 32000000);
```

---
## Synthetic ATM Algorithm
For each unique `(trade_date, trade_time, expiry_date)`:
1. Build the _paired set_ of strikes that have **both** a Call and Put row with
   valid close prices.
2. For each paired strike compute:
```
SyntheticFuture = strike + CE_close − PE_close
```
3. Compute `abs_diff = |SyntheticFuture − spot|`.
4. Choose the strike with the **smallest** `abs_diff` → `atm_strike`.
5. If no paired strike exists, fallback:
```
atm_strike = round(spot / 50) * 50   -- standard 50-point rounding
```
The algorithm is executed _per tick_ (per unique time snapshot).

---
## Trading-Day DTE
Count of valid trading days **after** `trade_date` **through** `expiry_date`
(inclusive).  Non-trading days = weekends (Sat/Sun) + entries in the embedded
NSE holiday list.

---
## Expiry Buckets
* CW – earliest expiry ≥ trade_date
* NW – second-earliest expiry ≥ trade_date
* CM – nearest **monthly** expiry (last Thursday of month)
* NM – second-nearest monthly expiry

---
## Futures Join Logic
For every option row:
1. Filter futures rows with matching `trade_date`.
2. Convert option and futures times to integer `HHMMSS`.
3. Choose the row where `fut_time ≤ opt_time` with the smallest gap.
4. If none earlier, choose the nearest later row.
5. Copy required futures columns.

---
## Intraday Zones
| zone_id | zone_name | Time range |
|---------|-----------|------------|
| 1 | OPEN | 09:15-10:30 |
| 2 | MID_MORN | 10:30-12:00 |
| 3 | LUNCH | 12:00-13:30 |
| 4 | AFTERNOON | 13:30-15:00 |
| 5 | CLOSE | 15:00-15:30 |

---
This schema is **authoritative**; any processing code must output columns in
this exact order and semantic definition. 

## Strategy Mapping Documentation

This section provides references to detailed documentation on how each strategy type's input fields map to the `nifty_option_chain` schema.

### Strategy Type Mappings

Each strategy type has specific ways it interacts with the option chain data structure. Comprehensive documentation on field mappings, SQL query patterns, and database integration details can be found in:

1. **Time-Based Strategy (TBS)**: [column_mapping_ml_tbs.md](/bt/backtester_stable/BTRUN/input_sheets/column_mapping_ml_tbs.md)
   - Maps time-window parameters to SQL TIME filters
   - Documents direct mappings for strike selection methods
   - Provides example SQL patterns for entry/exit time queries

2. **Opening Range Breakout (ORB)**: [column_mapping_ml_orb.md](/bt/backtester_stable/BTRUN/input_sheets/column_mapping_ml_orb.md)
   - Details range calculation SQL patterns
   - Includes breakout detection logic
   - Shows optimized join strategies for range/price comparisons

3. **Indicator-Based Strategy**: [column_mapping_ml_indicator.md](/bt/backtester_stable/BTRUN/input_sheets/column_mapping_ml_indicator.md)
   - Documents calculation of indicators (EMA, RSI, VWAP) from raw price data
   - Provides time-series bucketing optimization techniques
   - Shows SQL patterns for combining multiple indicator conditions

4. **Open Interest (OI) Strategy**: [column_mapping_ml_oi.md](/bt/backtester_stable/BTRUN/input_sheets/column_mapping_ml_oi.md)
   - Details MAXOI and MAXCOI strike selection SQL
   - Shows time-bucketed OI analysis patterns
   - Includes implementation details for OI thresholds and ranking

### Common Database Integration Features

All strategy types share these common features when interacting with the database schema:

1. **Time Transformations**: Excel HHMMSS format (91600) → HeavyDB TIME format ('09:16:00')
2. **Symbol Adaptation**: Strategy `Index` field maps to the appropriate `{symbol}_option_chain` table
3. **Expiry Selection**: Excel expiry values (CURRENT, NEXT) map to expiry_bucket values (CW, NW, CM, NM)
4. **Strike Selection**: Moneyness parameters map to strike filters using atm_strike, call_strike_type, put_strike_type

### Performance Considerations

Query patterns in the mapping documentation include considerations for:

1. **Row Limits**: HeavyDB has a default row limit of 128,000,000 rows
2. **GPU Acceleration**: Complex queries use `/*+ gpu_enable(true) */` hint
3. **Time/Date Filters**: All queries include date filters to leverage partitioning
4. **Query Optimization**: Subqueries, CTEs, and join strategies optimized for GPU execution

For details on how specific strategy parameters map to SQL queries, refer to the corresponding strategy mapping document. 