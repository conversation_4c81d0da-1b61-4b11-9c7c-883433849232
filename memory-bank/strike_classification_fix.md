# Strike Classification Fix Documentation

## Issue Description

The Nifty Option Chain data had two issues with strike classification:

1. Duplicated values in the `call_strike_type` and `put_strike_type` columns (like "ATM,ATM1" or "ITM1,ITM1")
2. Incorrect step size calculation - Nifty always uses 50-point increments, not 100 points when above 10,000

This issue was visible in the Excel export where multiple classifications appeared for the same strike, and some strikes had incorrect numbering.

## Cause Analysis

After investigation, the issues appeared to be in:

1. Multiple classification functions running in sequence and concatenating results
2. Incorrect assumption about strike increment size (Nifty always uses 50-point increments)
3. The batch loading process potentially concatenating values during import
4. Data transformation pipeline not properly validating the output format

## Solution Components

We've implemented several fixes to address these issues:

### 1. Helper Function for Clean Classification

Created a dedicated function `get_clean_strike_classification` that consistently produces correct strike classifications and properly uses 50-point increments:

```sql
CREATE OR REPLACE FUNCTION get_clean_strike_classification(
    p_strike DOUBLE,
    p_atm_strike DOUBLE,
    p_underlying_price DOUBLE,
    p_option_type TEXT
)
RETURNS TEXT AS $$
BEGIN
    IF p_strike = p_atm_strike THEN
        RETURN 'ATM';
    END IF;
    
    DECLARE
        -- Always use 50-point increment for Nifty
        v_step_size DOUBLE := 50;
        v_distance DOUBLE := ABS(p_strike - p_atm_strike);
        v_steps INTEGER := CEILING(v_distance / v_step_size);
    BEGIN
        IF p_option_type = 'call' THEN
            IF p_strike < p_atm_strike THEN
                RETURN 'ITM' || v_steps::TEXT;
            ELSE
                RETURN 'OTM' || v_steps::TEXT;
            END IF;
        ELSE -- put
            IF p_strike > p_atm_strike THEN
                RETURN 'ITM' || v_steps::TEXT;
            ELSE
                RETURN 'OTM' || v_steps::TEXT;
            END IF;
        END IF;
    END;
END;
$$ LANGUAGE SQL;
```

### 2. Python Script for Fixing All Files

Created a Python script (`fix_moneyness.py`) that:
- Fixes any existing comma-separated classifications
- Correctly calculates strike distance using the proper 50-point increment for Nifty
- Processes and fixes all compatible files in the directory

```python
def classify_strike(strike, atm_strike, underlying_price, option_type='call'):
    """
    Classify a strike as ATM, ITM or OTM with proper numeric ranking.
    For calls: ITM if strike < ATM, OTM if strike > ATM
    For puts: ITM if strike > ATM, OTM if strike < ATM
    
    Nifty always uses 50-point increments regardless of price level
    """
    if strike == atm_strike:
        return 'ATM'
    
    # Always use 50-point increment for Nifty
    step_size = 50
    
    # Calculate distance in steps (ceiling division)
    distance = abs(strike - atm_strike)
    steps = int(np.ceil(distance / step_size))
    
    if option_type == 'call':
        return f'ITM{steps}' if strike < atm_strike else f'OTM{steps}'
    else:  # put
        return f'ITM{steps}' if strike > atm_strike else f'OTM{steps}'
```

### 3. Repair Function for Existing Data

Created a function to fix existing data by extracting the first part of duplicated classifications:

```sql
CREATE FUNCTION fix_strike_classification(
    p_strike_type TEXT
)
RETURNS TEXT
AS $$
BEGIN
    IF p_strike_type LIKE '%,%' THEN
        RETURN SUBSTRING(p_strike_type FROM 1 FOR POSITION(',' IN p_strike_type) - 1);
    ELSE
        RETURN p_strike_type;
    END IF;
END;
$$ LANGUAGE SQL;
```

## Implementation Steps

1. Run `sql_functions/fix_strike_classification.sql` to create the repair function
2. Run `fix_moneyness.py` to fix all files in the directory

## Verification

After applying the fix, the strike classifications follow the correct pattern:

```
For ATM strike of 17400:
- 17300 -> ITM2/OTM2 (100 point difference = 2 * 50-point steps)
- 17350 -> ITM1/OTM1 (50 point difference = 1 * 50-point step)
- 17400 -> ATM/ATM (at the money)
- 17450 -> OTM1/ITM1 (50 point difference = 1 * 50-point step)
- 17500 -> OTM2/ITM2 (100 point difference = 2 * 50-point steps)
```

## Strike Classification Logic

### Call Options
- ATM: strike = atm_strike
- ITM: strike < atm_strike
- OTM: strike > atm_strike

### Put Options
- ATM: strike = atm_strike
- ITM: strike > atm_strike
- OTM: strike < atm_strike

The number after ITM/OTM indicates distance in 50-point strike increments. Nifty always uses 50-point increments regardless of the index value. 