## 4. Direct COPY Loader (2025-05-17)

The original two-stage loader (temp table + type-cast INSERT) has been
superseded by a **single-stage, GPU-optimised path** that copies each CSV
straight into the production table `nifty_option_chain`.

### Why we changed
1. TRY_CAST on every column was costly and silently rejected rows when a
   single field failed to cast.
2. HeavyDB 6.x now allows direct `COPY` into a dictionary-encoded table as
   long as the incoming text values fit the target encodings.
3. Performance: 0.5 M rows / second on A100 – ~10× faster than the temp-table
   approach.

### New workflow
```bash
# 1️⃣  Ensure all CSVs are physically inside HeavyDB's import area
sudo rsync -av --ignore-existing \
     /srv/samba/shared/market_data/nifty/oc_with_futures/ \
     /var/lib/heavyai/storage/import/

# 2️⃣  (Re)create the table and bulk-load every file
python3 load_with_proven_method.py \
        --csv-dir /srv/samba/shared/market_data/nifty/oc_with_futures \
        --recreate-table
```
The script performs for each file:
1. `sudo cp` (only if the file is not already in `/var/lib/heavyai/storage/import/`).
2. `COPY nifty_option_chain FROM '<file>' WITH (header='true', delimiter=',');`
3. Parses the "Loaded: <n> recs" message and logs per-file row counts.

### Table definition
`load_with_proven_method.py` creates the final table only – schema matches
`memory-bank/nifty_option_chain_shema.md` exactly (48 columns, DICT encodings).
No temporary artefacts remain after load.

### Monitoring
Loading progress is written to `nifty_option_chain_load.log` and echoed to the
console.  Typical rates on the production server:

| File | Rows | Time (s) | Rows/s |
|------|------|---------|--------|
| `IV_2023_june_nifty_futures.csv` | 291 324 | 0.84 | 348 k |
| … | … | … | … |

Expect the full 28-file history (~9 M rows) to complete in under two minutes.

### Refresh downstream objects
After a successful load run:
```sql
REFRESH MATERIALIZED VIEW nifty_option_chain;
```
(if the project still uses the materialised-view layer.)

### Rollback / Re-load
Re-run the loader with `--recreate-table` to drop + recreate the table from
scratch.  For incremental loads omit the flag – existing rows are retained
and new CSVs simply append. 