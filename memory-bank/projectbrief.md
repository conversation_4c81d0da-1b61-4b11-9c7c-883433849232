---
title: Nifty Option Chain Backtester - Project Brief
version: 1.2
last_updated: 2025-05-11
---

## 1. Project Overview

This project aims to develop a high-performance portfolio backtesting system for Nifty, BankNifty, and Finnifty options strategies, leveraging HeavyDB for data storage and GPU acceleration for computation. The system will replace a legacy REST-based engine while maintaining compatibility with existing Excel-based strategy definition formats.

## 2. Core Requirements

*   **Functional Parity:** Replicate all critical features of the existing legacy backtesting engine.
*   **Excel Input:** Continue to use existing Excel templates (`input_portfolio.xlsx` and strategy-specific files like `input_tbs_multi_legs.xlsx`) for defining portfolios, strategies, and legs.
*   **HeavyDB Integration:** Utilize HeavyDB as the primary data source for historical option chain data (tick-by-tick or 1-minute bars).
*   **GPU Acceleration:** Employ GPU (cuDF, Numba, or HeavyDB's native GPU capabilities) for computationally intensive tasks, particularly trade generation and potentially some aspects of analytics.
*   **Accurate Trade Generation:** Implement robust logic for:
    *   Dynamic strike selection based on various criteria (ATM, OTM, ITM, Delta, Premium).
    *   Expiry rule handling (weekly, next weekly, monthly, DTE-based).
    *   Precise entry/exit timing based on strategy parameters.
    *   Application of stop-loss, take-profit, and trailing stop-loss rules.
    *   Wait-and-trade logic.
    *   Re-entry conditions.
    *   Hedging.
*   **Output Generation:** Produce outputs (Excel, JSON, charts) consistent with the legacy system, including detailed trade logs, performance metrics, and P&L summaries.
*   **Performance:** Achieve significant speed improvements over the legacy system for large backtests.
*   **Extensibility:** Design the system to easily accommodate new indices, strike selection rules, risk management features, and data sources.
*   **Maintainability:** Ensure clean, well-documented, and testable code.

## 3. Key System Components (High-Level)

*   **Excel Parser:** Module to read and interpret data from input Excel files, transforming it into an internal structured representation.
*   **Data Access Layer (HeavyDB Helpers):** Utilities to connect to HeavyDB and fetch option chain data efficiently.
*   **Query Builder:** Constructs optimized SQL queries for HeavyDB to retrieve specific option snapshots based on strategy parameters (strike, expiry, time).
*   **Trade Generation Engine:** Core logic that iterates through trading dates and strategy legs, applying rules to generate trade records (entry price, exit price, timestamp, P&L).
*   **Risk Management Module:** Applies SL/TP/Trailing logic.
*   **Analytics & Reporting:** Calculates performance metrics and generates output files.
*   **Runtime Orchestrator:** Manages the overall backtesting process, including date iteration, parallel execution, and GPU resource management.

## 4. Scope Boundaries

*   **Initial Focus:** Nifty, BankNifty, Finnifty options.
*   **Data Source:** Primarily HeavyDB. Adapters for other sources can be future enhancements.
*   **Order Execution:** This is a backtesting system; no live trading execution is in scope.

## 5. Success Criteria

*   Successful backtesting of all existing benchmark strategy Excel files with results matching (or demonstrably correct if different due to improved precision) the legacy system.
*   Demonstrable performance improvement for large-scale backtests.
*   Codebase with high unit test coverage (≥80%).
*   Clear and comprehensive documentation.
*   The system correctly processes all columns and logic from `PortfolioSetting`, `StrategySetting`, strategy-specific `GeneralParameter` & `LegParameter` sheets, and `lotsize.csv` as per archive implementation, translating them into a HeavyDB-native workflow.
*   Strike selection and option data retrieval are performed natively against HeavyDB based on semantic rules derived from Excel.

## 6. Future Considerations (Post-MVP)

*   Support for additional asset classes or indices.
*   More advanced risk management features.
*   Integration with live data feeds for paper trading.
*   Web-based UI for strategy definition and results visualization.
*   Machine learning-based strategy optimization. 