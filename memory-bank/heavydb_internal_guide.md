# HeavyDB Internal Documentation: Nifty Option Chain Project

## 1. Introduction

This document provides essential internal information for connecting to and utilizing the HeavyDB database instance dedicated to the Nifty Option Chain project. It covers connection details, key database objects, usage guidelines, and maintenance procedures.

## 2. Connection Details

**IMPORTANT**: Treat the password as confidential information. Do not hardcode it directly in scripts or check it into version control without proper security measures (e.g., environment variables, secrets management).

- **Host**: `127.0.0.1`
- **Port**: `6274`
- **User**: `admin`
- **Password**: `HyperInteractive`
- **Database Name**: `heavyai`

## 3. Connection Methods

### 3.1. Using `heavysql` (Command-Line Interface)
The `heavysql` client is the primary command-line tool for interacting with HeavyDB.

```bash
# Connect and execute a query directly
/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai --execute "SELECT COUNT(*) FROM nifty_greeks;"

# Connect and run SQL from a file (ensure file has no leading comments)
/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai -q < your_script.sql

# Interactive session (will prompt for password if -p is omitted)
/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai
```

### 3.2. Using Python (`pymapd`)
The recommended Python library is `pymapd`.

```python
import pymapd
import pandas as pd

def get_heavydb_connection():
    """Establishes a connection to the HeavyDB database."""
    try:
        conn = pymapd.connect(
            host='127.0.0.1',
            port=6274,
            user='admin',
            password='HyperInteractive', # Consider using environment variables
            dbname='heavyai'
        )
        print("Successfully connected to HeavyDB.")
        return conn
    except Exception as e:
        print(f"Error connecting to HeavyDB: {e}")
        return None

def execute_query(conn, query):
    """Executes a query and returns results as a Pandas DataFrame."""
    if not conn:
        print("No database connection available.")
        return None
    try:
        df = pd.read_sql(query, conn)
        return df
    except Exception as e:
        print(f"Error executing query: {e}")
        return None

# Example Usage
connection = get_heavydb_connection()
if connection:
    sample_query = """
        SELECT trade_date, expiry_date, strike, underlying_price, dte, call_strike_type
        FROM nifty_option_chain
        WHERE trade_date = '2023-01-03' AND expiry_date = '2023-01-26'
        LIMIT 10;
    """
    # Note: This query will fail until the view 'nifty_option_chain' is successfully created
    # results_df = execute_query(connection, sample_query)
    # if results_df is not None:
    #     print("Query Results:")
    #     print(results_df)

    # Example checking existing table
    check_holidays_query = "SELECT COUNT(*) FROM nse_holidays;"
    holidays_count_df = execute_query(connection, check_holidays_query)
    if holidays_count_df is not None:
         print("Holiday Count:")
         print(holidays_count_df)

    connection.close()
    print("Database connection closed.")

### 3.3. Using Python (`heavydb`) - [PREFERRED]
The `heavydb` module is the newer, preferred library for connecting to HeavyDB. It provides better GPU acceleration and native integration with the HeavyDB GPU pipeline.

```python
import heavydb
import pandas as pd

def get_heavydb_connection():
    """Establishes a connection to the HeavyDB database using the heavydb module."""
    try:
        conn = heavydb.connect(
            host='127.0.0.1',
            port=6274,
            user='admin',
            password='HyperInteractive',  # Consider using environment variables
            dbname='heavyai'
        )
        print("Successfully connected to HeavyDB.")
        return conn
    except Exception as e:
        print(f"Error connecting to HeavyDB: {e}")
        return None

def execute_query(conn, query):
    """Executes a query and returns results."""
    if not conn:
        print("No database connection available.")
        return None
    
    try:
        # Direct execution for non-SELECT queries
        if not query.strip().upper().startswith("SELECT"):
            result = conn.execute(query)
            return result
        
        # For SELECT queries, we can use pandas conversion
        # Note: This may show a warning about pandas only supporting SQLAlchemy
        df = pd.read_sql(query, conn)
        return df
    except Exception as e:
        print(f"Error executing query: {e}")
        return None

# Example Usage
connection = get_heavydb_connection()
if connection:
    # Important: Always use trade_time (not time or entry_time) for time-based filtering
    sample_query = """
        SELECT trade_date, trade_time, expiry_date, strike, underlying_price, dte, call_strike_type
        FROM nifty_option_chain
        WHERE trade_date = '2023-01-03' 
          AND expiry_date = '2023-01-26'
          AND trade_time >= '09:15:00'
          AND trade_time <= '15:30:00'
        LIMIT 10;
    """
    
    results_df = execute_query(connection, sample_query)
    if results_df is not None:
        print("Query Results:")
        print(results_df.head())
    
    # Example for time-specific filtering (e.g., for backtesting)
    entry_query = """
        SELECT *
        FROM nifty_option_chain
        WHERE trade_date = '2023-01-03'
          AND expiry_date = '2023-01-26'
          AND strike = 18000
          AND trade_time >= '09:15:00'  -- Using trade_time for time-based filtering
        ORDER BY trade_time ASC
        LIMIT 1;
    """
    
    # Example working with time columns
    query_by_time_window = """
        SELECT 
            trade_date,
            trade_time,  -- Use this column for time-based operations
            expiry_date,
            strike,
            underlying_price,
            dte
        FROM nifty_option_chain
        WHERE trade_date = '2023-01-03'
          AND trade_time BETWEEN '09:15:00' AND '15:30:00'
        ORDER BY trade_time
        LIMIT 100;
    """
    
    connection.close()
    print("Database connection closed.")

## 4. Key Database Objects

### 4.1. `nifty_greeks` (Table)

- **Description**: The primary source table containing raw, time-series data for Nifty options, including prices, volumes, open interest, and calculated Greeks for both call and put options at various strikes.
- **Key Columns**:
    - `trade_date` (DATE): Date of the trade record.
    - `trade_time` (TIME): Time of the trade record.
    - `expiry_date` (DATE): Expiration date of the option contract.
    - `strike` (DOUBLE): Strike price of the option.
    - `underlying_price` (DOUBLE): Price of the Nifty index at the time of the record.
    - `ce_*` (various DOUBLE/TEXT): Columns related to Call options (Open, High, Low, Close price, Volume, OI, COI, IV, Delta, Gamma, Theta, Vega, Rho).
    - `pe_*` (various DOUBLE/TEXT): Columns related to Put options (Open, High, Low, Close price, Volume, OI, COI, IV, Delta, Gamma, Theta, Vega, Rho).
- **Notes**: This table serves as the foundation for the `nifty_option_chain` view. Data is typically loaded via the process described in `etl_process_guide.md`. The frequency of updates (e.g., daily, intra-day) dictates when the `nifty_option_chain` view should be refreshed.

### 4.2. `nse_holidays` (Table)

- **Description**: Reference table containing the official list of NSE trading holidays.
- **Schema**:
    - `holiday_date` (DATE): The date of the holiday.
    - `reason` (TEXT): Description or reason for the holiday.
- **Content**: Currently populated with 123 official holidays from 2019 to 2026.
- **Maintenance**: Requires annual updates. See Maintenance section below.

### 4.3. `nifty_option_chain` (Regular View)

- **Description**: The primary analytical object. This Regular view transforms the raw `nifty_greeks` data into a structured option chain format. It pre-calculates key metrics like DTE and strike classification.
- **Key Columns**:
    - `trade_date` (DATE): Date of the trade record.
    - `trade_time` (TIME): Time of the trade record.
    - `expiry_date` (DATE): Option expiration date.
    - `underlying_price` (DOUBLE): Index price for the trade date.
    - `atm_strike` (DOUBLE): Calculated At-The-Money strike price (closest to underlying).
    - `strike` (DOUBLE): The specific strike price for the row.
    - `dte` (INTEGER): Calculated Days To Expiry, excluding weekends and NSE holidays.
    - `call_strike_type` (VARCHAR): Classification for the call option (e.g., 'ATM', 'ITM1', 'OTM2').
    - `put_strike_type` (VARCHAR): Classification for the put option (e.g., 'ATM', 'ITM1', 'OTM2').
    - `ce_*` (various DOUBLE/TEXT): Call option specific data (Symbol, Prices, Volume, OI, Greeks).
    - `pe_*` (various DOUBLE/TEXT): Put option specific data (Symbol, Prices, Volume, OI, Greeks).
- **Notes**: Being a Regular view, it needs to be refreshed to reflect new data in `nifty_greeks`. Queries against this view are significantly faster than querying `nifty_greeks` directly for chain analysis due to pre-computation and structure. *This view was created using `nifty_option_chain_no_comments.sql`*.

**Important:**
- The `trade_time` column is now present and should be used for all intraday or time-window logic in queries and in the backtest engine. Do **not** use a generic `time` or `entry_time` column name in SQL or code.

## 5. Using the `nifty_option_chain` Regular View

### 5.1. Querying Best Practices

- **Mandatory Filtering**: Due to the large data volume and HeavyDB's row limits (currently 128M), **queries MUST include filters**, especially on `trade_date` and/or `expiry_date`.
  ```sql
  -- GOOD: Filters applied
  SELECT * FROM nifty_option_chain
  WHERE trade_date = '2023-01-10' AND expiry_date = '2023-01-26';

  -- BAD: No filters - Will likely hit row limit
  -- SELECT * FROM nifty_option_chain LIMIT 10;
  ```
- **Select Specific Columns**: Avoid `SELECT *`. Specify only the columns you need to reduce GPU memory consumption and improve performance.
- **Use Derived Fields**: Leverage pre-calculated fields like `dte`, `atm_strike`, `call_strike_type`, `put_strike_type` for filtering and analysis instead of recalculating them.
- **Refer to Examples**: See `example_queries.sql` for various common analytical query patterns.

### 5.2. Refreshing the View
Regular views do not update automatically when the base table (`nifty_greeks`) changes. You must explicitly refresh it:

```sql
REFRESH Regular VIEW nifty_option_chain;
```

- **Frequency**: The refresh frequency should align with the completion of the ETL process for `nifty_greeks`. If ETL runs daily after market close, the refresh should run shortly after.
- **Scheduling**: For production, this refresh command should be scheduled (e.g., via cron or an orchestration tool) to run *after* the `nifty_greeks` ETL process successfully completes.
    - *Example Cron Job (conceptual)*:
      ```cron
      # Runs daily at 8:00 PM, assuming ETL finishes before then
      0 20 * * * /path/to/scripts/run_heavysql.sh "REFRESH Regular VIEW nifty_option_chain;"
      ```
    - *Dependency Management*: If using a workflow tool like Airflow, the refresh task should be downstream of the ETL task.
- **Performance**: Refreshing can be resource-intensive, especially for large base tables. Consider scheduling during off-peak hours. Monitor refresh durations.
- **Monitoring**: Log the start time, end time, and success/failure status of each refresh operation.

## 6. Maintenance

### 6.1. Updating the Holiday Calendar (`nse_holidays`)

- **Frequency**: Annually, when the NSE releases the official holiday list for the upcoming year.
- **Process**:
    1. Obtain the official list of holidays for the new year(s).
    2. Create or update SQL `INSERT` statements for the new holidays (similar to `holidays_2025_2026_clean.sql`).
    3. Use the `load_holidays_clean.sh` script (or adapt it) to insert the new data. Ensure the script targets the correct SQL files.
    4. Verify the data load by checking the count in the table (`SELECT COUNT(*) FROM nse_holidays;`).
    5. Update relevant documentation (like this guide and `holiday_update_summary.md`) to reflect the new date range covered.

### 6.2. Updating the View Definition

- **Trigger**: If the schema of the base table (`nifty_greeks`) changes significantly, or if the logic for ATM, DTE, or strike classification needs refinement.
- **Process**:
    1. Modify the `CREATE VIEW nifty_option_chain AS ...` statement in the SQL file (`nifty_option_chain_no_comments.sql`).
    2. Execute the updated script using `heavysql` to drop and recreate the view: `/opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai -q < nifty_option_chain_no_comments.sql`
    3. Remember that recreating the view will require it to be Regular again, which might take time.

## 7. Important Considerations / HeavyDB Specifics

- **SQL Syntax**: SQL statements intended for execution via `heavysql -q < file.sql` **must not** begin with comments (`--` or `/* */`).
- **Row Limits**: Be mindful of the 128 million row processing limit per query stage. Design queries with effective filters.
- **CTEs**: Use Common Table Expressions (CTEs) for breaking down complex logic, as demonstrated in the view definition.
- **`QUALIFY` Clause**: Preferred over subqueries with window functions for filtering based on window function results where applicable.
- **Regular Views**: Remember they require explicit refreshing.
- **GPU Optimization**: The view uses standard SQL functions generally optimized for GPU, but complex analytics might require further tuning specific to HeavyDB.

## 8. Troubleshooting & Support

- **Common Errors**:
    - `Object not found`: Ensure the view (`nifty_option_chain`) has been created successfully (check `SHOW TABLES;`). It might have been dropped or the creation script failed. Re-run `nifty_option_chain_no_comments.sql`.
    - `Row limit exceeded`: Add or refine `WHERE` clause filters, especially on dates.
    - `SQL statements starting with comments...`: Remove leading comments from SQL scripts run with `-q`.
- **Further Documentation**:
    - `memory-bank/nifty_option_chain.md` (Implementation Details - Broader Doc)
    - `example_queries.sql` (Usage Examples)
    - `.cursorrules` (Project Intelligence & Patterns)
- **Contact**: For unresolved issues, consult [Your Name/Team Lead/Designated DB Admin].