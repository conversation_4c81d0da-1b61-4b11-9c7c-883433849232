# HeavyDB Migration Verification Report (May 9 2025)

**Scope**  Full comparison between legacy MySQL-based back-tester (archive tree) and new HeavyDB / GPU stack (active tree).

---
## 1  Artifacts Compared
| Stack | Portfolio Runner | Utility Module | DAL / Connection |
|-------|-----------------|----------------|------------------|
| Legacy | `archive/BTRunPortfolio.py` | `archive/Util.py` | raw MySQL connectors inside Util |
| HeavyDB | `backtester_stable/BTRunPortfolio_GPU.py` | `backtester_stable/Util.py` | `heavydb_helpers`, `dal/heavydb_conn.py` |

---
## 2  Key Findings
1. **No MySQL references** remain anywhere in the HeavyDB code-path (`grep` confirmed).
2. **Config-driven path resolution** – all Excel workbooks now resolved through
   ```python
   os.path.join(config.INPUT_FILE_FOLDER,
                config.PORTFOLIO_LEGACY_FILE_PATH)
   ```
3. **HeavyDB connection** handled by `heavydb_helpers.get_connection()` which
   prefers a real `pymapd` connection but gracefully falls back to
   `mock_heavydb` if the library or server is unavailable.
4. **Materialised-view names** (`nifty_option_chain`, `nse_holidays`, …) are
   referenced only in the new stack; legacy stack never mentions them (as
   expected).
5. **GPU helpers** (`gpu_helpers`) are active; `--cpu-only` flag verified.
6. **Input contract unchanged** – still reads `PortfolioSetting` &
   `StrategySetting`.
7. **Output contract unchanged** – still writes `PortfolioParameter`,
   `GeneralParameter`, `LegParameter`.

---
## 3  Remaining Checks / TODO
| Item | Status | Notes |
|------|--------|-------|
| Audit `dal/heavydb_conn.py` | ⚠ mock implementation by default | Replace with direct call to `heavydb_helpers` once real DB available |
| End-to-end run against live HeavyDB | ☐ | Need populated DB & CUDA-enabled environment |
| Clean-up stray experimental imports (`dal.نمایش_داده_ها`) | ☐ | Cosmetic |
| Remove mock fallback in production builds | ☐ | After live DB in CI |

---
## 4  Conclusion
The new GPU runner and utilities are structurally HeavyDB-native and free of
MySQL code.  Only the DAL layer still defaults to a mock connection; once
`pyheavydb`/`pymapd` is installed and reachable, the system is ready for true
GPU-accelerated HeavyDB back-testing. 