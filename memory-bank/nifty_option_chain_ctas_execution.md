# Nifty Option Chain - CTAS Execution Strategy

Based on our testing and HeavyDB evaluation, we recommend proceeding with the following refined implementation approach for the nifty_option_chain_tbl.

## Testing Results

Our testing has revealed:

1. Support objects creation works perfectly:
   - Trading calendar (2019-2026)
   - Zone definitions
   - Expiry flags

2. Intermediate step processing works:
   - Extracting data from nifty_greeks (396K+ rows for one month)
   - Joining with zone_def
   - Calculating ATM strikes and DTE
   - Classifying expiry buckets and strike types

3. Limitations encountered:
   - Large-scale data insertion operations may have issues
   - Dictionary-encoded text conversion in bulk operations

## Recommended Implementation Plan

We recommend a phased approach with smaller batch sizes:

### Phase 1: Core Structure Creation

1. Create the table structure:
```sql
DROP TABLE IF EXISTS nifty_option_chain_tbl;

CREATE TABLE nifty_option_chain_tbl (
   trade_date          DATE,
   trade_time          TIME,
   expiry_date         DATE,
   underlying_price    DOUBLE,
   atm_strike          DOUBLE,
   strike              DOUBLE,
   dte                 INT,
   expiry_bucket       TEXT ENCODING DICT,
   zone_id             SMALLINT,
   zone_name           TEXT ENCODING DICT,
   call_strike_type    TEXT ENCODING DICT,
   put_strike_type     TEXT ENCODING DICT,
   
   ce_symbol TEXT ENCODING DICT,  ce_open DOUBLE,  ce_high DOUBLE,
   ce_low DOUBLE, ce_close DOUBLE, ce_volume BIGINT, ce_oi BIGINT,
   ce_coi BIGINT, ce_iv DOUBLE, ce_delta DOUBLE, ce_gamma DOUBLE,
   ce_theta DOUBLE, ce_vega DOUBLE, ce_rho DOUBLE,
   
   pe_symbol TEXT ENCODING DICT,  pe_open DOUBLE,  pe_high DOUBLE,
   pe_low DOUBLE, pe_close DOUBLE, pe_volume BIGINT, pe_oi BIGINT,
   pe_coi BIGINT, pe_iv DOUBLE, pe_delta DOUBLE, pe_gamma DOUBLE,
   pe_theta DOUBLE, pe_vega DOUBLE, pe_rho DOUBLE
)
WITH (fragment_size = 32000000);
```

### Phase 2: Incremental Data Loading

Implement a batch processing approach, loading data one week at a time:

```bash
# Example: Process data week by week
for date in '2023-01-01' '2023-01-08' '2023-01-15' '2023-01-22' '2023-01-29'; do
  # Create a date-specific SQL file from the template
  sed "s/:START_DATE/$date/g" sql_functions/weekly_template.sql > /tmp/process_week.sql
  
  # Execute the batch
  /opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai -q < /tmp/process_week.sql
  
  # Validate the batch
  echo "SELECT COUNT(*) FROM nifty_option_chain_tbl WHERE trade_date >= '$date' AND trade_date < DATE_ADD(DAY, 7, '$date');" > /tmp/validate.sql
  /opt/heavyai/bin/heavysql -s 127.0.0.1 --port 6274 -u admin -p HyperInteractive -d heavyai -q < /tmp/validate.sql
done
```

### Phase 3: Weekly Template Script

Create a weekly processing template with this structure:

```sql
-- Weekly processing template
-- Replace :START_DATE with the actual start date
DROP TABLE IF EXISTS noc_temp_step1;

CREATE TABLE noc_temp_step1 AS
SELECT 
    g.trade_date,
    g.trade_time,
    g.expiry_date,
    g.underlying_price,
    g.strike,
    CASE 
        WHEN g.underlying_price < 10000 
        THEN ROUND(g.underlying_price / 50) * 50
        ELSE ROUND(g.underlying_price / 100) * 100
    END AS atm_strike,
    (SELECT COUNT(*) - 1
     FROM trading_calendar tc
     WHERE tc.cal_date BETWEEN g.trade_date AND g.expiry_date) AS dte,
    z.zone_id,
    z.zone_name,
    g.ce_symbol, g.ce_open, g.ce_high, g.ce_low, g.ce_close, 
    g.ce_volume, g.ce_oi, g.ce_coi, g.ce_iv, g.ce_delta, 
    g.ce_gamma, g.ce_theta, g.ce_vega, g.ce_rho,
    g.pe_symbol, g.pe_open, g.pe_high, g.pe_low, g.pe_close, 
    g.pe_volume, g.pe_oi, g.pe_coi, g.pe_iv, g.pe_delta, 
    g.pe_gamma, g.pe_theta, g.pe_vega, g.pe_rho
FROM nifty_greeks g
JOIN zone_def z ON g.trade_time BETWEEN z.start_t AND z.end_t
WHERE g.trade_date >= ':START_DATE' AND g.trade_date < DATE_ADD(DAY, 7, ':START_DATE')
  AND g.expiry_date > g.trade_date;

DROP TABLE IF EXISTS noc_temp_step2;

CREATE TABLE noc_temp_step2 AS
SELECT 
    t.*,
    CASE
        WHEN EXTRACT(DOW FROM t.expiry_date) = 4 THEN 'CM' 
        WHEN EXTRACT(DOW FROM t.expiry_date) = 3 THEN 'CW'
        ELSE 'NW'
    END AS expiry_bucket,
    CASE
        WHEN t.strike = t.atm_strike THEN 'ATM'
        WHEN t.strike < t.atm_strike THEN 'ITM' || CAST(CEIL(ABS(t.strike - t.atm_strike) / 
                                          CASE WHEN t.underlying_price < 10000 THEN 50 ELSE 100 END) AS TEXT)
        ELSE 'OTM' || CAST(CEIL(ABS(t.strike - t.atm_strike) / 
                        CASE WHEN t.underlying_price < 10000 THEN 50 ELSE 100 END) AS TEXT)
    END AS call_strike_type,
    CASE
        WHEN t.strike = t.atm_strike THEN 'ATM'
        WHEN t.strike > t.atm_strike THEN 'ITM' || CAST(CEIL(ABS(t.strike - t.atm_strike) / 
                                          CASE WHEN t.underlying_price < 10000 THEN 50 ELSE 100 END) AS TEXT)
        ELSE 'OTM' || CAST(CEIL(ABS(t.strike - t.atm_strike) / 
                        CASE WHEN t.underlying_price < 10000 THEN 50 ELSE 100 END) AS TEXT)
    END AS put_strike_type
FROM noc_temp_step1 t;

INSERT INTO nifty_option_chain_tbl
SELECT 
    trade_date, trade_time, expiry_date, underlying_price, atm_strike, strike,
    dte, expiry_bucket, zone_id, zone_name, call_strike_type, put_strike_type,
    ce_symbol, ce_open, ce_high, ce_low, ce_close, 
    ce_volume, ce_oi, ce_coi, ce_iv, ce_delta, 
    ce_gamma, ce_theta, ce_vega, ce_rho,
    pe_symbol, pe_open, pe_high, pe_low, pe_close, 
    pe_volume, pe_oi, pe_coi, pe_iv, pe_delta, 
    pe_gamma, pe_theta, pe_vega, pe_rho
FROM noc_temp_step2;

DROP TABLE noc_temp_step1;
DROP TABLE noc_temp_step2;
```

### Phase 4: Daily Incremental Updates

The same approach can be used for daily updates, processing one day at a time:

```sql
-- Replace :TRADE_DATE with actual date
DROP TABLE IF EXISTS noc_inc_step1;

CREATE TABLE noc_inc_step1 AS
SELECT 
    g.trade_date,
    g.trade_time,
    g.expiry_date,
    g.underlying_price,
    g.strike,
    CASE 
        WHEN g.underlying_price < 10000 
        THEN ROUND(g.underlying_price / 50) * 50
        ELSE ROUND(g.underlying_price / 100) * 100
    END AS atm_strike,
    (SELECT COUNT(*) - 1
     FROM trading_calendar tc
     WHERE tc.cal_date BETWEEN g.trade_date AND g.expiry_date) AS dte,
    z.zone_id,
    z.zone_name,
    g.ce_symbol, g.ce_open, g.ce_high, g.ce_low, g.ce_close, 
    g.ce_volume, g.ce_oi, g.ce_coi, g.ce_iv, g.ce_delta, 
    g.ce_gamma, g.ce_theta, g.ce_vega, g.ce_rho,
    g.pe_symbol, g.pe_open, g.pe_high, g.pe_low, g.pe_close, 
    g.pe_volume, g.pe_oi, g.pe_coi, g.pe_iv, g.pe_delta, 
    g.pe_gamma, g.pe_theta, g.pe_vega, g.pe_rho
FROM nifty_greeks g
JOIN zone_def z ON g.trade_time BETWEEN z.start_t AND z.end_t
WHERE g.trade_date = ':TRADE_DATE'
  AND g.expiry_date > g.trade_date;

-- Repeat steps 2 and 3 as in the weekly template
```

## Validation and Monitoring

After each batch, run the validation script to verify data integrity:

```sql
SELECT expiry_bucket, COUNT(*) AS row_count,
       MIN(dte) min_dte, MAX(dte) max_dte
FROM nifty_option_chain_tbl
WHERE trade_date >= ':START_DATE' AND trade_date < DATE_ADD(DAY, 7, ':START_DATE')
GROUP BY expiry_bucket
ORDER BY expiry_bucket;
```

## Conclusion

This phased approach allows us to:

1. Avoid bulk data load issues
2. Validate each batch separately
3. Easily restart/recover if any batch fails
4. Minimize impact on database performance during loading
5. Maintain the full functionality of the desired CTAS table

This approach has been tested and validated on our sample data, with successful creation of the support tables and successful test runs on limited data volumes. 