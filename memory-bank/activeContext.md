# Active Context: Nifty Option Chain

## Current Goal (as of 2025-05-11 20:30)

Debug and resolve the backtesting pipeline's integration issues with HeavyDB.

**Recent Accomplishments / Discoveries (2025-05-11 20:30):**
*   **Backtest Running with Data Access**: The portfolio backtest (`NIF0DTE`) is successfully running and accessing data from HeavyDB. The SQL query fallbacks are functioning as expected.
*   **Database Connection**: Verified connections to HeavyDB are working properly, with data available for the specified time range (2023-01-02 to 2025-04-11).
*   **Excel Configuration**: Confirmed the Excel files (input_portfolio.xlsx and input_tbs_multi_legs.xlsx) are in the correct format. However, GeneralParameter sheet is missing an "Enabled" column, which is being handled by fallback logic.
*   **SQL Generation**: The fallback to per-leg SQL queries is working when union queries fail.

**Current Work Focus:**
*   Debugging why the backtest is still not producing output files despite successfully querying data.
*   Resolving permissions and configuration issues in the Excel files.

**Next Immediate Steps:**
1.  Create a copy of the strategy Excel file with proper permissions and add the missing "Enabled" column.
2.  Enable more detailed debug output to trace where the process is failing.
3.  Address lot size errors for NIFTY by updating the lotsize.csv file.

**Key Considerations / Blockers:**
*   File permissions preventing direct writing to Excel files.
*   Missing "Enabled" column in GeneralParameter sheet.
*   Lot size errors for NIFTY (defaulting to 1).
*   Union query failures forcing per-leg fallback, which is slower but functional.

**Open Questions for User:**
*   Should we proceed with optimizing the union queries or focus on fixing the missing lot sizes and Excel structure first?

---
*Previous context below this line for historical reference.*
---

## Current Goal (as of 2025-05-13)

Begin implementation of the HeavyDB GPU Backtester project, starting with **Phase 0: Model Scaffolding** as per the `enterprise_integration_plan.md`.

**Recent Accomplishments / Discoveries (2025-05-13):**
*   **Phase 0 Started**: Initiated Phase 0 - Model Scaffolding.
*   **Models Package Created**: Created the `models/` directory and `models/__init__.py` to establish it as a Python package. This is the first micro-task of Phase 0.

**Current Work Focus:**
*   Continuing with **Phase 0: Model Scaffolding**.
*   Next micro-task: Implement `models.common` with Enum stubs (`StrikeRule`, `ExpiryRule`, `OptionType`, `TransactionType`) and write pytest verifying Enum values.

**Next Immediate Steps:**
1.  Implement `models.common` enums.
2.  Create basic pytest tests for these enums.
3.  Update `memory-bank/progress.md` and the Phase 0 checklist in `enterprise_integration_plan.md`.

**Key Considerations / Blockers:**
*   None currently.

**Open Questions for User:**
*   None at this moment.

---
*Previous context below this line for historical reference.*
---

## Current Focus

The team is currently focused on the following key areas:

1. **Strike Classification Fix:** Addressing issues with strike type calculations including duplicated values and incorrect step size
2. Data loading stability and optimization
3. Option chain visualization and analysis capabilities

## Recent Changes

### Strike Classification Fix (Latest)

We identified and fixed two issues with the strike classification:

1. **Duplicated Values:** Some rows had duplicated classifications like "ATM,ATM1" or "ITM1,ITM1"
2. **Incorrect Step Size:** The code incorrectly used 100-point increments for Nifty values above 10,000, but Nifty always uses 50-point increments regardless of price level

Key components of the fix:
- Created a Python script `fix_moneyness.py` that processes all CSV files and fixes both issues
- Modified `classify_strike` function to always use 50-point increments for Nifty
- Implemented proper fix for any comma-separated classifications
- Full documentation in `memory-bank/strike_classification_fix.md`

This fix ensures all strike classifications follow the correct format and numbering:
- ATM (At The Money)
- ITM1, ITM2, etc. (In The Money with distance indication in 50-point increments)
- OTM1, OTM2, etc. (Out The Money with distance indication in 50-point increments)

For example, with an ATM strike of 17400:
- 17300 is ITM2 for calls (100 points = 2 * 50-point steps)
- 17500 is OTM2 for calls (100 points = 2 * 50-point steps)

### Data Loading Improvements

We've implemented several batch loading strategies in the SQL scripts, each optimized for different scenarios:

1. `batch_load.sql` - Full historical load
2. `fix_batch_load.sql` - Improved version with strike classification fix
3. `load_by_date.sh` - Daily load script

## Next Steps

1. **Apply strike classification fix to production data**
2. Enhance the loader to detect and prevent similar issues
3. Implement data quality validation checks
4. Continue performance optimization for query response

## Active Decisions

1. Use uniform 50-point increments for all Nifty strike classifications
2. Run data validation queries after each batch load to verify correct classification
3. Document the strike classification logic thoroughly
4. Use incremental updates to minimize processing time

## Implementation Plan

1. Deploy the strike classification fix
   - Run the `fix_moneyness.py` script on all data files
   - Verify the strike classifications with validation queries
2. Update the batch loading process
   - Implement the fixed classification logic in all data loaders
   - Add verification steps to confirm data quality
3. Establish automated data quality checks

## Current Goal (as of 2025-05-14)

Achieve full functional parity between the HeavyDB/GPU back-tester (`BTRunPortfolio_GPU.py`) and the legacy archive engine by closing the gaps identified in *Section 8* of `enterprise_integration_plan.md`.

**Recent Accomplishments / Discoveries (2025-05-14):**
*   Added Section 8 (Parity Analysis) to `enterprise_integration_plan.md`, enumerating replicated features, gaps, and recommended next steps.
*   Confirmed model-driven HeavyDB path is producing real trades; outputs validated for ATM strategies.
*   CLI worker fan-out and CPU-fallback are stable in multi-portfolio runs.

**Current Work Focus:**
*   Implement per-strategy reporting and margin calculation so Excel/JSON outputs match the archive format.
*   Extend strike/expiry rule mapping so non-ATM legs are parsed and executed correctly.

**Next Immediate Steps:**
1.  **Strategy grouping in results** – update `runtime.process_backtest_results` to fill `stgy_wise_transactionDf`, `stgyDayWiseStats`, `stgyMonthWiseStats`, and `stgyMarginPercentageWiseStats`.
2.  **Real margin requirements** – in `builders.parse_backtest_response`, compute `margin_req_for_each_stgy` using `Util.MARGIN_INFO` and pass it downstream.
3.  **Strike rule mapping** – complete `util_adapter._map_strike_rule` and integrate archive `Util.getStrikeValueAndMethod` logic (move into `excel_parser/strike_rules.py`).
4.  **Legacy niceties** – restore Merge-folder copy (`io.copy_files_to_merge_folder`) and Text-to-Speech for CLI parity.
5.  **Continue Phase 5 optimisation:** once the above are complete, implement UNION-ALL batch SQL and GPU memory telemetry.

**Key Considerations / Blockers:**
*   Accurate replication of archive margin logic is essential for correct CAGR/Sharpe calculations.
*   Strike rule enumeration must align with `models.common.StrikeRule` for query-builder compatibility.

**Open Questions for User:**
1.  Are the Merge-folder copy and Text-to-Speech features required for the first production drop, or can they wait until post-parity polish? 