# System Patterns: Nifty Option Chain

## System Architecture

The Nifty Option Chain system is structured around a layered architecture with the following components:

```
┌────────────────────────────────────────────────────────────┐
│                   Client Applications                       │
└───────────────────────────┬────────────────────────────────┘
                           │
┌───────────────────────────┴────────────────────────────────┐
│                   HeavyDB Query Interface                   │
└───────────────────────────┬────────────────────────────────┘
                           │
┌───────────────────────────┴────────────────────────────────┐
│               nifty_option_chain (Materialized View)        │
└───────────────────────────┬────────────────────────────────┘
                           │
┌───────────────────────┬───┴────┬───────────────────────────┐
│    Core Functions     │  CTEs  │     Refresh Mechanism     │
└───────────────────────┴────────┴───────────────────────────┘
                           │
┌───────────────────────────┴────────────────────────────────┐
│                    nifty_greeks (Base Table)                │
└────────────────────────────────────────────────────────────┘
```

## Key Technical Decisions

1. **Materialized View Pattern**: 
   - Using a materialized view rather than a regular view for performance optimization
   - Pre-computing complex calculations to avoid runtime overhead
   - Creating appropriate indexes to optimize query performance

2. **Function-Based Logic Separation**:
   - Encapsulating business logic in dedicated SQL functions
   - Using functions for ATM determination, strike classification, and strike increment calculation
   - Making functions reusable for other components in the system

3. **Common Table Expressions (CTEs)**:
   - Using CTEs to break down complex SQL into manageable, logical units
   - Organizing the processing pipeline into distinct transformation stages
   - Improving readability and maintainability of SQL code

4. **GPU Acceleration Strategy**:
   - Designing SQL operations to leverage HeavyDB's GPU capabilities
   - Using optimized data types and operations that map well to GPU execution
   - Minimizing CPU-bound operations where possible

5. **Refresh Mechanism**:
   - Implementing a dedicated refresh function for the materialized view
   - Supporting concurrent refresh to minimize downtime
   - Including fallback mechanisms for exceptional cases

## Design Patterns in Use

1. **Transform-Load Pattern**:
   - Transforming raw data into a structured format optimized for analysis
   - Pre-computing derived metrics during view materialization
   - Separating storage optimization from analytical optimization

2. **Calculated Column Pattern**:
   - Adding derived columns that summarize or transform base data
   - Computing values like ATM strike, moneyness classification, and PCR metrics
   - Enabling direct querying of business metrics without additional calculations

3. **Layered Functions Pattern**:
   - Building higher-level functions on top of lower-level functions
   - Creating a logical hierarchy of operations
   - Enabling reuse and consistent application of business rules

4. **Incremental Refresh Pattern**:
   - Supporting efficient updates to the materialized view
   - Optimizing refresh operations to minimize resource usage
   - Maintaining consistency between base data and derived view

## Component Relationships

1. **Core Functions**:
   - `find_atm_hybrid()`: Determines the ATM strike using synthetic futures with spot price fallback
   - `get_strike_increment()`: Calculates the appropriate strike increment based on index level
   - `classify_strikes()`: Categorizes options as ATM, ITM, or OTM based on strike distance

2. **CTEs in Materialized View**:
   - `date_expiry_pairs`: Extracts distinct date/expiry combinations
   - `atm_data`: Calculates ATM strikes and related metrics
   - `strike_classifications`: Classifies all strikes relative to their ATM
   - `pcr_calculations`: Computes Put-Call Ratio metrics

3. **Refresh Mechanism**:
   - `refresh_nifty_option_chain()`: Function to refresh the materialized view
   - Handles concurrent refreshes when possible
   - Maintains statistics for query optimization 

### HeavyDB GPU Package Layout (2025-05-10)
```
bt/backtester_stable/BTRUN/
├── models/               # pydantic dataclasses
├── excel_parser/         # sheet → model transformers
├── query_builder/        # SQL generation helpers
├── runtime/portfolio_runner.py  # orchestrator
└── heavydb_helpers.py    # executes SQL, returns trades
```
Patterns:
• Enum + dataclass for every business concept – StrikeRule, ExpiryRule, EntryWindow …
• Builder functions named build_* or resolve_* (snake_case).
• SIMPLE vs ADVANCED performance modes toggled by env variable. 

## System Patterns (Updated 2025-05-12)

### Data Flow & Processing

1.  **Input Layer (Excel):**
    *   Portfolio definitions reside in `input_portfolio.xlsx` (`PortfolioSetting` sheet).
    *   Strategy definitions also reside in `input_portfolio.xlsx` (`StrategySetting` sheet), which crucially contains `StrategyExcelFilePath` pointing to strategy-specific Excel files.
    *   Detailed strategy parameters (general and leg-specific) are in individual Excel files (e.g., `input_tbs_multi_legs.xlsx`), containing `GeneralParameter` and `LegParameter` sheets.
    *   Instrument lot sizes are defined in `lotsize.csv`.

2.  **Parsing & Transformation Layer (`BTRunPortfolio_GPU.py`, `Util.py`):
    *   `BTRunPortfolio_GPU.py` (`_row_build_request`): Orchestrates the reading of `input_portfolio.xlsx`.
    *   For each portfolio, it iterates through its associated strategies (from `StrategySetting`).
    *   For each strategy, it dynamically loads and parses the specific Excel file indicated by `StrategyExcelFilePath` by calling `Util.getStategyJson()`.
    *   `Util.py` (functions like `getStategyJson`, `getBackendStrategyJson`, `getLegJson`, `getBackendLegJson`): Contains the core logic to meticulously map all columns from `GeneralParameter` and `LegParameter` sheets into a structured Python dictionary (`bt_params`). This includes:
        *   Applying portfolio multipliers.
        *   Calculating final trade quantities using `config.LOT_SIZE` (populated from `lotsize.csv`).
        *   Transforming Excel string rules (for strikes, expiry, SL/TP, W&T, re-entry, hedging, events, indicators) into structured *semantic parameters*.
    *   The outcome is a rich `bt_params` dictionary where `portfolio.strategies[n].legs` are fully populated with these semantic rules, ready for HeavyDB processing.

3.  **HeavyDB Data Access & Trade Generation (`heavydb_helpers.py`, `query_builder/`):
    *   `heavydb_helpers.get_trades_for_portfolio()`: Consumes the `bt_params`.
    *   It iterates through strategies and their legs (now rich with semantic rules).
    *   For each leg, it instantiates a `models.leg.LegModel` object, populating it with the semantic strike rules (e.g., method, parameters), expiry rules, and timings from the parsed `leg_dict`.
    *   The `query_builder` modules (e.g., `entry_exit_sql.py`) take these `LegModel` instances.
    *   `query_builder` generates optimized HeavyDB SQL to:
        *   Select the correct option instrument (strike price) based on the `LegModel`'s semantic rules (e.g., ATM, Nth OTM based on delta, closest to premium) by querying the option chain view (e.g., `nifty_option_chain`).
        *   Fetch entry and exit tick/bar data for the selected instrument at the specified times/conditions.
    *   `heavydb_helpers.execute_query()` runs the SQL against HeavyDB.
    *   `trade_builder.py` (or logic within `heavydb_helpers`) assembles the fetched entry/exit data into `TradeRecord` objects.

4.  **Output & Analytics Layer (`builders.py`, `stats.py`, `io.py`):
    *   The list of `TradeRecord` objects is processed by `builders.parse_backtest_response()` (legacy path).
    *   `stats.py` calculates performance metrics.
    *   `io.py` handles writing results to Excel and JSON formats.

### Key Design Patterns

*   **Configuration-Driven Backtesting:** Strategy parameters are externalized to Excel files, allowing changes without code modification.
*   **Layered Architecture:** Separation of concerns (parsing, data access, trade logic, output).
*   **Semantic Rule Processing:** Excel inputs are first parsed into intermediate semantic rules, which are then translated into specific HeavyDB queries. This decouples input format from database query specifics.
*   **Optimized Data Access:** Use of HeavyDB views and targeted SQL queries for efficient data retrieval.
*   **GPU Acceleration:** Leveraged where possible, primarily in data retrieval (HeavyDB) and potentially in DataFrame manipulations (cuDF via `gpu_helpers.py`).
*   **Modular SQL Generation:** The `query_builder` package constructs SQL dynamically based on strategy parameters, promoting reusability and maintainability.

### Error Handling & Logging

*   Comprehensive logging at each stage of processing.
*   Graceful handling of file-not-found errors for Excel inputs.
*   Validation of critical parameters during parsing.
*   Retry mechanisms or CPU fallback for GPU-specific operations (planned for parallel execution phase). 