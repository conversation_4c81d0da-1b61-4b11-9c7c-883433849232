# NIFTY OPTION CHAIN - CTAS Implementation Plan
*Last updated: 2024-05-15*

## Overview
Transform the Regular view `nifty_option_chain` into a physical CTAS table with:
- Four expiry buckets (CW/NW/CM/NM)
- Trading-day DTE calculation
- Configurable intraday zone labeling
- Optimized GPU-friendly storage

## Implementation Status (2024-05-15)
| Component | Status | Notes |
|-----------|--------|-------|
| Support Objects | |
| - Trading Calendar | ✅ Completed | 2019-2026 trading days, excluding weekends and NSE holidays |
| - Zone Definition | ✅ Completed | 5 intraday zones defined (OPEN, MID_MORN, LUNCH, AFTERNOON, CLOSE) |
| - Expiry Flags | ✅ Completed | View to identify monthly expiry dates (Thursdays) |
| Main Table | |
| - Table Schema | ✅ Completed | All columns defined with appropriate types and encoding |
| - Data Loading | 🔄 In Progress | Working through HeavyDB SQL dialect challenges |
| - Complex Expiry Assignment | 🔄 In Progress | Simplifying expiry bucket logic for compatibility |
| - Strike Classification | 🔄 In Progress | ATM/ITM/OTM calculation framework ready |
| - DTE Calculation | ✅ Completed | Trading-day calculation using calendar table |
| Testing & Validation | ⏳ Pending | |
| Cut-over | ⏳ Pending | |

### Current Challenges
- Adapting complex SQL queries to HeavyDB's specific SQL dialect
- Handling subqueries with GROUP BY/HAVING clauses
- Managing large data volume during initial load
- Optimizing the expiry bucket determination logic

### Next Steps
- Complete the simplified data loading test
- Incrementally add complexity to SQL
- Verify correct expiry bucket assignment
- Validate ATM determination and strike classification
- Complete full table population

## Phase 0: Prerequisites & Assumptions
- HeavyDB running with admin access
- Source tables `nifty_greeks` and `nse_holidays` exist
- SQL execution via `/opt/heavyai/bin/heavysql -q <file.sql>`

## Phase 1: Support Objects
### 1-1 Trading Calendar
```sql
DROP TABLE IF EXISTS trading_calendar;
CREATE TABLE trading_calendar AS
SELECT cal_date
FROM (
      SELECT add_days('2019-01-01', seq4()) AS cal_date
      FROM table(generate_series(0, 8 * 366))  -- covers 2019-2026
) x
WHERE DATE_PART(DOW, cal_date) NOT IN (0,6)    -- Mon-Fri only
  AND cal_date NOT IN (SELECT holiday_date FROM nse_holidays);
```

### 1-2 Zone Definition
```sql
DROP TABLE IF EXISTS zone_def;
CREATE TABLE zone_def (
   zone_id   SMALLINT,
   zone_name TEXT,
   start_t   TIME,
   end_t     TIME
);
INSERT INTO zone_def VALUES
 (1,'OPEN'      ,'09:15:00','10:30:00'),
 (2,'MID_MORN'  ,'10:30:01','12:00:00'),
 (3,'LUNCH'     ,'12:00:01','13:30:00'),
 (4,'AFTERNOON' ,'13:30:01','15:00:00'),
 (5,'CLOSE'     ,'15:00:01','15:30:00');
```

### 1-3 Monthly Expiry Flag View
```sql
CREATE OR REPLACE VIEW expiry_flags AS
WITH monthly_candidates AS (
    SELECT DISTINCT
           index_name,
           expiry_date,
           DATE_PART(YEAR, expiry_date) AS exp_year,
           DATE_PART(MONTH, expiry_date) AS exp_month,
           -- Find last Thursday (or earlier if holiday)
           CASE WHEN expiry_date = (
                SELECT MAX(e2.expiry_date)
                FROM nifty_greeks e2
                WHERE e2.index_name = nifty_greeks.index_name
                  AND DATE_PART(YEAR, e2.expiry_date) = DATE_PART(YEAR, nifty_greeks.expiry_date)
                  AND DATE_PART(MONTH, e2.expiry_date) = DATE_PART(MONTH, nifty_greeks.expiry_date)
                  AND DATE_PART(DOW, e2.expiry_date) = 4  -- Thursday
                  AND e2.expiry_date NOT IN (SELECT holiday_date FROM nse_holidays)
           ) THEN 1
           ELSE 0
           END AS is_monthly
    FROM nifty_greeks
)
SELECT index_name, expiry_date, is_monthly
FROM monthly_candidates;
```

## Phase 2: Physical Table Schema
```sql
DROP TABLE IF EXISTS nifty_option_chain_tbl;

CREATE TABLE nifty_option_chain_tbl (
   -- Key columns
   trade_date          DATE,
   trade_time          TIME,
   expiry_date         DATE,
   index_name          TEXT ENCODING DICT,
   underlying_price    DOUBLE,
   atm_strike          DOUBLE,
   strike              DOUBLE,
   dte                 INT,                    -- trading-day DTE
   expiry_bucket       TEXT ENCODING DICT,     -- CW|NW|CM|NM
   zone_id             SMALLINT,
   zone_name           TEXT ENCODING DICT,
   
   -- Classification
   call_strike_type    TEXT ENCODING DICT,
   put_strike_type     TEXT ENCODING DICT,
   
   -- Call metrics
   ce_symbol TEXT ENCODING DICT,  ce_open DOUBLE,  ce_high DOUBLE,
   ce_low DOUBLE,                 ce_close DOUBLE,
   ce_volume BIGINT,              ce_oi BIGINT,    ce_coi BIGINT,
   ce_iv DOUBLE,                  ce_delta DOUBLE, ce_gamma DOUBLE,
   ce_theta DOUBLE,               ce_vega DOUBLE,  ce_rho DOUBLE,
   
   -- Put metrics
   pe_symbol TEXT ENCODING DICT,  pe_open DOUBLE,  pe_high DOUBLE,
   pe_low DOUBLE,                 pe_close DOUBLE,
   pe_volume BIGINT,              pe_oi BIGINT,    pe_coi BIGINT,
   pe_iv DOUBLE,                  pe_delta DOUBLE, pe_gamma DOUBLE,
   pe_theta DOUBLE,               pe_vega DOUBLE,  pe_rho DOUBLE
)
WITH (fragment_size = 32000000);
```

## Phase 3: Historical Back-fill
```sql
INSERT INTO nifty_option_chain_tbl
WITH src AS (
    /* replicate view logic for atm_strike & strike_type */
    SELECT g.*, atm.atm_strike, st.call_strike_type, st.put_strike_type
    FROM nifty_greeks g
    /* existing CTEs/joins */
)
, flags AS (
    SELECT s.*,
           ef.is_monthly,
           ROW_NUMBER() OVER (PARTITION BY s.index_name, s.trade_date
                             ORDER BY s.expiry_date) AS exp_rank,
           ROW_NUMBER() OVER (PARTITION BY s.index_name, s.trade_date
                             ORDER BY CASE WHEN ef.is_monthly=1 THEN 0 END,
                                      s.expiry_date) AS monthly_rank
    FROM src s
    JOIN expiry_flags ef
      ON s.index_name = ef.index_name
     AND s.expiry_date = ef.expiry_date
)
, pick AS (
    -- Current Week
    SELECT f.*, 'CW' AS expiry_bucket FROM flags f WHERE exp_rank = 1
    UNION ALL
    -- Next Week
    SELECT f.*, 'NW' FROM flags f WHERE exp_rank = 2
    UNION ALL
    -- Current Month
    SELECT f.*, 'CM' FROM flags f 
    WHERE is_monthly = 1 AND monthly_rank = 1
    UNION ALL
    -- Next Month
    SELECT f.*, 'NM' FROM flags f
    WHERE is_monthly = 1 AND monthly_rank = 2
)
, dte_calc AS (
    SELECT p.*,
           (SELECT COUNT(*)-1
            FROM trading_calendar tc
            WHERE tc.cal_date BETWEEN p.trade_date AND p.expiry_date) AS dte
    FROM pick p
)
, zoned AS (
    SELECT d.*,
           z.zone_id,
           z.zone_name
    FROM dte_calc d
    JOIN zone_def z ON d.trade_time BETWEEN z.start_t AND z.end_t
)
SELECT * FROM zoned;
```

## Phase 4: Nightly ETL
### 4-1 Incremental Append
```sql
INSERT INTO nifty_option_chain_tbl
WITH src AS (
    /* same CTEs but with WHERE g.trade_date = :yesterday */
)
SELECT * FROM zoned;
```

### 4-2 Maintenance
- Schedule after `nifty_greeks` ingest
- Weekly `VACUUM TABLE nifty_option_chain_tbl;`

## Phase 5: Validation & Cut-over
1. Sample date validation:
```sql
SELECT expiry_bucket, COUNT(*), MIN(dte), MAX(dte)
FROM nifty_option_chain_tbl
WHERE trade_date = '2024-05-15'
GROUP BY expiry_bucket
ORDER BY expiry_bucket;
```

2. Spot-check:
- DTE calculation
- Zone assignment
- Monthly expiry selection (especially around holidays)

3. Cut-over:
```sql
ALTER TABLE nifty_greeks RENAME TO nifty_greeks_raw_backup;
DROP VIEW IF EXISTS nifty_option_chain;
-- After retention: DROP TABLE nifty_greeks_raw_backup;
```

## Phase 6: Maintenance Operations
### 6-1 Update Zone Boundaries
```sql
UPDATE zone_def 
SET start_t = '10:00:00', end_t = '11:30:00' 
WHERE zone_name = 'MID_MORN';

UPDATE nifty_option_chain_tbl t
SET   zone_id = z.zone_id,
      zone_name = z.zone_name
FROM  zone_def z
WHERE t.trade_time BETWEEN z.start_t AND z.end_t;
```

### 6-2 Add New Columns
```sql
ALTER TABLE nifty_option_chain_tbl 
ADD COLUMN ce_vanna DOUBLE;
```

## Implementation Files
1. `build_support_objs.sql` - Phase 1
2. `build_noc_table.sql` - Phases 2 & 3
3. `append_noc_incremental.sql` - Phase 4 template

## Performance Notes
- Fragment size (32M) optimized for ≥40GB GPUs
- Dictionary encoding minimizes string storage
- No secondary indexes needed; rely on fragment pruning
- Always filter queries on `trade_date` and `expiry_bucket` 

### 🚀 Rolling Synthetic ATM Logic (per trade_time)

We replace the previous date-level ATM selection with an *intraday rolling* calculation.  For **each** combination `(trade_date, trade_time, expiry_date, index_name)`:

1.  Look at all strikes trading at that timestamp.
2.  If *both* CE and PE have valid prices for a strike, compute
    `|CE – PE|`.
3.  Choose the strike with the **smallest** `|CE – PE|` as
   `atm_strike` (`atm_method='SYNTHETIC_TICK'`).
4.  If no strike has a valid CE/PE pair, fall back to the rounded-spot
    strike (`atm_method='SPOT_TICK'`).
5.  Moneyness (`ITMᵢ` / `OTMᵢ`) is then computed against that rolling
   `atm_strike` for every strike row at the same timestamp.

SQL skeleton (drop-in for *src* CTE in both scripts):
```sql
WITH src AS (
  /* 1️⃣ Synthetic ATM per tick */
  WITH ranked AS (
      SELECT g.*,
             ABS(g.ce_close - g.pe_close)                AS price_diff,
             ROW_NUMBER() OVER (
                 PARTITION BY g.trade_date, g.trade_time,
                              g.expiry_date, g.index_name
                 ORDER BY ABS(g.ce_close - g.pe_close)
             )                                           AS diff_rank
      FROM   nifty_greeks g
      WHERE  g.ce_close > 0 AND g.pe_close > 0          -- valid pairs
  ),
  atm_tick AS (
      SELECT trade_date, trade_time, expiry_date, index_name,
             strike           AS atm_strike,
             underlying_price,
             'SYNTHETIC_TICK' AS atm_method
      FROM   ranked r
      WHERE  r.diff_rank = 1                             -- best pair

      UNION ALL
      -- Fallback rows where no synthetic pair exists
      SELECT g.trade_date, g.trade_time, g.expiry_date, g.index_name,
             CASE WHEN g.underlying_price < 10000
                  THEN ROUND(g.underlying_price/50)*50
                  ELSE ROUND(g.underlying_price/100)*100
             END                  AS atm_strike,
             g.underlying_price,
             'SPOT_TICK'          AS atm_method
      FROM   nifty_greeks g
      WHERE  NOT EXISTS (
               SELECT 1 FROM ranked r
               WHERE  r.trade_date = g.trade_date
                 AND  r.trade_time = g.trade_time
                 AND  r.expiry_date = g.expiry_date
                 AND  r.index_name  = g.index_name
             )
      GROUP BY 1,2,3,4,5,6
  )
  /* 2️⃣ Attach rolling ATM to every option row */
  SELECT g.*, a.atm_strike, a.atm_method,
         CASE
             WHEN g.strike = a.atm_strike THEN 'ATM'
             WHEN g.strike < a.atm_strike THEN 'ITM' || CAST(CEIL(ABS(g.strike-a.atm_strike)/
                     CASE WHEN a.underlying_price<10000 THEN 50 ELSE 100 END) AS TEXT)
             ELSE 'OTM' || CAST(CEIL(ABS(g.strike-a.atm_strike)/
                     CASE WHEN a.underlying_price<10000 THEN 50 ELSE 100 END) AS TEXT)
         END AS call_strike_type,
         CASE
             WHEN g.strike = a.atm_strike THEN 'ATM'
             WHEN g.strike > a.atm_strike THEN 'ITM' || CAST(CEIL(ABS(g.strike-a.atm_strike)/
                     CASE WHEN a.underlying_price<10000 THEN 50 ELSE 100 END) AS TEXT)
             ELSE 'OTM' || CAST(CEIL(ABS(g.strike-a.atm_strike)/
                     CASE WHEN a.underlying_price<10000 THEN 50 ELSE 100 END) AS TEXT)
         END AS put_strike_type
  FROM   nifty_greeks g
  JOIN   atm_tick a USING (trade_date, trade_time, expiry_date, index_name)
)
```

**Validation metric**:  compare `atm_method` distribution; typically ≥90 % of ticks should use `SYNTHETIC_TICK` when the market is liquid. 