# Nifty Option-Chain – Historical Data Load (2025-05-17)

_This document records the **exact, reproducible steps** used to ingest the
28 historical CSV files (≈4.4 GB) into the HeavyDB table
`nifty_option_chain` on gpu-server3-A100.  Keep it under version
control – every future load or audit can be traced back to this recipe._

---
## 1. Environment
* **Server**: `gpu-server3-A100`  
  CentOS 8 ‑ kernel 5.15  
  HeavyDB 6.x installed under `/opt/heavyai` (GPU-mode)
* **DB connection**  
  `admin / HyperInteractive`  
  Host `127.0.0.1` Port `6274` Database `heavyai`
* **CSV source dir**  
  `/srv/samba/shared/market_data/nifty/oc_with_futures/`  
  28 files, each `IV_<YYYY>_<mon>_nifty_futures.csv`
* **Import dir** (HeavyDB whitelist)  
  `/var/lib/heavyai/storage/import/`

---
## 2. One-time file sync
HeavyDB only ingests files that _physically live_ inside its import tree.
We copy _once_ (idempotent):
```bash
sudo rsync -av --ignore-existing \
     /srv/samba/shared/market_data/nifty/oc_with_futures/ \
     /var/lib/heavyai/storage/import/
```
Outcome: 28 files → import dir, ~3 GB actually copied (hard-linked files were
skipped thanks to `--ignore-existing`).

---
## 3. Loader script (single-stage COPY)
`load_with_proven_method.py` _commit 9b7f12e_ – key behaviour:
1. Re-creates `nifty_option_chain` (48 cols, schema == `nifty_option_chain_shema.md`).
2. For **each** CSV:
   * `sudo cp` to import dir _if_ not present.
   * `COPY nifty_option_chain FROM '<file>' WITH (header='true', delimiter=',');`
   * Parses HeavyDB response _"Loaded: <n> recs"_ → per-file count.
3. Logs to `nifty_option_chain_load.log`, prints progress/ETA.

Command executed (08:57:23 IST):
```bash
python3 load_with_proven_method.py \
        --csv-dir /srv/samba/shared/market_data/nifty/oc_with_futures \
        --recreate-table | tee full_load.log
```

---
## 4. Run statistics
| # | File | Rows loaded |
|---|------|-------------|
| 1 | IV_2023_june_nifty_futures.csv | 291 324 |
| 2 | IV_2023_feb_nifty_futures.csv  | 357 510 |
| 3 | IV_2023_oct_nifty_futures.csv  | 417 952 |
| 4 | IV_2025_apr_nifty_futures.csv  | 182 375 |
| 5 | IV_2024_jan_nifty_futures.csv  | 506 882 |
| 6 | IV_2023_jan_nifty_futures.csv  | 370 861 |
| 7 | IV_2023_may_nifty_futures.csv  | 373 151 |
| 8 | IV_2023_aug_nifty_futures.csv  | 447 168 |
| 9 | IV_2024_feb_nifty_futures.csv  | 492 616 |
|10 | IV_2025_feb_nifty_futures.csv  | 508 928 |
|11 | IV_2025_jan_nifty_futures.csv  | 604 332 |
|12 | IV_2023_july_nifty_futures.csv | 460 477 |
|13 | IV_2023_apr_nifty_futures.csv  | 357 478 |
|14 | IV_2024_june_nifty_futures.csv | 574 831 |
|15 | IV_2023_mar_nifty_futures.csv  | 378 002 |
|16 | IV_2024_july_nifty_futures.csv | 552 569 |
|17 | IV_2023_nov_nifty_futures.csv  | 406 875 |
|18 | IV_2024_nov_nifty_futures.csv  | 544 258 |
|19 | IV_2025_mar_nifty_futures.csv  | 473 852 |
|20 | IV_2024_apr_nifty_futures.csv  | 471 416 |
|21 | IV_2024_dec_nifty_futures.csv  | 596 838 |
|22 | IV_2023_dec_nifty_futures.csv  | 518 939 |
|23 | IV_2024_aug_nifty_futures.csv  | 551 787 |
|24 | IV_2024_oct_nifty_futures.csv  | 603 959 |
|25 | IV_2024_mar_nifty_futures.csv  | 437 629 |
|26 | IV_2024_sep_nifty_futures.csv  | 541 631 |
|27 | IV_2023_sep_nifty_futures.csv  | 437 272 |
|28 | IV_2024_may_nifty_futures.csv  | 541 209 |
|   | **TOTAL** | **13 002 121** |

Performance: **23.4 s** wall-time → **556 k rows/s** sustained.

---
## 5. Integrity verification
1. **Database count:**
   ```sql
   SELECT COUNT(*) FROM nifty_option_chain;  -- 13 002 121
   ```
2. **CSV sum:** awk/`wc -l` over the 28 input files = **13 002 121** (header
   lines excluded).  Perfect match ✅
3. **Spot sample:**
   ```sql
   SELECT *
   FROM   nifty_option_chain
   WHERE  trade_date = '2023-06-01'
     AND  trade_time = '09:15:00'
     AND  strike = 17950
     AND  expiry_date = '2023-06-01';
   -- returns expected row matching CSV #1
   ```

---
## 6. Post-load tasks
* If still using the materialised-view layer:
  ```sql
  REFRESH MATERIALIZED VIEW nifty_option_chain;
  ```
* Grant read-only permissions if required:
  ```sql
  GRANT SELECT ON TABLE nifty_option_chain TO analyst;
  ```

---
## 7. Incremental daily loads
For next-day files just re-run
```bash
python3 load_with_proven_method.py --csv-dir <dir>
```
(without `--recreate-table`).  COPY will append new rows; duplicates are not
automatically de-duped – ensure source ETL does not regenerate historical
files.

---
## 8. Troubleshooting
* **File not whitelisted** – ensure it's in `/var/lib/heavyai/storage/import/`.
* **Loaded 0 recs** – Wrong delimiter or broken header.
* **Dictionary overflow** – enlarge string dictionary with `ENCODING NONE`.
* **Row limit on SELECT** – always filter on `trade_date` / `expiry_date` in
  analytics queries (see HeavyDB doc).

---
_End of document_ 