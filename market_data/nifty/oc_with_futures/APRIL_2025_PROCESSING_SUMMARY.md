# April 2025 Nifty Data Processing Summary

## Processing Date
- **Executed**: May 28, 2025 at 10:54:26
- **Completed**: May 28, 2025 at 11:02:17
- **Processing Time**: ~8 minutes

## Input Files
- **Option Data**: `/srv/samba/shared/market_data/nifty/nifty_file.csv`
- **Futures Data**: `/srv/samba/shared/market_data/nifty/nifty_future.csv`

## Output File
- **Location**: `/srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_updated.csv`
- **Size**: 249 MB
- **Format**: 48 columns in standardized format

## Data Coverage
- **Date Range**: April 15, 2025 to May 26, 2025
- **Total Rows**: 807,003
- **Unique Trading Days**: 28
- **Unique Strikes**: 137

## Futures Data Availability
- **Available**: April 15 to May 12, 2025
- **Missing**: May 13 to May 26, 2025 (futures columns set to NaN for these dates)

## Expiry Bucket Distribution
- **CW (Current Week)**: 335,821 rows
- **NW (Next Week)**: 244,465 rows
- **CM (Current Month)**: 192,708 rows
- **NM (Next Month)**: 34,009 rows

## Time Zone Distribution
- **OPEN (09:15-10:30)**: 187,522 rows
- **MID_MORN (10:30-12:00)**: 186,632 rows
- **AFTERNOON (13:30-15:00)**: 186,337 rows
- **LUNCH (12:00-13:30)**: 174,953 rows
- **CLOSE (15:00-15:30)**: 71,559 rows

## Key Transformations Applied

### 1. Date/Time Formatting
- Converted date from YYMMDD to YYYY-MM-DD format
- Converted time to HH:MM:SS format

### 2. Column Mapping
- Renamed columns to standardized lowercase format (e.g., CE_open → ce_open)
- Added index_name = 'NIFTY' to all rows

### 3. ATM Strike Calculation
- Used synthetic future method: `strike + ce_close - pe_close`
- Calculated for each unique (trade_date, trade_time, expiry_date) group
- Fallback to rounded spot/50 when no valid CE/PE pairs

### 4. DTE Calculation
- Counted trading days from trade_date (exclusive) to expiry_date (inclusive)
- Excluded weekends and NSE holidays

### 5. Strike Classification
- **Calls**: ITM if strike < ATM, OTM if strike > ATM
- **Puts**: ITM if strike > ATM, OTM if strike < ATM
- Distance measured in 50-point increments

### 6. Futures Data Join
- Matched by date and nearest earlier time
- Used earliest futures row if no earlier time available

## NSE Holidays Considered (2025)
- April 10 (Thursday)
- April 14 (Monday)
- April 18 (Friday)
- May 1 (Thursday)

## Script Used
- **Location**: `/srv/samba/shared/scripts/process_april_2025_nifty.py`
- **Command**: `python3 scripts/process_april_2025_nifty.py`

## Data Sorting (May 28, 2025 at 11:20)

### Sorted Output File
- **Location**: `/srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_sorted.csv`
- **Size**: 249 MB (same as unsorted)
- **Rows**: 807,003 (verified no data loss)

### Sort Order Applied
1. **trade_date** (ascending)
2. **trade_time** (ascending)
3. **expiry_bucket** (custom order: CW → NW → CM → NM)
4. **strike** (ascending)

### Sorting Script
- **Location**: `/srv/samba/shared/scripts/sort_april_2025_data.py`
- **Command**: `python3 scripts/sort_april_2025_data.py`
- **Execution Time**: ~20 seconds

### Verification
- ✓ Trade dates are in ascending chronological order
- ✓ Trade times are sorted within each date
- ✓ Expiry buckets follow the order: CW, NW, CM, NM
- ✓ Strikes are sorted in ascending order within each bucket

## Notes
- Data from May 13 onwards has no corresponding futures data
- All calculations and transformations completed successfully
- Output format matches the standard 48-column structure used in other processed files
- Final sorted file ready for loading into databases or analytical tools 