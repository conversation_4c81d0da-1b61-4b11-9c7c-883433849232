#!/usr/bin/env python3
"""
Enhanced OI Input Sheet Template Creator

This script creates comprehensive Excel templates for the enhanced OI system with dynamic weightage functionality.
It generates two main input files:
1. input_enhanced_oi_config.xlsx - Comprehensive OI strategy configuration
2. input_dynamic_weightage_oi.xlsx - Dynamic weightage parameters and factor configuration

The templates include:
- Detailed parameter structures based on TBS enhancement patterns
- Dynamic weightage configuration inspired by market regime optimizer
- Backward compatibility with existing input_maxoi.xlsx format
- Comprehensive validation and default values
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
import os

def create_enhanced_oi_config_template():
    """Create the enhanced OI configuration template with comprehensive parameters"""
    
    # GeneralParameter Sheet - 45 comprehensive parameters
    general_params = pd.DataFrame([
        {
            # Core Strategy Parameters (15)
            'StrategyName': 'ENHANCED_OI_MAXOI_1',
            'Underlying': 'SPOT',
            'Index': 'NIFTY',
            'DTE': 0,
            'Timeframe': 3,
            'StartTime': '091600',
            'EndTime': '152000',
            'LastEntryTime': '150000',
            'StrikeSelectionTime': '091530',
            'MaxOpenPositions': 2,
            'OiThreshold': 800000,
            'StrikeCount': 10,
            'Weekdays': '1,2,3,4,5',
            'StrategyProfit': 0,
            'StrategyLoss': 0,
            
            # OI-Specific Parameters (15)
            'OiMethod': 'MAXOI_1',
            'CoiBasedOn': 'YESTERDAY_CLOSE',
            'OiRecheckInterval': 180,  # seconds
            'OiThresholdType': 'ABSOLUTE',  # ABSOLUTE, PERCENTAGE, PERCENTILE
            'OiConcentrationThreshold': 0.3,  # 30% concentration threshold
            'OiDistributionAnalysis': 'YES',
            'StrikeRangeType': 'FIXED',  # FIXED, DYNAMIC, ATM_BASED
            'StrikeRangeValue': 10,
            'OiMomentumPeriod': 5,  # periods for momentum calculation
            'OiTrendAnalysis': 'YES',
            'OiSeasonalAdjustment': 'NO',
            'OiVolumeCorrelation': 'YES',
            'OiLiquidityFilter': 'YES',
            'OiAnomalyDetection': 'YES',
            'OiSignalConfirmation': 'YES',
            
            # Dynamic Weightage Parameters (15)
            'EnableDynamicWeights': 'YES',
            'WeightAdjustmentPeriod': 20,  # periods
            'LearningRate': 0.01,
            'PerformanceWindow': 100,  # periods for performance calculation
            'MinWeight': 0.05,
            'MaxWeight': 0.50,
            'WeightDecayFactor': 0.95,
            'CorrelationThreshold': 0.7,
            'DiversificationBonus': 1.1,
            'RegimeAdjustment': 'YES',
            'VolatilityAdjustment': 'YES',
            'LiquidityAdjustment': 'YES',
            'TrendAdjustment': 'YES',
            'MomentumAdjustment': 'YES',
            'SeasonalAdjustment': 'NO'
        },
        {
            # Second strategy example with different parameters
            'StrategyName': 'ENHANCED_OI_MAXCOI_2',
            'Underlying': 'SPOT',
            'Index': 'NIFTY',
            'DTE': 0,
            'Timeframe': 6,
            'StartTime': '092000',
            'EndTime': '151500',
            'LastEntryTime': '143000',
            'StrikeSelectionTime': '091530',
            'MaxOpenPositions': 3,
            'OiThreshold': 1000000,
            'StrikeCount': 15,
            'Weekdays': '1,2,3,4,5',
            'StrategyProfit': 10000,
            'StrategyLoss': -5000,
            
            # OI-Specific Parameters
            'OiMethod': 'MAXCOI_2',
            'CoiBasedOn': 'TODAY_OPEN',
            'OiRecheckInterval': 300,
            'OiThresholdType': 'PERCENTILE',
            'OiConcentrationThreshold': 0.25,
            'OiDistributionAnalysis': 'YES',
            'StrikeRangeType': 'DYNAMIC',
            'StrikeRangeValue': 12,
            'OiMomentumPeriod': 10,
            'OiTrendAnalysis': 'YES',
            'OiSeasonalAdjustment': 'YES',
            'OiVolumeCorrelation': 'YES',
            'OiLiquidityFilter': 'YES',
            'OiAnomalyDetection': 'YES',
            'OiSignalConfirmation': 'YES',
            
            # Dynamic Weightage Parameters
            'EnableDynamicWeights': 'YES',
            'WeightAdjustmentPeriod': 15,
            'LearningRate': 0.015,
            'PerformanceWindow': 80,
            'MinWeight': 0.03,
            'MaxWeight': 0.60,
            'WeightDecayFactor': 0.92,
            'CorrelationThreshold': 0.65,
            'DiversificationBonus': 1.15,
            'RegimeAdjustment': 'YES',
            'VolatilityAdjustment': 'YES',
            'LiquidityAdjustment': 'YES',
            'TrendAdjustment': 'YES',
            'MomentumAdjustment': 'YES',
            'SeasonalAdjustment': 'YES'
        }
    ])
    
    # LegParameter Sheet - 35 comprehensive parameters
    leg_params = pd.DataFrame([
        {
            # Basic Leg Parameters (15)
            'StrategyName': 'ENHANCED_OI_MAXOI_1',
            'LegID': 'CE_LEG_1',
            'Instrument': 'CE',
            'Transaction': 'SELL',
            'Expiry': 'current',
            'StrikeMethod': 'MAXOI_1',
            'StrikeValue': 0,
            'Lots': 1,
            'SLType': 'percentage',
            'SLValue': 30,
            'TGTType': 'percentage',
            'TGTValue': 50,
            'TrailSLType': 'percentage',
            'SL_TrailAt': 25,
            'SL_TrailBy': 10,
            
            # OI-Specific Leg Parameters (10)
            'LegOiThreshold': 800000,
            'LegOiWeight': 0.4,
            'LegCoiWeight': 0.3,
            'LegOiRank': 1,
            'LegOiConcentration': 0.3,
            'LegOiMomentum': 0.2,
            'LegOiTrend': 0.15,
            'LegOiLiquidity': 0.1,
            'LegOiAnomaly': 0.05,
            'LegOiConfirmation': 'YES',
            
            # Greek-Based Parameters (10)
            'DeltaWeight': 0.25,
            'GammaWeight': 0.20,
            'ThetaWeight': 0.15,
            'VegaWeight': 0.10,
            'DeltaThreshold': 0.5,
            'GammaThreshold': 0.1,
            'ThetaThreshold': -0.05,
            'VegaThreshold': 0.2,
            'GreekRebalanceFreq': 300,  # seconds
            'GreekRiskLimit': 10000
        },
        {
            'StrategyName': 'ENHANCED_OI_MAXOI_1',
            'LegID': 'PE_LEG_1',
            'Instrument': 'PE',
            'Transaction': 'SELL',
            'Expiry': 'current',
            'StrikeMethod': 'MAXOI_1',
            'StrikeValue': 0,
            'Lots': 1,
            'SLType': 'percentage',
            'SLValue': 30,
            'TGTType': 'percentage',
            'TGTValue': 50,
            'TrailSLType': 'percentage',
            'SL_TrailAt': 25,
            'SL_TrailBy': 10,
            
            # OI-Specific Leg Parameters
            'LegOiThreshold': 800000,
            'LegOiWeight': 0.4,
            'LegCoiWeight': 0.3,
            'LegOiRank': 1,
            'LegOiConcentration': 0.3,
            'LegOiMomentum': 0.2,
            'LegOiTrend': 0.15,
            'LegOiLiquidity': 0.1,
            'LegOiAnomaly': 0.05,
            'LegOiConfirmation': 'YES',
            
            # Greek-Based Parameters
            'DeltaWeight': 0.25,
            'GammaWeight': 0.20,
            'ThetaWeight': 0.15,
            'VegaWeight': 0.10,
            'DeltaThreshold': -0.5,
            'GammaThreshold': 0.1,
            'ThetaThreshold': -0.05,
            'VegaThreshold': 0.2,
            'GreekRebalanceFreq': 300,
            'GreekRiskLimit': 10000
        }
    ])
    
    return general_params, leg_params

def create_dynamic_weightage_template():
    """Create the dynamic weightage configuration template"""
    
    # WeightConfiguration Sheet - 25 parameters for weight management
    weight_config = pd.DataFrame([
        {
            # Base Factor Weights (5)
            'OiFactorWeight': 0.35,
            'CoiFactorWeight': 0.25,
            'GreekFactorWeight': 0.20,
            'MarketFactorWeight': 0.15,
            'PerformanceFactorWeight': 0.05,
            
            # OI Sub-Factor Weights (8)
            'CurrentOiWeight': 0.30,
            'OiConcentrationWeight': 0.20,
            'OiDistributionWeight': 0.15,
            'OiMomentumWeight': 0.15,
            'OiTrendWeight': 0.10,
            'OiSeasonalWeight': 0.05,
            'OiLiquidityWeight': 0.03,
            'OiAnomalyWeight': 0.02,
            
            # Adjustment Parameters (12)
            'WeightLearningRate': 0.01,
            'WeightDecayFactor': 0.95,
            'WeightSmoothingFactor': 0.15,
            'MinFactorWeight': 0.05,
            'MaxFactorWeight': 0.50,
            'WeightRebalanceFreq': 300,  # seconds
            'PerformanceThreshold': 0.6,
            'CorrelationThreshold': 0.7,
            'DiversificationBonus': 1.1,
            'VolatilityAdjustment': 1.15,
            'TrendAdjustment': 1.1,
            'RegimeAdjustment': 1.2
        }
    ])
    
    # FactorParameters Sheet - Detailed factor configurations
    factor_params = pd.DataFrame([
        {
            'FactorName': 'OI_CURRENT',
            'FactorType': 'OI',
            'BaseWeight': 0.30,
            'MinWeight': 0.10,
            'MaxWeight': 0.50,
            'LookbackPeriod': 5,
            'SmoothingFactor': 0.2,
            'ThresholdType': 'ABSOLUTE',
            'ThresholdValue': 800000,
            'NormalizationMethod': 'ZSCORE',
            'OutlierHandling': 'WINSORIZE',
            'SeasonalAdjustment': 'NO',
            'VolatilityAdjustment': 'YES',
            'TrendAdjustment': 'YES',
            'RegimeAdjustment': 'YES',
            'PerformanceTracking': 'YES'
        },
        {
            'FactorName': 'COI_MOMENTUM',
            'FactorType': 'COI',
            'BaseWeight': 0.25,
            'MinWeight': 0.08,
            'MaxWeight': 0.45,
            'LookbackPeriod': 10,
            'SmoothingFactor': 0.15,
            'ThresholdType': 'PERCENTAGE',
            'ThresholdValue': 5.0,
            'NormalizationMethod': 'MINMAX',
            'OutlierHandling': 'CLIP',
            'SeasonalAdjustment': 'YES',
            'VolatilityAdjustment': 'YES',
            'TrendAdjustment': 'YES',
            'RegimeAdjustment': 'YES',
            'PerformanceTracking': 'YES'
        },
        {
            'FactorName': 'DELTA_EXPOSURE',
            'FactorType': 'GREEK',
            'BaseWeight': 0.20,
            'MinWeight': 0.05,
            'MaxWeight': 0.40,
            'LookbackPeriod': 3,
            'SmoothingFactor': 0.25,
            'ThresholdType': 'ABSOLUTE',
            'ThresholdValue': 0.5,
            'NormalizationMethod': 'ZSCORE',
            'OutlierHandling': 'WINSORIZE',
            'SeasonalAdjustment': 'NO',
            'VolatilityAdjustment': 'YES',
            'TrendAdjustment': 'NO',
            'RegimeAdjustment': 'YES',
            'PerformanceTracking': 'YES'
        }
    ])
    
    return weight_config, factor_params

def create_excel_templates():
    """Create the Excel template files"""
    
    # Create enhanced OI configuration template
    general_params, leg_params = create_enhanced_oi_config_template()
    
    # Create dynamic weightage template
    weight_config, factor_params = create_dynamic_weightage_template()
    
    # Define output directory
    output_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi/'
    
    # Create enhanced OI config file
    enhanced_config_file = os.path.join(output_dir, 'input_enhanced_oi_config.xlsx')
    with pd.ExcelWriter(enhanced_config_file, engine='openpyxl') as writer:
        general_params.to_excel(writer, sheet_name='GeneralParameter', index=False)
        leg_params.to_excel(writer, sheet_name='LegParameter', index=False)
    
    # Create dynamic weightage config file
    dynamic_weight_file = os.path.join(output_dir, 'input_dynamic_weightage_oi.xlsx')
    with pd.ExcelWriter(dynamic_weight_file, engine='openpyxl') as writer:
        weight_config.to_excel(writer, sheet_name='WeightConfiguration', index=False)
        factor_params.to_excel(writer, sheet_name='FactorParameters', index=False)
    
    print(f"Created enhanced OI configuration template: {enhanced_config_file}")
    print(f"Created dynamic weightage template: {dynamic_weight_file}")
    
    # Print summary
    print(f"\nTemplate Summary:")
    print(f"Enhanced OI Config - GeneralParameter: {len(general_params.columns)} columns")
    print(f"Enhanced OI Config - LegParameter: {len(leg_params.columns)} columns")
    print(f"Dynamic Weightage - WeightConfiguration: {len(weight_config.columns)} columns")
    print(f"Dynamic Weightage - FactorParameters: {len(factor_params.columns)} columns")

if __name__ == "__main__":
    create_excel_templates()
