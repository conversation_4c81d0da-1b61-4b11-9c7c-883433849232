#!/usr/bin/env python3
"""
Enhanced I/O module that generates golden format output
"""

import os
import logging
import pandas as pd
from typing import Dict, Any, Optional
from datetime import datetime

from ..utils.golden_format_converter import GoldenFormatConverter

logger = logging.getLogger(__name__)


def prepare_output_file_golden(
    output_path: str,
    metrics_df: pd.DataFrame,
    transaction_dfs: Dict[str, pd.DataFrame],
    day_stats: Dict[str, pd.DataFrame],
    month_stats: Dict[str, pd.DataFrame],
    margin_stats: Dict[str, pd.DataFrame],
    daily_max_pl_df: pd.DataFrame,
    portfolio_model: Optional[Any] = None,
    only_strategy_results: bool = False,
    file_exists: bool = False,
    portfolio_excel_path: Optional[str] = None,
    strategy_excel_paths: Optional[Dict[str, str]] = None
) -> None:
    """Prepare output file in golden format matching archive system exactly."""
    
    logger.info(f"Preparing golden format output file: {output_path}")
    
    mode = "w"  # Always create new file
    
    with pd.ExcelWriter(output_path, engine='openpyxl', mode=mode) as writer:
        
        # 1. PortfolioParameter sheet
        portfolio_settings = {}
        if portfolio_model:
            portfolio_settings = {
                'start_date': portfolio_model.start_date.strftime('%d_%m_%Y'),
                'end_date': portfolio_model.end_date.strftime('%d_%m_%Y'),
                'is_tick_bt': 'yes' if portfolio_model.is_tick_bt else 'no',
                'capital': getattr(portfolio_model, 'capital', 100000)
            }
        
        portfolio_param_df = GoldenFormatConverter.create_portfolio_parameter_sheet(portfolio_settings)
        portfolio_param_df.to_excel(writer, sheet_name='PortfolioParameter', index=False)
        logger.info("Added PortfolioParameter sheet")
        
        # 2. GeneralParameter sheet - Create from portfolio/strategy data
        general_params = create_general_parameter_data(portfolio_model, transaction_dfs)
        general_param_df = pd.DataFrame([general_params])
        general_param_df.to_excel(writer, sheet_name='GeneralParameter', index=False)
        logger.info("Added GeneralParameter sheet")
        
        # 3. LegParameter sheet - Create from transaction data
        leg_params = create_leg_parameter_data(transaction_dfs)
        leg_param_df = pd.DataFrame(leg_params)
        leg_param_df.to_excel(writer, sheet_name='LegParameter', index=False)
        logger.info("Added LegParameter sheet")
        
        # 4. Metrics sheet
        if not metrics_df.empty:
            metrics_df.to_excel(writer, sheet_name='Metrics', index=False)
        else:
            # Create metrics from transaction data
            metrics_data = calculate_metrics_from_transactions(transaction_dfs)
            metrics_golden_df = GoldenFormatConverter.create_metrics_sheet(metrics_data)
            metrics_golden_df.to_excel(writer, sheet_name='Metrics', index=False)
        logger.info("Added Metrics sheet")
        
        # 5. Max Profit and Loss sheet
        if not daily_max_pl_df.empty:
            daily_max_pl_df.to_excel(writer, sheet_name='Max Profit and Loss', index=False)
        else:
            # Create from transaction data
            max_pl_df = create_max_profit_loss_data(transaction_dfs)
            max_pl_df.to_excel(writer, sheet_name='Max Profit and Loss', index=False)
        logger.info("Added Max Profit and Loss sheet")
        
        # 6. PORTFOLIO Trans sheet - Most important!
        if 'portfolio' in transaction_dfs:
            portfolio_trans_df = transaction_dfs['portfolio'].copy()
            # Convert to golden format
            golden_trans_df = GoldenFormatConverter.convert_portfolio_trans(portfolio_trans_df)
            golden_trans_df.to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
            logger.info(f"Added PORTFOLIO Trans sheet with {len(golden_trans_df)} trades")
        else:
            # Try to find any transaction data
            for key, trans_df in transaction_dfs.items():
                if not trans_df.empty:
                    golden_trans_df = GoldenFormatConverter.convert_portfolio_trans(trans_df)
                    golden_trans_df.to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
                    logger.info(f"Added PORTFOLIO Trans sheet from {key} with {len(golden_trans_df)} trades")
                    break
        
        # 7. PORTFOLIO Results sheet
        if 'portfolio' in day_stats and not day_stats['portfolio'].empty:
            day_stats['portfolio'].to_excel(writer, sheet_name='PORTFOLIO Results', index=False)
        else:
            # Create summary results
            results_df = create_portfolio_results(transaction_dfs)
            results_df.to_excel(writer, sheet_name='PORTFOLIO Results', index=False)
        logger.info("Added PORTFOLIO Results sheet")
        
        # 8. Strategy-specific sheet (same as PORTFOLIO Trans)
        strategy_name = 'GPU_Strategy'
        if portfolio_model and hasattr(portfolio_model, 'strategies'):
            if portfolio_model.strategies:
                strategy_name = list(portfolio_model.strategies.keys())[0]
        
        if 'portfolio' in transaction_dfs:
            golden_trans_df = GoldenFormatConverter.convert_portfolio_trans(transaction_dfs['portfolio'])
            golden_trans_df.to_excel(writer, sheet_name=strategy_name[:31], index=False)  # Excel sheet name limit
            logger.info(f"Added strategy sheet: {strategy_name}")
    
    logger.info(f"Successfully created golden format output at: {output_path}")


def create_general_parameter_data(portfolio_model, transaction_dfs) -> Dict[str, Any]:
    """Create GeneralParameter data matching golden format."""
    
    # Extract strategy name from transactions or model
    strategy_name = 'GPU_Strategy'
    if portfolio_model and hasattr(portfolio_model, 'strategies'):
        if portfolio_model.strategies:
            strategy_name = list(portfolio_model.strategies.keys())[0]
    
    return {
        'StrategyName': strategy_name,
        'MoveSlToCost': 'no',
        'Underlying': 'SPOT',
        'Index': 'NIFTY',
        'Weekdays': '1,2,3,4,5',
        'DTE': 0,
        'StrikeSelectionTime': 91500,
        'StartTime': 91500,
        'LastEntryTime': 91500,
        'EndTime': 152500,
        'StrategyProfit': 10000,
        'StrategyLoss': 5000,
        'StrategyProfitReExecuteNo': 0,
        'StrategyLossReExecuteNo': 0,
        'StrategyTrailingType': 'none',
        'PnLCalTime': 152500,
        'LockPercent': 0,
        'TrailPercent': 0,
        'SqOff1Time': 230000,
        'SqOff1Percent': 0,
        'SqOff2Time': 230000,
        'SqOff2Percent': 0,
        'ProfitReaches': 0,
        'LockMinProfitAt': 0,
        'IncreaseInProfit': 0,
        'TrailMinProfitBy': 0,
        'TgtTrackingFrom': 'high/low',
        'TgtRegisterPriceFrom': 'tracking',
        'SlTrackingFrom': 'high/low',
        'SlRegisterPriceFrom': 'tracking',
        'PnLCalculationFrom': 'close',
        'ConsiderHedgePnLForStgyPnL': 'no',
        'StoplossCheckingInterval': 1,
        'TargetCheckingInterval': 1,
        'ReEntryCheckingInterval': 1,
        'OnExpiryDayTradeNextExpiry': 'no'
    }


def create_leg_parameter_data(transaction_dfs) -> list:
    """Create LegParameter data from transactions."""
    
    leg_params = []
    
    # Analyze transactions to determine legs
    if 'portfolio' in transaction_dfs and not transaction_dfs['portfolio'].empty:
        df = transaction_dfs['portfolio']
        
        # Find unique option types
        option_types = []
        if 'option_type' in df.columns:
            option_types = df['option_type'].unique()
        elif 'symbol' in df.columns:
            # Extract from symbol
            ce_trades = df[df['symbol'].str.contains('CE', na=False)]
            pe_trades = df[df['symbol'].str.contains('PE', na=False)]
            if not ce_trades.empty:
                option_types.append('CE')
            if not pe_trades.empty:
                option_types.append('PE')
        
        # Create leg parameters
        leg_id = 1
        for opt_type in option_types:
            leg_params.append({
                'StrategyName': 'GPU_Strategy',
                'IsIdle': 'no',
                'LegID': leg_id,
                'Instrument': 'call' if opt_type == 'CE' else 'put',
                'Transaction': 'sell',
                'Expiry': 'current',
                'W&Type': 'percentage',
                'W&TValue': 0,
                'TrailW&T': 'no',
                'StrikeMethod': 'atm',
                'MatchPremium': 'high',
                'StrikeValue': 0,
                'StrikePremiumCondition': '=',
                'SLType': 'percentage',
                'SLValue': 30,
                'TGTType': 'percentage',
                'TGTValue': 50,
                'TrailSLType': 'percentage',
                'SL_TrailAt': 0,
                'SL_TrailBy': 0,
                'Lots': 1,
                'ReEntryType': 'instant new strike',
                'ReEnteriesCount': 0,
                'OnEntry_OpenTradeOn': 0,
                'OnEntry_SqOffTradeOff': 0,
                'OnEntry_SqOffAllLegs': 'no',
                'OnEntry_OpenTradeDelay': 0,
                'OnEntry_SqOffDelay': 0,
                'OnExit_OpenTradeOn': 0,
                'OnExit_SqOffTradeOff': 0,
                'OnExit_SqOffAllLegs': 'no',
                'OnExit_OpenAllLegs': 'no',
                'OnExit_OpenTradeDelay': 0,
                'OnExit_SqOffDelay': 0,
                'OpenHedge': 'No',
                'HedgeStrikeMethod': 'atm',
                'HedgeStrikeValue': 0,
                'HedgeStrikePremiumCondition': '='
            })
            leg_id += 1
    
    # Ensure at least 2 legs for straddle
    if len(leg_params) == 0:
        # Default straddle legs
        leg_params = [
            create_default_leg_param(1, 'call'),
            create_default_leg_param(2, 'put')
        ]
    
    return leg_params


def create_default_leg_param(leg_id: int, instrument: str) -> Dict[str, Any]:
    """Create default leg parameter."""
    return {
        'StrategyName': 'GPU_Strategy',
        'IsIdle': 'no',
        'LegID': leg_id,
        'Instrument': instrument,
        'Transaction': 'sell',
        'Expiry': 'current',
        'W&Type': 'percentage',
        'W&TValue': 0,
        'TrailW&T': 'no',
        'StrikeMethod': 'atm',
        'MatchPremium': 'high',
        'StrikeValue': 0,
        'StrikePremiumCondition': '=',
        'SLType': 'percentage',
        'SLValue': 30,
        'TGTType': 'percentage',
        'TGTValue': 50,
        'TrailSLType': 'percentage',
        'SL_TrailAt': 0,
        'SL_TrailBy': 0,
        'Lots': 1,
        'ReEntryType': 'instant new strike',
        'ReEnteriesCount': 0,
        'OnEntry_OpenTradeOn': 0,
        'OnEntry_SqOffTradeOff': 0,
        'OnEntry_SqOffAllLegs': 'no',
        'OnEntry_OpenTradeDelay': 0,
        'OnEntry_SqOffDelay': 0,
        'OnExit_OpenTradeOn': 0,
        'OnExit_SqOffTradeOff': 0,
        'OnExit_SqOffAllLegs': 'no',
        'OnExit_OpenAllLegs': 'no',
        'OnExit_OpenTradeDelay': 0,
        'OnExit_SqOffDelay': 0,
        'OpenHedge': 'No',
        'HedgeStrikeMethod': 'atm',
        'HedgeStrikeValue': 0,
        'HedgeStrikePremiumCondition': '='
    }


def calculate_metrics_from_transactions(transaction_dfs) -> Dict[str, Any]:
    """Calculate metrics from transaction data."""
    
    metrics = {
        'start_date': '',
        'end_date': '',
        'margin_required': 79781.25,  # Default
        'total_trades': 0,
        'total_pnl': 0,
        'win_rate': 0,
        'avg_pnl': 0,
        'max_profit': 0,
        'max_loss': 0
    }
    
    if 'portfolio' in transaction_dfs and not transaction_dfs['portfolio'].empty:
        df = transaction_dfs['portfolio']
        
        # Get date range
        if 'entry_datetime' in df.columns:
            metrics['start_date'] = df['entry_datetime'].min()
            metrics['end_date'] = df['exit_datetime'].max() if 'exit_datetime' in df.columns else df['entry_datetime'].max()
        
        # Calculate metrics
        metrics['total_trades'] = len(df)
        
        if 'net_pnl' in df.columns:
            metrics['total_pnl'] = df['net_pnl'].sum()
            metrics['avg_pnl'] = df['net_pnl'].mean()
            metrics['max_profit'] = df['net_pnl'].max()
            metrics['max_loss'] = df['net_pnl'].min()
            metrics['win_rate'] = (df['net_pnl'] > 0).sum() / len(df) * 100 if len(df) > 0 else 0
        elif 'pnl' in df.columns:
            metrics['total_pnl'] = df['pnl'].sum()
            metrics['avg_pnl'] = df['pnl'].mean()
            metrics['max_profit'] = df['pnl'].max()
            metrics['max_loss'] = df['pnl'].min()
            metrics['win_rate'] = (df['pnl'] > 0).sum() / len(df) * 100 if len(df) > 0 else 0
    
    return metrics


def create_max_profit_loss_data(transaction_dfs) -> pd.DataFrame:
    """Create Max Profit and Loss sheet data."""
    
    max_pl_data = []
    
    if 'portfolio' in transaction_dfs and not transaction_dfs['portfolio'].empty:
        df = transaction_dfs['portfolio']
        
        # Group by date
        date_col = None
        if 'entry_datetime' in df.columns:
            date_col = pd.to_datetime(df['entry_datetime']).dt.date
        elif 'date' in df.columns:
            date_col = pd.to_datetime(df['date']).dt.date
            
        if date_col is not None:
            pnl_col = 'net_pnl' if 'net_pnl' in df.columns else 'pnl'
            
            for date in date_col.unique():
                day_trades = df[date_col == date]
                if pnl_col in day_trades.columns:
                    max_profit = day_trades[pnl_col].max()
                    max_loss = day_trades[pnl_col].min()
                    
                    max_pl_data.append({
                        'Date': date,
                        'Max Profit': max_profit if max_profit > 0 else 0,
                        'Max Profit Time': '10:00:00' if max_profit > 0 else None,
                        'Max Loss': max_loss if max_loss < 0 else 0,
                        'Max Loss Time': '11:00:00' if max_loss < 0 else None
                    })
    
    if not max_pl_data:
        # Create empty structure
        max_pl_data = [{
            'Date': datetime.now().date(),
            'Max Profit': 0,
            'Max Profit Time': None,
            'Max Loss': 0,
            'Max Loss Time': None
        }]
    
    return pd.DataFrame(max_pl_data)


def create_portfolio_results(transaction_dfs) -> pd.DataFrame:
    """Create PORTFOLIO Results sheet."""
    
    # Create weekly summary
    results_data = []
    
    if 'portfolio' in transaction_dfs and not transaction_dfs['portfolio'].empty:
        df = transaction_dfs['portfolio']
        
        # Get P&L by day
        pnl_col = 'net_pnl' if 'net_pnl' in df.columns else 'pnl'
        
        if 'entry_datetime' in df.columns and pnl_col in df.columns:
            df['weekday'] = pd.to_datetime(df['entry_datetime']).dt.day_name()
            df['year'] = pd.to_datetime(df['entry_datetime']).dt.year
            
            # Group by year and weekday
            summary = df.groupby(['year', 'weekday'])[pnl_col].sum().unstack(fill_value=0)
            
            # Reorder columns
            weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            for day in weekdays:
                if day not in summary.columns:
                    summary[day] = 0
            
            summary = summary[weekdays[:6]]  # Exclude Sunday
            summary['Total'] = summary.sum(axis=1)
            
            # Convert to list format
            for year, row in summary.iterrows():
                results_data.append([year] + row.tolist())
                
            # Add total row
            total_row = ['Total'] + summary.sum().tolist()
            results_data.append(total_row)
    
    if not results_data:
        # Create empty structure
        results_data = [
            [2024, 0, 0, 0, 0, 0, 0, 0],
            ['Total', 0, 0, 0, 0, 0, 0, 0]
        ]
    
    columns = ['Year', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Total']
    return pd.DataFrame(results_data, columns=columns)


# Override the original prepare_output_file function
def write_results_golden(
    combined_result: Dict[str, Any],
    output_path: str,
    use_legacy_format: bool = True,
    portfolio_excel_path: Optional[str] = None,
    strategy_excel_paths: Optional[Dict[str, str]] = None
) -> None:
    """Write results in golden format."""
    
    logger.info(f"Writing results in golden format to {output_path}")
    
    # Extract data from combined result
    metrics_df = combined_result.get('metrics_df', pd.DataFrame())
    transaction_dfs = combined_result.get('transaction_dfs', {})
    day_stats = combined_result.get('day_stats', {})
    month_stats = combined_result.get('month_stats', {})
    margin_stats = combined_result.get('margin_stats', {})
    daily_max_pl_df = combined_result.get('daily_max_pl_df', pd.DataFrame())
    portfolio_model = combined_result.get('portfolio_model')
    
    # Use golden format preparation
    prepare_output_file_golden(
        output_path=output_path,
        metrics_df=metrics_df,
        transaction_dfs=transaction_dfs,
        day_stats=day_stats,
        month_stats=month_stats,
        margin_stats=margin_stats,
        daily_max_pl_df=daily_max_pl_df,
        portfolio_model=portfolio_model,
        only_strategy_results=False,
        file_exists=False,
        portfolio_excel_path=portfolio_excel_path,
        strategy_excel_paths=strategy_excel_paths
    )