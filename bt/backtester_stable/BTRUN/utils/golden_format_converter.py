#!/usr/bin/env python3
"""
Golden Format Converter - Ensures GPU output matches archive golden format exactly
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class GoldenFormatConverter:
    """Converts GPU output to match archive golden format exactly."""
    
    # Column mapping from GPU format to Golden format
    COLUMN_MAPPING = {
        # GPU column -> Golden column
        'portfolio_name': 'Portfolio Name',
        'strategy_name': 'Strategy Name',
        'id': 'ID',
        'entry_datetime': 'Entry Date',
        'entry_time': 'Enter On',
        'entry_day': 'Entry Day',
        'exit_datetime': 'Exit Date',
        'exit_time': 'Exit at',
        'exit_day': 'Exit Day',
        'index': 'Index',
        'expiry': 'Expiry',
        'strike': 'Strike',
        'option_type': 'CE/PE',
        'transaction_type': 'Trade',
        'quantity': 'Qty',
        'entry_price': 'Entry at',
        'exit_price': 'Exit at.1',
        'points': 'Points',
        'points_after_slippage': 'Points After Slippage',
        'pnl': 'PNL',
        'pnl_after_slippage': 'AfterSlippage',
        'taxes': 'Taxes',
        'net_pnl': 'Net PNL',
        're_entry_no': 'Re-entry No',
        'sl_re_entry_no': 'SL Re-entry No',
        'tgt_re_entry_no': 'TGT Re-entry No',
        'reason': 'Reason',
        'strategy_entry_no': 'Strategy Entry No',
        'index_at_entry': 'Index At Entry',
        'index_at_exit': 'Index At Exit',
        'max_profit': 'MaxProfit',
        'max_loss': 'MaxLoss',
        # Additional mappings
        'symbol': 'Symbol',
        'leg': 'Leg',
        'date': 'Date',
        'time': 'Time',
        'transaction': 'Transaction',
        'p&l': 'P&L',
        'p&l %': 'P&L %'
    }
    
    @classmethod
    def convert_portfolio_trans(cls, df: pd.DataFrame) -> pd.DataFrame:
        """Convert GPU transaction DataFrame to golden format."""
        logger.info("Converting transaction DataFrame to golden format")
        
        # Create a copy
        golden_df = df.copy()
        
        # Rename columns
        rename_map = {}
        for gpu_col, golden_col in cls.COLUMN_MAPPING.items():
            # Check both lowercase and as-is
            if gpu_col in golden_df.columns:
                rename_map[gpu_col] = golden_col
            elif gpu_col.lower() in [c.lower() for c in golden_df.columns]:
                # Find the actual column name
                for col in golden_df.columns:
                    if col.lower() == gpu_col.lower():
                        rename_map[col] = golden_col
                        break
        
        golden_df = golden_df.rename(columns=rename_map)
        
        # Handle special columns
        if 'Symbol' in golden_df.columns:
            # Extract strike and CE/PE from symbol
            golden_df = cls._extract_strike_and_option_type(golden_df)
        
        # Format dates and times
        if 'Entry Date' in golden_df.columns:
            golden_df['Entry Date'] = pd.to_datetime(golden_df['Entry Date']).dt.strftime('%Y-%m-%d %H:%M:%S')
        
        if 'Exit Date' in golden_df.columns:
            golden_df['Exit Date'] = pd.to_datetime(golden_df['Exit Date']).dt.strftime('%Y-%m-%d %H:%M:%S')
            
        if 'Exit at' not in golden_df.columns and 'Exit Time' in golden_df.columns:
            golden_df['Exit at'] = golden_df['Exit Time'].apply(lambda x: cls._format_time(x))
            
        # Ensure all required columns exist
        required_columns = [
            'Portfolio Name', 'Strategy Name', 'ID', 'Entry Date', 'Enter On', 'Entry Day',
            'Exit Date', 'Exit at', 'Exit Day', 'Index', 'Expiry', 'Strike', 'CE/PE', 'Trade',
            'Qty', 'Entry at', 'Exit at.1', 'Points', 'Points After Slippage', 'PNL',
            'AfterSlippage', 'Taxes', 'Net PNL', 'Re-entry No', 'SL Re-entry No',
            'TGT Re-entry No', 'Reason', 'Strategy Entry No', 'Index At Entry',
            'Index At Exit', 'MaxProfit', 'MaxLoss'
        ]
        
        for col in required_columns:
            if col not in golden_df.columns:
                # Add missing columns with default values
                if col in ['SL Re-entry No', 'TGT Re-entry No']:
                    golden_df[col] = np.nan
                elif col in ['Re-entry No', 'Strategy Entry No', 'Taxes']:
                    golden_df[col] = 0
                elif col == 'Trade':
                    golden_df[col] = 'SELL'  # Default
                elif col == 'Index':
                    golden_df[col] = 'NIFTY'  # Default
                elif col == 'Reason':
                    golden_df[col] = 'Exit Time Hit'  # Default
                else:
                    golden_df[col] = ''
        
        # Reorder columns to match golden format
        golden_df = golden_df[required_columns]
        
        return golden_df
    
    @classmethod
    def _extract_strike_and_option_type(cls, df: pd.DataFrame) -> pd.DataFrame:
        """Extract strike and CE/PE from symbol like NIFTY04APR2422500CE."""
        if 'Symbol' not in df.columns:
            return df
            
        # Initialize columns if not exist
        if 'Strike' not in df.columns:
            df['Strike'] = 0
        if 'CE/PE' not in df.columns:
            df['CE/PE'] = ''
            
        for idx, symbol in df['Symbol'].items():
            if pd.isna(symbol):
                continue
                
            symbol_str = str(symbol)
            
            # Extract CE/PE
            if symbol_str.endswith('CE'):
                df.at[idx, 'CE/PE'] = 'CALL'
                strike_part = symbol_str[:-2]
            elif symbol_str.endswith('PE'):
                df.at[idx, 'CE/PE'] = 'PUT'
                strike_part = symbol_str[:-2]
            else:
                continue
                
            # Extract strike price (last 5 digits before CE/PE)
            try:
                strike = int(strike_part[-5:])
                df.at[idx, 'Strike'] = strike
            except:
                pass
                
        return df
    
    @classmethod
    def _format_time(cls, time_val) -> str:
        """Format time to HH:MM:SS format."""
        if pd.isna(time_val):
            return ''
            
        if isinstance(time_val, str):
            return time_val
            
        if isinstance(time_val, (int, float)):
            # Convert HHMMSS to HH:MM:SS
            time_str = str(int(time_val)).zfill(6)
            return f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:6]}"
            
        return str(time_val)
    
    @classmethod
    def create_portfolio_parameter_sheet(cls, portfolio_settings: Dict[str, Any]) -> pd.DataFrame:
        """Create PortfolioParameter sheet in golden format."""
        params = [
            ['StartDate', portfolio_settings.get('start_date', '01_04_2024')],
            ['EndDate', portfolio_settings.get('end_date', '01_04_2024')],
            ['IsTickBT', portfolio_settings.get('is_tick_bt', 'yes')],
            ['Capital', portfolio_settings.get('capital', 100000)],
            ['LotMultiplier', portfolio_settings.get('lot_multiplier', 1)],
            ['PositionSize', portfolio_settings.get('position_size', 100)],
            ['MaxOpenPositions', portfolio_settings.get('max_open_positions', 10)],
            ['Slippage', portfolio_settings.get('slippage', 0.05)],
            ['TransactionCost', portfolio_settings.get('transaction_cost', 20)],
            ['MarginPercentage', portfolio_settings.get('margin_percentage', 15)],
            ['Leverage', portfolio_settings.get('leverage', 1)],
            ['RiskPerTrade', portfolio_settings.get('risk_per_trade', 2)],
            ['MaxDrawdown', portfolio_settings.get('max_drawdown', 20)],
            ['TradingDays', portfolio_settings.get('trading_days', 'Mon,Tue,Wed,Thu,Fri')],
            ['StartTime', portfolio_settings.get('start_time', '09:15:00')],
            ['EndTime', portfolio_settings.get('end_time', '15:30:00')],
            ['SquareOffTime', portfolio_settings.get('square_off_time', '15:25:00')],
            ['UseATM', portfolio_settings.get('use_atm', 'yes')],
            ['ATMType', portfolio_settings.get('atm_type', 'Synthetic Future')],
            ['DataSource', portfolio_settings.get('data_source', 'HeavyDB')],
            ['BacktestMode', portfolio_settings.get('backtest_mode', 'production')]
        ]
        
        return pd.DataFrame(params, columns=['Head', 'Value'])
    
    @classmethod
    def create_metrics_sheet(cls, metrics_data: Dict[str, Any], strategy_name: str = 'Combined') -> pd.DataFrame:
        """Create Metrics sheet in golden format."""
        metrics = [
            ['Backtest Start Date', metrics_data.get('start_date', ''), metrics_data.get('start_date', '')],
            ['Backtest End Date', metrics_data.get('end_date', ''), metrics_data.get('end_date', '')],
            ['Margin Required', metrics_data.get('margin_required', 0), metrics_data.get('margin_required', 0)],
            ['Total Trades', metrics_data.get('total_trades', 0), metrics_data.get('total_trades', 0)],
            ['Total P&L', metrics_data.get('total_pnl', 0), metrics_data.get('total_pnl', 0)],
            ['Win Rate %', metrics_data.get('win_rate', 0), metrics_data.get('win_rate', 0)],
            ['Average P&L', metrics_data.get('avg_pnl', 0), metrics_data.get('avg_pnl', 0)],
            ['Max Profit', metrics_data.get('max_profit', 0), metrics_data.get('max_profit', 0)],
            ['Max Loss', metrics_data.get('max_loss', 0), metrics_data.get('max_loss', 0)],
            ['Sharpe Ratio', metrics_data.get('sharpe_ratio', 0), metrics_data.get('sharpe_ratio', 0)],
            ['Max Drawdown', metrics_data.get('max_drawdown', 0), metrics_data.get('max_drawdown', 0)],
            ['Profit Factor', metrics_data.get('profit_factor', 0), metrics_data.get('profit_factor', 0)]
        ]
        
        return pd.DataFrame(metrics, columns=['Particulars', 'Combined', strategy_name])