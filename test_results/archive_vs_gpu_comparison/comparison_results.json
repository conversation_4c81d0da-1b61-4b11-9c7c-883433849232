{"timestamp": "2025-06-10T08:20:31.191962", "files": {"archive": "/srv/samba/shared/test_results/archive_vs_gpu_comparison/archive_output.xlsx", "gpu": "/srv/samba/shared/test_results/archive_vs_gpu_comparison/gpu_output/gpu_output.xlsx"}, "metrics": {}, "differences": [], "sheets": {"archive": ["PORTFOLIO Trans", "Summary"], "gpu": ["PORTFOLIO Trans"], "common": ["PORTFOLIO Trans"], "archive_only": ["Summary"], "gpu_only": []}, "trades": {"archive_count": 50, "gpu_count": 50, "difference": 0}, "pnl": {"archive": 30000.0, "gpu": 32500.0, "difference": 2500.0, "percentage_diff": 8.333333333333332}, "columns": {"archive": ["Exit Time", "Date", "Symbol", "Exit at.1", "P&L %", "Exit at", "Exit Date", "P&L", "Transaction", "Quantity", "Time", "Strategy", "Entry Price", "Leg"], "gpu": ["Exit Time", "Date", "Symbol", "P&L %", "Exit Date", "P&L", "Transaction", "Quantity", "Time", "Strategy", "Exit Price", "Entry Price", "Leg"], "common": ["Exit Time", "Date", "Symbol", "P&L %", "Strategy", "Exit Date", "Transaction", "Quantity", "Time", "P&L", "Entry Price", "Leg"], "archive_only": ["Exit at.1", "Exit at"], "gpu_only": ["Exit Price"]}}