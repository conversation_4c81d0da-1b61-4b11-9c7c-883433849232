
import sys
sys.path.append('/srv/samba/shared')
sys.path.append('/srv/samba/shared/bt/archive/backtester_stable/BTRUN')

# Import archive version
from BTRunPortfolio import BTRun

# Run backtest
btrun = BTRun()
btrun.portfolio_file = '/srv/samba/shared/test_results/archive_vs_gpu_comparison/test_portfolio_1month.xlsx'
btrun.output_dir = '/srv/samba/shared/test_results/archive_vs_gpu_comparison//archive_output/'

# Execute
btrun.run()
