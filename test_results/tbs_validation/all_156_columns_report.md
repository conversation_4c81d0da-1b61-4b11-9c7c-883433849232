# Comprehensive Column Validation Report

**Date**: 2025-06-10 08:41:40
**Total Columns Documented**: 81
**Columns Found**: 71
**Columns Validated**: 69
**Coverage Rate**: 87.7%
**Validation Rate**: 85.2%

## Sheet-by-Sheet Analysis


### PortfolioSetting
- Total Columns: 8
- Found: 8 (100.0%)
- Validated: 8 (100.0%)

### StrategySetting
- Total Columns: 4
- Found: 4 (100.0%)
- Validated: 4 (100.0%)

### GeneralParameter
- Total Columns: 42
- Found: 35 (83.3%)
- Validated: 34 (81.0%)

**Missing Columns**: CheckPremiumDiffCondition, PremiumDiffType, PremiumDiffValue, PremiumDiffChangeStrike, PremiumDiffDoForceEntry, PremiumDiffDoForceAfter, PremiumDiffForceEntryConsiderPremium

### LegParameter
- Total Columns: 27
- Found: 24 (88.9%)
- Validated: 23 (85.2%)

**Missing Columns**: SL_ReEntryType, TGT_ReEntryType, Parameter

## Missing Columns Analysis

- **CheckPremiumDiffCondition** (GeneralParameter): Check premium difference
- **PremiumDiffType** (GeneralParameter): Premium difference type
- **PremiumDiffValue** (GeneralParameter): Premium difference value
- **PremiumDiffChangeStrike** (GeneralParameter): Change strike on diff
- **PremiumDiffDoForceEntry** (GeneralParameter): Force entry on diff
- **PremiumDiffDoForceAfter** (GeneralParameter): Force after minutes
- **PremiumDiffForceEntryConsiderPremium** (GeneralParameter): Premium for force entry
- **SL_ReEntryType** (LegParameter): Stop loss re-entry
- **SL_ReEntryNo** (LegParameter): SL re-entries
- **TGT_ReEntryType** (LegParameter): Target re-entry
- **TGT_ReEntryNo** (LegParameter): Target re-entries
- **Parameter** (LegParameter): Description