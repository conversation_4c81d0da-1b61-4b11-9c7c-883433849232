{"summary": {"total_columns": 81, "found_columns": 71, "validated_columns": 69, "missing_columns": [{"column": "CheckPremiumDiffCondition", "sheet": "GeneralParameter", "description": "Check premium difference"}, {"column": "PremiumDiffType", "sheet": "GeneralParameter", "description": "Premium difference type"}, {"column": "PremiumDiffValue", "sheet": "GeneralParameter", "description": "Premium difference value"}, {"column": "PremiumDiffChangeStrike", "sheet": "GeneralParameter", "description": "Change strike on diff"}, {"column": "PremiumDiffDoForceEntry", "sheet": "GeneralParameter", "description": "Force entry on diff"}, {"column": "PremiumDiffDoForceAfter", "sheet": "GeneralParameter", "description": "Force after minutes"}, {"column": "PremiumDiffForceEntryConsiderPremium", "sheet": "GeneralParameter", "description": "Premium for force entry"}, {"column": "SL_ReEntryType", "sheet": "LegParameter", "description": "Stop loss re-entry"}, {"column": "SL_ReEntryNo", "sheet": "LegParameter", "description": "SL re-entries"}, {"column": "TGT_ReEntryType", "sheet": "LegParameter", "description": "Target re-entry"}, {"column": "TGT_ReEntryNo", "sheet": "LegParameter", "description": "Target re-entries"}, {"column": "Parameter", "sheet": "LegParameter", "description": "Description"}], "validation_details": {}}, "columns": {"StartDate": {"description": "Backtest start date", "valid_options": "DD_MM_YYYY format", "archive_handling": "Converted to YYYYMMDD", "gpu_handling": "Parsed to date object", "found": true, "sheet": "PortfolioSetting"}, "EndDate": {"description": "Backtest end date", "valid_options": "DD_MM_YYYY or column with date format", "archive_handling": "Converted to YYYYMMDD", "gpu_handling": "Parsed to date object", "found": true, "sheet": "PortfolioSetting"}, "IsTickBT": {"description": "Use tick-by-tick data", "valid_options": "YES/NO/yes/no", "archive_handling": "Boolean conversion", "gpu_handling": "Boolean conversion", "found": true, "sheet": "PortfolioSetting"}, "Enabled": {"description": "Enable this strategy", "valid_options": "YES/NO", "archive_handling": "Row filter only", "gpu_handling": "Row filter only", "found": true, "sheet": "StrategySetting"}, "PortfolioName": {"description": "Portfolio identifier", "valid_options": "Must match PortfolioSetting", "archive_handling": "Uppercase conversion", "gpu_handling": "Case-insensitive match", "found": true, "sheet": "StrategySetting"}, "PortfolioTarget": {"description": "Portfolio profit target", "valid_options": "Number", "archive_handling": "Direct float conversion", "gpu_handling": "Float with validation", "found": true, "sheet": "PortfolioSetting"}, "PortfolioStoploss": {"description": "Portfolio stop loss", "valid_options": "Number", "archive_handling": "Direct float conversion", "gpu_handling": "Float with validation", "found": true, "sheet": "PortfolioSetting"}, "PortfolioTrailingType": {"description": "Portfolio trailing type", "valid_options": "See trailing types below", "archive_handling": "String mapping", "gpu_handling": "Stored in extra_params", "found": true, "sheet": "PortfolioSetting"}, "PnLCalTime": {"description": "P&L calculation time", "valid_options": "HHMMSS format", "archive_handling": "Time conversion", "gpu_handling": "Time in extra_params", "found": true, "sheet": "GeneralParameter"}, "LockPercent": {"description": "Profit lock percentage", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "TrailPercent": {"description": "Trail percentage", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "SqOff1Time": {"description": "First square-off time", "valid_options": "HHMMSS format", "archive_handling": "Time conversion", "gpu_handling": "Time in extra_params", "found": true, "sheet": "GeneralParameter"}, "SqOff1Percent": {"description": "First square-off percentage", "valid_options": "Number (0-100)", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "SqOff2Time": {"description": "Second square-off time", "valid_options": "HHMMSS format", "archive_handling": "Time conversion", "gpu_handling": "Time in extra_params", "found": true, "sheet": "GeneralParameter"}, "SqOff2Percent": {"description": "Second square-off percentage", "valid_options": "Number (0-100)", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "ProfitReaches": {"description": "Profit target threshold", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "LockMinProfitAt": {"description": "Minimum profit to lock", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "IncreaseInProfit": {"description": "Profit increase step", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "TrailMinProfitBy": {"description": "Minimum profit to trail", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "Multiplier": {"description": "<PERSON>gin multiplier", "valid_options": "Number > 0", "archive_handling": "Float conversion", "gpu_handling": "Float with validation", "found": true, "sheet": "PortfolioSetting"}, "SlippagePercent": {"description": "Slippage percentage", "valid_options": "Number", "archive_handling": "Divided by 100", "gpu_handling": "Float with validation", "found": true, "sheet": "PortfolioSetting"}, "StrategyType": {"description": "Strategy engine type", "valid_options": "TBS, TV, ORB, OI, INDICATOR, IBS, VWAP", "archive_handling": "Uppercase, IBS→VWAP", "gpu_handling": "Stored as evaluator", "found": true, "sheet": "StrategySetting"}, "StrategyExcelFilePath": {"description": "Path to strategy Excel", "valid_options": "Valid file path", "archive_handling": "File existence check", "gpu_handling": "Path resolution", "found": true, "sheet": "StrategySetting"}, "StrategyName": {"description": "Strategy identifier", "valid_options": "Must match GeneralParameter", "archive_handling": "String matching", "gpu_handling": "String matching", "found": true, "sheet": "LegParameter"}, "MoveSlToCost": {"description": "Move stop-loss to cost basis", "valid_options": "YES/NO/yes/no", "archive_handling": "Boolean conversion", "gpu_handling": "Boolean in extra_params", "found": true, "sheet": "GeneralParameter"}, "Underlying": {"description": "Underlying security", "valid_options": "SPOT, FUT", "archive_handling": "String mapping", "gpu_handling": "Stored in extra_params", "found": true, "sheet": "GeneralParameter"}, "Index": {"description": "Index name", "valid_options": "NIFTY, BANKNIFTY, FINNIFTY, etc.", "archive_handling": "String validation", "gpu_handling": "Stored in extra_params", "found": true, "sheet": "GeneralParameter"}, "Weekdays": {"description": "Trading days filter", "valid_options": "1,2,3,4,5", "archive_handling": "String parsing", "gpu_handling": "Stored in extra_params", "found": true, "sheet": "GeneralParameter"}, "DTE": {"description": "Days to expiry", "valid_options": "Integer (0,1,2...)", "archive_handling": "Integer conversion", "gpu_handling": "Direct to dte_filter", "found": true, "sheet": "GeneralParameter"}, "StrikeSelectionTime": {"description": "When strikes are selected", "valid_options": "HHMMSS", "archive_handling": "91600", "gpu_handling": "9:16 AM", "found": true, "sheet": "GeneralParameter"}, "StartTime": {"description": "When trade entry begins", "valid_options": "HHMMSS", "archive_handling": "91600", "gpu_handling": "9:16 AM", "found": true, "sheet": "GeneralParameter"}, "LastEntryTime": {"description": "Latest time to enter trades", "valid_options": "HHMMSS", "archive_handling": "120000", "gpu_handling": "12:00 PM", "found": true, "sheet": "GeneralParameter"}, "EndTime": {"description": "When trades are exited", "valid_options": "HHMMSS", "archive_handling": "120000", "gpu_handling": "12:00 PM", "found": true, "sheet": "GeneralParameter"}, "StrategyProfit": {"description": "Strategy profit target", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "StrategyLoss": {"description": "Strategy stop loss", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "GeneralParameter"}, "StrategyProfitReExecuteNo": {"description": "Re-entries after profit", "valid_options": "Integer", "archive_handling": "Integer conversion", "gpu_handling": "Integer in extra_params", "found": true, "sheet": "GeneralParameter"}, "StrategyLossReExecuteNo": {"description": "Re-entries after loss", "valid_options": "Integer", "archive_handling": "Integer conversion", "gpu_handling": "Integer in extra_params", "found": true, "sheet": "GeneralParameter"}, "StrategyTrailingType": {"description": "Strategy trailing type", "valid_options": "Lock Minimum Profit, Lock & Trail Profits", "archive_handling": "String mapping", "gpu_handling": "String in extra_params", "found": true, "sheet": "GeneralParameter"}, "TgtTrackingFrom": {"description": "Target tracking mode", "valid_options": "close, open, high/low", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "GeneralParameter"}, "TgtRegisterPriceFrom": {"description": "Target price mode", "valid_options": "tick, tracking", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "GeneralParameter"}, "SlTrackingFrom": {"description": "SL tracking mode", "valid_options": "close, open, high/low", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "GeneralParameter"}, "SlRegisterPriceFrom": {"description": "SL price mode", "valid_options": "tick, tracking", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "GeneralParameter"}, "PnLCalculationFrom": {"description": "P&L calculation mode", "valid_options": "close, open, high/low", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "GeneralParameter"}, "ConsiderHedgePnLForStgyPnL": {"description": "Include hedge in P&L", "valid_options": "yes/no", "archive_handling": "Boolean conversion", "gpu_handling": "Boolean in extra_params", "found": true, "sheet": "GeneralParameter"}, "CheckPremiumDiffCondition": {"description": "Check premium difference", "valid_options": "yes/no", "archive_handling": "Boolean conversion", "gpu_handling": "Boolean in extra_params", "found": false, "sheet": "GeneralParameter"}, "PremiumDiffType": {"description": "Premium difference type", "valid_options": "point, percentage", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": false, "sheet": "GeneralParameter"}, "PremiumDiffValue": {"description": "Premium difference value", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": false, "sheet": "GeneralParameter"}, "PremiumDiffChangeStrike": {"description": "Change strike on diff", "valid_options": "yes/no", "archive_handling": "Boolean conversion", "gpu_handling": "Boolean in extra_params", "found": false, "sheet": "GeneralParameter"}, "PremiumDiffDoForceEntry": {"description": "Force entry on diff", "valid_options": "yes/no", "archive_handling": "Boolean conversion", "gpu_handling": "Boolean in extra_params", "found": false, "sheet": "GeneralParameter"}, "PremiumDiffDoForceAfter": {"description": "Force after minutes", "valid_options": "Number, \"in minutes\"", "archive_handling": "String parsing", "gpu_handling": "String in extra_params", "found": false, "sheet": "GeneralParameter"}, "PremiumDiffForceEntryConsiderPremium": {"description": "Premium for force entry", "valid_options": "higher, lower", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": false, "sheet": "GeneralParameter"}, "StoplossCheckingInterval": {"description": "SL check interval", "valid_options": "Integer (seconds)", "archive_handling": "Integer conversion", "gpu_handling": "Integer in extra_params", "found": true, "sheet": "GeneralParameter"}, "TargetCheckingInterval": {"description": "Target check interval", "valid_options": "Integer (seconds)", "archive_handling": "Integer conversion", "gpu_handling": "Integer in extra_params", "found": true, "sheet": "GeneralParameter"}, "ReEntryCheckingInterval": {"description": "Re-entry check interval", "valid_options": "Integer (seconds)", "archive_handling": "Integer conversion", "gpu_handling": "Integer in extra_params", "found": true, "sheet": "GeneralParameter"}, "OnExpiryDayTradeNextExpiry": {"description": "Trade next expiry on expiry day", "valid_options": "yes/no", "archive_handling": "Boolean conversion", "gpu_handling": "Boolean in extra_params", "found": true, "sheet": "GeneralParameter"}, "IsIdle": {"description": "Inactive leg", "valid_options": "yes/no", "archive_handling": "Boolean conversion", "gpu_handling": "Boolean in extra_params", "found": true, "sheet": "LegParameter"}, "LegID": {"description": "Leg identifier", "valid_options": "Any unique identifier", "archive_handling": "Direct string", "gpu_handling": "Direct to leg_id", "found": true, "sheet": "LegParameter"}, "Instrument": {"description": "Option type", "valid_options": "call, put, FUT", "archive_handling": "Uppercase mapping", "gpu_handling": "Enum conversion", "found": true, "sheet": "LegParameter"}, "Transaction": {"description": "Transaction type", "valid_options": "buy, sell", "archive_handling": "Uppercase mapping", "gpu_handling": "Enum conversion", "found": true, "sheet": "LegParameter"}, "Expiry": {"description": "Expiry selection", "valid_options": "current, next, monthly, CW, NW, CM, NM", "archive_handling": "String mapping", "gpu_handling": "Enum conversion", "found": true, "sheet": "LegParameter"}, "StrikeMethod": {"description": "Strike selection", "valid_options": "ATM, ITM1-n, OTM1-n, FIXED, PREMIUM, ATM WIDTH, DELTA", "archive_handling": "String mapping", "gpu_handling": "Enum conversion", "found": true, "sheet": "LegParameter"}, "MatchPremium": {"description": "Premium matching", "valid_options": "high, low", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "LegParameter"}, "StrikeValue": {"description": "Fixed strike value", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "To fixed_strike or strike_distance", "found": true, "sheet": "LegParameter"}, "StrikePremiumCondition": {"description": "Premium condition", "valid_options": "=, <, >, <=", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "LegParameter"}, "SLType": {"description": "Stop loss type", "valid_options": "percentage, point, index point, index percentage, absolute, delta", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "LegParameter"}, "SLValue": {"description": "Stop loss value", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "LegParameter"}, "TGTType": {"description": "Target type", "valid_options": "percentage, point, index point, index percentage, absolute, delta", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "LegParameter"}, "TGTValue": {"description": "Target value", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "LegParameter"}, "TrailSLType": {"description": "Trailing SL type", "valid_options": "percentage, point, index point, index percentage, absolute, delta", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "LegParameter"}, "SL_TrailAt": {"description": "When to start trailing", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "LegParameter"}, "SL_TrailBy": {"description": "Trail step", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "LegParameter"}, "Lots": {"description": "Number of lots", "valid_options": "Integer", "archive_handling": "Integer conversion", "gpu_handling": "Direct to lots", "found": true, "sheet": "LegParameter"}, "SL_ReEntryType": {"description": "Stop loss re-entry", "valid_options": "cost, original, instant new strike, instant same strike", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": false, "sheet": "LegParameter"}, "SL_ReEntryNo": {"description": "SL re-entries", "valid_options": "Integer", "archive_handling": "Integer conversion", "gpu_handling": "Integer in extra_params", "found": true, "sheet": "LegParameter"}, "TGT_ReEntryType": {"description": "Target re-entry", "valid_options": "cost, original, instant new strike, instant same strike", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": false, "sheet": "LegParameter"}, "TGT_ReEntryNo": {"description": "Target re-entries", "valid_options": "Integer", "archive_handling": "Integer conversion", "gpu_handling": "Integer in extra_params", "found": true, "sheet": "LegParameter"}, "OpenHedge": {"description": "Use hedging", "valid_options": "Yes/No", "archive_handling": "Boolean conversion", "gpu_handling": "Boolean in extra_params", "found": true, "sheet": "LegParameter"}, "HedgeStrikeMethod": {"description": "Hedge strike method", "valid_options": "atm, premium, atm width, delta", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "LegParameter"}, "HedgeStrikeValue": {"description": "Hedge strike value", "valid_options": "Number", "archive_handling": "Float conversion", "gpu_handling": "Float in extra_params", "found": true, "sheet": "LegParameter"}, "HedgeStrikePremiumCondition": {"description": "Hedge premium condition", "valid_options": "=, <, >, <=", "archive_handling": "String validation", "gpu_handling": "String in extra_params", "found": true, "sheet": "LegParameter"}, "Parameter": {"description": "Description", "valid_options": "Format", "archive_handling": "Example", "gpu_handling": "Notes", "found": false, "sheet": "LegParameter"}}, "timestamp": "2025-06-10T08:41:40.235879"}