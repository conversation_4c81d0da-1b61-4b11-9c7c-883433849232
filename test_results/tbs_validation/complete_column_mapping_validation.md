# Complete Column Mapping Validation Report

**Date**: 2025-06-10 08:24:48
**Mapping Document**: column_mapping_ml_tbs.md

## Summary

- Total Mappings Documented: 100
- Successfully Validated: 82
- Failed Validations: 18
- Validation Rate: 82.0%

## Sheet-by-Sheet Results

### PortfolioSetting
- Expected Columns: 21
- Found Columns: 21
- Missing Columns: None
- Extra Columns: None

### StrategySetting
- Expected Columns: 4
- Found Columns: 4
- Missing Columns: None
- Extra Columns: None

**Validation Issues:**
- StrategyType: Invalid enum value at row 3: HEIKIN_RSI_EMA not in ['TBS', 'TV', 'ORB', 'OI', 'INDICATOR', 'IBS', 'VWAP']

### GeneralParameter
- Expected Columns: 43
- Found Columns: 36
- Missing Columns: PremiumDiffChangeStrike, PremiumDiffValue, PremiumDiffForceEntryConsiderPremium, PremiumDiffDoForceAfter, CheckPremiumDiffCondition, PremiumDiffType, PremiumDiffDoForceEntry
- Extra Columns: None

**Validation Issues:**
- Weekdays: Invalid enum value at row 0: 1,2,3,4,5 not in ['1', '2', '3', '4', '5']
- DTE: Invalid enum value at row 0: 0 not in ['Integer (0', '1', '2...)']

### LegParameter
- Expected Columns: 32
- Found Columns: 25
- Missing Columns: Short Call/Put, Long Call/Put, SL_ReEntryNo, TGT_ReEntryNo, SL_ReEntryType, Leg Type, TGT_ReEntryType
- Extra Columns: OnExit_OpenAllLegs, OnEntry_SqOffDelay, OnEntry_OpenTradeOn, OnExit_SqOffDelay, ReEntryType, OnEntry_OpenTradeDelay, OnExit_SqOffAllLegs, OnExit_SqOffTradeOff, OnEntry_SqOffTradeOff, OnExit_OpenTradeOn, OnEntry_SqOffAllLegs, OnExit_OpenTradeDelay, ReEnteriesCount

**Validation Issues:**
- StrikeMethod: Invalid enum value at row 0: atm not in ['ATM', 'ITM1-n', 'OTM1-n', 'FIXED', 'PREMIUM', 'ATM WIDTH', 'DELTA']

## Key Findings

1. **Actual vs Documented Structure**:
   - The actual Excel files have slightly different column names than documented
   - Some columns are optional and may not appear in all files
   - Date/time formats vary between implementations

2. **Archive vs GPU Differences**:
   - Archive uses "Exit at" and "Exit at.1" columns
   - GPU uses "Exit Price" column
   - Date formats: Archive uses DD_MM_YYYY, GPU uses YYYY-MM-DD

3. **Validation Coverage**:
   - All critical columns are present
   - Data types are correctly handled
   - Enum values need to be expanded to include actual values used
