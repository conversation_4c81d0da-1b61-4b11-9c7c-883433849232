#!/bin/bash

# Enterprise GPU Backtester - Monitoring Setup Script
# Sets up Prometheus and Grafana for production monitoring
# Date: June 6, 2025

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
PROMETHEUS_VERSION="2.45.0"
GRAFANA_VERSION="10.0.0"
NODE_EXPORTER_VERSION="1.6.0"
INSTALL_DIR="/opt/monitoring"
DATA_DIR="/var/lib/monitoring"
CONFIG_DIR="/etc/monitoring"

echo -e "${GREEN}=== Enterprise GPU Backtester Monitoring Setup ===${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    echo -e "${RED}Please run as root (use sudo)${NC}"
    exit 1
fi

# Create directories
echo -e "${YELLOW}Creating directories...${NC}"
mkdir -p $INSTALL_DIR/{prometheus,grafana,exporters}
mkdir -p $DATA_DIR/{prometheus,grafana}
mkdir -p $CONFIG_DIR/{prometheus,grafana,alerts}

# Install Prometheus
echo -e "${YELLOW}Installing Prometheus...${NC}"
cd /tmp
wget https://github.com/prometheus/prometheus/releases/download/v${PROMETHEUS_VERSION}/prometheus-${PROMETHEUS_VERSION}.linux-amd64.tar.gz
tar xzf prometheus-${PROMETHEUS_VERSION}.linux-amd64.tar.gz
cp prometheus-${PROMETHEUS_VERSION}.linux-amd64/{prometheus,promtool} $INSTALL_DIR/prometheus/
rm -rf prometheus-${PROMETHEUS_VERSION}.linux-amd64*

# Install Node Exporter
echo -e "${YELLOW}Installing Node Exporter...${NC}"
wget https://github.com/prometheus/node_exporter/releases/download/v${NODE_EXPORTER_VERSION}/node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz
tar xzf node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz
cp node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64/node_exporter $INSTALL_DIR/exporters/
rm -rf node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64*

# Install Grafana
echo -e "${YELLOW}Installing Grafana...${NC}"
apt-get install -y adduser libfontconfig1
wget https://dl.grafana.com/oss/release/grafana_${GRAFANA_VERSION}_amd64.deb
dpkg -i grafana_${GRAFANA_VERSION}_amd64.deb
rm grafana_${GRAFANA_VERSION}_amd64.deb

# Create Prometheus configuration
echo -e "${YELLOW}Creating Prometheus configuration...${NC}"
cat > $CONFIG_DIR/prometheus/prometheus.yml << 'EOF'
# Enterprise GPU Backtester Prometheus Configuration
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'gpu-backtester'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets: ['localhost:9093']

# Load rules
rule_files:
  - "/etc/monitoring/alerts/*.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']

  # GPU Backtester API
  - job_name: 'gpu-backtester-api'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['localhost:8000']

  # Redis
  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']

  # MySQL
  - job_name: 'mysql'
    static_configs:
      - targets: ['localhost:9104']

  # HeavyDB
  - job_name: 'heavydb'
    static_configs:
      - targets: ['localhost:9092']

  # NVIDIA GPU
  - job_name: 'nvidia-gpu'
    static_configs:
      - targets: ['localhost:9835']
EOF

# Create alert rules
echo -e "${YELLOW}Creating alert rules...${NC}"
cat > $CONFIG_DIR/alerts/gpu_backtester.yml << 'EOF'
groups:
  - name: gpu_backtester
    interval: 30s
    rules:
      # API Health
      - alert: APIDown
        expr: up{job="gpu-backtester-api"} == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "GPU Backtester API is down"
          description: "API has been down for more than 5 minutes"

      # High Response Time
      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 0.5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High API response time"
          description: "95th percentile response time is above 500ms"

      # Cache Hit Rate
      - alert: LowCacheHitRate
        expr: cache_hit_rate < 0.7
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is below 70%"

      # GPU Utilization
      - alert: HighGPUUtilization
        expr: gpu_utilization_percent > 90
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "High GPU utilization"
          description: "GPU utilization above 90% for 30 minutes"

      # Memory Usage
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.9
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 90%"

      # Disk Space
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"}) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Less than 10% disk space remaining"

      # Database Connection Pool
      - alert: DatabaseConnectionPoolExhausted
        expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Database connection pool nearly exhausted"
          description: "More than 80% of database connections in use"

      # Backtest Queue
      - alert: BacktestQueueBacklog
        expr: backtest_queue_size > 100
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Large backtest queue backlog"
          description: "More than 100 backtests queued for processing"

      # Error Rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "High error rate"
          description: "Error rate above 5% for 10 minutes"
EOF

# Create systemd service for Prometheus
echo -e "${YELLOW}Creating Prometheus service...${NC}"
cat > /etc/systemd/system/prometheus.service << EOF
[Unit]
Description=Prometheus Server
Documentation=https://prometheus.io/docs/introduction/overview/
After=network-online.target

[Service]
Type=simple
User=prometheus
Group=prometheus
ExecReload=/bin/kill -HUP \$MAINPID
ExecStart=$INSTALL_DIR/prometheus/prometheus \\
  --config.file=$CONFIG_DIR/prometheus/prometheus.yml \\
  --storage.tsdb.path=$DATA_DIR/prometheus \\
  --web.console.templates=$INSTALL_DIR/prometheus/consoles \\
  --web.console.libraries=$INSTALL_DIR/prometheus/console_libraries \\
  --web.listen-address=0.0.0.0:9090 \\
  --web.enable-lifecycle \\
  --storage.tsdb.retention.time=30d

SyslogIdentifier=prometheus
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# Create systemd service for Node Exporter
echo -e "${YELLOW}Creating Node Exporter service...${NC}"
cat > /etc/systemd/system/node_exporter.service << EOF
[Unit]
Description=Node Exporter
Documentation=https://github.com/prometheus/node_exporter
After=network-online.target

[Service]
Type=simple
User=prometheus
Group=prometheus
ExecStart=$INSTALL_DIR/exporters/node_exporter \\
  --collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/) \\
  --collector.netclass.ignored-devices=^(veth.*|docker.*|br-.*)$$ \\
  --collector.systemd \\
  --collector.processes

Restart=always

[Install]
WantedBy=multi-user.target
EOF

# Create Grafana datasource
echo -e "${YELLOW}Configuring Grafana...${NC}"
cat > /etc/grafana/provisioning/datasources/prometheus.yaml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://localhost:9090
    isDefault: true
    editable: true
EOF

# Create GPU Backtester Dashboard
cat > /etc/grafana/provisioning/dashboards/dashboard.yaml << EOF
apiVersion: 1

providers:
  - name: 'GPU Backtester'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    options:
      path: /var/lib/grafana/dashboards
EOF

# Create monitoring user
echo -e "${YELLOW}Creating monitoring user...${NC}"
useradd --no-create-home --shell /bin/false prometheus || true
useradd --no-create-home --shell /bin/false node_exporter || true

# Set permissions
chown -R prometheus:prometheus $INSTALL_DIR/prometheus
chown -R prometheus:prometheus $DATA_DIR/prometheus
chown -R prometheus:prometheus $CONFIG_DIR/prometheus
chown prometheus:prometheus $INSTALL_DIR/exporters/node_exporter

# Enable and start services
echo -e "${YELLOW}Starting services...${NC}"
systemctl daemon-reload
systemctl enable prometheus node_exporter grafana-server
systemctl start prometheus node_exporter grafana-server

# Install additional exporters
echo -e "${YELLOW}Installing additional exporters...${NC}"

# Redis exporter
docker run -d \
  --name redis_exporter \
  --restart always \
  -p 9121:9121 \
  oliver006/redis_exporter:latest \
  --redis.addr=redis://localhost:6379

# MySQL exporter
docker run -d \
  --name mysql_exporter \
  --restart always \
  -p 9104:9104 \
  -e DATA_SOURCE_NAME="exporter:password@(localhost:3306)/" \
  prom/mysqld-exporter:latest

# NVIDIA GPU exporter
docker run -d \
  --name nvidia_gpu_exporter \
  --restart always \
  --gpus all \
  -p 9835:9835 \
  mindprince/nvidia_gpu_prometheus_exporter:0.3.0

# Create GPU Backtester Dashboard JSON
echo -e "${YELLOW}Creating Grafana dashboard...${NC}"
mkdir -p /var/lib/grafana/dashboards
cat > /var/lib/grafana/dashboards/gpu-backtester.json << 'EOF'
{
  "dashboard": {
    "title": "GPU Backtester Monitoring",
    "panels": [
      {
        "title": "API Response Time",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, http_request_duration_seconds_bucket)",
            "legendFormat": "95th percentile"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "title": "Request Rate",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "title": "Cache Hit Rate",
        "targets": [
          {
            "expr": "cache_hit_rate",
            "legendFormat": "Hit Rate %"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
      },
      {
        "title": "GPU Utilization",
        "targets": [
          {
            "expr": "gpu_utilization_percent",
            "legendFormat": "GPU {{gpu_id}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
      },
      {
        "title": "Active Backtests",
        "targets": [
          {
            "expr": "backtest_active_count",
            "legendFormat": "{{strategy_type}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
      },
      {
        "title": "System Resources",
        "targets": [
          {
            "expr": "100 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100)",
            "legendFormat": "Memory Usage %"
          },
          {
            "expr": "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}
      }
    ],
    "refresh": "10s",
    "time": {"from": "now-1h", "to": "now"}
  }
}
EOF

# Set Grafana admin password
echo -e "${YELLOW}Setting Grafana admin password...${NC}"
grafana-cli admin reset-admin-password 'Admin@GPU2025'

# Display access information
echo -e "${GREEN}=== Monitoring Setup Complete ===${NC}"
echo -e "${YELLOW}Access URLs:${NC}"
echo "Prometheus: http://localhost:9090"
echo "Grafana: http://localhost:3000"
echo "  Username: admin"
echo "  Password: Admin@GPU2025"
echo ""
echo -e "${YELLOW}Useful Commands:${NC}"
echo "Check service status: systemctl status prometheus grafana-server node_exporter"
echo "View logs: journalctl -u prometheus -f"
echo "Test alerts: promtool check rules $CONFIG_DIR/alerts/*.yml"
echo ""
echo -e "${GREEN}Next Steps:${NC}"
echo "1. Configure alert notifications in Grafana"
echo "2. Import additional dashboards from grafana.com"
echo "3. Set up Alertmanager for alert routing"
echo "4. Configure data retention policies"

# Create monitoring documentation
cat > /opt/monitoring/README.md << 'EOF'
# GPU Backtester Monitoring

## Architecture
- **Prometheus**: Metrics collection and storage
- **Grafana**: Visualization and alerting
- **Node Exporter**: System metrics
- **Custom Exporters**: Application-specific metrics

## Key Metrics
1. **API Performance**
   - Response time (p50, p95, p99)
   - Request rate
   - Error rate

2. **System Resources**
   - CPU usage
   - Memory usage
   - Disk I/O
   - Network traffic

3. **GPU Metrics**
   - GPU utilization
   - GPU memory usage
   - GPU temperature

4. **Application Metrics**
   - Active backtests
   - Queue size
   - Cache hit rate
   - Database connections

## Alert Configuration
Alerts are defined in `/etc/monitoring/alerts/`

## Maintenance
- Prometheus data retention: 30 days
- Grafana backup: Daily at 2 AM
- Log rotation: Weekly

## Troubleshooting
1. Check service status: `systemctl status <service>`
2. View logs: `journalctl -u <service> -f`
3. Test connectivity: `curl http://localhost:<port>/metrics`
EOF

echo -e "${GREEN}✅ Monitoring setup completed successfully!${NC}"