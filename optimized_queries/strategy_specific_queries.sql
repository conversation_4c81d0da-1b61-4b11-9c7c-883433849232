-- Strategy-Specific Optimized Queries for Enterprise GPU Backtester
-- Created: June 6, 2025
-- Optimized for HeavyDB GPU acceleration

-- ============================================
-- TBS (TRADE BUILDER STRATEGY) QUERIES
-- ============================================

-- TBS Multi-leg Position Query
CREATE OR REPLACE VIEW vw_tbs_positions AS
WITH leg_positions AS (
    SELECT 
        trade_date,
        ticker,
        strategy_name,
        leg_id,
        CASE 
            WHEN position_type = 'BUY' THEN 1
            WHEN position_type = 'SELL' THEN -1
            ELSE 0
        END as position_multiplier,
        strike_price,
        option_type,
        expiry_date,
        quantity,
        close as current_price,
        timestamp_value
    FROM nifty_option_chain
    WHERE ticker IN ('NIFTY', 'BANKNIFTY', 'FINNIFTY')
)
SELECT 
    trade_date,
    ticker,
    strategy_name,
    SUM(position_multiplier * quantity * current_price) as net_position_value,
    SUM(ABS(quantity)) as total_contracts,
    COUNT(DISTINCT leg_id) as active_legs,
    MAX(timestamp_value) as last_update
FROM leg_positions
GROUP BY trade_date, ticker, strategy_name;

-- TBS Exit Time Optimization
PREPARE tbs_exit_check AS
SELECT 
    ticker,
    strike_price,
    option_type,
    close,
    high,
    low,
    CASE 
        WHEN close >= ? THEN 'SL_HIT'
        WHEN close <= ? THEN 'TP_HIT'
        WHEN EXTRACT(HOUR FROM timestamp_value) = ? 
         AND EXTRACT(MINUTE FROM timestamp_value) >= ? THEN 'TIME_EXIT'
        ELSE 'ACTIVE'
    END as exit_status
FROM nifty_option_chain
WHERE ticker = ?
  AND trade_date = ?
  AND strike_price = ?
  AND option_type = ?
  AND timestamp_value BETWEEN ? AND ?
ORDER BY timestamp_value;

-- ============================================
-- TV (TRADINGVIEW) QUERIES
-- ============================================

-- TV Signal Processing Query
CREATE OR REPLACE VIEW vw_tv_signals AS
WITH signal_windows AS (
    SELECT 
        trade_date,
        ticker,
        timestamp_value,
        signal_type,
        strike_price,
        option_type,
        LEAD(timestamp_value) OVER (
            PARTITION BY ticker, trade_date 
            ORDER BY timestamp_value
        ) as next_signal_time
    FROM tv_signals
)
SELECT 
    sw.trade_date,
    sw.ticker,
    sw.signal_type,
    sw.strike_price,
    sw.option_type,
    sw.timestamp_value as signal_time,
    AVG(oc.close) as avg_price,
    SUM(oc.volume) as total_volume,
    COUNT(*) as tick_count
FROM signal_windows sw
JOIN nifty_option_chain oc
    ON sw.ticker = oc.ticker
    AND sw.trade_date = oc.trade_date
    AND sw.strike_price = oc.strike_price
    AND sw.option_type = oc.option_type
    AND oc.timestamp_value BETWEEN sw.timestamp_value 
        AND COALESCE(sw.next_signal_time, sw.timestamp_value + INTERVAL '1' DAY)
GROUP BY sw.trade_date, sw.ticker, sw.signal_type, 
         sw.strike_price, sw.option_type, sw.timestamp_value;

-- TV Portfolio Performance Query
PREPARE tv_portfolio_performance AS
WITH position_pnl AS (
    SELECT 
        portfolio_id,
        strategy_id,
        trade_date,
        SUM(CASE WHEN action = 'BUY' THEN -1 * quantity * price
                 WHEN action = 'SELL' THEN quantity * price
                 ELSE 0 END) as realized_pnl,
        SUM(CASE WHEN position_status = 'OPEN' 
                 THEN quantity * (current_price - entry_price)
                 ELSE 0 END) as unrealized_pnl
    FROM tv_positions
    WHERE portfolio_id = ?
      AND trade_date BETWEEN ? AND ?
    GROUP BY portfolio_id, strategy_id, trade_date
)
SELECT 
    trade_date,
    COUNT(DISTINCT strategy_id) as active_strategies,
    SUM(realized_pnl) as total_realized,
    SUM(unrealized_pnl) as total_unrealized,
    SUM(realized_pnl + unrealized_pnl) as total_pnl,
    MAX(realized_pnl + unrealized_pnl) as max_pnl,
    MIN(realized_pnl + unrealized_pnl) as min_pnl
FROM position_pnl
GROUP BY trade_date
ORDER BY trade_date;

-- ============================================
-- ORB (OPENING RANGE BREAKOUT) QUERIES
-- ============================================

-- ORB Range Calculation Query
CREATE OR REPLACE VIEW vw_orb_ranges AS
WITH opening_range AS (
    SELECT 
        trade_date,
        ticker,
        strike_price,
        option_type,
        MIN(low) as range_low,
        MAX(high) as range_high,
        FIRST(open) as opening_price,
        AVG(volume) as avg_volume
    FROM nifty_option_chain
    WHERE EXTRACT(HOUR FROM timestamp_value) = 9
      AND EXTRACT(MINUTE FROM timestamp_value) BETWEEN 15 AND 30
    GROUP BY trade_date, ticker, strike_price, option_type
)
SELECT 
    *,
    range_high - range_low as range_size,
    (range_high + range_low) / 2 as range_midpoint,
    range_high + (range_high - range_low) * 0.5 as upper_target,
    range_low - (range_high - range_low) * 0.5 as lower_target
FROM opening_range;

-- ORB Breakout Detection Query
PREPARE orb_breakout_detection AS
WITH orb_levels AS (
    SELECT * FROM vw_orb_ranges
    WHERE trade_date = ?
      AND ticker = ?
)
SELECT 
    oc.timestamp_value,
    oc.strike_price,
    oc.option_type,
    oc.close,
    CASE 
        WHEN oc.high > ol.range_high AND oc.close > ol.range_high THEN 'BULLISH_BREAKOUT'
        WHEN oc.low < ol.range_low AND oc.close < ol.range_low THEN 'BEARISH_BREAKOUT'
        ELSE 'WITHIN_RANGE'
    END as breakout_status,
    oc.volume,
    ol.range_high,
    ol.range_low
FROM nifty_option_chain oc
JOIN orb_levels ol
    ON oc.ticker = ol.ticker
    AND oc.strike_price = ol.strike_price
    AND oc.option_type = ol.option_type
WHERE oc.trade_date = ?
  AND oc.ticker = ?
  AND oc.timestamp_value > CAST(? || ' 09:30:00' AS TIMESTAMP)
ORDER BY oc.timestamp_value, oc.strike_price;

-- ============================================
-- OI (OPEN INTEREST) QUERIES
-- ============================================

-- OI Analysis for Support/Resistance
CREATE OR REPLACE VIEW vw_oi_analysis AS
WITH oi_changes AS (
    SELECT 
        trade_date,
        ticker,
        strike_price,
        option_type,
        open_interest,
        LAG(open_interest) OVER (
            PARTITION BY ticker, strike_price, option_type 
            ORDER BY trade_date
        ) as prev_oi,
        change_in_oi
    FROM nifty_option_chain
    WHERE expiry_date >= CURRENT_DATE
)
SELECT 
    trade_date,
    ticker,
    strike_price,
    option_type,
    open_interest,
    COALESCE(change_in_oi, open_interest - prev_oi, 0) as oi_change,
    CASE 
        WHEN option_type = 'CE' AND open_interest > 1000000 THEN 'STRONG_RESISTANCE'
        WHEN option_type = 'PE' AND open_interest > 1000000 THEN 'STRONG_SUPPORT'
        WHEN option_type = 'CE' AND open_interest > 500000 THEN 'RESISTANCE'
        WHEN option_type = 'PE' AND open_interest > 500000 THEN 'SUPPORT'
        ELSE 'NEUTRAL'
    END as level_type
FROM oi_changes;

-- OI-Based Entry Signal Query
PREPARE oi_entry_signals AS
WITH oi_metrics AS (
    SELECT 
        trade_date,
        ticker,
        strike_price,
        MAX(CASE WHEN option_type = 'CE' THEN open_interest END) as ce_oi,
        MAX(CASE WHEN option_type = 'PE' THEN open_interest END) as pe_oi,
        MAX(CASE WHEN option_type = 'CE' THEN change_in_oi END) as ce_oi_change,
        MAX(CASE WHEN option_type = 'PE' THEN change_in_oi END) as pe_oi_change
    FROM nifty_option_chain
    WHERE ticker = ?
      AND trade_date = ?
      AND expiry_date = ?
    GROUP BY trade_date, ticker, strike_price
)
SELECT 
    strike_price,
    ce_oi,
    pe_oi,
    pe_oi - ce_oi as pcr_oi,
    CASE 
        WHEN pe_oi > ce_oi * 1.5 THEN 'BULLISH'
        WHEN ce_oi > pe_oi * 1.5 THEN 'BEARISH'
        ELSE 'NEUTRAL'
    END as oi_signal,
    ce_oi_change,
    pe_oi_change,
    CASE 
        WHEN pe_oi_change > 0 AND ce_oi_change < 0 THEN 'LONG_BUILDUP'
        WHEN ce_oi_change > 0 AND pe_oi_change < 0 THEN 'SHORT_BUILDUP'
        WHEN pe_oi_change < 0 AND ce_oi_change < 0 THEN 'UNWINDING'
        ELSE 'MIXED'
    END as position_status
FROM oi_metrics
WHERE ce_oi > 0 OR pe_oi > 0
ORDER BY strike_price;

-- ============================================
-- CROSS-STRATEGY OPTIMIZATION QUERIES
-- ============================================

-- Multi-Strategy Performance Comparison
CREATE OR REPLACE VIEW vw_strategy_comparison AS
WITH strategy_results AS (
    SELECT 
        'TBS' as strategy_type,
        trade_date,
        SUM(pnl) as daily_pnl,
        COUNT(DISTINCT strategy_id) as active_strategies,
        AVG(win_rate) as avg_win_rate
    FROM tbs_results
    GROUP BY trade_date
    
    UNION ALL
    
    SELECT 
        'TV' as strategy_type,
        trade_date,
        SUM(pnl) as daily_pnl,
        COUNT(DISTINCT strategy_id) as active_strategies,
        AVG(win_rate) as avg_win_rate
    FROM tv_results
    GROUP BY trade_date
    
    UNION ALL
    
    SELECT 
        'ORB' as strategy_type,
        trade_date,
        SUM(pnl) as daily_pnl,
        COUNT(DISTINCT strategy_id) as active_strategies,
        AVG(win_rate) as avg_win_rate
    FROM orb_results
    GROUP BY trade_date
    
    UNION ALL
    
    SELECT 
        'OI' as strategy_type,
        trade_date,
        SUM(pnl) as daily_pnl,
        COUNT(DISTINCT strategy_id) as active_strategies,
        AVG(win_rate) as avg_win_rate
    FROM oi_results
    GROUP BY trade_date
)
SELECT 
    strategy_type,
    COUNT(DISTINCT trade_date) as trading_days,
    SUM(daily_pnl) as total_pnl,
    AVG(daily_pnl) as avg_daily_pnl,
    STDDEV(daily_pnl) as pnl_volatility,
    AVG(daily_pnl) / NULLIF(STDDEV(daily_pnl), 0) as sharpe_ratio,
    MAX(daily_pnl) as max_daily_gain,
    MIN(daily_pnl) as max_daily_loss,
    AVG(active_strategies) as avg_active_strategies,
    AVG(avg_win_rate) as overall_win_rate
FROM strategy_results
GROUP BY strategy_type;

-- ============================================
-- PERFORMANCE MONITORING QUERIES
-- ============================================

-- GPU Query Performance Tracker
CREATE TABLE IF NOT EXISTS gpu_query_performance (
    query_id SERIAL PRIMARY KEY,
    strategy_type TEXT,
    query_name TEXT,
    execution_time_ms INT,
    gpu_time_ms INT,
    cpu_time_ms INT,
    rows_processed BIGINT,
    gpu_memory_used_mb INT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert performance metrics
CREATE OR REPLACE FUNCTION log_query_performance(
    p_strategy_type TEXT,
    p_query_name TEXT,
    p_start_time TIMESTAMP
) RETURNS VOID AS $$
DECLARE
    v_execution_time INT;
    v_gpu_info RECORD;
BEGIN
    v_execution_time := EXTRACT(MILLISECOND FROM CLOCK_TIMESTAMP() - p_start_time);
    
    SELECT * INTO v_gpu_info 
    FROM information_schema.executor_resource_info 
    LIMIT 1;
    
    INSERT INTO gpu_query_performance (
        strategy_type,
        query_name,
        execution_time_ms,
        gpu_memory_used_mb
    ) VALUES (
        p_strategy_type,
        p_query_name,
        v_execution_time,
        v_gpu_info.gpu_allocated_memory / 1024 / 1024
    );
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- REAL-TIME MONITORING QUERIES
-- ============================================

-- Active Backtest Monitor
CREATE OR REPLACE VIEW vw_active_backtests AS
SELECT 
    b.backtest_id,
    b.strategy_type,
    b.start_time,
    b.status,
    b.progress_percent,
    COUNT(DISTINCT t.trade_id) as trades_processed,
    SUM(t.pnl) as current_pnl,
    MAX(t.timestamp) as last_update
FROM backtests b
LEFT JOIN trades t ON b.backtest_id = t.backtest_id
WHERE b.status IN ('RUNNING', 'QUEUED')
GROUP BY b.backtest_id, b.strategy_type, b.start_time, b.status, b.progress_percent;

-- Resource Utilization View
CREATE OR REPLACE VIEW vw_resource_utilization AS
SELECT 
    'GPU' as resource_type,
    gpu_utilization as utilization_percent,
    gpu_memory_used / gpu_memory_total * 100 as memory_percent,
    gpu_temperature as temperature
FROM gpu_metrics
WHERE timestamp >= CURRENT_TIMESTAMP - INTERVAL '5' MINUTE

UNION ALL

SELECT 
    'CPU' as resource_type,
    cpu_utilization as utilization_percent,
    ram_used / ram_total * 100 as memory_percent,
    NULL as temperature
FROM system_metrics
WHERE timestamp >= CURRENT_TIMESTAMP - INTERVAL '5' MINUTE;

-- ============================================
-- OPTIMIZATION HINTS
-- ============================================

-- Create function to suggest query optimizations
CREATE OR REPLACE FUNCTION suggest_optimizations(
    p_query_text TEXT
) RETURNS TABLE (
    suggestion TEXT,
    priority TEXT
) AS $$
BEGIN
    -- Check for missing indexes
    IF p_query_text LIKE '%WHERE%trade_date%' 
       AND NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_trade_date') THEN
        RETURN QUERY SELECT 'Create index on trade_date', 'HIGH';
    END IF;
    
    -- Check for full table scans
    IF p_query_text NOT LIKE '%WHERE%' AND p_query_text LIKE '%FROM nifty_option_chain%' THEN
        RETURN QUERY SELECT 'Add WHERE clause to avoid full table scan', 'HIGH';
    END IF;
    
    -- Check for GPU-friendly operations
    IF p_query_text LIKE '%GROUP BY%' OR p_query_text LIKE '%SUM(%' THEN
        RETURN QUERY SELECT 'Query is GPU-optimized for aggregations', 'INFO';
    END IF;
    
    -- Check for date functions
    IF p_query_text LIKE '%EXTRACT(%' THEN
        RETURN QUERY SELECT 'Consider using date columns directly instead of EXTRACT', 'MEDIUM';
    END IF;
END;
$$ LANGUAGE plpgsql;