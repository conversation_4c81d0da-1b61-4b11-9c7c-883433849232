-- Optimized Queries for Enterprise GPU Backtester
-- Created: June 6, 2025
-- Target: HeavyDB with GPU acceleration

-- ============================================
-- 1. ATM STRIKE SELECTION QUERIES
-- ============================================

-- Fast ATM selection for specific date
CREATE OR REPLACE VIEW vw_atm_strikes AS
WITH spot_prices AS (
    SELECT 
        trade_date,
        ticker,
        MAX(underlying_value) as spot_price
    FROM nifty_option_chain
    GROUP BY trade_date, ticker
)
SELECT 
    oc.trade_date,
    oc.ticker,
    oc.strike_price,
    oc.option_type,
    oc.expiry_date,
    ABS(oc.strike_price - sp.spot_price) as moneyness,
    oc.close,
    oc.volume,
    oc.open_interest
FROM nifty_option_chain oc
JOIN spot_prices sp 
    ON oc.trade_date = sp.trade_date 
    AND oc.ticker = sp.ticker
WHERE ABS(oc.strike_price - sp.spot_price) <= 500;

-- Query to get ATM strikes
SELECT * FROM vw_atm_strikes
WHERE trade_date = DATE '2024-06-01'
  AND ticker = 'NIFTY'
  AND option_type = 'CE'
ORDER BY moneyness
LIMIT 1;

-- ============================================
-- 2. MULTI-LEG STRATEGY QUERIES
-- ============================================

-- Iron Condor setup query
WITH atm_strike AS (
    SELECT strike_price as atm
    FROM vw_atm_strikes
    WHERE trade_date = DATE '2024-06-01'
      AND ticker = 'NIFTY'
      AND option_type = 'CE'
    ORDER BY moneyness
    LIMIT 1
)
SELECT 
    'SELL_CE' as leg,
    ce1.strike_price,
    ce1.close as premium,
    ce1.open_interest
FROM nifty_option_chain ce1, atm_strike
WHERE ce1.trade_date = DATE '2024-06-01'
  AND ce1.ticker = 'NIFTY'
  AND ce1.option_type = 'CE'
  AND ce1.strike_price = atm.atm + 100
UNION ALL
SELECT 
    'BUY_CE' as leg,
    ce2.strike_price,
    -ce2.close as premium,
    ce2.open_interest
FROM nifty_option_chain ce2, atm_strike
WHERE ce2.trade_date = DATE '2024-06-01'
  AND ce2.ticker = 'NIFTY'
  AND ce2.option_type = 'CE'
  AND ce2.strike_price = atm.atm + 200
UNION ALL
SELECT 
    'SELL_PE' as leg,
    pe1.strike_price,
    pe1.close as premium,
    pe1.open_interest
FROM nifty_option_chain pe1, atm_strike
WHERE pe1.trade_date = DATE '2024-06-01'
  AND pe1.ticker = 'NIFTY'
  AND pe1.option_type = 'PE'
  AND pe1.strike_price = atm.atm - 100
UNION ALL
SELECT 
    'BUY_PE' as leg,
    pe2.strike_price,
    -pe2.close as premium,
    pe2.open_interest
FROM nifty_option_chain pe2, atm_strike
WHERE pe2.trade_date = DATE '2024-06-01'
  AND pe2.ticker = 'NIFTY'
  AND pe2.option_type = 'PE'
  AND pe2.strike_price = atm.atm - 200;

-- ============================================
-- 3. INTRADAY PRICE MOVEMENT QUERIES
-- ============================================

-- Get minute-level price data efficiently
SELECT 
    DATE_TRUNC('minute', timestamp_value) as minute,
    strike_price,
    option_type,
    FIRST(open) as open,
    MAX(high) as high,
    MIN(low) as low,
    LAST(close) as close,
    SUM(volume) as volume
FROM nifty_option_chain
WHERE ticker = 'NIFTY'
  AND trade_date = DATE '2024-06-01'
  AND strike_price IN (22000, 22100, 22200)
  AND option_type = 'CE'
GROUP BY DATE_TRUNC('minute', timestamp_value), strike_price, option_type
ORDER BY minute;

-- ============================================
-- 4. OPEN INTEREST ANALYSIS
-- ============================================

-- Max OI analysis for support/resistance
SELECT 
    'CALL_RESISTANCE' as analysis_type,
    strike_price,
    SUM(open_interest) as total_oi,
    SUM(change_in_oi) as oi_change
FROM nifty_option_chain
WHERE ticker = 'NIFTY'
  AND trade_date = DATE '2024-06-01'
  AND option_type = 'CE'
  AND expiry_date = DATE '2024-06-27'
GROUP BY strike_price
ORDER BY total_oi DESC
LIMIT 3
UNION ALL
SELECT 
    'PUT_SUPPORT' as analysis_type,
    strike_price,
    SUM(open_interest) as total_oi,
    SUM(change_in_oi) as oi_change
FROM nifty_option_chain
WHERE ticker = 'NIFTY'
  AND trade_date = DATE '2024-06-01'
  AND option_type = 'PE'
  AND expiry_date = DATE '2024-06-27'
GROUP BY strike_price
ORDER BY total_oi DESC
LIMIT 3;

-- ============================================
-- 5. EXPIRY SELECTION QUERIES
-- ============================================

-- Get nearest weekly/monthly expiries
WITH expiry_classification AS (
    SELECT DISTINCT
        expiry_date,
        ticker,
        CASE 
            WHEN EXTRACT(DAY FROM expiry_date) <= 7 THEN 'WEEKLY'
            ELSE 'MONTHLY'
        END as expiry_type,
        expiry_date - CURRENT_DATE as days_to_expiry
    FROM nifty_option_chain
    WHERE ticker = 'NIFTY'
      AND expiry_date >= CURRENT_DATE
)
SELECT * FROM expiry_classification
ORDER BY days_to_expiry
LIMIT 5;

-- ============================================
-- 6. HISTORICAL BACKTEST QUERIES
-- ============================================

-- Prepared statement for backtesting
PREPARE backtest_data AS
SELECT 
    trade_date,
    timestamp_value,
    strike_price,
    option_type,
    open,
    high,
    low,
    close,
    volume,
    open_interest,
    underlying_value
FROM nifty_option_chain
WHERE ticker = ?
  AND trade_date BETWEEN ? AND ?
  AND expiry_date = ?
  AND option_type IN (?, ?)
  AND strike_price BETWEEN ? AND ?
ORDER BY timestamp_value, strike_price;

-- Execute for specific backtest
EXECUTE backtest_data USING 
    'NIFTY',                    -- ticker
    '2024-01-01', '2024-01-31', -- date range
    '2024-01-25',               -- expiry
    'CE', 'PE',                 -- option types
    21000, 23000;               -- strike range

-- ============================================
-- 7. PERFORMANCE MONITORING QUERIES
-- ============================================

-- Create table for query performance tracking
CREATE TABLE IF NOT EXISTS query_performance_log (
    id SERIAL PRIMARY KEY,
    query_type TEXT,
    execution_time_ms INT,
    rows_processed BIGINT,
    gpu_utilized BOOLEAN,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Monitor fragment efficiency
SELECT 
    COUNT(DISTINCT fragment_id) as fragment_count,
    MIN(num_rows) as min_rows_per_fragment,
    MAX(num_rows) as max_rows_per_fragment,
    AVG(num_rows) as avg_rows_per_fragment,
    SUM(num_bytes) / 1024 / 1024 as total_mb
FROM information_schema.fragments
WHERE table_name = 'nifty_option_chain';

-- ============================================
-- 8. REAL-TIME DATA QUERIES
-- ============================================

-- Latest tick data for active monitoring
CREATE OR REPLACE VIEW vw_latest_ticks AS
SELECT 
    ticker,
    strike_price,
    option_type,
    close as ltp,
    volume,
    open_interest,
    timestamp_value
FROM nifty_option_chain
WHERE trade_date = CURRENT_DATE
  AND timestamp_value >= CURRENT_TIMESTAMP - INTERVAL '5' MINUTE;

-- Get current market snapshot
SELECT 
    ticker,
    COUNT(DISTINCT strike_price) as active_strikes,
    SUM(volume) as total_volume,
    SUM(open_interest) as total_oi,
    MAX(timestamp_value) as last_update
FROM vw_latest_ticks
GROUP BY ticker;

-- ============================================
-- 9. BATCH PROCESSING QUERIES
-- ============================================

-- Process backtests in batches for memory efficiency
CREATE OR REPLACE FUNCTION process_backtest_batch(
    p_ticker TEXT,
    p_start_date DATE,
    p_end_date DATE,
    p_batch_size INT DEFAULT 100000
) RETURNS TABLE (
    batch_id INT,
    rows_processed BIGINT,
    processing_time_ms INT
) AS $$
DECLARE
    v_offset INT := 0;
    v_batch_id INT := 1;
    v_start_time TIMESTAMP;
    v_rows_processed BIGINT;
BEGIN
    LOOP
        v_start_time := CLOCK_TIMESTAMP();
        
        -- Process batch
        SELECT COUNT(*) INTO v_rows_processed
        FROM nifty_option_chain
        WHERE ticker = p_ticker
          AND trade_date BETWEEN p_start_date AND p_end_date
        LIMIT p_batch_size
        OFFSET v_offset;
        
        IF v_rows_processed = 0 THEN
            EXIT;
        END IF;
        
        -- Return batch info
        RETURN QUERY
        SELECT 
            v_batch_id,
            v_rows_processed,
            EXTRACT(MILLISECOND FROM CLOCK_TIMESTAMP() - v_start_time)::INT;
        
        v_offset := v_offset + p_batch_size;
        v_batch_id := v_batch_id + 1;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- 10. INDEX OPTIMIZATION COMMANDS
-- ============================================

-- Create optimized indexes
CREATE INDEX idx_backtest_primary ON nifty_option_chain (ticker, trade_date, strike_price, option_type);
CREATE INDEX idx_expiry_lookup ON nifty_option_chain (ticker, expiry_date, option_type);
CREATE INDEX idx_timestamp_search ON nifty_option_chain (timestamp_value, ticker);
CREATE BITMAP INDEX idx_option_type_bitmap ON nifty_option_chain (option_type);

-- Analyze table for query optimization
ANALYZE nifty_option_chain;

-- Optimize table fragments
OPTIMIZE TABLE nifty_option_chain;