"""Leg model representing a single option leg in a strategy."""
from __future__ import annotations

from dataclasses import dataclass, asdict
from typing import Any, Dict

from .common import OptionType, TransactionType, StrikeRule, ExpiryRule
from .time_window import EntryWindow, ExitWindow
from .risk import RiskRule

__all__ = ["LegModel"]


@dataclass(frozen=True)
class LegModel:
    """Immutable data class capturing all relevant parameters for an option leg."""

    leg_id: str
    index: str  # e.g. NIFTY, BANKNIFTY
    option_type: OptionType
    transaction: TransactionType
    quantity: int
    strike_rule: StrikeRule
    expiry_rule: ExpiryRule
    entry_window: EntryWindow
    exit_window: ExitWindow
    risk: RiskRule | None = None

    def __post_init__(self):  # type: ignore[method-assign]
        if self.quantity <= 0:
            raise ValueError("quantity must be positive")
        if self.entry_window.seconds > self.exit_window.seconds:
            raise ValueError("entry_window cannot be after exit_window")

    # --------- serialization helpers ---------
    def to_dict(self) -> Dict[str, Any]:
        d = asdict(self)
        # Enums are serialised to their value for portability
        d["option_type"] = self.option_type.value
        d["transaction"] = self.transaction.value
        d["strike_rule"] = self.strike_rule.value
        d["expiry_rule"] = self.expiry_rule.value
        d["entry_window"] = {
            "time": self.entry_window.time_str,
            "seconds": self.entry_window.seconds,
        }
        d["exit_window"] = {
            "time": self.exit_window.time_str,
            "seconds": self.exit_window.seconds,
        }
        if self.risk:
            d["risk"] = self.risk.to_dict()
        return d

    @classmethod
    def from_dict(cls, d: Dict[str, Any]) -> "LegModel":
        return cls(
            leg_id=str(d["leg_id"]),
            index=str(d["index"]),
            option_type=OptionType(d["option_type"]),
            transaction=TransactionType(d["transaction"]),
            quantity=int(d["quantity"]),
            strike_rule=StrikeRule(d["strike_rule"]),
            expiry_rule=ExpiryRule(d["expiry_rule"]),
            entry_window=EntryWindow(time=d["entry_window"]["time"]),
            exit_window=ExitWindow(time=d["exit_window"]["time"]),
            risk=RiskRule.from_dict(d["risk"]) if d.get("risk") else None,
        ) 