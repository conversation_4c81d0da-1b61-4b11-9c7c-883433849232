"""Common enums used across portfolio back-tester models.

These are *initial* placeholders and will be extended as Phase-0 progresses.
"""
from __future__ import annotations

from enum import Enum

__all__ = [
    "StrikeRule",
    "ExpiryRule",
    "OptionType",
    "TransactionType",
]


class OptionType(str, Enum):
    """Call or Put option."""

    CALL = "CALL"
    PUT = "PUT"


class TransactionType(str, Enum):
    """Long or short transaction side."""

    BUY = "BUY"
    SELL = "SELL"


class StrikeRule(str, Enum):
    """High-level strike selection rule names.

    The exact list will grow; these cover the archive engine's core variants.
    """

    ATM = "ATM"  # at-the-money
    ITM = "ITM"  # in-the-money n levels handled via params
    OTM = "OTM"  # out-of-the-money
    DELTA_TARGET = "DELTA_TARGET"  # closest delta value
    FIXED = "FIXED"  # fixed strike price
    PREMIUM_TARGET = "PREMIUM_TARGET"  # target option premium
    PREMIUM_DIFF = "PREMIUM_DIFF"  # premium difference vs underlying


class ExpiryRule(str, Enum):
    """Expiry selection rule variants."""

    CURRENT_WEEK = "CURRENT_WEEK"
    NEXT_WEEK = "NEXT_WEEK"
    WEEKLY = "WEEKLY"
    MONTHLY = "MONTHLY"
    DTE_TARGET = "DTE_TARGET"  # specific DTE value
    FIXED_DATE = "FIXED_DATE"  # explicit expiry date 