"""Time window models for entry and exit times.

Supports input as either HH:MM:SS string or integer seconds (0-86399).
"""
from __future__ import annotations

import re
from datetime import time
from typing import Any, Union
from dataclasses import dataclass

__all__ = [
    "EntryWindow",
    "ExitWindow",
]

_TIME_PATTERN = re.compile(r"^(\d{2}):(\d{2}):(\d{2})$")
_SECONDS_IN_DAY = 24 * 60 * 60


def _hhmmss_to_seconds(value: str) -> int:
    match = _TIME_PATTERN.match(value)
    if not match:
        raise ValueError(f"Invalid HH:MM:SS string: {value}")
    h, m, s = map(int, match.groups())
    seconds = h * 3600 + m * 60 + s
    if seconds >= _SECONDS_IN_DAY:
        raise ValueError("Time exceeds 23:59:59")
    return seconds


def _seconds_to_hhmmss(seconds: int) -> str:
    if not 0 <= seconds < _SECONDS_IN_DAY:
        raise ValueError("seconds must be between 0 and 86399 inclusive")
    h = seconds // 3600
    m = (seconds % 3600) // 60
    s = seconds % 60
    return f"{h:02d}:{m:02d}:{s:02d}"


@dataclass(frozen=True)
class _TimeWindowBase:
    """Immutable base for entry/exit time windows."""

    time_str: str
    seconds: int

    def __post_init__(self):  # type: ignore[method-assign]
        object.__setattr__(self, "seconds", _hhmmss_to_seconds(self.time_str))

    @classmethod
    def from_seconds(cls, seconds: int) -> "_TimeWindowBase":
        return cls(time_str=_seconds_to_hhmmss(seconds), seconds=seconds)

    def __str__(self) -> str:  # pragma: no cover
        return self.time_str


class EntryWindow(_TimeWindowBase):
    """Entry window start time (inclusive)."""

    def __init__(self, *, time: str | None = None, seconds: int | None = None):  # type: ignore[override]
        if time is None and seconds is None:
            raise ValueError("Provide either time=HH:MM:SS or seconds=<int>")
        if time is not None and seconds is not None:
            raise ValueError("Provide only one of time or seconds")
        if time is not None:
            super().__init__(time_str=time, seconds=_hhmmss_to_seconds(time))
        else:
            if not 0 <= seconds < _SECONDS_IN_DAY:  # type: ignore[operator]
                raise ValueError("seconds out of bounds (0..86399)")
            super().__init__(time_str=_seconds_to_hhmmss(seconds), seconds=seconds)  # type: ignore[arg-type]


class ExitWindow(_TimeWindowBase):
    """Exit window end time (inclusive)."""

    def __init__(self, *, time: str | None = None, seconds: int | None = None):  # type: ignore[override]
        if time is None and seconds is None:
            raise ValueError("Provide either time=HH:MM:SS or seconds=<int>")
        if time is not None and seconds is not None:
            raise ValueError("Provide only one of time or seconds")
        if time is not None:
            super().__init__(time_str=time, seconds=_hhmmss_to_seconds(time))
        else:
            if not 0 <= seconds < _SECONDS_IN_DAY:  # type: ignore[operator]
                raise ValueError("seconds out of bounds (0..86399)")
            super().__init__(time_str=_seconds_to_hhmmss(seconds), seconds=seconds)  # type: ignore[arg-type] 