"""Risk rule model definitions.

Initial MVP supports three rule kinds:
• STOP_LOSS  – exit when price moves adverse by threshold % or absolute value.
• TAKE_PROFIT – exit when favourable move hits threshold.
• TRAILING   – dynamic stop that moves in favour of position.

The structure is intentionally simple for Phase-0; more fields will be added during
Phase-4 (Risk Rules Engine).
"""
from __future__ import annotations

from dataclasses import dataclass, asdict
from enum import Enum
from typing import Any, Dict

__all__ = [
    "RuleKind",
    "RiskRule",
]


class RuleKind(str, Enum):
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"
    TRAILING = "TRAILING"


@dataclass(frozen=True)
class RiskRule:
    """Generic risk rule configuration.

    Attributes
    ----------
    kind : RuleKind
        The type of risk rule.
    threshold : float
        Value interpretation depends on *kind* (e.g. price diff, pct).
    reentry_allowed : bool
        Whether strategy is allowed to re-enter after this rule triggers.
    trailing_step : float | None
        For trailing stops: the step size by which stop moves once price
        advances in favour of position.  Only meaningful for TRAILING.
    """

    kind: RuleKind
    threshold: float
    reentry_allowed: bool = False
    trailing_step: float | None = None

    # --------- helpers ---------
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    @classmethod
    def from_dict(cls, d: Dict[str, Any]) -> "RiskRule":
        return cls(kind=RuleKind(d["kind"]), threshold=float(d["threshold"]), reentry_allowed=bool(d.get("reentry_allowed", False)), trailing_step=d.get("trailing_step"))

def evaluate_risk_rule(trade_ticks_df, risk_rule, entry_price, is_long):
    """
    Evaluate the risk rule (SL/TP/Trail) on the given tick DataFrame.
    Args:
        trade_ticks_df: DataFrame with at least ['datetime', 'close'] columns, sorted by time ascending.
        risk_rule: RiskRule instance.
        entry_price: float, the entry price for the leg.
        is_long: bool, True if position is long, False if short.
    Returns:
        (exit_price, exit_time, triggered): tuple
            exit_price: price at which rule triggered (or None)
            exit_time: timestamp at which rule triggered (or None)
            triggered: bool, True if rule triggered, else False
    """
    if risk_rule is None or trade_ticks_df.empty:
        return None, None, False
    threshold = risk_rule.threshold
    if risk_rule.kind == RuleKind.STOP_LOSS:
        # For long: exit if price <= entry - threshold; for short: price >= entry + threshold
        if is_long:
            mask = trade_ticks_df['close'] <= entry_price - threshold
        else:
            mask = trade_ticks_df['close'] >= entry_price + threshold
        triggered_ticks = trade_ticks_df[mask]
        if not triggered_ticks.empty:
            row = triggered_ticks.iloc[0]
            return row['close'], row['datetime'], True
    elif risk_rule.kind == RuleKind.TAKE_PROFIT:
        # For long: exit if price >= entry + threshold; for short: price <= entry - threshold
        if is_long:
            mask = trade_ticks_df['close'] >= entry_price + threshold
        else:
            mask = trade_ticks_df['close'] <= entry_price - threshold
        triggered_ticks = trade_ticks_df[mask]
        if not triggered_ticks.empty:
            row = triggered_ticks.iloc[0]
            return row['close'], row['datetime'], True
    elif risk_rule.kind == RuleKind.TRAILING:
        # Trailing stop logic
        trail = risk_rule.trailing_step or threshold
        if is_long:
            stop = entry_price - threshold
            max_price = entry_price
            for idx, row in trade_ticks_df.iterrows():
                price = row['close']
                if price > max_price:
                    max_price = price
                    stop = max(stop, max_price - trail)
                if price <= stop:
                    return price, row['datetime'], True
        else:
            stop = entry_price + threshold
            min_price = entry_price
            for idx, row in trade_ticks_df.iterrows():
                price = row['close']
                if price < min_price:
                    min_price = price
                    stop = min(stop, min_price + trail)
                if price >= stop:
                    return price, row['datetime'], True
    return None, None, False 