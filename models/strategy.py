"""Strategy model representing a collection of legs plus strategy-level metadata."""
from __future__ import annotations

from dataclasses import dataclass, field, asdict
from typing import Any, Dict, List

from .leg import LegModel

__all__ = ["StrategyModel"]


@dataclass(frozen=True)
class StrategyModel:
    strategy_id: str
    name: str
    legs: List[LegModel] = field(default_factory=list)
    enabled: bool = True

    def __post_init__(self):  # type: ignore[method-assign]
        if not self.legs:
            raise ValueError("Strategy must have at least one leg")
        leg_ids = [l.leg_id for l in self.legs]
        if len(set(leg_ids)) != len(leg_ids):
            raise ValueError("Duplicate leg_id in strategy. IDs must be unique")

    # ---------- helpers ----------
    def to_dict(self) -> Dict[str, Any]:
        return {
            "strategy_id": self.strategy_id,
            "name": self.name,
            "enabled": self.enabled,
            "legs": [leg.to_dict() for leg in self.legs],
        }

    @classmethod
    def from_dict(cls, d: Dict[str, Any]) -> "StrategyModel":
        return cls(
            strategy_id=str(d["strategy_id"]),
            name=str(d["name"]),
            enabled=bool(d.get("enabled", True)),
            legs=[LegModel.from_dict(leg) for leg in d["legs"]],
        ) 