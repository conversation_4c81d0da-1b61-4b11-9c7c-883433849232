"""Portfolio model comprising multiple strategies plus global settings."""
from __future__ import annotations

from dataclasses import dataclass, field
from typing import Any, Dict, List

from .strategy import StrategyModel
from .risk import RiskRule

__all__ = ["PortfolioModel"]


@dataclass(frozen=True)
class PortfolioModel:
    portfolio_id: str
    name: str
    strategies: List[StrategyModel] = field(default_factory=list)
    multiplier: float = 1.0  # portfolio-level quantity multiplier
    risk: RiskRule | None = None  # portfolio-level risk override
    enabled: bool = True

    def __post_init__(self):  # type: ignore[method-assign]
        if not self.strategies:
            raise ValueError("Portfolio must contain at least one strategy")
        strat_ids = [s.strategy_id for s in self.strategies]
        if len(set(strat_ids)) != len(strat_ids):
            raise ValueError("Duplicate strategy_id in portfolio")
        if self.multiplier <= 0:
            raise ValueError("multiplier must be positive")

    # ------ helpers ------
    def to_dict(self) -> Dict[str, Any]:
        return {
            "portfolio_id": self.portfolio_id,
            "name": self.name,
            "multiplier": self.multiplier,
            "enabled": self.enabled,
            "strategies": [s.to_dict() for s in self.strategies],
            "risk": self.risk.to_dict() if self.risk else None,
        }

    @classmethod
    def from_dict(cls, d: Dict[str, Any]) -> "PortfolioModel":
        return cls(
            portfolio_id=str(d["portfolio_id"]),
            name=str(d["name"]),
            multiplier=float(d.get("multiplier", 1.0)),
            enabled=bool(d.get("enabled", True)),
            strategies=[StrategyModel.from_dict(x) for x in d["strategies"]],
            risk=RiskRule.from_dict(d["risk"]) if d.get("risk") else None,
        ) 