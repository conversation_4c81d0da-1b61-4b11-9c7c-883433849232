#!/usr/bin/env python3
"""
Complete test of GPU system with golden format output
"""

import os
import sys
import shutil
import pandas as pd
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_complete_golden_test():
    """Run a complete test using actual TBS input files."""
    
    logger.info("=== Running Complete GPU Golden Format Test ===")
    
    # Use actual TBS input files
    src_portfolio = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_portfolio.xlsx'
    src_strategy = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_tbs_portfolio.xlsx'
    
    # Create test directory
    test_dir = '/srv/samba/shared/test_results/gpu_golden_complete'
    os.makedirs(test_dir, exist_ok=True)
    
    # Copy input files to test directory
    test_portfolio = os.path.join(test_dir, 'test_portfolio.xlsx')
    test_strategy = os.path.join(test_dir, 'test_strategy.xlsx')
    
    logger.info(f"Copying input files to {test_dir}")
    shutil.copy(src_portfolio, test_portfolio)
    shutil.copy(src_strategy, test_strategy)
    
    # Modify portfolio to run for just 1 day for faster testing
    xl = pd.ExcelFile(test_portfolio)
    with pd.ExcelWriter(test_portfolio, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
        # Update PortfolioSetting
        portfolio_df = pd.read_excel(xl, 'PortfolioSetting')
        portfolio_df['StartDate'] = '01_04_2024'
        portfolio_df['EndDate'] = '01_04_2024'
        portfolio_df.to_excel(writer, sheet_name='PortfolioSetting', index=False)
        
        # Update StrategySetting to point to our test strategy
        strategy_df = pd.read_excel(xl, 'StrategySetting')
        strategy_df['StrategyExcelFilePath'] = test_strategy
        strategy_df.to_excel(writer, sheet_name='StrategySetting', index=False)
    
    # Prepare output path
    output_path = os.path.join(test_dir, 'gpu_golden_output.xlsx')
    
    # Run GPU backtest with golden format
    cmd = [
        'python3',
        '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio_GPU.py',
        '--portfolio-excel', test_portfolio,
        '--output-path', output_path,
        '--debug'
    ]
    
    logger.info(f"Running command: {' '.join(cmd)}")
    
    # Set environment to enable golden format
    env = os.environ.copy()
    env['USE_GOLDEN_FORMAT'] = 'true'
    
    import subprocess
    result = subprocess.run(cmd, capture_output=True, text=True, env=env)
    
    if result.returncode == 0:
        logger.info("✅ GPU backtest completed successfully")
        
        # Verify output
        if os.path.exists(output_path):
            xl = pd.ExcelFile(output_path)
            logger.info(f"\nOutput sheets: {xl.sheet_names}")
            logger.info(f"Number of sheets: {len(xl.sheet_names)}")
            
            # Check PORTFOLIO Trans
            trans_df = pd.read_excel(xl, 'PORTFOLIO Trans')
            logger.info(f"\nPORTFOLIO Trans:")
            logger.info(f"  Columns: {len(trans_df.columns)}")
            logger.info(f"  Rows: {len(trans_df)}")
            
            # Compare with archive format
            archive_xl = pd.ExcelFile('/srv/samba/shared/Nifty_Golden_Ouput.xlsx')
            archive_trans = pd.read_excel(archive_xl, 'PORTFOLIO Trans')
            
            logger.info(f"\nComparison with archive:")
            logger.info(f"  Archive columns: {len(archive_trans.columns)}")
            logger.info(f"  GPU columns: {len(trans_df.columns)}")
            logger.info(f"  Column names match: {set(trans_df.columns) == set(archive_trans.columns)}")
            
            if len(xl.sheet_names) >= 8 and len(trans_df.columns) == 32:
                logger.info("\n✅ GPU system successfully generates golden format!")
                return True
            else:
                logger.error("\n❌ Golden format validation failed")
                return False
        else:
            logger.error("Output file not found")
            return False
    else:
        logger.error(f"GPU backtest failed: {result.stderr}")
        return False


if __name__ == "__main__":
    # First check if source files exist
    if not os.path.exists('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_portfolio.xlsx'):
        logger.error("Source input files not found. Creating minimal test files...")
        
        # Create minimal test files
        os.makedirs('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs', exist_ok=True)
        
        # Portfolio file
        portfolio_data = {
            'PortfolioSetting': pd.DataFrame([{
                'StartDate': '01_04_2024',
                'EndDate': '01_04_2024',
                'IsTickBT': 'YES',
                'Enabled': 'YES',
                'PortfolioName': 'TEST_GOLDEN',
                'PortfolioTarget': 50000,
                'PortfolioStoploss': 20000,
                'Multiplier': 1.0
            }]),
            'StrategySetting': pd.DataFrame([{
                'Enabled': 'YES',
                'PortfolioName': 'TEST_GOLDEN',
                'StrategyType': 'TBS',
                'StrategyExcelFilePath': 'test_strategy.xlsx'
            }])
        }
        
        with pd.ExcelWriter('/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_portfolio.xlsx') as writer:
            for sheet, df in portfolio_data.items():
                df.to_excel(writer, sheet_name=sheet, index=False)
    
    success = run_complete_golden_test()
    if success:
        logger.info("\n🎉 GPU Golden Format Implementation Complete!")
    else:
        logger.info("\n⚠️ Golden format test needs further investigation")