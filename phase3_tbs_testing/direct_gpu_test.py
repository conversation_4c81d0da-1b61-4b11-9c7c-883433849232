
#!/usr/bin/env python3
"""
Direct TBS GPU Test - bypassing command line issues
"""

import os
import sys
import json
import shutil
import logging
from datetime import datetime

# Add paths
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable')
sys.path.insert(0, '/srv/samba/shared')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

TEST_DIR = '/srv/samba/shared/phase3_tbs_testing'
OUTPUT_DIR = os.path.join(TEST_DIR, 'output')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def run_direct_tbs_test():
    """Run TBS test directly"""
    
    # Change to BTRUN directory
    original_dir = os.getcwd()
    os.chdir('/srv/samba/shared/bt/backtester_stable/BTRUN')
    
    try:
        # Import what we need
        from backtester_stable.BTRUN.core import config
        from backtester_stable.BTRUN.excel_parser import portfolio_parser as modern_portfolio_parser
        from backtester_stable.BTRUN.backtester_v2.processor_engine import PortfolioProcessor
        
        # Prepare input file
        input_file = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/input_tbs_multi_legs.xlsx'
        working_input = os.path.join(TEST_DIR, 'input', 'test_input_tbs.xlsx')
        os.makedirs(os.path.dirname(working_input), exist_ok=True)
        shutil.copy(input_file, working_input)
        
        logging.info(f"Using input file: {working_input}")
        
        # Set config
        config.INPUT_FILE_FOLDER = os.path.dirname(working_input)
        
        # Parse portfolio
        logging.info("Parsing portfolio...")
        parsed_portfolio_models = modern_portfolio_parser.parse_portfolio_excel(working_input)
        
        # Process with date range
        logging.info("Processing backtest...")
        processor = PortfolioProcessor(
            start_date='240401',
            end_date='240405',
            portfolio_models=parsed_portfolio_models
        )
        
        results = processor.process()
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(OUTPUT_DIR, f'direct_tbs_output_{timestamp}.xlsx')
        
        if results:
            # Save to Excel
            import pandas as pd
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                if isinstance(results, list) and results:
                    df = pd.DataFrame(results)
                    df.to_excel(writer, sheet_name='Results', index=False)
                elif isinstance(results, dict):
                    for sheet_name, data in results.items():
                        if isinstance(data, pd.DataFrame):
                            data.to_excel(writer, sheet_name=sheet_name, index=False)
                        else:
                            df = pd.DataFrame(data)
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            logging.info(f"Results saved to: {output_file}")
            return output_file
        else:
            logging.error("No results generated")
            return None
            
    except Exception as e:
        logging.error(f"Error in direct test: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        os.chdir(original_dir)

def main():
    """Main function"""
    logging.info("=== Phase 3: Direct TBS Testing ===")
    
    # Run the test
    output_file = run_direct_tbs_test()
    
    if output_file:
        logging.info(f"Test completed successfully. Output: {output_file}")
        
        # Create summary
        summary = {
            "test_date": datetime.now().isoformat(),
            "phase": "3.1 - TBS Direct Testing",
            "input_file": "input_tbs_multi_legs.xlsx",
            "test_period": "2024-04-01 to 2024-04-05",
            "output_file": output_file,
            "method": "direct",
            "status": "success"
        }
        
        summary_path = os.path.join(TEST_DIR, 'phase3_direct_test_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logging.info(f"Summary saved to: {summary_path}")
        return output_file
    else:
        logging.error("Test failed - no output generated")
        return None

if __name__ == "__main__":
    main()
