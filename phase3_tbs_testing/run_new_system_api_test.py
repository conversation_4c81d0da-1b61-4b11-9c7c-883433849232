#!/usr/bin/env python3
"""
Phase 3: New System Test using Enterprise API
Tests the new GPU system using the API approach
"""

import os
import sys
import json
import requests
import time
import logging
from datetime import datetime
import pandas as pd

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Configuration
TEST_DIR = '/srv/samba/shared/phase3_tbs_testing'
OUTPUT_DIR = os.path.join(TEST_DIR, 'output')
API_BASE_URL = 'http://173.208.247.17:8000/api/v2'

def check_server_status():
    """Check if the enterprise server is running"""
    try:
        response = requests.get(f'{API_BASE_URL}/health', timeout=10)
        if response.status_code == 200:
            logging.info("Enterprise server is running")
            return True
        else:
            logging.error(f"Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        logging.error(f"Cannot connect to enterprise server: {e}")
        return False

def run_tbs_backtest_via_api():
    """Run TBS backtest using the enterprise API"""
    logging.info("=== Running TBS Backtest via Enterprise API ===")
    
    # Prepare backtest configuration
    backtest_config = {
        "strategy": "TBS",
        "symbol": "NIFTY",
        "start_date": "2024-04-01",
        "end_date": "2024-04-05",
        "settings": {
            "use_gpu": True,
            "use_synthetic_atm": True,
            "slippage_percent": 0.05
        }
    }
    
    # Read the input file and convert to JSON
    input_file = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/input_tbs_multi_legs.xlsx'
    
    try:
        # Read Excel file
        portfolio_df = pd.read_excel(input_file, sheet_name='PortfolioSetting')
        strategy_df = pd.read_excel(input_file, sheet_name='StrategySetting')
        
        # Convert to the expected format
        portfolio_config = portfolio_df.to_dict('records')[0] if not portfolio_df.empty else {}
        strategy_config = strategy_df.to_dict('records')
        
        backtest_config.update({
            "portfolio_settings": portfolio_config,
            "strategy_settings": strategy_config
        })
        
    except Exception as e:
        logging.error(f"Error reading input file: {e}")
        # Use default configuration
        backtest_config.update({
            "portfolio_settings": {
                "name": "Phase3_TBS_Test",
                "initial_capital": 100000,
                "slippage_percent": 0.05
            },
            "strategy_settings": [{
                "strategy_id": "TBS_1",
                "enabled": True,
                "capital_allocation": 100000
            }]
        })
    
    # Submit backtest request
    logging.info("Submitting backtest request...")
    
    try:
        response = requests.post(
            f'{API_BASE_URL}/backtest/submit',
            json=backtest_config,
            timeout=30
        )
        
        if response.status_code != 200:
            logging.error(f"Failed to submit backtest: {response.status_code} - {response.text}")
            return None
            
        result = response.json()
        backtest_id = result.get('backtest_id')
        
        if not backtest_id:
            logging.error("No backtest ID returned")
            return None
            
        logging.info(f"Backtest submitted with ID: {backtest_id}")
        
        # Monitor backtest progress
        return monitor_backtest_progress(backtest_id)
        
    except Exception as e:
        logging.error(f"Error submitting backtest: {e}")
        return None

def monitor_backtest_progress(backtest_id):
    """Monitor backtest progress and retrieve results"""
    logging.info(f"Monitoring backtest {backtest_id}...")
    
    max_wait_time = 300  # 5 minutes
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            # Check status
            response = requests.get(f'{API_BASE_URL}/backtest/status/{backtest_id}', timeout=10)
            
            if response.status_code == 200:
                status_data = response.json()
                status = status_data.get('status', 'unknown')
                progress = status_data.get('progress', 0)
                
                logging.info(f"Backtest status: {status}, Progress: {progress}%")
                
                if status == 'completed':
                    logging.info("Backtest completed successfully!")
                    return download_results(backtest_id)
                elif status == 'failed':
                    logging.error(f"Backtest failed: {status_data.get('error', 'Unknown error')}")
                    return None
                
            time.sleep(10)  # Wait 10 seconds before checking again
            
        except Exception as e:
            logging.error(f"Error checking backtest status: {e}")
            time.sleep(10)
    
    logging.error("Backtest timed out after 5 minutes")
    return None

def download_results(backtest_id):
    """Download backtest results"""
    logging.info(f"Downloading results for backtest {backtest_id}...")
    
    try:
        # Get results
        response = requests.get(f'{API_BASE_URL}/backtest/results/{backtest_id}', timeout=30)
        
        if response.status_code == 200:
            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(OUTPUT_DIR, f'new_system_tbs_output_{timestamp}.xlsx')
            
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            logging.info(f"Results saved to: {output_file}")
            return output_file
        else:
            logging.error(f"Failed to download results: {response.status_code}")
            return None
            
    except Exception as e:
        logging.error(f"Error downloading results: {e}")
        return None

def run_direct_gpu_test():
    """Alternative approach using direct GPU execution"""
    logging.info("=== Running Direct GPU Test ===")
    
    # Create a simple test script
    direct_test_script = '''
import sys
import os
import pandas as pd
from datetime import datetime
import json

# Add paths
sys.path.insert(0, '/srv/samba/shared')
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable')
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import our modules
try:
    from bt.backtester_stable.BTRUN.core.config import *
    from bt.backtester_stable.BTRUN.core.runtime import run_portfolio_backtest
    print("Successfully imported modules")
except Exception as e:
    print(f"Import error: {e}")
    sys.exit(1)

# Configuration
config = {
    "strategy": "TBS",
    "symbol": "NIFTY",
    "start_date": "2024-04-01",
    "end_date": "2024-04-05",
    "input_file": "/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/input_tbs_multi_legs.xlsx",
    "use_gpu": True,
    "use_synthetic_atm": True
}

print(f"Running backtest with config: {config}")

# Run test
try:
    results = run_portfolio_backtest(config)
    print(f"Results: {results}")
    
    # Save results
    output_file = f"/srv/samba/shared/phase3_tbs_testing/output/direct_gpu_tbs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"Results saved to: {output_file}")
    
except Exception as e:
    print(f"Error running backtest: {e}")
    import traceback
    traceback.print_exc()
'''
    
    script_path = os.path.join(TEST_DIR, 'direct_gpu_test.py')
    with open(script_path, 'w') as f:
        f.write(direct_test_script)
    
    # Run the script
    import subprocess
    result = subprocess.run([sys.executable, script_path], 
                          capture_output=True, 
                          text=True)
    
    if result.returncode == 0:
        logging.info("Direct GPU test completed")
        logging.info(f"Output: {result.stdout}")
        return True
    else:
        logging.error(f"Direct GPU test failed: {result.stderr}")
        logging.error(f"Output: {result.stdout}")
        return False

def main():
    """Main function"""
    logging.info("Starting Phase 3: New System Testing")
    
    # Check if server is running
    if check_server_status():
        # Try API approach
        output_file = run_tbs_backtest_via_api()
        
        if output_file:
            # Create summary
            summary = {
                "test_date": datetime.now().isoformat(),
                "phase": "3.1 - New System TBS Test",
                "input_file": "input_tbs_multi_legs.xlsx",
                "test_period": "2024-04-01 to 2024-04-05",
                "output_file": output_file,
                "method": "enterprise_api",
                "atm_method": "synthetic_future_based",
                "status": "success"
            }
            
            summary_path = os.path.join(TEST_DIR, 'phase3_new_system_summary.json')
            with open(summary_path, 'w') as f:
                json.dump(summary, f, indent=2)
            
            logging.info(f"New system test summary saved to: {summary_path}")
            logging.info("New system testing completed successfully")
        else:
            logging.error("API approach failed, trying direct GPU test")
            run_direct_gpu_test()
    else:
        logging.error("Enterprise server not available, trying direct GPU test")
        run_direct_gpu_test()

if __name__ == "__main__":
    main()