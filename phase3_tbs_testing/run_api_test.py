#!/usr/bin/env python3
"""
Run TBS test via API approach
"""

import os
import sys
import json
import requests
import shutil
import time
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

TEST_DIR = '/srv/samba/shared/phase3_tbs_testing'
OUTPUT_DIR = os.path.join(TEST_DIR, 'output')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def check_server_status():
    """Check if the API server is running"""
    try:
        response = requests.get('http://localhost:8000/api/v2/health', timeout=5)
        return response.status_code == 200
    except:
        return False

def run_tbs_via_api():
    """Run TBS test via API"""
    
    # Check if server is running
    if not check_server_status():
        logging.error("API server is not running on port 8000")
        return None
    
    # Prepare input file
    input_file = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/input_tbs_multi_legs.xlsx'
    working_input = os.path.join(TEST_DIR, 'input', 'test_input_tbs.xlsx')
    os.makedirs(os.path.dirname(working_input), exist_ok=True)
    shutil.copy(input_file, working_input)
    
    logging.info(f"Using input file: {working_input}")
    
    # Prepare API request
    api_url = 'http://localhost:8000/api/v2/backtest'
    
    # Upload file
    with open(working_input, 'rb') as f:
        files = {'file': f}
        data = {
            'start_date': '2024-04-01',
            'end_date': '2024-04-05',
            'strategy_type': 'TBS'
        }
        
        logging.info("Sending backtest request to API...")
        response = requests.post(api_url, files=files, data=data, timeout=300)
    
    if response.status_code == 200:
        result = response.json()
        logging.info(f"API response: {result}")
        
        # Check for output file
        if 'output_file' in result:
            # Copy to our output directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            dest_file = os.path.join(OUTPUT_DIR, f'api_tbs_output_{timestamp}.xlsx')
            shutil.copy(result['output_file'], dest_file)
            logging.info(f"Output copied to: {dest_file}")
            return dest_file
    else:
        logging.error(f"API request failed: {response.status_code} - {response.text}")
    
    return None

def main():
    """Main function"""
    logging.info("=== Phase 3: TBS API Testing ===")
    
    # Run the test
    output_file = run_tbs_via_api()
    
    if output_file:
        logging.info(f"Test completed successfully. Output: {output_file}")
        
        # Create summary
        summary = {
            "test_date": datetime.now().isoformat(),
            "phase": "3.1 - TBS API Testing",
            "input_file": "input_tbs_multi_legs.xlsx",
            "test_period": "2024-04-01 to 2024-04-05",
            "output_file": output_file,
            "method": "API",
            "status": "success"
        }
        
        summary_path = os.path.join(TEST_DIR, 'phase3_api_test_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logging.info(f"Summary saved to: {summary_path}")
        return output_file
    else:
        logging.error("Test failed - no output generated")
        return None

if __name__ == "__main__":
    main()