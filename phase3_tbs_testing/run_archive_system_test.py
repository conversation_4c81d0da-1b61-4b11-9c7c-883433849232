#!/usr/bin/env python3
"""
Phase 3: Run Archive System to Generate Baseline
This creates the baseline output from the archive system for comparison
"""

import os
import sys
import json
import shutil
import subprocess
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Directories
TEST_DIR = '/srv/samba/shared/phase3_tbs_testing'
ARCHIVE_DIR = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN'
OUTPUT_DIR = os.path.join(TEST_DIR, 'output')

def run_archive_system():
    """Run the archive system to generate baseline"""
    logging.info("=== Running Archive System for TBS Baseline ===")
    
    # Ensure output directory exists
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Check archive system components
    archive_btrun = os.path.join(ARCHIVE_DIR, 'BTRunPortfolio.py')
    archive_input = os.path.join(ARCHIVE_DIR, 'INPUT SHEETS', 'input_tbs_multi_legs.xlsx')
    
    if not os.path.exists(archive_btrun):
        logging.error(f"Archive BTRunPortfolio.py not found at {archive_btrun}")
        return None
        
    if not os.path.exists(archive_input):
        logging.error(f"Archive input file not found at {archive_input}")
        # List available files
        input_dir = os.path.join(ARCHIVE_DIR, 'INPUT SHEETS')
        logging.info(f"Available files in {input_dir}:")
        for f in os.listdir(input_dir):
            if 'tbs' in f.lower() and f.endswith('.xlsx'):
                logging.info(f"  - {f}")
        return None
    
    logging.info(f"Archive BTRun: {archive_btrun}")
    logging.info(f"Archive Input: {archive_input}")
    
    # Check and backup current config
    archive_config = os.path.join(ARCHIVE_DIR, 'config.py')
    if os.path.exists(archive_config):
        backup_config = os.path.join(TEST_DIR, 'archive_config_backup.py')
        shutil.copy(archive_config, backup_config)
        logging.info(f"Backed up config to {backup_config}")
    
    # Create test config for archive system
    test_config_content = '''
# Test configuration for Phase 3 TBS testing
TAXES = 0
LOT_SIZE = {}

VALID_TRADING_WEEKDAYS = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
]

VALID_MONTHS = [
    'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'
]

TRAIL_COST_RE_ENTRY = False
FIXED_VALUE_FOR_DYNAMIC_FACTOR = {}

# Database host
host = 'localhost'

# Updated to use localhost with SSH tunnels
BT_URII = {
    "tick": "http://127.0.0.1:5000/backtest/start",
    "minute": "http://127.0.0.1:5001/backtest/start"
}

VERSION_NO = {
    "TV": "29 Mar 25 (1.0.0)", 
    "PORTFOLIO": "15 Feb 25 (1.0.0)", 
    "FRONTEND": "22 Jan 25 (1.0.0)", 
    "FRONTENDTV": "22 Jan 25 (1.0.0)"
}

INPUT_FILE_FOLDER = "INPUT SHEETS"
PORTFOLIO_FILE_PATH = 'input_tbs_multi_legs.xlsx'
TV_FILE_PATH = "INPUT TV.xlsx"
LOG_FILE_FOLDER = "Logs"

# ATM calculation method - use spot-based for archive
USE_SYNTHETIC_FUTURE_ATM = False

# Use local backtesting engine
USE_LOCAL_ENGINE = True

# Date range for testing
TEST_START_DATE = "2024-04-01"
TEST_END_DATE = "2024-04-05"
'''
    
    # Write test config
    test_config_path = os.path.join(ARCHIVE_DIR, 'config.py')
    with open(test_config_path, 'w') as f:
        f.write(test_config_content)
    
    logging.info("Updated archive config for testing")
    
    # Change to archive directory
    original_dir = os.getcwd()
    os.chdir(ARCHIVE_DIR)
    
    try:
        # Run the archive system
        logging.info("Running archive system...")
        cmd = [sys.executable, 'BTRunPortfolio.py']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            logging.info("Archive system completed successfully")
            logging.info(f"STDOUT: {result.stdout}")
            
            # Find output files
            trades_dir = os.path.join(ARCHIVE_DIR, 'Trades')
            if os.path.exists(trades_dir):
                output_files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')]
                if output_files:
                    # Get the latest file
                    latest_file = sorted(output_files)[-1]
                    source_path = os.path.join(trades_dir, latest_file)
                    
                    # Copy to our output directory
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    dest_path = os.path.join(OUTPUT_DIR, f'archive_tbs_baseline_{timestamp}.xlsx')
                    shutil.copy(source_path, dest_path)
                    
                    logging.info(f"Archive output copied to: {dest_path}")
                    return dest_path
                else:
                    logging.error("No output files found in Trades directory")
            else:
                logging.error("Trades directory not found")
        else:
            logging.error(f"Archive system failed with return code {result.returncode}")
            logging.error(f"STDERR: {result.stderr}")
            logging.error(f"STDOUT: {result.stdout}")
    
    except subprocess.TimeoutExpired:
        logging.error("Archive system timed out after 5 minutes")
    except Exception as e:
        logging.error(f"Error running archive system: {e}")
    finally:
        # Restore original config if we backed it up
        backup_config = os.path.join(TEST_DIR, 'archive_config_backup.py')
        if os.path.exists(backup_config):
            shutil.copy(backup_config, archive_config)
            logging.info("Restored original archive config")
        
        # Return to original directory
        os.chdir(original_dir)
    
    return None

def main():
    """Main function"""
    logging.info("Starting Phase 3: Archive System Baseline Generation")
    
    # Run archive system
    baseline_file = run_archive_system()
    
    if baseline_file:
        # Create summary
        summary = {
            "test_date": datetime.now().isoformat(),
            "phase": "3.0 - Archive System Baseline",
            "input_file": "input_tbs_multi_legs.xlsx",
            "test_period": "2024-04-01 to 2024-04-05",
            "baseline_file": baseline_file,
            "atm_method": "spot-based",
            "status": "success"
        }
        
        summary_path = os.path.join(TEST_DIR, 'phase3_archive_baseline_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logging.info(f"Archive baseline summary saved to: {summary_path}")
        logging.info("Archive baseline generation completed successfully")
    else:
        logging.error("Failed to generate archive baseline")

if __name__ == "__main__":
    main()