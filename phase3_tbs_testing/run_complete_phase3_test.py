#!/usr/bin/env python3
"""
Complete Phase 3 Test: Run new system and compare with archive
"""

import os
import sys
import json
import shutil
import logging
import subprocess
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

TEST_DIR = '/srv/samba/shared/phase3_tbs_testing'
OUTPUT_DIR = os.path.join(TEST_DIR, 'output')
NEW_SYSTEM_DIR = '/srv/samba/shared/bt/backtester_stable/BTRUN'

def run_new_system_test():
    """Run new system using the working scripts"""
    logging.info("=== Running New System Test ===")
    
    original_dir = os.getcwd()
    os.chdir(NEW_SYSTEM_DIR)
    
    try:
        # Copy the archive input file to new system
        archive_input = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/input_tbs_multi_legs.xlsx'
        new_input_dir = os.path.join(NEW_SYSTEM_DIR, 'input_sheets')
        os.makedirs(new_input_dir, exist_ok=True)
        new_input_file = os.path.join(new_input_dir, 'test_tbs_input.xlsx')
        shutil.copy(archive_input, new_input_file)
        
        logging.info(f"Input file copied to: {new_input_file}")
        
        # Try using BT_TV_GPU.py which seems to be working
        cmd = [
            sys.executable,
            'BT_TV_GPU.py',
            '--portfolio-excel', new_input_file,
            '--start-date', '240401',
            '--end-date', '240405',
            '--strategy', 'TBS',
            '--output-dir', OUTPUT_DIR,
            '--use-gpu'
        ]
        
        logging.info(f"Running command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            logging.info("New system test completed successfully")
            logging.info(f"Output: {result.stdout}")
            
            # Find output files
            output_files = [f for f in os.listdir(OUTPUT_DIR) 
                          if f.endswith('.xlsx') and 'TBS' in f.upper()]
            
            if output_files:
                latest_file = sorted(output_files, 
                                   key=lambda x: os.path.getmtime(os.path.join(OUTPUT_DIR, x)))[-1]
                output_path = os.path.join(OUTPUT_DIR, latest_file)
                logging.info(f"Output file: {output_path}")
                return output_path
            else:
                # Check other locations
                for check_dir in ['Trades', 'output', 'results']:
                    full_dir = os.path.join(NEW_SYSTEM_DIR, check_dir)
                    if os.path.exists(full_dir):
                        files = [f for f in os.listdir(full_dir) if f.endswith('.xlsx')]
                        if files:
                            latest = sorted(files, 
                                          key=lambda x: os.path.getmtime(os.path.join(full_dir, x)))[-1]
                            source = os.path.join(full_dir, latest)
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            dest = os.path.join(OUTPUT_DIR, f'new_system_tbs_{timestamp}.xlsx')
                            shutil.copy(source, dest)
                            logging.info(f"Output copied from {source} to {dest}")
                            return dest
        else:
            logging.error(f"Command failed: {result.stderr}")
            logging.info(f"STDOUT: {result.stdout}")
            
    except Exception as e:
        logging.error(f"Error running new system: {e}")
        import traceback
        traceback.print_exc()
    finally:
        os.chdir(original_dir)
    
    return None

def main():
    """Main function to run complete phase 3 test"""
    logging.info("=== Starting Complete Phase 3 Test ===")
    
    # Step 1: Run new system
    new_output = run_new_system_test()
    
    if new_output:
        logging.info(f"New system output: {new_output}")
        
        # Step 2: Run comparison
        logging.info("Running comparison...")
        comparison_script = os.path.join(TEST_DIR, 'compare_phase3_results.py')
        result = subprocess.run([sys.executable, comparison_script], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            logging.info("Comparison completed successfully")
            logging.info(result.stdout)
        else:
            logging.error(f"Comparison failed: {result.stderr}")
        
        # Create final summary
        summary = {
            "test_date": datetime.now().isoformat(),
            "phase": "3.1 - Complete TBS Testing",
            "archive_baseline": "/srv/samba/shared/phase3_tbs_testing/output/archive_tbs_baseline_20250609_141621.xlsx",
            "new_system_output": new_output,
            "comparison_completed": True,
            "status": "success"
        }
        
        summary_file = os.path.join(TEST_DIR, 'final_phase3_report.json')
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logging.info(f"Final summary saved to: {summary_file}")
        
    else:
        logging.error("New system test failed - cannot proceed with comparison")

if __name__ == "__main__":
    main()