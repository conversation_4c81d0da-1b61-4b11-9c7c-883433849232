#!/usr/bin/env python3
"""
Direct TBS GPU Test for Phase 3
Using the actual GPU backtester
"""

import os
import sys
import json
import shutil
import subprocess
from datetime import datetime
import logging

# Add paths
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable')
sys.path.insert(0, '/srv/samba/shared')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

TEST_DIR = '/srv/samba/shared/phase3_tbs_testing'
OUTPUT_DIR = os.path.join(TEST_DIR, 'output')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def run_tbs_gpu_test():
    """Run TBS test using GPU backtester"""
    
    # Use the input file from archive system
    input_file = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/input_tbs_multi_legs.xlsx'
    
    # Copy to a working location
    working_input = os.path.join(TEST_DIR, 'input', 'test_input_tbs.xlsx')
    os.makedirs(os.path.dirname(working_input), exist_ok=True)
    shutil.copy(input_file, working_input)
    
    logging.info(f"Using input file: {working_input}")
    
    # Change to the BTRUN directory
    os.chdir('/srv/samba/shared/bt/backtester_stable/BTRUN')
    
    # Create config file for the test
    test_config = {
        "PORTFOLIO_FILE_PATH": working_input,
        "START_DATE": "01-04-2024",
        "END_DATE": "05-04-2024",
        "USE_GPU": True,
        "OUTPUT_DIR": OUTPUT_DIR
    }
    
    # Save config
    config_path = os.path.join(TEST_DIR, 'test_config.json')
    with open(config_path, 'w') as f:
        json.dump(test_config, f, indent=2)
    
    # Run the GPU backtester
    cmd = [
        sys.executable,
        'BTRunPortfolio_GPU.py',
        '--portfolio-excel', working_input,
        '--output-path', OUTPUT_DIR,
        '--start-date', '240401',
        '--end-date', '240405',
        '--legacy-excel'
    ]
    
    logging.info(f"Running command: {' '.join(cmd)}")
    
    # Execute
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"GPU backtester failed with return code {result.returncode}")
        logging.error(f"STDERR: {result.stderr}")
        logging.error(f"STDOUT: {result.stdout}")
        
        # Try alternative approach
        logging.info("Trying alternative approach...")
        return run_alternative_test(working_input)
    
    logging.info("GPU backtester completed successfully")
    logging.info(f"Output: {result.stdout}")
    
    # Find output file
    output_files = [f for f in os.listdir(OUTPUT_DIR) if f.endswith('.xlsx')]
    if output_files:
        latest_output = sorted(output_files)[-1]
        output_path = os.path.join(OUTPUT_DIR, latest_output)
        logging.info(f"Output file: {output_path}")
        return output_path
    
    return None

def run_alternative_test(input_file):
    """Alternative test approach using direct Python import"""
    logging.info("Running alternative test approach...")
    
    try:
        # Import the module directly
        os.chdir('/srv/samba/shared/bt/backtester_stable/BTRUN')
        
        # Create a simple test script
        test_script = f"""
import os
import sys
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import config and set parameters
import config
config.PORTFOLIO_FILE_PATH = '{input_file}'
config.INPUT_FILE_FOLDER = os.path.dirname('{input_file}')

# Import and run
import BTRunPortfolio_GPU

# The script should run when imported
"""
        
        script_path = os.path.join(TEST_DIR, 'direct_test.py')
        with open(script_path, 'w') as f:
            f.write(test_script)
        
        # Run it
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, 
                              text=True,
                              cwd='/srv/samba/shared/bt/backtester_stable/BTRUN')
        
        if result.returncode == 0:
            logging.info("Alternative test completed")
            # Check for output files
            trades_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN/Trades'
            if os.path.exists(trades_dir):
                output_files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')]
                if output_files:
                    latest = sorted(output_files)[-1]
                    source = os.path.join(trades_dir, latest)
                    dest = os.path.join(OUTPUT_DIR, f'gpu_tbs_output_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
                    shutil.copy(source, dest)
                    logging.info(f"Output copied to: {dest}")
                    return dest
        else:
            logging.error(f"Alternative test failed: {result.stderr}")
            
    except Exception as e:
        logging.error(f"Error in alternative test: {e}")
        import traceback
        traceback.print_exc()
    
    return None

def main():
    """Main function"""
    logging.info("=== Phase 3: TBS GPU Testing ===")
    
    # Run the test
    output_file = run_tbs_gpu_test()
    
    if output_file:
        logging.info(f"Test completed successfully. Output: {output_file}")
        
        # Create summary
        summary = {
            "test_date": datetime.now().isoformat(),
            "phase": "3.1 - TBS GPU Testing",
            "input_file": "input_tbs_multi_legs.xlsx",
            "test_period": "2024-04-01 to 2024-04-05",
            "output_file": output_file,
            "status": "success"
        }
        
        summary_path = os.path.join(TEST_DIR, 'phase3_gpu_test_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logging.info(f"Summary saved to: {summary_path}")
    else:
        logging.error("Test failed - no output generated")

if __name__ == "__main__":
    main()