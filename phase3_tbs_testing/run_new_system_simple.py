#!/usr/bin/env python3
"""
Simple approach to run new system TBS test
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

TEST_DIR = '/srv/samba/shared/phase3_tbs_testing'
OUTPUT_DIR = os.path.join(TEST_DIR, 'output')

def run_new_system_test():
    """Run new system using the legacy BTRunPortfolio"""
    
    # Change to the new system directory
    new_btrun_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN'
    original_dir = os.getcwd()
    os.chdir(new_btrun_dir)
    
    try:
        # Use the same input file as archive
        input_file = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN/INPUT SHEETS/input_tbs_multi_legs.xlsx'
        
        # Copy input file to new system input folder
        import shutil
        dest_input = os.path.join(new_btrun_dir, 'INPUT SHEETS', 'test_input_tbs.xlsx')
        os.makedirs(os.path.dirname(dest_input), exist_ok=True)
        shutil.copy(input_file, dest_input)
        
        logging.info(f"Input file copied to: {dest_input}")
        
        # Try using the existing BTRunPortfolio.py from new system
        # But first, let's check what legacy scripts exist
        btrun_scripts = [f for f in os.listdir('.') if f.startswith('BTRun') and f.endswith('.py')]
        logging.info(f"Available BTRun scripts: {btrun_scripts}")
        
        # Try the simplest approach - use python module directly
        script_content = f"""
import os
import sys
sys.path.insert(0, '{new_btrun_dir}')

# Try to import and run the legacy system
try:
    # Set basic config
    os.environ['PORTFOLIO_FILE_PATH'] = 'test_input_tbs.xlsx'
    os.environ['BACKTEST_START_DATE'] = '01-04-2024'
    os.environ['BACKTEST_END_DATE'] = '05-04-2024'
    
    # Change to the right directory
    os.chdir('{new_btrun_dir}')
    
    # Import the working legacy module
    exec(open('BTRunPortfolio.py').read())
    
    print("Backtest completed successfully")
    
except Exception as e:
    print(f"Error: {{e}}")
    import traceback
    traceback.print_exc()
"""
        
        # Write and execute the script
        script_path = os.path.join(TEST_DIR, 'new_system_runner.py')
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        logging.info("Running new system backtest...")
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, 
                              text=True,
                              timeout=300)
        
        logging.info(f"Return code: {result.returncode}")
        logging.info(f"STDOUT: {result.stdout}")
        logging.info(f"STDERR: {result.stderr}")
        
        # Check for output files in Trades directory
        trades_dir = os.path.join(new_btrun_dir, 'Trades')
        if os.path.exists(trades_dir):
            output_files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')]
            if output_files:
                # Get the latest file
                latest_file = sorted(output_files, 
                                   key=lambda x: os.path.getmtime(os.path.join(trades_dir, x)))[-1]
                
                source_path = os.path.join(trades_dir, latest_file)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                dest_path = os.path.join(OUTPUT_DIR, f'new_system_tbs_output_{timestamp}.xlsx')
                
                shutil.copy(source_path, dest_path)
                logging.info(f"Output copied to: {dest_path}")
                return dest_path
        
        logging.error("No output files found")
        return None
        
    except Exception as e:
        logging.error(f"Error in new system test: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        os.chdir(original_dir)

def main():
    """Main function"""
    logging.info("=== Running New System TBS Test ===")
    
    output_file = run_new_system_test()
    
    if output_file:
        logging.info(f"New system test completed. Output: {output_file}")
        return output_file
    else:
        logging.error("New system test failed")
        return None

if __name__ == "__main__":
    main()