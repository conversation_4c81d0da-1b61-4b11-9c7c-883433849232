
import os
import sys
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import config and set parameters
import config
config.PORTFOLIO_FILE_PATH = '/srv/samba/shared/phase3_tbs_testing/input/test_input_tbs.xlsx'
config.INPUT_FILE_FOLDER = os.path.dirname('/srv/samba/shared/phase3_tbs_testing/input/test_input_tbs.xlsx')

# Import and run
import BTRunPortfolio_GPU

# The script should run when imported
