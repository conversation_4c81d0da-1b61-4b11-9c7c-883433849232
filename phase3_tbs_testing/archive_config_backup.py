TAXES = 0
LOT_SIZE = {}
VALID_TRADING_WEEKDAYS = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
]
VALID_MONTHS = [
    'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'
]
TRAIL_COST_RE_ENTRY = False
FIXED_VALUE_FOR_DYNAMIC_FACTOR = {}

# Database host
host = 'localhost'

# Updated to use localhost with SSH tunnels
BT_URII = {
    "tick": "http://127.0.0.1:5000/backtest/start",    # SSH tunnel to ***************:5000
    "minute": "http://127.0.0.1:5001/backtest/start"   # SSH tunnel to ***************:5000
}

VERSION_NO = {
    "TV": "29 Mar 25 (1.0.0)", 
    "PORTFOLIO": "15 Feb 25 (1.0.0)", 
    "FRONTEND": "22 Jan 25 (1.0.0)", 
    "FRONTENDTV": "22 Jan 25 (1.0.0)"
}

INPUT_FILE_FOLDER = "INPUT SHEETS"
PORTFOLIO_FILE_PATH = 'input_orb_phase2.xlsx'
TV_FILE_PATH = "INPUT TV.xlsx"
LOG_FILE_FOLDER = "Logs"

# ATM calculation method for parity with HeavyDB
USE_SYNTHETIC_FUTURE_ATM = True

# Use local backtesting engine instead of Flask services
USE_LOCAL_ENGINE = True
