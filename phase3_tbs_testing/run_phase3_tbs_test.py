#!/usr/bin/env python3
"""
Phase 3: New System Testing for TBS Strategy
Test Date: April 1-5, 2024
"""

import os
import sys
import json
import shutil
import pandas as pd
from datetime import datetime
import subprocess
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/srv/samba/shared/phase3_tbs_testing/phase3_test.log'),
        logging.StreamHandler()
    ]
)

# Directories
TEST_DIR = '/srv/samba/shared/phase3_tbs_testing'
INPUT_DIR = os.path.join(TEST_DIR, 'input')
OUTPUT_DIR = os.path.join(TEST_DIR, 'output')
COMPARISON_DIR = os.path.join(TEST_DIR, 'comparison')

# Archive system paths
ARCHIVE_PATH = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN'
ARCHIVE_INPUT = os.path.join(ARCHIVE_PATH, 'INPUT SHEETS', 'input_tbs_multi_legs.xlsx')

# New system paths
NEW_SYSTEM_PATH = '/srv/samba/shared'
NEW_SYSTEM_INPUT = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_portfolio.xlsx'

def setup_archive_test():
    """Setup and run archive system test"""
    logging.info("=== Phase 3.1: Setting up Archive System Test ===")
    
    # Copy archive system files
    archive_test_dir = os.path.join(TEST_DIR, 'archive_test')
    os.makedirs(archive_test_dir, exist_ok=True)
    
    # Copy the necessary files
    shutil.copy(ARCHIVE_INPUT, os.path.join(INPUT_DIR, 'archive_input_tbs.xlsx'))
    
    # Create archive config
    archive_config = {
        "PORTFOLIO_FILE_PATH": "input_tbs_multi_legs.xlsx",
        "USE_SYNTHETIC_FUTURE_ATM": False,  # Archive uses spot-based ATM
        "USE_LOCAL_ENGINE": True,
        "START_DATE": "2024-04-01",
        "END_DATE": "2024-04-05"
    }
    
    with open(os.path.join(archive_test_dir, 'archive_config.json'), 'w') as f:
        json.dump(archive_config, f, indent=2)
    
    logging.info(f"Archive input file: {ARCHIVE_INPUT}")
    logging.info(f"Test dates: {archive_config['START_DATE']} to {archive_config['END_DATE']}")
    
    # Run archive system
    os.chdir(ARCHIVE_PATH)
    
    # Update config for archive test
    config_update = """
# Test configuration for Phase 3
PORTFOLIO_FILE_PATH = 'input_tbs_multi_legs.xlsx'
USE_SYNTHETIC_FUTURE_ATM = False  # Archive uses spot-based
TEST_START_DATE = '2024-04-01'
TEST_END_DATE = '2024-04-05'
"""
    
    with open('test_config.py', 'w') as f:
        f.write(config_update)
    
    logging.info("Running archive system backtest...")
    
    # Run the backtest
    cmd = [sys.executable, 'BTRunPortfolio.py']
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"Archive backtest failed: {result.stderr}")
        return None
    
    # Find the output file
    trades_dir = os.path.join(ARCHIVE_PATH, 'Trades')
    output_files = [f for f in os.listdir(trades_dir) if f.endswith('.xlsx')]
    
    if not output_files:
        logging.error("No output file generated by archive system")
        return None
    
    # Get the latest output file
    latest_output = sorted(output_files)[-1]
    archive_output = os.path.join(trades_dir, latest_output)
    
    # Copy to our test directory
    archive_result = os.path.join(OUTPUT_DIR, f'archive_tbs_output_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx')
    shutil.copy(archive_output, archive_result)
    
    logging.info(f"Archive output saved to: {archive_result}")
    return archive_result

def run_new_system_test():
    """Run TBS strategy on new GPU system"""
    logging.info("=== Phase 3.2: Running New System Test ===")
    
    # Check if we have the correct input file
    if os.path.exists(NEW_SYSTEM_INPUT):
        input_file = NEW_SYSTEM_INPUT
    else:
        # Use the archive input file
        input_file = os.path.join(INPUT_DIR, 'archive_input_tbs.xlsx')
    
    logging.info(f"New system input file: {input_file}")
    
    # Create test configuration
    test_config = {
        "strategy": "TBS",
        "input_file": input_file,
        "start_date": "2024-04-01",
        "end_date": "2024-04-05",
        "use_gpu": True,
        "use_synthetic_atm": True,  # New system uses synthetic future-based ATM
        "output_dir": OUTPUT_DIR
    }
    
    # Save config
    config_path = os.path.join(TEST_DIR, 'new_system_config.json')
    with open(config_path, 'w') as f:
        json.dump(test_config, f, indent=2)
    
    # Run using BTRunPortfolio.py from new system
    os.chdir(NEW_SYSTEM_PATH)
    
    # Check if BTRunPortfolio.py exists in new system
    if not os.path.exists('BTRunPortfolio.py'):
        logging.error("BTRunPortfolio.py not found in new system path")
        # Try alternative location
        alt_path = '/srv/samba/shared/bt/backtester_stable/BTRUN/BTRunPortfolio.py'
        if os.path.exists(alt_path):
            logging.info(f"Using alternative path: {alt_path}")
            os.chdir(os.path.dirname(alt_path))
        else:
            return None
    
    # Create a test runner script
    runner_script = """
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import necessary modules
import config
import BTRunPortfolio

# Override config for test
config.PORTFOLIO_FILE_PATH = '{input_file}'
config.USE_SYNTHETIC_FUTURE_ATM = True
config.INPUT_FILE_FOLDER = '{input_dir}'

# Run the backtest
if __name__ == "__main__":
    BTRunPortfolio.main()
""".format(
        input_file=os.path.basename(input_file),
        input_dir=os.path.dirname(input_file)
    )
    
    runner_path = os.path.join(TEST_DIR, 'run_new_system.py')
    with open(runner_path, 'w') as f:
        f.write(runner_script)
    
    logging.info("Running new system backtest...")
    
    # Run the test
    cmd = [sys.executable, runner_path]
    result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.path.dirname(runner_path))
    
    if result.returncode != 0:
        logging.error(f"New system backtest failed: {result.stderr}")
        # Try direct approach
        logging.info("Trying direct approach with GPU backtester...")
        return run_gpu_backtester_direct(input_file)
    
    # Find output file
    output_files = [f for f in os.listdir(OUTPUT_DIR) if f.startswith('new_system_') and f.endswith('.xlsx')]
    
    if not output_files:
        logging.warning("No output from standard run, trying GPU backtester directly")
        return run_gpu_backtester_direct(input_file)
    
    latest_output = sorted(output_files)[-1]
    new_system_output = os.path.join(OUTPUT_DIR, latest_output)
    
    logging.info(f"New system output saved to: {new_system_output}")
    return new_system_output

def run_gpu_backtester_direct(input_file):
    """Run GPU backtester directly"""
    logging.info("Running GPU backtester directly...")
    
    # Use the enterprise server approach
    test_script = """
import pandas as pd
import json
from datetime import datetime
import sys
import os

# Add path for imports
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable')

from backtester_v2.strategies.tbs.strategy import TBSStrategy
from backtester_v2.data_providers.heavydb_provider import HeavyDBDataProvider
from backtester_v2.backtest_engine import BacktestEngine

# Configuration
config = {
    'strategy': 'TBS',
    'symbol': 'NIFTY',
    'start_date': '2024-04-01',
    'end_date': '2024-04-05',
    'input_file': '{input_file}',
    'use_synthetic_atm': True
}

# Initialize components
data_provider = HeavyDBDataProvider()
strategy = TBSStrategy(config)
engine = BacktestEngine(data_provider, strategy)

# Run backtest
results = engine.run_backtest()

# Save results
output_file = '/srv/samba/shared/phase3_tbs_testing/output/new_system_tbs_output_{timestamp}.xlsx'
output_file = output_file.format(timestamp=datetime.now().strftime('%Y%m%d_%H%M%S'))

# Create Excel output
with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
    # Write results to Excel
    if 'portfolio_results' in results:
        results['portfolio_results'].to_excel(writer, sheet_name='PORTFOLIO Results', index=False)
    if 'portfolio_trans' in results:
        results['portfolio_trans'].to_excel(writer, sheet_name='PORTFOLIO Trans', index=False)
    if 'metrics' in results:
        pd.DataFrame(results['metrics'], index=[0]).to_excel(writer, sheet_name='Metrics', index=False)

print(f"Results saved to: {{output_file}}")
""".format(input_file=input_file)
    
    script_path = os.path.join(TEST_DIR, 'gpu_direct_test.py')
    with open(script_path, 'w') as f:
        f.write(test_script)
    
    # Run the script
    cmd = [sys.executable, script_path]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        logging.error(f"GPU backtester failed: {result.stderr}")
        logging.info("Output: " + result.stdout)
        return None
    
    # Extract output file path from stdout
    for line in result.stdout.split('\n'):
        if 'Results saved to:' in line:
            output_file = line.split('Results saved to:')[1].strip()
            if os.path.exists(output_file):
                logging.info(f"GPU backtester output: {output_file}")
                return output_file
    
    return None

def compare_results(archive_file, new_system_file):
    """Compare results with ATM conversion"""
    logging.info("=== Phase 3.3: Comparing Results ===")
    
    if not archive_file or not new_system_file:
        logging.error("Missing output files for comparison")
        return
    
    # Create comparison script
    comparison_script = """
import pandas as pd
import numpy as np
from datetime import datetime

def convert_spot_to_synthetic_atm(spot_price):
    '''Convert spot-based ATM to synthetic future-based ATM'''
    # This is a simplified conversion - actual logic would consider futures premium
    # For now, we'll use a simple offset based on typical NIFTY futures premium
    futures_premium = 0.0015  # 0.15% typical premium
    synthetic_price = spot_price * (1 + futures_premium)
    
    # Round to nearest 50 for NIFTY
    return round(synthetic_price / 50) * 50

def compare_trades(archive_df, new_df):
    '''Compare trades accounting for ATM calculation differences'''
    
    results = {
        'total_archive_trades': len(archive_df),
        'total_new_trades': len(new_df),
        'matching_trades': 0,
        'atm_differences': [],
        'pnl_differences': []
    }
    
    # Compare trade by trade
    for idx, archive_trade in archive_df.iterrows():
        # Find corresponding trade in new system
        new_trade = new_df[
            (new_df['entry_datetime'] == archive_trade['entry_datetime']) &
            (new_df['strategy'] == archive_trade['strategy'])
        ]
        
        if not new_trade.empty:
            new_trade = new_trade.iloc[0]
            
            # Check if strikes are different due to ATM calculation
            if archive_trade['strike'] != new_trade['strike']:
                # Check if difference is due to ATM calculation
                archive_atm = archive_trade['strike']
                expected_synthetic = convert_spot_to_synthetic_atm(archive_trade['spot_price'])
                
                if abs(new_trade['strike'] - expected_synthetic) <= 50:
                    results['atm_differences'].append({
                        'datetime': archive_trade['entry_datetime'],
                        'archive_strike': archive_atm,
                        'new_strike': new_trade['strike'],
                        'expected_synthetic': expected_synthetic
                    })
            
            # Compare P&L
            pnl_diff = abs(new_trade['netPnlAfterExpenses'] - archive_trade['netPnlAfterExpenses'])
            if pnl_diff > 0.01:  # Allow small rounding differences
                results['pnl_differences'].append({
                    'datetime': archive_trade['entry_datetime'],
                    'archive_pnl': archive_trade['netPnlAfterExpenses'],
                    'new_pnl': new_trade['netPnlAfterExpenses'],
                    'difference': pnl_diff
                })
            else:
                results['matching_trades'] += 1
    
    return results

# Load files
archive_file = '{archive_file}'
new_file = '{new_file}'

try:
    # Read Excel files
    archive_trans = pd.read_excel(archive_file, sheet_name='PORTFOLIO Trans')
    new_trans = pd.read_excel(new_file, sheet_name='PORTFOLIO Trans')
    
    # Perform comparison
    comparison = compare_trades(archive_trans, new_trans)
    
    # Save comparison report
    report_file = '/srv/samba/shared/phase3_tbs_testing/comparison/phase3_comparison_report.json'
    import json
    with open(report_file, 'w') as f:
        json.dump(comparison, f, indent=2, default=str)
    
    # Print summary
    print("=== Comparison Summary ===")
    print(f"Archive trades: {comparison['total_archive_trades']}")
    print(f"New system trades: {comparison['total_new_trades']}")
    print(f"Matching trades: {comparison['matching_trades']}")
    print(f"ATM differences: {len(comparison['atm_differences'])}")
    print(f"P&L differences: {len(comparison['pnl_differences'])}")
    
except Exception as e:
    print(f"Error in comparison: {e}")
    import traceback
    traceback.print_exc()
"""
    
    script_content = comparison_script.format(
        archive_file=archive_file,
        new_file=new_system_file
    )
    
    script_path = os.path.join(COMPARISON_DIR, 'compare_results.py')
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # Run comparison
    cmd = [sys.executable, script_path]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        logging.info("Comparison completed successfully")
        logging.info(result.stdout)
    else:
        logging.error(f"Comparison failed: {result.stderr}")

def main():
    """Main execution function"""
    logging.info("Starting Phase 3: TBS Strategy Testing")
    logging.info(f"Test directory: {TEST_DIR}")
    
    # Step 1: Run archive system test
    archive_output = setup_archive_test()
    
    # Step 2: Run new system test
    new_system_output = run_new_system_test()
    
    # Step 3: Compare results
    if archive_output and new_system_output:
        compare_results(archive_output, new_system_output)
    else:
        logging.error("Could not complete comparison - missing output files")
    
    # Generate final report
    report = {
        'test_date': datetime.now().isoformat(),
        'phase': '3.1 - TBS Strategy Testing',
        'archive_output': archive_output,
        'new_system_output': new_system_output,
        'status': 'completed' if (archive_output and new_system_output) else 'failed'
    }
    
    report_path = os.path.join(TEST_DIR, 'phase3_test_report.json')
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    logging.info(f"Phase 3 test report saved to: {report_path}")
    logging.info("Phase 3 testing completed")

if __name__ == "__main__":
    main()