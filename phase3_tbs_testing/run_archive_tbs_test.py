#!/usr/bin/env python3
"""
Run TBS test using the working archive system
"""

import os
import sys
import json
import shutil
import logging
import subprocess
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

TEST_DIR = '/srv/samba/shared/phase3_tbs_testing'
OUTPUT_DIR = os.path.join(TEST_DIR, 'output')
ARCHIVE_DIR = '/srv/samba/shared/bt/archive/backtester_stable/BTRUN'
os.makedirs(OUTPUT_DIR, exist_ok=True)

def run_archive_tbs_test():
    """Run TBS test using archive system"""
    
    # Change to archive directory
    original_dir = os.getcwd()
    os.chdir(ARCHIVE_DIR)
    
    try:
        # Copy the original config for backup
        if os.path.exists('config.py.backup'):
            shutil.copy('config.py.backup', 'config.py')
        
        # Set the date range for our test in config
        config_update = """
# Update config for phase 3 test
import os
import sys

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

VERSION_NO = {"TBS": "v3.3", "TV": "v4.5", "ORB": "v2.2", "PORTFOLIO": "v1.6", "OI": "v1.1"}

# File paths
INPUT_FILE_FOLDER = "INPUT SHEETS"
PORTFOLIO_FILE_PATH = "input_tbs_multi_legs.xlsx"
TBS_MULTI_LEGS_FILE_PATH = "INPUT TBS MULTI LEGS.xlsx"

# Date range for test
BACKTEST_START_DATE = "01-04-2024"
BACKTEST_END_DATE = "05-04-2024"

# Database settings
DB_TYPE = "mysql"  # Use mysql for archive system
"""
        
        # Write updated config
        with open('config.py', 'w') as f:
            f.write(config_update)
        
        logging.info("Updated config for test period")
        
        # Run the archive backtester
        logging.info("Running archive TBS backtester...")
        result = subprocess.run([sys.executable, 'BTRunPortfolio.py'], 
                              capture_output=True, 
                              text=True,
                              timeout=300)
        
        if result.returncode == 0:
            logging.info("Archive backtester completed successfully")
            logging.info(f"Output: {result.stdout}")
            
            # Find and copy output files
            trades_dir = os.path.join(ARCHIVE_DIR, 'Trades')
            if os.path.exists(trades_dir):
                output_files = [f for f in os.listdir(trades_dir) 
                              if f.endswith('.xlsx') and 'TBS' in f.upper()]
                
                if output_files:
                    # Get the latest file
                    latest_file = sorted(output_files, 
                                       key=lambda x: os.path.getmtime(os.path.join(trades_dir, x)))[-1]
                    
                    source_path = os.path.join(trades_dir, latest_file)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    dest_path = os.path.join(OUTPUT_DIR, f'archive_tbs_new_test_{timestamp}.xlsx')
                    
                    shutil.copy(source_path, dest_path)
                    logging.info(f"Output copied to: {dest_path}")
                    return dest_path
                else:
                    logging.error("No TBS output files found")
            else:
                logging.error("Trades directory not found")
        else:
            logging.error(f"Archive backtester failed: {result.stderr}")
            
    except Exception as e:
        logging.error(f"Error running archive test: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        os.chdir(original_dir)
    
    return None

def main():
    """Main function"""
    logging.info("=== Phase 3: Archive TBS Testing ===")
    
    # Run the test
    output_file = run_archive_tbs_test()
    
    if output_file:
        logging.info(f"Test completed successfully. Output: {output_file}")
        
        # Create summary
        summary = {
            "test_date": datetime.now().isoformat(),
            "phase": "3.1 - Archive TBS New Test",
            "input_file": "input_tbs_multi_legs.xlsx",
            "test_period": "2024-04-01 to 2024-04-05",
            "output_file": output_file,
            "method": "archive_system",
            "status": "success"
        }
        
        summary_path = os.path.join(TEST_DIR, 'phase3_archive_new_test_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logging.info(f"Summary saved to: {summary_path}")
        return output_file
    else:
        logging.error("Test failed - no output generated")
        return None

if __name__ == "__main__":
    main()