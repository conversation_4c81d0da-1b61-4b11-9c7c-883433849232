#!/usr/bin/env python3
"""
Analyze the archive output file to understand its structure
"""

import pandas as pd
import os

archive_file = '/srv/samba/shared/phase3_tbs_testing/output/archive_tbs_baseline_20250609_141621.xlsx'

print(f"Analyzing: {archive_file}")
print("=" * 50)

# Get all sheet names
xl_file = pd.ExcelFile(archive_file)
print(f"Available sheets: {xl_file.sheet_names}")
print()

# Analyze each sheet
for sheet_name in xl_file.sheet_names:
    print(f"Sheet: {sheet_name}")
    print("-" * 30)
    
    try:
        df = pd.read_excel(archive_file, sheet_name=sheet_name)
        print(f"Rows: {len(df)}")
        print(f"Columns: {list(df.columns)}")
        
        if len(df) > 0:
            print("Sample data:")
            print(df.head())
        print()
        
    except Exception as e:
        print(f"Error reading sheet: {e}")
        print()