{"archive_file": "/srv/samba/shared/phase3_tbs_testing/output/archive_tbs_baseline_20250609_141621.xlsx", "new_file": "/srv/samba/shared/phase3_tbs_testing/output/new_system_tbs_synthetic_20250609_143353.xlsx", "analysis_date": "2025-06-09T14:33:54.152264", "comparison_type": "portfolio_transactions", "results": {"archive_analysis": {"total_trades": 4, "columns": ["Portfolio Name", "Strategy Name", "ID", "Entry Date", "Enter On", "Entry Day", "Exit Date", "Exit at", "Exit Day", "Index", "Expiry", "Strike", "CE/PE", "Trade", "Qty", "Entry at", "Exit at.1", "Points", "Points After Slippage", "PNL", "AfterSlippage", "Taxes", "Net PNL", "Re-entry No", "SL Re-entry No", "TGT Re-entry No", "Reason", "Index At Entry", "Index At Exit", "MaxProfit", "MaxLoss"], "strategies": [], "date_range": null, "total_pnl": null}, "new_system_analysis": {"file_path": "/srv/samba/shared/phase3_tbs_testing/output/new_system_tbs_synthetic_20250609_143353.xlsx", "sheets": ["PortfolioParameter", "GeneralParameter", "LegParameter", "PORTFOLIO Trans", "PORTFOLIO Results"], "data": {"PORTFOLIO Trans": {"rows": 4, "columns": ["Portfolio Name", "Strategy Name", "ID", "Entry Date", "Enter On", "Entry Day", "Exit Date", "Exit at", "Exit Day", "Index", "Expiry", "Strike", "CE/PE", "Trade", "Qty", "Entry at", "Exit at.1", "Points", "Points After Slippage", "PNL", "AfterSlippage", "Taxes", "Net PNL", "Re-entry No", "SL Re-entry No", "TGT Re-entry No", "Reason", "Index At Entry", "Index At Exit", "MaxProfit", "MaxLoss", "System"], "sample_data": [{"Portfolio Name": "NIF0DTE", "Strategy Name": "RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL", "ID": 1, "Entry Date": "2024-01-03 00:00:00", "Enter On": "09:16:00", "Entry Day": "Wednesday", "Exit Date": "2024-01-03 00:00:00", "Exit at": "12:00:00", "Exit Day": "Wednesday", "Index": "NIFTY", "Expiry": "2024-01-04 00:00:00", "Strike": 22150, "CE/PE": "CALL", "Trade": "SELL", "Qty": 75, "Entry at": 132.4883549582247, "Exit at.1": 348.5462876593296, "Points": -216.0579327011048, "Points After Slippage": -214.9776430375993, "PNL": -16204.34495258286, "AfterSlippage": -16123.32322781995, "Taxes": 0, "Net PNL": -16123.32322781995, "Re-entry No": 1, "SL Re-entry No": 0, "TGT Re-entry No": 0, "Reason": "Exit Time Hit", "Index At Entry": 21615.75, "Index At Exit": 21572.3, "MaxProfit": 0.0, "MaxLoss": -15900.0, "System": "New_Synthetic_Future_Based"}, {"Portfolio Name": "NIF0DTE", "Strategy Name": "RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL", "ID": 2, "Entry Date": "2024-01-03 00:00:00", "Enter On": "09:16:00", "Entry Day": "Wednesday", "Exit Date": "2024-01-03 00:00:00", "Exit at": "12:00:00", "Exit Day": "Wednesday", "Index": "NIFTY", "Expiry": "2024-01-04 00:00:00", "Strike": 22150, "CE/PE": "PUT", "Trade": "SELL", "Qty": 75, "Entry at": 603.3634989029468, "Exit at.1": 501.7806038483719, "Points": 101.582895054575, "Points After Slippage": 101.0749805793021, "PNL": 7618.717129093123, "AfterSlippage": 7580.623543447658, "Taxes": 0, "Net PNL": 7580.623543447658, "Re-entry No": 1, "SL Re-entry No": 0, "TGT Re-entry No": 0, "Reason": "Exit Time Hit", "Index At Entry": 21615.75, "Index At Exit": 21572.3, "MaxProfit": 7638.750000000002, "MaxLoss": 0.0, "System": "New_Synthetic_Future_Based"}, {"Portfolio Name": "NIF0DTE", "Strategy Name": "RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL", "ID": 3, "Entry Date": "2024-01-03 00:00:00", "Enter On": "09:16:00", "Entry Day": "Wednesday", "Exit Date": "2024-01-03 00:00:00", "Exit at": "12:00:00", "Exit Day": "Wednesday", "Index": "NIFTY", "Expiry": "2024-01-04 00:00:00", "Strike": 22350, "CE/PE": "CALL", "Trade": "BUY", "Qty": 75, "Entry at": 80.28319872669954, "Exit at.1": 71.64551489575828, "Points": -8.637683830941256, "Points After Slippage": -8.59449541178655, "PNL": -647.8262873205942, "AfterSlippage": -644.5871558839913, "Taxes": 0, "Net PNL": -644.5871558839913, "Re-entry No": 1, "SL Re-entry No": 0, "TGT Re-entry No": 0, "Reason": "Exit Time Hit", "Index At Entry": 21615.75, "Index At Exit": 21572.3, "MaxProfit": 0.0, "MaxLoss": -644.2500000000002, "System": "New_Synthetic_Future_Based"}, {"Portfolio Name": "NIF0DTE", "Strategy Name": "RS,916-1200,ATM-SELL,OTM2-BUY WITH 100%SL", "ID": 4, "Entry Date": "2024-01-03 00:00:00", "Enter On": "09:16:00", "Entry Day": "Wednesday", "Exit Date": "2024-01-03 00:00:00", "Exit at": "12:00:00", "Exit Day": "Wednesday", "Index": "NIFTY", "Expiry": "2024-01-04 00:00:00", "Strike": 21950, "CE/PE": "PUT", "Trade": "BUY", "Qty": 75, "Entry at": 326.0422278174339, "Exit at.1": 306.2460876108534, "Points": -19.7961402065805, "Points After Slippage": -19.6971595055476, "PNL": -1484.710515493538, "AfterSlippage": -1477.28696291607, "Taxes": 0, "Net PNL": -1477.28696291607, "Re-entry No": 1, "SL Re-entry No": 0, "TGT Re-entry No": 0, "Reason": "Exit Time Hit", "Index At Entry": 21615.75, "Index At Exit": 21572.3, "MaxProfit": 0.0, "MaxLoss": -1474.500000000002, "System": "New_Synthetic_Future_Based"}]}, "PORTFOLIO Results": {"rows": 4, "columns": ["Metric", "Value"], "sample_data": [{"Metric": "Total Trades", "Value": 4}, {"Metric": "Total P&L", "Value": -10664.57380317235}, {"Metric": "Win Rate", "Value": 25}, {"Metric": "ATM Method", "Value": "Synthetic Future-Based"}]}, "PortfolioParameter": {"rows": 21, "columns": ["Head", "Value"], "sample_data": [{"Head": "StartDate", "Value": "01_04_2024"}, {"Head": "EndDate", "Value": "05_04_2024"}, {"Head": "IsTickBT", "Value": "no"}, {"Head": "Enabled", "Value": "YES"}, {"Head": "PortfolioName", "Value": "NIF0DTE"}]}, "GeneralParameter": {"rows": 1, "columns": ["StrategyName", "MoveSlToCost", "Underlying", "Index", "Weekdays", "DTE", "StrikeSelectionTime", "StartTime", "LastEntryTime", "EndTime", "StrategyProfit", "StrategyLoss", "StrategyProfitReExecuteNo", "StrategyLossReExecuteNo", "StrategyTrailingType", "PnLCalTime", "LockPercent", "TrailPercent", "SqOff1Time", "SqOff1Percent", "SqOff2Time", "SqOff2Percent", "ProfitReaches", "LockMinProfitAt", "IncreaseInProfit", "TrailMinProfitBy", "TgtTrackingFrom", "TgtRegisterPriceFrom", "SlTrackingFrom", "SlRegisterPriceFrom", "PnLCalculationFrom", "ConsiderHedgePnLForStgyPnL", "StoplossCheckingInterval", "TargetCheckingInterval", "ReEntryCheckingInterval", "OnExpiryDayTradeNextExpiry"], "sample_data": [{"StrategyName": "RS,916-1200,ATM-Sell,OTM2-Buy with 100%SL", "MoveSlToCost": "no", "Underlying": "SPOT", "Index": "NIFTY", "Weekdays": "1,2,3,4,5", "DTE": 0, "StrikeSelectionTime": 91600, "StartTime": 91600, "LastEntryTime": 120000, "EndTime": 120000, "StrategyProfit": 0, "StrategyLoss": 0, "StrategyProfitReExecuteNo": 0, "StrategyLossReExecuteNo": 0, "StrategyTrailingType": "Lock & Trail Profits", "PnLCalTime": 230000, "LockPercent": 0, "TrailPercent": 0, "SqOff1Time": 230000, "SqOff1Percent": 0, "SqOff2Time": 230000, "SqOff2Percent": 0, "ProfitReaches": 0, "LockMinProfitAt": 0, "IncreaseInProfit": 0, "TrailMinProfitBy": 0, "TgtTrackingFrom": "high/low", "TgtRegisterPriceFrom": "tracking", "SlTrackingFrom": "high/low", "SlRegisterPriceFrom": "tracking", "PnLCalculationFrom": "close", "ConsiderHedgePnLForStgyPnL": "no", "StoplossCheckingInterval": 1, "TargetCheckingInterval": 1, "ReEntryCheckingInterval": 1, "OnExpiryDayTradeNextExpiry": "no"}]}}}}}