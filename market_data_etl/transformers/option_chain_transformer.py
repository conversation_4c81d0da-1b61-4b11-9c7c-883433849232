"""
Option Chain Data Transformer for Market Data ETL
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from .base_transformer import BaseTransformer

class OptionChainTransformer(BaseTransformer):
    """
    Transforms raw option chain data to HeavyDB schema format
    """
    
    def __init__(self, config: Dict):
        """
        Initialize option chain transformer
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        self.indices_config = config.get('indices', {})
        
        # Define column mappings for different symbols
        self.column_mappings = {
            'BANKNIFTY': {
                'expiry_buckets': ['CM', 'NM'],  # Only monthly
                'strike_interval': 100
            },
            'MIDCAPNIFTY': {
                'expiry_buckets': ['CM', 'NM'],  # Only monthly
                'strike_interval': 25
            },
            'SENSEX': {
                'expiry_buckets': ['CW', 'NW', 'CM', 'NM'],
                'strike_interval': 100
            },
            'NIFTY': {
                'expiry_buckets': ['CW', 'NW', 'CM', 'NM'],
                'strike_interval': 50
            }
        }
        
    def transform(self, df: pd.DataFrame, metadata: Dict = None) -> pd.DataFrame:
        """
        Transform option chain dataframe
        
        Args:
            df: Raw DataFrame to transform
            metadata: Optional metadata (e.g., {'symbol': 'NIFTY'})
            
        Returns:
            Transformed DataFrame
        """
        # Make a copy to avoid modifying original
        df = df.copy()
        
        # Get symbol from metadata or dataframe
        symbol = metadata.get('symbol') if metadata else self._detect_symbol(df)
        
        # 1. Basic data type conversions
        df = self._convert_data_types(df)
        
        # 2. Add exchange information
        df = self._add_exchange_info(df, symbol)
        
        # 3. Calculate synthetic ATM if not present
        if 'atm_strike' not in df.columns:
            df = self._calculate_atm_strikes(df)
            
        # 4. Calculate DTE if not present
        if 'dte' not in df.columns:
            df = self._calculate_dte(df)
            
        # 5. Add strike classification
        df = self._classify_strikes(df)
        
        # 6. Add time zones
        df = self._add_time_zones(df)
        
        # 7. Add expiry buckets based on symbol rules
        df = self._add_expiry_buckets(df, symbol)
        
        # 8. Add metadata columns
        df = self._add_metadata(df)
        
        # 9. Ensure column order matches HeavyDB schema
        df = self._ensure_schema_compliance(df)
        
        return df
        
    def _detect_symbol(self, df: pd.DataFrame) -> Optional[str]:
        """
        Detect symbol from dataframe
        
        Args:
            df: DataFrame to check
            
        Returns:
            Detected symbol or None
        """
        if 'symbol' in df.columns and not df['symbol'].empty:
            return df['symbol'].iloc[0]
            
        # Try to detect from other columns or filename
        for symbol in ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX']:
            if any(symbol in str(col).upper() for col in df.columns):
                return symbol
                
        return None
        
    def _convert_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert columns to appropriate data types
        
        Args:
            df: DataFrame to convert
            
        Returns:
            DataFrame with converted types
        """
        # Date conversions
        date_columns = ['trade_date', 'expiry_date']
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
                
        # Time conversion
        if 'trade_time' in df.columns:
            # Ensure time is in HH:MM:SS format
            df['trade_time'] = pd.to_datetime(df['trade_time'], format='%H:%M:%S', errors='coerce').dt.strftime('%H:%M:%S')
            
        # Create timestamp column if not present
        if 'trade_ts' not in df.columns and 'trade_date' in df.columns and 'trade_time' in df.columns:
            df['trade_ts'] = pd.to_datetime(
                df['trade_date'].astype(str) + ' ' + df['trade_time'].astype(str)
            )
            
        # Numeric conversions
        numeric_columns = [
            'spot', 'strike', 'atm_strike', 'dte',
            'ce_open', 'ce_high', 'ce_low', 'ce_close', 'ce_volume', 'ce_oi', 'ce_coi',
            'ce_iv', 'ce_delta', 'ce_gamma', 'ce_theta', 'ce_vega', 'ce_rho',
            'pe_open', 'pe_high', 'pe_low', 'pe_close', 'pe_volume', 'pe_oi', 'pe_coi',
            'pe_iv', 'pe_delta', 'pe_gamma', 'pe_theta', 'pe_vega', 'pe_rho',
            'future_open', 'future_high', 'future_low', 'future_close',
            'future_volume', 'future_oi', 'future_coi'
        ]
        
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
                
        # Integer columns
        int_columns = ['strike', 'atm_strike', 'dte', 'zone_id', 'ce_volume', 'ce_oi', 
                      'ce_coi', 'pe_volume', 'pe_oi', 'pe_coi', 'future_volume', 
                      'future_oi', 'future_coi']
        
        for col in int_columns:
            if col in df.columns:
                df[col] = df[col].astype('int64', errors='ignore')
                
        return df
        
    def _add_exchange_info(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """
        Add exchange information based on symbol
        
        Args:
            df: DataFrame to update
            symbol: Symbol name
            
        Returns:
            DataFrame with exchange info
        """
        if 'exchange' not in df.columns:
            if symbol in self.indices_config:
                df['exchange'] = self.indices_config[symbol].get('exchange', 'NSE')
            elif symbol == 'SENSEX':
                df['exchange'] = 'BSE'
            else:
                df['exchange'] = 'NSE'
                
        return df
        
    def _calculate_atm_strikes(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate ATM strike using synthetic future method
        
        Args:
            df: DataFrame to process
            
        Returns:
            DataFrame with atm_strike column
        """
        # Group by date and time
        grouped = df.groupby(['trade_date', 'trade_time'])
        
        atm_strikes = []
        for (date, time), group in grouped:
            # Calculate synthetic future for each strike
            if 'ce_close' in group.columns and 'pe_close' in group.columns:
                group['synthetic_future'] = (
                    group['strike'] + 
                    group['ce_close'] - 
                    group['pe_close']
                )
                
                # Find strike closest to spot
                if 'spot' in group.columns:
                    spot_price = group['spot'].iloc[0]
                    idx = (group['synthetic_future'] - spot_price).abs().idxmin()
                    atm_strike = group.loc[idx, 'strike']
                else:
                    # Use median strike as fallback
                    atm_strike = group['strike'].median()
                    
                atm_strikes.extend([atm_strike] * len(group))
            else:
                # Fallback to nearest strike to spot
                if 'spot' in group.columns:
                    spot_price = group['spot'].iloc[0]
                    idx = (group['strike'] - spot_price).abs().idxmin()
                    atm_strike = group.loc[idx, 'strike']
                else:
                    atm_strike = group['strike'].median()
                    
                atm_strikes.extend([atm_strike] * len(group))
                
        df['atm_strike'] = atm_strikes
        return df
        
    def _calculate_dte(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate days to expiry
        
        Args:
            df: DataFrame to process
            
        Returns:
            DataFrame with dte column
        """
        if 'trade_date' in df.columns and 'expiry_date' in df.columns:
            # Simple calendar days calculation
            df['dte'] = (df['expiry_date'] - df['trade_date']).dt.days
            
            # Ensure non-negative DTE
            df['dte'] = df['dte'].clip(lower=0)
            
        return df
        
    def _classify_strikes(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Classify strikes as ITM/ATM/OTM
        
        Args:
            df: DataFrame to process
            
        Returns:
            DataFrame with moneyness columns
        """
        if 'strike' in df.columns and 'atm_strike' in df.columns:
            # Calculate moneyness value
            df['strike_diff'] = df['strike'] - df['atm_strike']
            
            # Get strike interval for the symbol
            symbol = self._detect_symbol(df)
            strike_interval = self.column_mappings.get(symbol, {}).get('strike_interval', 50)
            
            df['moneyness_value'] = (df['strike_diff'] / strike_interval).round().astype(int)
            
            # Classify moneyness
            df['moneyness'] = df['moneyness_value'].apply(
                lambda x: 'ATM' if x == 0 else f'OTM{x}' if x > 0 else f'ITM{abs(x)}'
            )
            
            # Clean up temp column
            df.drop('strike_diff', axis=1, inplace=True)
            
        return df
        
    def _add_time_zones(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add intraday time zone classification
        
        Args:
            df: DataFrame to process
            
        Returns:
            DataFrame with zone columns
        """
        if 'trade_time' in df.columns:
            def get_zone(time_str):
                try:
                    time_obj = pd.to_datetime(time_str, format='%H:%M:%S')
                    hour = time_obj.hour
                    minute = time_obj.minute
                    total_minutes = hour * 60 + minute
                    
                    if total_minutes < 585:      # Before 9:45
                        return 1, 'OPEN'
                    elif total_minutes < 705:    # 9:45 - 11:45
                        return 2, 'MID_MORN'
                    elif total_minutes < 825:    # 11:45 - 13:45
                        return 3, 'LUNCH'
                    elif total_minutes < 900:    # 13:45 - 15:00
                        return 4, 'AFTERNOON'
                    else:                       # After 15:00
                        return 5, 'CLOSE'
                except:
                    return 0, 'UNKNOWN'
                    
            zone_data = df['trade_time'].apply(get_zone)
            df['zone_id'] = zone_data.apply(lambda x: x[0])
            df['zone_name'] = zone_data.apply(lambda x: x[1])
            
        return df
        
    def _add_expiry_buckets(self, df: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """
        Add expiry bucket classification based on symbol rules
        
        Args:
            df: DataFrame to process
            symbol: Symbol name
            
        Returns:
            DataFrame with expiry_bucket column
        """
        if 'trade_date' in df.columns and 'expiry_date' in df.columns:
            # Get allowed buckets for this symbol
            allowed_buckets = self.column_mappings.get(symbol, {}).get('expiry_buckets', ['CW', 'NW', 'CM', 'NM'])
            
            def get_bucket(row):
                trade_date = row['trade_date']
                expiry_date = row['expiry_date']
                
                if pd.isna(trade_date) or pd.isna(expiry_date):
                    return 'UNKNOWN'
                    
                days_diff = (expiry_date - trade_date).days
                
                # Determine base bucket
                if days_diff <= 7:
                    bucket = 'CW'  # Current week
                elif days_diff <= 14:
                    bucket = 'NW'  # Next week
                elif trade_date.month == expiry_date.month and trade_date.year == expiry_date.year:
                    bucket = 'CM'  # Current month
                else:
                    bucket = 'NM'  # Next month
                    
                # For BANKNIFTY and MIDCAPNIFTY, convert weekly to monthly
                if symbol in ['BANKNIFTY', 'MIDCAPNIFTY'] and bucket in ['CW', 'NW']:
                    # Check if it's actually a monthly expiry (last Thursday)
                    # For now, map weekly to monthly
                    if bucket == 'CW':
                        bucket = 'CM'
                    else:
                        bucket = 'NM'
                        
                return bucket if bucket in allowed_buckets else 'OTHER'
                
            df['expiry_bucket'] = df.apply(get_bucket, axis=1)
            
        return df
        
    def _add_metadata(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Add metadata columns
        
        Args:
            df: DataFrame to process
            
        Returns:
            DataFrame with metadata
        """
        # Add creation timestamp
        df['created_at'] = datetime.now()
        
        # Add monthly expiry flag if not present
        if 'is_monthly_expiry' not in df.columns:
            # Simple logic: if expiry is last Thursday of month
            if 'expiry_date' in df.columns:
                df['is_monthly_expiry'] = df.apply(
                    lambda row: self._is_monthly_expiry(row['expiry_date']), 
                    axis=1
                )
            else:
                df['is_monthly_expiry'] = False
                
        # Add expiry type if not present
        if 'expiry_type' not in df.columns:
            df['expiry_type'] = df.apply(
                lambda row: 'Monthly' if row.get('is_monthly_expiry', False) else 'Weekly',
                axis=1
            )
            
        return df
        
    def _is_monthly_expiry(self, date) -> bool:
        """
        Check if date is a monthly expiry (last Thursday of month)
        
        Args:
            date: Date to check
            
        Returns:
            True if monthly expiry
        """
        if pd.isna(date):
            return False
            
        try:
            # Get last day of month
            if isinstance(date, str):
                date = pd.to_datetime(date)
                
            last_day = date + pd.offsets.MonthEnd(0)
            
            # Find last Thursday
            while last_day.weekday() != 3:  # Thursday is 3
                last_day -= timedelta(days=1)
                
            return date.date() == last_day.date()
        except:
            return False
            
    def _ensure_schema_compliance(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Ensure dataframe matches HeavyDB schema
        
        Args:
            df: DataFrame to process
            
        Returns:
            DataFrame with correct schema
        """
        # Define expected column order for HeavyDB (matching actual schema)
        column_order = [
            'trade_date', 'trade_time', 'expiry_date', 'index_name',
            'spot', 'atm_strike', 'strike', 'dte', 'expiry_bucket',
            'zone_id', 'zone_name', 'call_strike_type', 'put_strike_type',
            'ce_symbol', 'ce_open', 'ce_high', 'ce_low', 'ce_close',
            'ce_volume', 'ce_oi', 'ce_coi',
            'ce_iv', 'ce_delta', 'ce_gamma', 'ce_theta', 'ce_vega', 'ce_rho',
            'pe_symbol', 'pe_open', 'pe_high', 'pe_low', 'pe_close',
            'pe_volume', 'pe_oi', 'pe_coi',
            'pe_iv', 'pe_delta', 'pe_gamma', 'pe_theta', 'pe_vega', 'pe_rho',
            'future_open', 'future_high', 'future_low', 'future_close',
            'future_volume', 'future_oi', 'future_coi'
        ]
        
        # Map symbol column to index_name
        if 'symbol' in df.columns and 'index_name' not in df.columns:
            df['index_name'] = df['symbol']
            
        # Add strike type columns based on moneyness
        if 'moneyness' in df.columns:
            df['call_strike_type'] = df['moneyness']
            df['put_strike_type'] = df['moneyness']
                
        # Ensure all required columns exist (fill with defaults if missing)
        for col in column_order:
            if col not in df.columns:
                if col.endswith('_symbol'):
                    df[col] = ''
                elif col in ['zone_id', 'dte']:
                    df[col] = 0
                elif col.endswith('_strike_type'):
                    df[col] = 'ATM'  # Default strike type
                elif col == 'index_name' and 'symbol' in df.columns:
                    df[col] = df['symbol']
                else:
                    df[col] = None
                    
        # Reorder columns
        df = self.ensure_column_order(df, column_order)
        
        return df