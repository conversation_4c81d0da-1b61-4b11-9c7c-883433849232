"""
Base Transformer Class for Market Data ETL
"""

import logging
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, Optional
from pathlib import Path

class BaseTransformer(ABC):
    """
    Abstract base class for all data transformers
    """
    
    def __init__(self, config: Dict):
        """
        Initialize base transformer
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.chunk_size = config.get('etl', {}).get('chunk_size', 100000)
        
    @abstractmethod
    def transform(self, df: pd.DataFrame, metadata: Dict = None) -> pd.DataFrame:
        """
        Transform dataframe
        
        Args:
            df: DataFrame to transform
            metadata: Optional metadata about the data
            
        Returns:
            Transformed DataFrame
        """
        pass
        
    def transform_file(self, input_path: Path, output_path: Path, metadata: Dict = None) -> bool:
        """
        Transform a file in chunks
        
        Args:
            input_path: Path to input file
            output_path: Path to output file
            metadata: Optional metadata about the data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Process in chunks for memory efficiency
            first_chunk = True
            total_rows = 0
            
            for chunk in pd.read_csv(input_path, chunksize=self.chunk_size):
                # Transform chunk
                transformed_chunk = self.transform(chunk, metadata)
                
                # Write to output
                mode = 'w' if first_chunk else 'a'
                header = first_chunk
                transformed_chunk.to_csv(output_path, mode=mode, header=header, index=False)
                
                first_chunk = False
                total_rows += len(transformed_chunk)
                
            self.logger.info(f"Transformed {total_rows:,} rows from {input_path.name} to {output_path.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error transforming file: {str(e)}")
            return False
            
    def ensure_column_order(self, df: pd.DataFrame, column_order: list) -> pd.DataFrame:
        """
        Ensure dataframe has columns in specified order
        
        Args:
            df: DataFrame to reorder
            column_order: List of column names in desired order
            
        Returns:
            DataFrame with reordered columns
        """
        # Get columns that exist in both df and column_order
        existing_columns = [col for col in column_order if col in df.columns]
        
        # Add any remaining columns not in column_order
        remaining_columns = [col for col in df.columns if col not in existing_columns]
        
        # Combine and reorder
        final_order = existing_columns + remaining_columns
        
        return df[final_order]