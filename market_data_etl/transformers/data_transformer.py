#!/usr/bin/env python3
"""
Data transformer for multi-index option chain data
"""
import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.etl_config import INDEX_CONFIG, TABLE_SCHEMA

logger = logging.getLogger('etl.transformer')

class DataTransformer:
    def __init__(self):
        self.column_mapping = self._load_column_mappings()
    
    def transform_file(self, filepath, index_name):
        """Transform a single CSV file"""
        logger.info(f"Transforming {filepath} for {index_name}")
        
        try:
            # Read CSV
            df = pd.read_csv(filepath, low_memory=False)
            logger.info(f"Read {len(df)} rows from {os.path.basename(filepath)}")
            
            # Check if data is already in final format
            required_cols = ['trade_date', 'trade_time', 'expiry_date', 'index_name', 
                           'spot', 'atm_strike', 'strike', 'dte', 'expiry_bucket']
            
            if all(col in df.columns for col in required_cols):
                logger.info(f"Data already in final format, validating...")
                # Filter by configured expiry types
                config = INDEX_CONFIG[index_name]
                df = df[df['expiry_bucket'].isin(config['expiry_types'])]
                
                if len(df) == 0:
                    logger.warning(f"No valid data after expiry filter for {filepath}")
                    return None
                
                logger.info(f"Using {len(df)} pre-transformed rows for {index_name}")
                return df
            
            # Apply transformations
            df = self._apply_basic_transformations(df, index_name)
            df = self._apply_expiry_filters(df, index_name)
            df = self._calculate_derived_fields(df, index_name)
            df = self._format_output_columns(df, index_name)
            
            # Validate transformed data
            if len(df) == 0:
                logger.warning(f"No valid data after transformation for {filepath}")
                return None
            
            logger.info(f"Transformed {len(df)} rows for {index_name}")
            return df
            
        except Exception as e:
            logger.error(f"Error transforming {filepath}: {e}")
            return None
    
    def _apply_basic_transformations(self, df, index_name):
        """Apply basic data transformations"""
        # Convert date columns
        date_columns = ['date', 'Date', 'DATE']
        for col in date_columns:
            if col in df.columns:
                df['trade_date'] = pd.to_datetime(df[col], format='%y%m%d', errors='coerce')
                break
        
        expiry_columns = ['expiry', 'Expiry', 'EXPIRY']
        for col in expiry_columns:
            if col in df.columns:
                df['expiry_date'] = pd.to_datetime(df[col], format='%y%m%d', errors='coerce')
                break
        
        # Drop rows with invalid dates
        df = df.dropna(subset=['trade_date', 'expiry_date'])
        
        # Convert time
        if 'time' in df.columns:
            if ':' in str(df['time'].iloc[0]):
                df['trade_time'] = df['time'].astype(str) + ':00'
            else:
                df['trade_time'] = pd.to_datetime(
                    df['time'].astype(str).str.zfill(4), 
                    format='%H%M'
                ).dt.strftime('%H:%M:%S')
        else:
            df['trade_time'] = '09:15:00'  # Default to market open
        
        # Add index name
        df['index_name'] = index_name
        
        # Get spot price
        spot_columns = ['underlying_price', 'spot', 'Spot', 'SPOT']
        for col in spot_columns:
            if col in df.columns:
                df['spot'] = df[col]
                break
        
        return df
    
    def _apply_expiry_filters(self, df, index_name):
        """Apply expiry type filters based on index configuration"""
        config = INDEX_CONFIG[index_name]
        allowed_expiries = config['expiry_types']
        
        # Calculate DTE and month difference
        df['dte'] = (df['expiry_date'] - df['trade_date']).dt.days
        df['month_diff'] = (
            (df['expiry_date'].dt.year - df['trade_date'].dt.year) * 12 + 
            (df['expiry_date'].dt.month - df['trade_date'].dt.month)
        )
        
        # Classify expiry types
        def classify_expiry(row):
            dte = row['dte']
            month_diff = row['month_diff']
            
            # For indices with weekly expiries (NIFTY, SENSEX)
            if 'CW' in allowed_expiries:
                if dte <= 7:
                    return 'CW'
                elif dte <= 14:
                    return 'NW'
                elif month_diff == 0:
                    return 'CM'
                elif month_diff == 1:
                    return 'NM'
            # For monthly-only indices (BANKNIFTY, MIDCAPNIFTY)
            else:
                if month_diff == 0:
                    return 'CM'
                elif month_diff == 1:
                    return 'NM'
            
            return None
        
        df['expiry_bucket'] = df.apply(classify_expiry, axis=1)
        
        # Filter to allowed expiry types
        df = df[df['expiry_bucket'].isin(allowed_expiries)]
        
        logger.info(f"After expiry filter: {len(df)} rows, expiry types: {df['expiry_bucket'].value_counts().to_dict()}")
        
        return df
    
    def _calculate_derived_fields(self, df, index_name):
        """Calculate derived fields like ATM, zones, strike types"""
        config = INDEX_CONFIG[index_name]
        strike_increment = config['strike_increment']
        
        # Calculate ATM strike
        if 'ATM' in df.columns:
            df['atm_strike'] = df['ATM']
        else:
            df['atm_strike'] = (df['spot'] / strike_increment).round() * strike_increment
        
        # Calculate zone classification
        df['strike_diff'] = (df['strike'] - df['atm_strike']).abs()
        df['zone_id'] = pd.cut(
            df['strike_diff'] / strike_increment,
            bins=[-np.inf, 0.5, 5, 10, 20, np.inf],
            labels=[0, 1, 2, 3, 4]
        ).astype(int)
        
        zone_map = {0: 'ATM', 1: 'OPEN', 2: 'NEAR', 3: 'MID', 4: 'FAR'}
        df['zone_name'] = df['zone_id'].map(zone_map)
        
        # Calculate strike types
        df['call_strike_type'] = df.apply(
            lambda row: 'ATM' if row['strike'] == row['atm_strike'] 
            else f"OTM{int(abs(row['strike'] - row['atm_strike']) / strike_increment)}" 
            if row['strike'] > row['atm_strike'] 
            else f"ITM{int(abs(row['strike'] - row['atm_strike']) / strike_increment)}", 
            axis=1
        )
        
        df['put_strike_type'] = df.apply(
            lambda row: 'ATM' if row['strike'] == row['atm_strike'] 
            else f"ITM{int(abs(row['strike'] - row['atm_strike']) / strike_increment)}" 
            if row['strike'] > row['atm_strike'] 
            else f"OTM{int(abs(row['strike'] - row['atm_strike']) / strike_increment)}", 
            axis=1
        )
        
        return df
    
    def _format_output_columns(self, df, index_name):
        """Format columns to match table schema"""
        output_df = pd.DataFrame()
        
        # Basic fields
        output_df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d')
        output_df['trade_time'] = df['trade_time']
        output_df['expiry_date'] = df['expiry_date'].dt.strftime('%Y-%m-%d')
        output_df['index_name'] = df['index_name']
        output_df['spot'] = df['spot']
        output_df['atm_strike'] = df['atm_strike']
        output_df['strike'] = df['strike']
        output_df['dte'] = df['dte']
        output_df['expiry_bucket'] = df['expiry_bucket']
        output_df['zone_id'] = df['zone_id']
        output_df['zone_name'] = df['zone_name']
        output_df['call_strike_type'] = df['call_strike_type']
        output_df['put_strike_type'] = df['put_strike_type']
        
        # Option data - map columns based on source format
        option_fields = ['symbol', 'open', 'high', 'low', 'close', 'volume', 'oi', 'coi']
        
        for field in option_fields:
            # Call options
            ce_col = self._find_column(df, f'CE_{field}', f'call_{field}')
            if ce_col:
                output_df[f'ce_{field}'] = df[ce_col]
            else:
                output_df[f'ce_{field}'] = '' if field == 'symbol' else 0
            
            # Put options
            pe_col = self._find_column(df, f'PE_{field}', f'put_{field}')
            if pe_col:
                output_df[f'pe_{field}'] = df[pe_col]
            else:
                output_df[f'pe_{field}'] = '' if field == 'symbol' else 0
        
        # Greeks
        greek_fields = ['iv', 'delta', 'gamma', 'theta', 'vega', 'rho']
        
        for greek in greek_fields:
            # Call greeks
            ce_greek = self._find_column(df, f'CE_{greek.upper()}', f'call_{greek}', f'call_implied_volatility' if greek == 'iv' else None)
            output_df[f'ce_{greek}'] = df[ce_greek] if ce_greek else np.nan
            
            # Put greeks
            pe_greek = self._find_column(df, f'PE_{greek.upper()}', f'put_{greek}', f'put_implied_volatility' if greek == 'iv' else None)
            output_df[f'pe_{greek}'] = df[pe_greek] if pe_greek else np.nan
        
        # Future data (placeholder for now)
        future_fields = ['open', 'high', 'low', 'close', 'volume', 'oi', 'coi']
        for field in future_fields:
            output_df[f'future_{field}'] = np.nan if field in ['open', 'high', 'low', 'close'] else 0
        
        # Ensure all required columns exist
        for col in TABLE_SCHEMA['columns']:
            if col not in output_df.columns:
                output_df[col] = np.nan if 'float' in str(output_df.dtypes.get(col, 'float')) else ''
        
        # Reorder columns to match schema
        output_df = output_df[TABLE_SCHEMA['columns']]
        
        return output_df
    
    def _find_column(self, df, *possible_names):
        """Find column by trying multiple possible names"""
        for name in possible_names:
            if name and name in df.columns:
                return name
        return None
    
    def _load_column_mappings(self):
        """Load column mappings for different data sources"""
        # This can be extended to load from a config file
        return {
            'standard': {
                'date': 'date',
                'time': 'time',
                'expiry': 'expiry',
                'strike': 'strike',
                'spot': 'underlying_price'
            }
        }

def transform_data(filepath, index_name):
    """Convenience function to transform a single file"""
    transformer = DataTransformer()
    return transformer.transform_file(filepath, index_name)