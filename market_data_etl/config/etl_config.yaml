# Market Data ETL Configuration
# Version: 1.0
# Date: 2025-01-06

# Dropbox Sources
dropbox:
  # General daily data folder
  daily_general:
    url: "https://www.dropbox.com/scl/fo/gy85j8fe4qyff97urjf9j/AOpgTIMN9bWpgByhwOCcRNc?rlkey=8p26r4ord47gdfdeuf5eubem6&e=3&st=ys54g9iu&dl=0"
    description: "Daily market data for all indices"
  
  # Monthly specific data
  monthly_specific:
    url: "https://www.dropbox.com/scl/fo/gy85j8fe4qyff97urjf9j/ALVfwnUKbXoj_PcyYvfzqH0/31.03.2026_Nifty%2CBankNifity%2C%20Midcapnifty%20%26%20Finnifty%20(Future%20%26%20Option)/2025?dl=0&rlkey=8p26r4ord47gdfdeuf5eubem6&subfolder_nav_tracking=1"
    description: "Monthly futures and options data"

# Indices Configuration
indices:
  NIFTY:
    symbol: "NIFTY"
    exchange: "NSE"
    lot_size: 50
    expiry_buckets: ["CW", "NW", "CM", "NM"]
    has_futures: true
    
  BANKNIFTY:
    symbol: "BANKNIFTY" 
    exchange: "NSE"
    lot_size: 25
    expiry_buckets: ["CM", "NM"]  # Only monthly expiries
    has_futures: true
    
  MIDCAPNIFTY:
    symbol: "MIDCAPNIFTY"
    exchange: "NSE"
    lot_size: 75
    expiry_buckets: ["CM", "NM"]  # Only monthly expiries
    has_futures: true
    
  SENSEX:
    symbol: "SENSEX"
    exchange: "BSE"
    lot_size: 10
    expiry_buckets: ["CW", "NW", "CM", "NM"]
    has_futures: true

# HeavyDB Configuration
heavydb:
  host: "localhost"
  port: 6274
  user: "admin"
  password: "HyperInteractive"
  database: "heavyai"
  
  # Performance settings
  batch_size: 500000
  num_workers: 4
  use_gpu: true
  
  # Table configuration
  table_name: "nifty_option_chain"
  import_dir: "/var/lib/heavyai/import"

# ETL Settings
etl:
  # Data directories
  data_dir: "/srv/samba/shared/market_data"
  temp_dir: "/srv/samba/shared/market_data_etl/temp"
  archive_dir: "/srv/samba/shared/market_data_etl/archive"
  log_dir: "/srv/samba/shared/market_data_etl/logs"
  
  # Processing settings
  chunk_size: 100000
  parallel_downloads: 3
  
  # Retry settings
  retry_attempts: 3
  retry_delay: 60  # seconds
  
  # Data validation
  validation:
    check_nulls: true
    check_duplicates: true
    price_tolerance: 0.10  # 10% price movement tolerance
    volume_min: 0
    oi_min: 0

# Schedule Configuration
schedule:
  nse_bse_equity:
    enabled: true
    time: "18:00"  # 6:00 PM IST
    timezone: "Asia/Kolkata"
    retry_until: "19:00"
    
  mcx_commodity:
    enabled: false  # Not implemented yet
    time: "23:30"
    timezone: "Asia/Kolkata"
    retry_until: "01:00+1"
    
  reference_data:
    enabled: true
    time: "08:00"  # 8:00 AM IST
    timezone: "Asia/Kolkata"

# Notification Settings
notifications:
  telegram:
    enabled: false  # Configure when bot token is available
    bot_token: ""
    chat_ids: []
    
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    recipients: []
    
  log_file:
    enabled: true
    level: "INFO"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Archive Settings
archive:
  enabled: true
  retention_days: 30
  compress: true
  move_to_cold_storage: false
  
# Monitoring
monitoring:
  health_check_interval: 300  # seconds
  disk_space_warning: 80  # percentage
  disk_space_critical: 90  # percentage
  
  metrics:
    - name: "records_processed"
      type: "counter"
    - name: "etl_duration"
      type: "gauge"
    - name: "validation_errors"
      type: "counter"
    - name: "data_quality_score"
      type: "gauge"