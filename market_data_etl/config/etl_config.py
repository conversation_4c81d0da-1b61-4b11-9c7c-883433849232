#!/usr/bin/env python3
"""
ETL Configuration for Multi-Index Option Chain Data
"""
import os
from datetime import datetime, time

# Base directories
BASE_DIR = "/srv/samba/shared/market_data_etl"
DATA_DIR = "/srv/samba/shared/market_data"
TEMP_DIR = os.path.join(BASE_DIR, "temp")
LOG_DIR = os.path.join(BASE_DIR, "logs")
ARCHIVE_DIR = os.path.join(BASE_DIR, "archive")

# Create directories if they don't exist
for dir_path in [BASE_DIR, DATA_DIR, TEMP_DIR, LOG_DIR, ARCHIVE_DIR]:
    os.makedirs(dir_path, exist_ok=True)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 6274,
    'user': 'admin',
    'password': 'HyperInteractive',
    'dbname': 'heavyai'
}

# Index configurations
INDEX_CONFIG = {
    'NIFTY': {
        'strike_increment': 50,
        'expiry_types': ['CW', 'NW', 'CM', 'NM'],  # All types
        'data_source': 'dropbox',  # Changed to dropbox for production
        'dropbox_url': 'https://www.dropbox.com/scl/fo/gy85j8fe4qyff97urjf9j/AOpgTIMN9bWpgByhwOCcRNc?rlkey=8p26r4ord47gdfdeuf5eubem6&e=3&st=ys54g9iu&dl=0',
        'local_path': os.path.join(DATA_DIR, 'nifty/oc_with_futures'),
        'file_pattern': '*nifty*.csv',
        'active': True
    },
    'BANKNIFTY': {
        'strike_increment': 100,
        'expiry_types': ['CM', 'NM'],  # Monthly only
        'data_source': 'dropbox',  # Changed to dropbox for production
        'dropbox_url': 'https://www.dropbox.com/scl/fo/gy85j8fe4qyff97urjf9j/AOpgTIMN9bWpgByhwOCcRNc?rlkey=8p26r4ord47gdfdeuf5eubem6&e=3&st=ys54g9iu&dl=0',
        'local_path': os.path.join(DATA_DIR, 'banknifty'),
        'file_pattern': '*banknifty*.csv',
        'active': True
    },
    'MIDCAPNIFTY': {
        'strike_increment': 25,
        'expiry_types': ['CM', 'NM'],  # Monthly only
        'data_source': 'dropbox',  # Changed to dropbox for production
        'dropbox_url': 'https://www.dropbox.com/scl/fo/gy85j8fe4qyff97urjf9j/AOpgTIMN9bWpgByhwOCcRNc?rlkey=8p26r4ord47gdfdeuf5eubem6&e=3&st=ys54g9iu&dl=0',
        'local_path': os.path.join(DATA_DIR, 'midcpnifty'),
        'file_pattern': '*midcpnifty*.csv',
        'active': True
    },
    'SENSEX': {
        'strike_increment': 100,
        'expiry_types': ['CW', 'NW', 'CM', 'NM'],  # All types
        'data_source': 'dropbox',  # Changed to dropbox for production
        'dropbox_url': 'https://www.dropbox.com/scl/fo/gy85j8fe4qyff97urjf9j/AOpgTIMN9bWpgByhwOCcRNc?rlkey=8p26r4ord47gdfdeuf5eubem6&e=3&st=ys54g9iu&dl=0',
        'local_path': os.path.join(DATA_DIR, 'sensex'),
        'file_pattern': '*sensex*.csv',
        'active': True
    }
}

# ETL settings
ETL_SETTINGS = {
    'batch_size': 100000,  # Rows per batch
    'parallel_workers': 4,  # Parallel processing workers
    'retry_attempts': 3,
    'retry_delay': 60,  # seconds
    'data_retention_days': 90,  # Keep processed files for 90 days
    'log_retention_days': 30,   # Keep logs for 30 days
    'market_close_time': time(15, 30),  # 3:30 PM IST
    'etl_run_time': time(18, 30),  # 6:30 PM IST
}

# Dropbox configuration (to be updated with actual URLs)
DROPBOX_CONFIG = {
    'download_timeout': 300,  # 5 minutes
    'chunk_size': 8192,
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
}

# Data quality thresholds
QUALITY_THRESHOLDS = {
    'min_rows_per_file': 1000,
    'max_null_percentage': 5.0,
    'min_strikes_per_expiry': 10,
    'max_dte_days': 365,
    'min_spot_price': 100,
    'max_spot_price': 1000000,
}

# Notification settings (optional)
NOTIFICATION_CONFIG = {
    'enabled': False,
    'email': {
        'smtp_server': '',
        'smtp_port': 587,
        'username': '',
        'password': '',
        'from_address': '',
        'to_addresses': []
    },
    'webhook_url': ''  # For Slack/Discord notifications
}

# Logging configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s [%(filename)s:%(lineno)d]: %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'standard',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'DEBUG',
            'formatter': 'detailed',
            'filename': os.path.join(LOG_DIR, 'daily_etl.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 10
        },
        'error_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'ERROR',
            'formatter': 'detailed',
            'filename': os.path.join(LOG_DIR, 'etl_errors.log'),
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5
        }
    },
    'loggers': {
        'etl': {
            'level': 'DEBUG',
            'handlers': ['console', 'file', 'error_file'],
            'propagate': False
        }
    }
}

# Table schema
TABLE_SCHEMA = {
    'table_name': 'nifty_option_chain',
    'columns': [
        'trade_date', 'trade_time', 'expiry_date', 'index_name', 'spot',
        'atm_strike', 'strike', 'dte', 'expiry_bucket', 'zone_id', 'zone_name',
        'call_strike_type', 'put_strike_type',
        'ce_symbol', 'ce_open', 'ce_high', 'ce_low', 'ce_close', 
        'ce_volume', 'ce_oi', 'ce_coi', 'ce_iv',
        'ce_delta', 'ce_gamma', 'ce_theta', 'ce_vega', 'ce_rho',
        'pe_symbol', 'pe_open', 'pe_high', 'pe_low', 'pe_close',
        'pe_volume', 'pe_oi', 'pe_coi', 'pe_iv',
        'pe_delta', 'pe_gamma', 'pe_theta', 'pe_vega', 'pe_rho',
        'future_open', 'future_high', 'future_low', 'future_close',
        'future_volume', 'future_oi', 'future_coi'
    ]
}