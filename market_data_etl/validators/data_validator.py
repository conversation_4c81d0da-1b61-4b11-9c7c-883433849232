#!/usr/bin/env python3
"""
Data validator for option chain data quality checks
"""
import os
import sys
import pandas as pd
import logging
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.etl_config import QUALITY_THRESHOLDS, INDEX_CONFIG

logger = logging.getLogger('etl.validator')

class DataValidator:
    def __init__(self):
        self.thresholds = QUALITY_THRESHOLDS
        self.validation_results = []
    
    def validate_dataframe(self, df, index_name):
        """Validate transformed dataframe"""
        logger.info(f"Validating {len(df)} rows for {index_name}")
        
        self.validation_results = []
        is_valid = True
        
        # Run all validation checks
        checks = [
            self._check_row_count,
            self._check_null_values,
            self._check_date_ranges,
            self._check_spot_prices,
            self._check_strike_increments,
            self._check_dte_values,
            self._check_expiry_types,
            self._check_zone_distribution,
            self._check_duplicate_rows
        ]
        
        for check in checks:
            result = check(df, index_name)
            if not result:
                is_valid = False
        
        # Log validation summary
        if is_valid:
            logger.info(f"✅ All validations passed for {index_name}")
        else:
            logger.error(f"❌ Validation failed for {index_name}")
            for result in self.validation_results:
                if not result['passed']:
                    logger.error(f"  - {result['check']}: {result['message']}")
        
        return is_valid
    
    def _check_row_count(self, df, index_name):
        """Check if row count meets minimum threshold"""
        min_rows = self.thresholds['min_rows_per_file']
        passed = len(df) >= min_rows
        
        self.validation_results.append({
            'check': 'Row Count',
            'passed': passed,
            'message': f"Found {len(df)} rows (minimum: {min_rows})"
        })
        
        return passed
    
    def _check_null_values(self, df, index_name):
        """Check for excessive null values"""
        critical_columns = ['trade_date', 'expiry_date', 'strike', 'spot', 'atm_strike']
        
        null_counts = {}
        for col in critical_columns:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                null_pct = (null_count / len(df)) * 100
                null_counts[col] = null_pct
        
        max_null_pct = max(null_counts.values()) if null_counts else 0
        passed = max_null_pct <= self.thresholds['max_null_percentage']
        
        self.validation_results.append({
            'check': 'Null Values',
            'passed': passed,
            'message': f"Max null percentage: {max_null_pct:.2f}% (threshold: {self.thresholds['max_null_percentage']}%)"
        })
        
        return passed
    
    def _check_date_ranges(self, df, index_name):
        """Check if dates are reasonable"""
        today = datetime.now().date()
        min_date = pd.to_datetime(df['trade_date']).min().date()
        max_date = pd.to_datetime(df['trade_date']).max().date()
        
        # Check if dates are not in future
        passed = max_date <= today
        
        # Check if date range is reasonable (not too old)
        if passed and (today - min_date).days > 365:
            logger.warning(f"Data contains dates older than 1 year: {min_date}")
        
        self.validation_results.append({
            'check': 'Date Ranges',
            'passed': passed,
            'message': f"Date range: {min_date} to {max_date}"
        })
        
        return passed
    
    def _check_spot_prices(self, df, index_name):
        """Check if spot prices are within reasonable range"""
        min_spot = df['spot'].min()
        max_spot = df['spot'].max()
        
        passed = (min_spot >= self.thresholds['min_spot_price'] and 
                 max_spot <= self.thresholds['max_spot_price'])
        
        self.validation_results.append({
            'check': 'Spot Prices',
            'passed': passed,
            'message': f"Spot range: {min_spot:.2f} to {max_spot:.2f}"
        })
        
        return passed
    
    def _check_strike_increments(self, df, index_name):
        """Check if strike increments are valid"""
        config = INDEX_CONFIG[index_name]
        expected_increment = config['strike_increment']
        
        # Sample strikes from ATM zone
        atm_strikes = df[df['zone_name'] == 'ATM']['strike'].unique()
        if len(atm_strikes) > 1:
            atm_strikes_sorted = sorted(atm_strikes)
            increments = [atm_strikes_sorted[i+1] - atm_strikes_sorted[i] 
                         for i in range(len(atm_strikes_sorted)-1)]
            
            min_increment = min(increments) if increments else expected_increment
            passed = min_increment >= expected_increment
        else:
            passed = True  # Can't verify with single strike
        
        self.validation_results.append({
            'check': 'Strike Increments',
            'passed': passed,
            'message': f"Expected increment: {expected_increment}"
        })
        
        return passed
    
    def _check_dte_values(self, df, index_name):
        """Check if DTE values are reasonable"""
        min_dte = df['dte'].min()
        max_dte = df['dte'].max()
        
        passed = (min_dte >= 0 and max_dte <= self.thresholds['max_dte_days'])
        
        self.validation_results.append({
            'check': 'DTE Values',
            'passed': passed,
            'message': f"DTE range: {min_dte} to {max_dte} days"
        })
        
        return passed
    
    def _check_expiry_types(self, df, index_name):
        """Check if expiry types match configuration"""
        config = INDEX_CONFIG[index_name]
        expected_types = set(config['expiry_types'])
        actual_types = set(df['expiry_bucket'].unique())
        
        # It's okay to have subset of expected types
        passed = actual_types.issubset(expected_types)
        
        self.validation_results.append({
            'check': 'Expiry Types',
            'passed': passed,
            'message': f"Expected: {expected_types}, Found: {actual_types}"
        })
        
        return passed
    
    def _check_zone_distribution(self, df, index_name):
        """Check if zone distribution is reasonable"""
        zone_counts = df['zone_name'].value_counts()
        total_rows = len(df)
        
        # Check for expected zone types
        distance_zones = {'ATM', 'NEAR', 'MID', 'FAR'}
        time_zones = {'MID_MORN', 'LUNCH', 'AFTERNOON', 'CLOSE'}
        
        actual_zones = set(df['zone_name'].unique())
        
        # If using time-based zones (check for unique time zone markers)
        if actual_zones.intersection(time_zones):
            # Just check that we have reasonable distribution
            passed = len(zone_counts) >= 3  # At least 3 different zones
            message = f"Time zones found: {', '.join(sorted(actual_zones))}"
        # If using distance-based zones
        elif 'ATM' in actual_zones:
            atm_pct = (zone_counts.get('ATM', 0) / total_rows) * 100
            passed = atm_pct > 0 and atm_pct < 20  # ATM should be 1-20% of data
            message = f"ATM percentage: {atm_pct:.1f}%"
        else:
            # For any other zone distribution, just check we have data
            passed = len(zone_counts) > 0
            message = f"Zones found: {', '.join(sorted(actual_zones))}"
        
        self.validation_results.append({
            'check': 'Zone Distribution',
            'passed': passed,
            'message': message
        })
        
        return passed
    
    def _check_duplicate_rows(self, df, index_name):
        """Check for duplicate entries"""
        # Define columns that should be unique together
        unique_cols = ['trade_date', 'trade_time', 'expiry_date', 'strike']
        
        duplicates = df.duplicated(subset=unique_cols, keep=False)
        duplicate_count = duplicates.sum()
        
        passed = duplicate_count == 0
        
        self.validation_results.append({
            'check': 'Duplicate Rows',
            'passed': passed,
            'message': f"Found {duplicate_count} duplicate rows"
        })
        
        if not passed and duplicate_count > 0:
            # Log sample of duplicates
            sample_dups = df[duplicates].head(5)
            logger.warning(f"Sample duplicate rows:\n{sample_dups[unique_cols]}")
        
        return passed
    
    def get_validation_summary(self):
        """Get summary of validation results"""
        total_checks = len(self.validation_results)
        passed_checks = sum(1 for r in self.validation_results if r['passed'])
        
        return {
            'total_checks': total_checks,
            'passed_checks': passed_checks,
            'failed_checks': total_checks - passed_checks,
            'success_rate': (passed_checks / total_checks) * 100 if total_checks > 0 else 0,
            'details': self.validation_results
        }

def validate_data(df, index_name):
    """Convenience function to validate dataframe"""
    validator = DataValidator()
    return validator.validate_dataframe(df, index_name)