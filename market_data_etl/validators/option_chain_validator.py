"""
Option Chain Data Validator for Market Data ETL
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from .base_validator import BaseValidator

class OptionChainValidator(BaseValidator):
    """
    Validates option chain data quality and integrity
    """
    
    def __init__(self, config: Dict):
        """
        Initialize option chain validator
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        
        # Define required columns for option chain data
        self.required_columns = [
            'trade_date', 'trade_time', 'symbol', 'expiry_date',
            'strike', 'spot', 'ce_close', 'pe_close'
        ]
        
        # Define critical columns that should not have nulls
        self.critical_columns = [
            'trade_date', 'trade_time', 'symbol', 'strike', 'expiry_date'
        ]
        
        # Define expected data types
        self.expected_types = {
            'trade_date': 'datetime',
            'trade_time': 'string',
            'expiry_date': 'datetime',
            'strike': 'numeric',
            'spot': 'numeric',
            'ce_close': 'numeric',
            'pe_close': 'numeric',
            'ce_volume': 'numeric',
            'pe_volume': 'numeric',
            'ce_oi': 'numeric',
            'pe_oi': 'numeric'
        }
        
    def validate(self, df: pd.DataFrame, metadata: Dict = None) -> Tuple[bool, List[Dict]]:
        """
        Validate option chain dataframe
        
        Args:
            df: DataFrame to validate
            metadata: Optional metadata about the data
            
        Returns:
            Tuple of (is_valid, validation_errors)
        """
        errors = []
        
        # 1. Check required columns
        column_errors = self.check_required_columns(df, self.required_columns)
        errors.extend(column_errors)
        
        # If critical columns are missing, stop validation
        if column_errors and any(e['severity'] == 'CRITICAL' for e in column_errors):
            return False, errors
            
        # 2. Check null values
        null_errors = self.check_null_values(df, self.critical_columns)
        errors.extend(null_errors)
        
        # 3. Check duplicates
        key_columns = ['trade_date', 'trade_time', 'symbol', 'strike', 'expiry_date']
        duplicate_errors = self.check_duplicates(df, key_columns)
        errors.extend(duplicate_errors)
        
        # 4. Check data types
        type_errors = self.check_data_types(df, self.expected_types)
        errors.extend(type_errors)
        
        # 5. Business rule validation
        business_errors = self._validate_business_rules(df)
        errors.extend(business_errors)
        
        # 6. Data quality checks
        quality_errors = self._check_data_quality(df)
        errors.extend(quality_errors)
        
        # Determine if data is valid
        critical_errors = [e for e in errors if e['severity'] == 'CRITICAL']
        high_errors = [e for e in errors if e['severity'] == 'HIGH']
        
        is_valid = len(critical_errors) == 0 and len(high_errors) < 5
        
        return is_valid, errors
        
    def _validate_business_rules(self, df: pd.DataFrame) -> List[Dict]:
        """
        Validate business rules specific to option chain data
        
        Args:
            df: DataFrame to validate
            
        Returns:
            List of error dictionaries
        """
        errors = []
        
        # 1. Check if strike prices are valid (should be multiples of 50 for indices)
        if 'strike' in df.columns and 'symbol' in df.columns:
            for symbol in df['symbol'].unique():
                symbol_df = df[df['symbol'] == symbol]
                invalid_strikes = symbol_df[symbol_df['strike'] % 50 != 0]['strike'].unique()
                
                if len(invalid_strikes) > 0:
                    errors.append({
                        'type': 'INVALID_STRIKES',
                        'message': f"{symbol}: {len(invalid_strikes)} strikes not multiples of 50",
                        'severity': 'MEDIUM',
                        'symbol': symbol,
                        'invalid_strikes': invalid_strikes[:10].tolist()  # Show first 10
                    })
                    
        # 2. Check expiry dates are in the future
        if 'trade_date' in df.columns and 'expiry_date' in df.columns:
            # Convert to datetime if not already
            try:
                trade_dates = pd.to_datetime(df['trade_date'])
                expiry_dates = pd.to_datetime(df['expiry_date'])
                
                past_expiries = (expiry_dates < trade_dates).sum()
                if past_expiries > 0:
                    errors.append({
                        'type': 'PAST_EXPIRIES',
                        'message': f"{past_expiries:,} records have expiry dates in the past",
                        'severity': 'HIGH',
                        'count': past_expiries
                    })
            except Exception as e:
                self.logger.warning(f"Could not validate expiry dates: {str(e)}")
                
        # 3. Check price relationships (high >= low, etc.)
        price_checks = [
            ('ce_high', 'ce_low', 'Call'),
            ('pe_high', 'pe_low', 'Put')
        ]
        
        for high_col, low_col, option_type in price_checks:
            if high_col in df.columns and low_col in df.columns:
                invalid_prices = (df[high_col] < df[low_col]).sum()
                if invalid_prices > 0:
                    errors.append({
                        'type': 'INVALID_PRICE_RELATIONSHIP',
                        'message': f"{option_type}: {invalid_prices:,} records where high < low",
                        'severity': 'HIGH',
                        'count': invalid_prices,
                        'option_type': option_type
                    })
                    
        return errors
        
    def _check_data_quality(self, df: pd.DataFrame) -> List[Dict]:
        """
        Check data quality metrics
        
        Args:
            df: DataFrame to check
            
        Returns:
            List of error dictionaries
        """
        errors = []
        
        # 1. Check for negative prices
        price_columns = ['ce_open', 'ce_high', 'ce_low', 'ce_close',
                        'pe_open', 'pe_high', 'pe_low', 'pe_close', 'spot']
        
        for col in price_columns:
            if col in df.columns:
                negative_count = (df[col] < 0).sum()
                if negative_count > 0:
                    errors.append({
                        'type': 'NEGATIVE_PRICES',
                        'message': f"{negative_count:,} negative values in {col}",
                        'severity': 'HIGH',
                        'column': col,
                        'count': negative_count
                    })
                    
        # 2. Check for extreme price movements
        tolerance = self.validation_config.get('price_tolerance', 0.10)  # 10% default
        
        if 'ce_close' in df.columns:
            # Check for extreme values (more than 100x median)
            median_price = df[df['ce_close'] > 0]['ce_close'].median()
            if median_price > 0:
                extreme_prices = (df['ce_close'] > median_price * 100).sum()
                if extreme_prices > 0:
                    errors.append({
                        'type': 'EXTREME_PRICES',
                        'message': f"{extreme_prices:,} call prices more than 100x median",
                        'severity': 'MEDIUM',
                        'count': extreme_prices,
                        'median_price': median_price
                    })
                    
        # 3. Check volume and OI consistency
        if 'ce_volume' in df.columns and 'ce_oi' in df.columns:
            # Volume should not be negative
            negative_volume = (df['ce_volume'] < 0).sum()
            if negative_volume > 0:
                errors.append({
                    'type': 'NEGATIVE_VOLUME',
                    'message': f"{negative_volume:,} negative volume values",
                    'severity': 'HIGH',
                    'count': negative_volume
                })
                
        # 4. Check for data completeness by time
        if 'trade_time' in df.columns:
            unique_times = df['trade_time'].nunique()
            expected_times = 375  # Assuming 1-minute data from 9:15 to 15:30
            
            if unique_times < expected_times * 0.9:  # Allow 10% missing
                errors.append({
                    'type': 'INCOMPLETE_TIME_SERIES',
                    'message': f"Only {unique_times} unique timestamps, expected ~{expected_times}",
                    'severity': 'MEDIUM',
                    'actual_times': unique_times,
                    'expected_times': expected_times
                })
                
        return errors