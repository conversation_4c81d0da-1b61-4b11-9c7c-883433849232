"""
Base Validator Class for Market Data ETL
"""

import logging
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple
from pathlib import Path

class BaseValidator(ABC):
    """
    Abstract base class for all data validators
    """
    
    def __init__(self, config: Dict):
        """
        Initialize base validator
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.validation_config = config.get('etl', {}).get('validation', {})
        
    @abstractmethod
    def validate(self, df: pd.DataFrame, metadata: Dict = None) -> Tuple[bool, List[Dict]]:
        """
        Validate dataframe
        
        Args:
            df: DataFrame to validate
            metadata: Optional metadata about the data
            
        Returns:
            Tuple of (is_valid, validation_errors)
        """
        pass
        
    def check_required_columns(self, df: pd.DataFrame, required_columns: List[str]) -> List[Dict]:
        """
        Check if all required columns are present
        
        Args:
            df: DataFrame to check
            required_columns: List of required column names
            
        Returns:
            List of error dictionaries
        """
        errors = []
        missing_columns = set(required_columns) - set(df.columns)
        
        if missing_columns:
            errors.append({
                'type': 'MISSING_COLUMNS',
                'message': f"Missing required columns: {missing_columns}",
                'severity': 'CRITICAL',
                'columns': list(missing_columns)
            })
            
        return errors
        
    def check_null_values(self, df: pd.DataFrame, critical_columns: List[str]) -> List[Dict]:
        """
        Check for null values in critical columns
        
        Args:
            df: DataFrame to check
            critical_columns: Columns that should not have nulls
            
        Returns:
            List of error dictionaries
        """
        errors = []
        
        if not self.validation_config.get('check_nulls', True):
            return errors
            
        for col in critical_columns:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    null_pct = (null_count / len(df)) * 100
                    errors.append({
                        'type': 'NULL_VALUES',
                        'message': f"{null_count:,} null values ({null_pct:.2f}%) in {col}",
                        'severity': 'HIGH' if null_pct > 10 else 'MEDIUM',
                        'column': col,
                        'count': null_count,
                        'percentage': null_pct
                    })
                    
        return errors
        
    def check_duplicates(self, df: pd.DataFrame, key_columns: List[str]) -> List[Dict]:
        """
        Check for duplicate records
        
        Args:
            df: DataFrame to check
            key_columns: Columns that define uniqueness
            
        Returns:
            List of error dictionaries
        """
        errors = []
        
        if not self.validation_config.get('check_duplicates', True):
            return errors
            
        # Check for exact duplicates
        duplicate_count = df.duplicated().sum()
        if duplicate_count > 0:
            errors.append({
                'type': 'DUPLICATE_ROWS',
                'message': f"{duplicate_count:,} exact duplicate rows found",
                'severity': 'MEDIUM',
                'count': duplicate_count
            })
            
        # Check for duplicates based on key columns
        if key_columns and all(col in df.columns for col in key_columns):
            key_duplicates = df.duplicated(subset=key_columns).sum()
            if key_duplicates > 0:
                errors.append({
                    'type': 'DUPLICATE_KEYS',
                    'message': f"{key_duplicates:,} duplicate records based on {key_columns}",
                    'severity': 'HIGH',
                    'count': key_duplicates,
                    'key_columns': key_columns
                })
                
        return errors
        
    def check_data_types(self, df: pd.DataFrame, expected_types: Dict[str, str]) -> List[Dict]:
        """
        Check if columns have expected data types
        
        Args:
            df: DataFrame to check
            expected_types: Dict mapping column names to expected types
            
        Returns:
            List of error dictionaries
        """
        errors = []
        
        for col, expected_type in expected_types.items():
            if col in df.columns:
                actual_type = str(df[col].dtype)
                
                # Check if types match (with some flexibility)
                type_match = False
                if expected_type == 'numeric':
                    type_match = df[col].dtype.kind in 'biufc'
                elif expected_type == 'datetime':
                    type_match = df[col].dtype.kind == 'M'
                elif expected_type == 'string':
                    type_match = df[col].dtype.kind == 'O'
                else:
                    type_match = actual_type == expected_type
                    
                if not type_match:
                    errors.append({
                        'type': 'WRONG_DATA_TYPE',
                        'message': f"Column {col} has type {actual_type}, expected {expected_type}",
                        'severity': 'MEDIUM',
                        'column': col,
                        'actual_type': actual_type,
                        'expected_type': expected_type
                    })
                    
        return errors