#!/usr/bin/env python3
"""
HeavyDB loader for option chain data
"""
import os
import sys
import pandas as pd
import logging
from heavydb import connect
import tempfile
import time

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.etl_config import DB_CONFIG, TABLE_SCHEMA, ETL_SETTINGS

logger = logging.getLogger('etl.loader')

class HeavyDBLoader:
    def __init__(self):
        self.conn = None
        self.cursor = None
        self._connect()
    
    def _connect(self):
        """Establish connection to HeavyDB"""
        try:
            self.conn = connect(**DB_CONFIG)
            self.cursor = self.conn.cursor()
            logger.info("Connected to HeavyDB")
        except Exception as e:
            logger.error(f"Failed to connect to HeavyDB: {e}")
            raise
    
    def load_dataframe(self, df, index_name):
        """Load dataframe into HeavyDB using COPY FROM"""
        if df is None or len(df) == 0:
            logger.warning(f"No data to load for {index_name}")
            return False
        
        logger.info(f"Loading {len(df)} rows for {index_name}")
        
        try:
            # Save to temporary CSV
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp_file:
                df.to_csv(tmp_file.name, index=False)
                temp_path = tmp_file.name
            
            # Use COPY FROM for fast loading
            start_time = time.time()
            
            copy_query = f"""
            COPY {TABLE_SCHEMA['table_name']} FROM '{temp_path}' 
            WITH (header='true', delimiter=',')
            """
            
            self.cursor.execute(copy_query)
            
            elapsed_time = time.time() - start_time
            rows_per_second = len(df) / elapsed_time if elapsed_time > 0 else 0
            
            logger.info(f"✅ Loaded {len(df):,} rows in {elapsed_time:.2f}s ({rows_per_second:,.0f} rows/sec)")
            
            # Clean up temp file
            os.remove(temp_path)
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            # Clean up temp file on error
            if 'temp_path' in locals() and os.path.exists(temp_path):
                os.remove(temp_path)
            return False
    
    def check_existing_data(self, index_name, date):
        """Check if data already exists for given index and date"""
        try:
            query = f"""
            SELECT COUNT(*) as cnt
            FROM {TABLE_SCHEMA['table_name']}
            WHERE index_name = '{index_name}'
            AND trade_date = '{date}'
            """
            
            self.cursor.execute(query)
            count = self.cursor.fetchone()[0]
            
            if count > 0:
                logger.info(f"Found {count:,} existing rows for {index_name} on {date}")
            
            return count
            
        except Exception as e:
            logger.error(f"Error checking existing data: {e}")
            return 0
    
    def delete_existing_data(self, index_name, date):
        """Delete existing data for given index and date"""
        try:
            query = f"""
            DELETE FROM {TABLE_SCHEMA['table_name']}
            WHERE index_name = '{index_name}'
            AND trade_date = '{date}'
            """
            
            self.cursor.execute(query)
            logger.info(f"Deleted existing data for {index_name} on {date}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting existing data: {e}")
            return False
    
    def get_latest_data_date(self, index_name):
        """Get the latest date for which data exists"""
        try:
            query = f"""
            SELECT MAX(trade_date) as latest_date
            FROM {TABLE_SCHEMA['table_name']}
            WHERE index_name = '{index_name}'
            """
            
            self.cursor.execute(query)
            result = self.cursor.fetchone()
            
            if result and result[0]:
                return pd.to_datetime(result[0]).date()
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting latest data date: {e}")
            return None
    
    def get_data_statistics(self, index_name=None, date=None):
        """Get statistics about loaded data"""
        try:
            conditions = []
            if index_name:
                conditions.append(f"index_name = '{index_name}'")
            if date:
                conditions.append(f"trade_date = '{date}'")
            
            where_clause = f"WHERE {' AND '.join(conditions)}" if conditions else ""
            
            query = f"""
            SELECT index_name, 
                   trade_date,
                   COUNT(*) as row_count,
                   COUNT(DISTINCT expiry_bucket) as expiry_types,
                   COUNT(DISTINCT strike) as unique_strikes,
                   MIN(dte) as min_dte,
                   MAX(dte) as max_dte
            FROM {TABLE_SCHEMA['table_name']}
            {where_clause}
            GROUP BY index_name, trade_date
            ORDER BY index_name, trade_date DESC
            LIMIT 10
            """
            
            self.cursor.execute(query)
            results = self.cursor.fetchall()
            
            stats = []
            for row in results:
                stats.append({
                    'index_name': row[0],
                    'trade_date': row[1],
                    'row_count': row[2],
                    'expiry_types': row[3],
                    'unique_strikes': row[4],
                    'min_dte': row[5],
                    'max_dte': row[6]
                })
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting data statistics: {e}")
            return []
    
    def close(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("Closed HeavyDB connection")

class BatchLoader:
    """Handle batch loading of multiple files"""
    
    def __init__(self, loader):
        self.loader = loader
        self.batch_size = ETL_SETTINGS['batch_size']
        
    def load_files(self, file_list, index_name, transformer, validator):
        """Load multiple files with batching"""
        total_files = len(file_list)
        loaded_files = 0
        total_rows = 0
        
        logger.info(f"Loading {total_files} files for {index_name}")
        
        batch_df = pd.DataFrame()
        
        for i, filepath in enumerate(file_list):
            # Transform file
            df = transformer.transform_file(filepath, index_name)
            
            if df is None or len(df) == 0:
                logger.warning(f"Skipping {filepath} - no valid data")
                continue
            
            # Validate data
            if validator and not validator.validate_dataframe(df, index_name):
                logger.warning(f"Skipping {filepath} - validation failed")
                continue
            
            # Add to batch
            batch_df = pd.concat([batch_df, df], ignore_index=True)
            
            # Load batch if it's large enough or last file
            if len(batch_df) >= self.batch_size or i == total_files - 1:
                if self.loader.load_dataframe(batch_df, index_name):
                    loaded_files += 1
                    total_rows += len(batch_df)
                    batch_df = pd.DataFrame()
        
        logger.info(f"Loaded {loaded_files}/{total_files} files, {total_rows:,} total rows")
        return loaded_files, total_rows