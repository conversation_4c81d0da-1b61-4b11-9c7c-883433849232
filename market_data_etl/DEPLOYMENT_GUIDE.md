# Daily ETL Deployment Guide

## Overview
The Daily ETL system automatically downloads and processes multi-index option chain data from Dropbox and loads it into HeavyDB for backtesting.

## System Architecture

```
Dropbox/Local Data Sources
        ↓
    Extractor
        ↓
   Transformer
        ↓
    Validator
        ↓
  HeavyDB Loader
```

## Quick Start

### 1. Initial Setup
```bash
cd /srv/samba/shared/market_data_etl
./scripts/setup_daily_etl.sh
```

### 2. Configure Dropbox URLs
Edit `/srv/samba/shared/market_data_etl/config/etl_config.py`:
```python
INDEX_CONFIG = {
    'NIFTY': {
        'dropbox_url': 'https://www.dropbox.com/s/xxxxx/nifty.zip?dl=0',
        ...
    },
    'BANKNIFTY': {
        'dropbox_url': 'https://www.dropbox.com/s/xxxxx/banknifty.zip?dl=0',
        ...
    },
    # Add other indices...
}
```

### 3. Test Run
```bash
# Dry run (validation only)
python3 daily_etl_runner.py --dry-run

# Run for specific date
python3 daily_etl_runner.py --date 2025-04-01

# Run for specific indices
python3 daily_etl_runner.py --indices NIFTY BANKNIFTY
```

### 4. Monitor ETL
```bash
# Check latest run status
./scripts/monitor_etl.sh

# Generate detailed report
./scripts/monitor_etl.sh --report
```

## Configuration

### Index Configuration
Each index in `etl_config.py` requires:
- `strike_increment`: Strike price increment (e.g., 50 for NIFTY)
- `expiry_types`: List of expiry buckets to process
  - Weekly indices (NIFTY, SENSEX): ['CW', 'NW', 'CM', 'NM']
  - Monthly indices (BANKNIFTY, MIDCAPNIFTY): ['CM', 'NM']
- `data_source`: Either 'dropbox' or 'local'
- `dropbox_url`: Dropbox share link (if using Dropbox)
- `local_path`: Local directory path (if using local files)
- `active`: Enable/disable processing

### ETL Settings
- `batch_size`: Rows to process per batch (default: 100,000)
- `retry_attempts`: Number of download retries (default: 3)
- `data_retention_days`: Keep processed files for N days
- `log_retention_days`: Keep logs for N days

### Quality Thresholds
- `min_rows_per_file`: Minimum expected rows (default: 1,000)
- `max_null_percentage`: Maximum allowed null values (default: 5%)
- `min_strikes_per_expiry`: Minimum strikes per expiry
- `max_dte_days`: Maximum days to expiry allowed

## Data Flow

### 1. Extraction
- Downloads ZIP files from Dropbox
- Extracts CSV files to temp directory
- Handles retry logic for failed downloads

### 2. Transformation
- Detects if data is pre-transformed or raw
- Applies date/time conversions
- Filters by configured expiry types
- Calculates derived fields (if needed)

### 3. Validation
- Row count checks
- Null value validation
- Date range verification
- Strike increment validation
- Zone distribution checks
- Duplicate detection

### 4. Loading
- Uses HeavyDB COPY FROM for performance
- Batch loading for large files
- Automatic cleanup of existing data
- Performance tracking (rows/sec)

## Scheduling

### Cron Job (Installed by setup script)
```cron
30 18 * * 1-5 cd /srv/samba/shared/market_data_etl && python3 daily_etl_runner.py >> logs/cron.log 2>&1
```
Runs Monday-Friday at 6:30 PM IST

### Manual Scheduling
```bash
# Add to crontab
crontab -e

# Or use systemd timer (see setup script output)
```

## Monitoring

### Log Files
- `logs/daily_etl.log`: Main ETL log
- `logs/etl_errors.log`: Error-only log
- `logs/etl_results_YYYYMMDD.json`: Daily run results
- `logs/cron.log`: Cron execution log

### Check Status
```bash
# Recent runs
ls -lt logs/etl_results_*.json | head -5

# Check for errors
grep ERROR logs/daily_etl.log | tail -20

# Disk usage
df -h /srv/samba/shared/market_data_etl
```

### Performance Metrics
Typical loading speeds:
- NIFTY: 800,000+ rows/sec
- Total daily data: 2-4M rows
- Processing time: 5-10 minutes

## Troubleshooting

### Common Issues

1. **No Dropbox URL configured**
   - Update `dropbox_url` in config
   - Ensure URL ends with `?dl=1` for direct download

2. **Download timeout**
   - Increase `download_timeout` in config
   - Check network connectivity

3. **Validation failures**
   - Check zone_name format (time-based vs distance-based)
   - Verify expiry_bucket values match configuration

4. **Loading errors**
   - Check HeavyDB connection settings
   - Verify table schema matches
   - Check disk space

5. **No data for date**
   - ETL uses most recent file if exact date not found
   - Check file naming patterns

### Debug Mode
```bash
# Run with debug logging
python3 -u daily_etl_runner.py --date 2025-04-01 2>&1 | tee debug.log
```

### Manual Data Check
```bash
# Check what's in HeavyDB
echo "SELECT index_name, trade_date, COUNT(*) FROM nifty_option_chain 
      GROUP BY index_name, trade_date 
      ORDER BY trade_date DESC LIMIT 10;" | \
/opt/heavyai/bin/heavysql -p HyperInteractive
```

## Maintenance

### Archive Old Data
```bash
# Archives logs older than retention period
# Runs automatically during ETL
```

### Clean Temp Files
```bash
rm -rf /srv/samba/shared/market_data_etl/temp/*
```

### Backup Configuration
```bash
cp -r config config.backup.$(date +%Y%m%d)
```

## Integration with Backtester

The loaded data is immediately available for backtesting:
```python
# Data is in nifty_option_chain table
# Filtered by index_name and date range
# Includes all indices configured in ETL
```

## Support

### Logs Location
- ETL Logs: `/srv/samba/shared/market_data_etl/logs/`
- HeavyDB Logs: `/var/lib/heavyai/storage/mapd_log/`

### Key Files
- Runner: `daily_etl_runner.py`
- Config: `config/etl_config.py`
- Monitor: `scripts/monitor_etl.sh`
- Setup: `scripts/setup_daily_etl.sh`

## Future Enhancements

1. **Email/Webhook Notifications**
   - Configure in `NOTIFICATION_CONFIG`
   - Alerts on failures

2. **Additional Indices**
   - Add BANKEX configuration
   - Support for custom indices

3. **Real-time Processing**
   - Process intraday updates
   - Stream processing support

4. **Data Quality Reports**
   - Automated quality metrics
   - Anomaly detection