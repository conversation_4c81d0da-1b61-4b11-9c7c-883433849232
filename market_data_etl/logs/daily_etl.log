2025-06-03 21:25:59,186 [INFO] etl [daily_etl_runner.py:54]: ================================================================================
2025-06-03 21:25:59,186 [INFO] etl [daily_etl_runner.py:55]: DAILY ETL RUN - 2025-06-03
2025-06-03 21:25:59,186 [INFO] etl [daily_etl_runner.py:56]: ================================================================================
2025-06-03 21:25:59,187 [INFO] etl [daily_etl_runner.py:57]: Indices to process: NIFTY, BANKNIFTY, MIDCAPNIFTY, SENSEX
2025-06-03 21:25:59,187 [INFO] etl [daily_etl_runner.py:58]: Dry run: True
2025-06-03 21:25:59,187 [INFO] etl [daily_etl_runner.py:88]: 
Processing NIFTY...
2025-06-03 21:25:59,187 [WARNING] etl [daily_etl_runner.py:115]: No Dropbox URL configured for NIFTY
2025-06-03 21:25:59,187 [INFO] etl [daily_etl_runner.py:88]: 
Processing BANKNIFTY...
2025-06-03 21:25:59,187 [WARNING] etl [daily_etl_runner.py:115]: No Dropbox URL configured for BANKNIFTY
2025-06-03 21:25:59,187 [INFO] etl [daily_etl_runner.py:88]: 
Processing MIDCAPNIFTY...
2025-06-03 21:25:59,187 [WARNING] etl [daily_etl_runner.py:115]: No Dropbox URL configured for MIDCAPNIFTY
2025-06-03 21:25:59,187 [INFO] etl [daily_etl_runner.py:88]: 
Processing SENSEX...
2025-06-03 21:25:59,187 [WARNING] etl [daily_etl_runner.py:115]: No Dropbox URL configured for SENSEX
2025-06-03 21:25:59,188 [INFO] etl [daily_etl_runner.py:184]: 
Cleaning up temporary files...
2025-06-03 21:25:59,188 [INFO] etl [daily_etl_runner.py:223]: Results saved to /srv/samba/shared/market_data_etl/logs/etl_results_20250603.json
2025-06-03 21:25:59,188 [INFO] etl [daily_etl_runner.py:229]: 
============================================================
2025-06-03 21:25:59,188 [INFO] etl [daily_etl_runner.py:230]: ETL RUN SUMMARY
2025-06-03 21:25:59,188 [INFO] etl [daily_etl_runner.py:231]: ============================================================
2025-06-03 21:25:59,188 [INFO] etl [daily_etl_runner.py:232]: Run date: 2025-06-03
2025-06-03 21:25:59,188 [INFO] etl [daily_etl_runner.py:233]: Indices processed: 4
2025-06-03 21:25:59,188 [INFO] etl [daily_etl_runner.py:234]: Successful: 4
2025-06-03 21:25:59,188 [INFO] etl [daily_etl_runner.py:235]: Failed: 0
2025-06-03 21:25:59,189 [INFO] etl [daily_etl_runner.py:236]: Total files: 0
2025-06-03 21:25:59,189 [INFO] etl [daily_etl_runner.py:237]: Total rows loaded: 0
2025-06-03 21:25:59,189 [INFO] etl [daily_etl_runner.py:242]: ❌ NIFTY: 0 rows from 0 files
2025-06-03 21:25:59,189 [INFO] etl [daily_etl_runner.py:242]: ❌ BANKNIFTY: 0 rows from 0 files
2025-06-03 21:25:59,189 [INFO] etl [daily_etl_runner.py:242]: ❌ MIDCAPNIFTY: 0 rows from 0 files
2025-06-03 21:25:59,189 [INFO] etl [daily_etl_runner.py:242]: ❌ SENSEX: 0 rows from 0 files
2025-06-03 21:25:59,189 [INFO] etl [daily_etl_runner.py:248]: ============================================================
2025-06-03 21:27:43,186 [INFO] etl [daily_etl_runner.py:54]: ================================================================================
2025-06-03 21:27:43,186 [INFO] etl [daily_etl_runner.py:55]: DAILY ETL RUN - 2025-04-01
2025-06-03 21:27:43,186 [INFO] etl [daily_etl_runner.py:56]: ================================================================================
2025-06-03 21:27:43,186 [INFO] etl [daily_etl_runner.py:57]: Indices to process: NIFTY
2025-06-03 21:27:43,186 [INFO] etl [daily_etl_runner.py:58]: Dry run: True
2025-06-03 21:27:43,186 [INFO] etl [daily_etl_runner.py:88]: 
Processing NIFTY...
2025-06-03 21:27:43,186 [INFO] etl.extractor [dropbox_extractor.py:166]: Extracting NIFTY data for 2025-04-01 from local directory
2025-06-03 21:27:43,187 [WARNING] etl.extractor [dropbox_extractor.py:179]: No files for 2025-04-01, using most recent: IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:27:43,188 [INFO] etl.transformer [data_transformer.py:23]: Transforming /srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_sorted.csv for NIFTY
2025-06-03 21:27:48,569 [INFO] etl.transformer [data_transformer.py:28]: Read 807003 rows from IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:27:49,158 [ERROR] etl.transformer [data_transformer.py:45]: Error transforming /srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_sorted.csv: unsupported operand type(s) for -: 'str' and 'str'
2025-06-03 21:27:49,174 [INFO] etl [daily_etl_runner.py:184]: 
Cleaning up temporary files...
2025-06-03 21:27:49,174 [INFO] etl [daily_etl_runner.py:223]: Results saved to /srv/samba/shared/market_data_etl/logs/etl_results_20250401.json
2025-06-03 21:27:49,174 [INFO] etl [daily_etl_runner.py:229]: 
============================================================
2025-06-03 21:27:49,174 [INFO] etl [daily_etl_runner.py:230]: ETL RUN SUMMARY
2025-06-03 21:27:49,175 [INFO] etl [daily_etl_runner.py:231]: ============================================================
2025-06-03 21:27:49,175 [INFO] etl [daily_etl_runner.py:232]: Run date: 2025-04-01
2025-06-03 21:27:49,175 [INFO] etl [daily_etl_runner.py:233]: Indices processed: 1
2025-06-03 21:27:49,175 [INFO] etl [daily_etl_runner.py:234]: Successful: 1
2025-06-03 21:27:49,175 [INFO] etl [daily_etl_runner.py:235]: Failed: 0
2025-06-03 21:27:49,175 [INFO] etl [daily_etl_runner.py:236]: Total files: 1
2025-06-03 21:27:49,175 [INFO] etl [daily_etl_runner.py:237]: Total rows loaded: 0
2025-06-03 21:27:49,175 [INFO] etl [daily_etl_runner.py:242]: ✅ NIFTY: 0 rows from 0 files
2025-06-03 21:27:49,175 [INFO] etl [daily_etl_runner.py:248]: ============================================================
2025-06-03 21:28:28,659 [INFO] etl [daily_etl_runner.py:54]: ================================================================================
2025-06-03 21:28:28,659 [INFO] etl [daily_etl_runner.py:55]: DAILY ETL RUN - 2025-04-01
2025-06-03 21:28:28,659 [INFO] etl [daily_etl_runner.py:56]: ================================================================================
2025-06-03 21:28:28,659 [INFO] etl [daily_etl_runner.py:57]: Indices to process: NIFTY
2025-06-03 21:28:28,659 [INFO] etl [daily_etl_runner.py:58]: Dry run: True
2025-06-03 21:28:28,659 [INFO] etl [daily_etl_runner.py:88]: 
Processing NIFTY...
2025-06-03 21:28:28,660 [INFO] etl.extractor [dropbox_extractor.py:166]: Extracting NIFTY data for 2025-04-01 from local directory
2025-06-03 21:28:28,661 [WARNING] etl.extractor [dropbox_extractor.py:179]: No files for 2025-04-01, using most recent: IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:28:28,661 [INFO] etl.transformer [data_transformer.py:23]: Transforming /srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_sorted.csv for NIFTY
2025-06-03 21:28:34,181 [INFO] etl.transformer [data_transformer.py:28]: Read 807003 rows from IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:28:34,182 [INFO] etl.transformer [data_transformer.py:35]: Data already in final format, validating...
2025-06-03 21:28:34,406 [INFO] etl.transformer [data_transformer.py:44]: Using 807003 pre-transformed rows for NIFTY
2025-06-03 21:28:34,406 [INFO] etl.validator [data_validator.py:23]: Validating 807003 rows for NIFTY
2025-06-03 21:28:34,754 [ERROR] etl.validator [data_validator.py:50]: ❌ Validation failed for NIFTY
2025-06-03 21:28:34,754 [ERROR] etl.validator [data_validator.py:53]:   - Zone Distribution: ATM percentage: 0.0%
2025-06-03 21:28:34,770 [INFO] etl [daily_etl_runner.py:184]: 
Cleaning up temporary files...
2025-06-03 21:28:34,770 [INFO] etl [daily_etl_runner.py:223]: Results saved to /srv/samba/shared/market_data_etl/logs/etl_results_20250401.json
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:229]: 
============================================================
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:230]: ETL RUN SUMMARY
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:231]: ============================================================
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:232]: Run date: 2025-04-01
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:233]: Indices processed: 1
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:234]: Successful: 1
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:235]: Failed: 0
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:236]: Total files: 1
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:237]: Total rows loaded: 0
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:242]: ✅ NIFTY: 0 rows from 0 files
2025-06-03 21:28:34,771 [INFO] etl [daily_etl_runner.py:248]: ============================================================
2025-06-03 21:30:23,959 [INFO] etl [daily_etl_runner.py:54]: ================================================================================
2025-06-03 21:30:23,960 [INFO] etl [daily_etl_runner.py:55]: DAILY ETL RUN - 2025-04-01
2025-06-03 21:30:23,960 [INFO] etl [daily_etl_runner.py:56]: ================================================================================
2025-06-03 21:30:23,960 [INFO] etl [daily_etl_runner.py:57]: Indices to process: NIFTY
2025-06-03 21:30:23,960 [INFO] etl [daily_etl_runner.py:58]: Dry run: True
2025-06-03 21:30:23,960 [INFO] etl [daily_etl_runner.py:88]: 
Processing NIFTY...
2025-06-03 21:30:23,960 [INFO] etl.extractor [dropbox_extractor.py:166]: Extracting NIFTY data for 2025-04-01 from local directory
2025-06-03 21:30:23,961 [WARNING] etl.extractor [dropbox_extractor.py:179]: No files for 2025-04-01, using most recent: IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:30:23,961 [INFO] etl.transformer [data_transformer.py:23]: Transforming /srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_sorted.csv for NIFTY
2025-06-03 21:30:30,909 [INFO] etl.transformer [data_transformer.py:28]: Read 807003 rows from IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:30:30,910 [INFO] etl.transformer [data_transformer.py:35]: Data already in final format, validating...
2025-06-03 21:30:31,128 [INFO] etl.transformer [data_transformer.py:44]: Using 807003 pre-transformed rows for NIFTY
2025-06-03 21:30:31,128 [INFO] etl.validator [data_validator.py:23]: Validating 807003 rows for NIFTY
2025-06-03 21:30:31,492 [ERROR] etl.validator [data_validator.py:50]: ❌ Validation failed for NIFTY
2025-06-03 21:30:31,493 [ERROR] etl.validator [data_validator.py:53]:   - Zone Distribution: ATM percentage: 0.0%
2025-06-03 21:30:31,509 [INFO] etl [daily_etl_runner.py:184]: 
Cleaning up temporary files...
2025-06-03 21:30:31,509 [INFO] etl [daily_etl_runner.py:223]: Results saved to /srv/samba/shared/market_data_etl/logs/etl_results_20250401.json
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:229]: 
============================================================
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:230]: ETL RUN SUMMARY
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:231]: ============================================================
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:232]: Run date: 2025-04-01
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:233]: Indices processed: 1
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:234]: Successful: 1
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:235]: Failed: 0
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:236]: Total files: 1
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:237]: Total rows loaded: 0
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:242]: ✅ NIFTY: 0 rows from 0 files
2025-06-03 21:30:31,510 [INFO] etl [daily_etl_runner.py:248]: ============================================================
2025-06-03 21:31:34,297 [INFO] etl [daily_etl_runner.py:54]: ================================================================================
2025-06-03 21:31:34,297 [INFO] etl [daily_etl_runner.py:55]: DAILY ETL RUN - 2025-04-01
2025-06-03 21:31:34,297 [INFO] etl [daily_etl_runner.py:56]: ================================================================================
2025-06-03 21:31:34,297 [INFO] etl [daily_etl_runner.py:57]: Indices to process: NIFTY
2025-06-03 21:31:34,297 [INFO] etl [daily_etl_runner.py:58]: Dry run: True
2025-06-03 21:31:34,297 [INFO] etl [daily_etl_runner.py:88]: 
Processing NIFTY...
2025-06-03 21:31:34,297 [INFO] etl.extractor [dropbox_extractor.py:166]: Extracting NIFTY data for 2025-04-01 from local directory
2025-06-03 21:31:34,298 [WARNING] etl.extractor [dropbox_extractor.py:179]: No files for 2025-04-01, using most recent: IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:31:34,299 [INFO] etl.transformer [data_transformer.py:23]: Transforming /srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_sorted.csv for NIFTY
2025-06-03 21:31:39,879 [INFO] etl.transformer [data_transformer.py:28]: Read 807003 rows from IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:31:39,879 [INFO] etl.transformer [data_transformer.py:35]: Data already in final format, validating...
2025-06-03 21:31:40,102 [INFO] etl.transformer [data_transformer.py:44]: Using 807003 pre-transformed rows for NIFTY
2025-06-03 21:31:40,102 [INFO] etl.validator [data_validator.py:23]: Validating 807003 rows for NIFTY
2025-06-03 21:31:40,461 [INFO] etl.validator [data_validator.py:48]: ✅ All validations passed for NIFTY
2025-06-03 21:31:40,477 [INFO] etl [daily_etl_runner.py:184]: 
Cleaning up temporary files...
2025-06-03 21:31:40,478 [INFO] etl [daily_etl_runner.py:223]: Results saved to /srv/samba/shared/market_data_etl/logs/etl_results_20250401.json
2025-06-03 21:31:40,478 [INFO] etl [daily_etl_runner.py:229]: 
============================================================
2025-06-03 21:31:40,478 [INFO] etl [daily_etl_runner.py:230]: ETL RUN SUMMARY
2025-06-03 21:31:40,478 [INFO] etl [daily_etl_runner.py:231]: ============================================================
2025-06-03 21:31:40,478 [INFO] etl [daily_etl_runner.py:232]: Run date: 2025-04-01
2025-06-03 21:31:40,478 [INFO] etl [daily_etl_runner.py:233]: Indices processed: 1
2025-06-03 21:31:40,478 [INFO] etl [daily_etl_runner.py:234]: Successful: 1
2025-06-03 21:31:40,479 [INFO] etl [daily_etl_runner.py:235]: Failed: 0
2025-06-03 21:31:40,479 [INFO] etl [daily_etl_runner.py:236]: Total files: 1
2025-06-03 21:31:40,479 [INFO] etl [daily_etl_runner.py:237]: Total rows loaded: 807,003
2025-06-03 21:31:40,479 [INFO] etl [daily_etl_runner.py:242]: ✅ NIFTY: 807,003 rows from 1 files
2025-06-03 21:31:40,479 [INFO] etl [daily_etl_runner.py:248]: ============================================================
2025-06-03 21:31:48,648 [INFO] etl.loader [heavydb_loader.py:29]: Connected to HeavyDB
2025-06-03 21:31:48,649 [INFO] etl [daily_etl_runner.py:54]: ================================================================================
2025-06-03 21:31:48,649 [INFO] etl [daily_etl_runner.py:55]: DAILY ETL RUN - 2025-04-01
2025-06-03 21:31:48,649 [INFO] etl [daily_etl_runner.py:56]: ================================================================================
2025-06-03 21:31:48,649 [INFO] etl [daily_etl_runner.py:57]: Indices to process: NIFTY
2025-06-03 21:31:48,649 [INFO] etl [daily_etl_runner.py:58]: Dry run: False
2025-06-03 21:31:48,649 [INFO] etl [daily_etl_runner.py:88]: 
Processing NIFTY...
2025-06-03 21:31:48,859 [INFO] etl.loader [heavydb_loader.py:89]: Found 19,682 existing rows for NIFTY on 2025-04-01
2025-06-03 21:31:48,859 [INFO] etl [daily_etl_runner.py:104]: Data already exists for NIFTY on 2025-04-01
2025-06-03 21:31:48,859 [INFO] etl [daily_etl_runner.py:107]: Deleting existing data...
2025-06-03 21:31:49,192 [INFO] etl.loader [heavydb_loader.py:107]: Deleted existing data for NIFTY on 2025-04-01
2025-06-03 21:31:49,193 [INFO] etl.extractor [dropbox_extractor.py:166]: Extracting NIFTY data for 2025-04-01 from local directory
2025-06-03 21:31:49,194 [WARNING] etl.extractor [dropbox_extractor.py:179]: No files for 2025-04-01, using most recent: IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:31:49,194 [INFO] etl.loader [heavydb_loader.py:203]: Loading 1 files for NIFTY
2025-06-03 21:31:49,195 [INFO] etl.transformer [data_transformer.py:23]: Transforming /srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_sorted.csv for NIFTY
2025-06-03 21:31:54,975 [INFO] etl.transformer [data_transformer.py:28]: Read 807003 rows from IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:31:54,975 [INFO] etl.transformer [data_transformer.py:35]: Data already in final format, validating...
2025-06-03 21:31:55,191 [INFO] etl.transformer [data_transformer.py:44]: Using 807003 pre-transformed rows for NIFTY
2025-06-03 21:31:55,191 [INFO] etl.validator [data_validator.py:23]: Validating 807003 rows for NIFTY
2025-06-03 21:31:55,577 [INFO] etl.validator [data_validator.py:48]: ✅ All validations passed for NIFTY
2025-06-03 21:31:55,729 [INFO] etl.loader [heavydb_loader.py:40]: Loading 807003 rows for NIFTY
2025-06-03 21:32:12,184 [INFO] etl.loader [heavydb_loader.py:61]: ✅ Loaded 807,003 rows in 0.99s (816,035 rows/sec)
2025-06-03 21:32:12,281 [INFO] etl.loader [heavydb_loader.py:230]: Loaded 1/1 files, 807,003 total rows
2025-06-03 21:32:12,312 [INFO] etl [daily_etl_runner.py:184]: 
Cleaning up temporary files...
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:223]: Results saved to /srv/samba/shared/market_data_etl/logs/etl_results_20250401.json
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:229]: 
============================================================
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:230]: ETL RUN SUMMARY
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:231]: ============================================================
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:232]: Run date: 2025-04-01
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:233]: Indices processed: 1
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:234]: Successful: 1
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:235]: Failed: 0
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:236]: Total files: 1
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:237]: Total rows loaded: 807,003
2025-06-03 21:32:12,313 [INFO] etl [daily_etl_runner.py:242]: ✅ NIFTY: 807,003 rows from 1 files
2025-06-03 21:32:12,314 [INFO] etl [daily_etl_runner.py:248]: ============================================================
2025-06-03 21:32:12,314 [INFO] etl.loader [heavydb_loader.py:188]: Closed HeavyDB connection
2025-06-03 21:32:20,009 [INFO] etl [daily_etl_runner.py:54]: ================================================================================
2025-06-03 21:32:20,009 [INFO] etl [daily_etl_runner.py:55]: DAILY ETL RUN - 2025-04-01
2025-06-03 21:32:20,009 [INFO] etl [daily_etl_runner.py:56]: ================================================================================
2025-06-03 21:32:20,010 [INFO] etl [daily_etl_runner.py:57]: Indices to process: NIFTY, BANKNIFTY, MIDCAPNIFTY, SENSEX
2025-06-03 21:32:20,010 [INFO] etl [daily_etl_runner.py:58]: Dry run: True
2025-06-03 21:32:20,010 [INFO] etl [daily_etl_runner.py:88]: 
Processing NIFTY...
2025-06-03 21:32:20,010 [INFO] etl.extractor [dropbox_extractor.py:166]: Extracting NIFTY data for 2025-04-01 from local directory
2025-06-03 21:32:20,011 [WARNING] etl.extractor [dropbox_extractor.py:179]: No files for 2025-04-01, using most recent: IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:32:20,011 [INFO] etl.transformer [data_transformer.py:23]: Transforming /srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_apr_nifty_futures_sorted.csv for NIFTY
2025-06-03 21:32:25,483 [INFO] etl.transformer [data_transformer.py:28]: Read 807003 rows from IV_2025_apr_nifty_futures_sorted.csv
2025-06-03 21:32:25,483 [INFO] etl.transformer [data_transformer.py:35]: Data already in final format, validating...
2025-06-03 21:32:25,706 [INFO] etl.transformer [data_transformer.py:44]: Using 807003 pre-transformed rows for NIFTY
2025-06-03 21:32:25,706 [INFO] etl.validator [data_validator.py:23]: Validating 807003 rows for NIFTY
2025-06-03 21:32:26,068 [INFO] etl.validator [data_validator.py:48]: ✅ All validations passed for NIFTY
2025-06-03 21:32:26,085 [INFO] etl [daily_etl_runner.py:88]: 
Processing BANKNIFTY...
2025-06-03 21:32:26,085 [INFO] etl.extractor [dropbox_extractor.py:166]: Extracting BANKNIFTY data for 2025-04-01 from local directory
2025-06-03 21:32:26,085 [WARNING] etl [daily_etl_runner.py:136]: No files found for BANKNIFTY
2025-06-03 21:32:26,086 [INFO] etl [daily_etl_runner.py:88]: 
Processing MIDCAPNIFTY...
2025-06-03 21:32:26,086 [INFO] etl.extractor [dropbox_extractor.py:166]: Extracting MIDCAPNIFTY data for 2025-04-01 from local directory
2025-06-03 21:32:26,086 [WARNING] etl [daily_etl_runner.py:136]: No files found for MIDCAPNIFTY
2025-06-03 21:32:26,086 [INFO] etl [daily_etl_runner.py:88]: 
Processing SENSEX...
2025-06-03 21:32:26,086 [INFO] etl.extractor [dropbox_extractor.py:166]: Extracting SENSEX data for 2025-04-01 from local directory
2025-06-03 21:32:26,086 [WARNING] etl [daily_etl_runner.py:136]: No files found for SENSEX
2025-06-03 21:32:26,086 [INFO] etl [daily_etl_runner.py:184]: 
Cleaning up temporary files...
2025-06-03 21:32:26,087 [INFO] etl [daily_etl_runner.py:223]: Results saved to /srv/samba/shared/market_data_etl/logs/etl_results_20250401.json
2025-06-03 21:32:26,087 [INFO] etl [daily_etl_runner.py:229]: 
============================================================
2025-06-03 21:32:26,087 [INFO] etl [daily_etl_runner.py:230]: ETL RUN SUMMARY
2025-06-03 21:32:26,087 [INFO] etl [daily_etl_runner.py:231]: ============================================================
2025-06-03 21:32:26,087 [INFO] etl [daily_etl_runner.py:232]: Run date: 2025-04-01
2025-06-03 21:32:26,087 [INFO] etl [daily_etl_runner.py:233]: Indices processed: 4
2025-06-03 21:32:26,087 [INFO] etl [daily_etl_runner.py:234]: Successful: 4
2025-06-03 21:32:26,087 [INFO] etl [daily_etl_runner.py:235]: Failed: 0
2025-06-03 21:32:26,087 [INFO] etl [daily_etl_runner.py:236]: Total files: 1
2025-06-03 21:32:26,088 [INFO] etl [daily_etl_runner.py:237]: Total rows loaded: 807,003
2025-06-03 21:32:26,088 [INFO] etl [daily_etl_runner.py:242]: ✅ NIFTY: 807,003 rows from 1 files
2025-06-03 21:32:26,088 [INFO] etl [daily_etl_runner.py:242]: ❌ BANKNIFTY: 0 rows from 0 files
2025-06-03 21:32:26,088 [INFO] etl [daily_etl_runner.py:242]: ❌ MIDCAPNIFTY: 0 rows from 0 files
2025-06-03 21:32:26,088 [INFO] etl [daily_etl_runner.py:242]: ❌ SENSEX: 0 rows from 0 files
2025-06-03 21:32:26,088 [INFO] etl [daily_etl_runner.py:248]: ============================================================
