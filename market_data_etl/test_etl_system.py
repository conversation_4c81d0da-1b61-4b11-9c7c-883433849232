#!/usr/bin/env python3
"""
Test script to verify ETL system components
"""

import sys
import os
import yaml
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    try:
        from market_data_etl.extractors import DropboxExtractor
        print("✓ Extractors imported successfully")
    except Exception as e:
        print(f"✗ Failed to import extractors: {e}")
        return False
        
    try:
        from market_data_etl.validators import OptionChainValidator
        print("✓ Validators imported successfully")
    except Exception as e:
        print(f"✗ Failed to import validators: {e}")
        return False
        
    try:
        from market_data_etl.transformers import OptionChainTransformer
        print("✓ Transformers imported successfully")
    except Exception as e:
        print(f"✗ Failed to import transformers: {e}")
        return False
        
    try:
        from market_data_etl.loaders import HeavyDBLoader
        print("✓ Loaders imported successfully")
    except Exception as e:
        print(f"✗ Failed to import loaders: {e}")
        return False
        
    return True

def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    config_path = Path(__file__).parent / 'config' / 'etl_config.yaml'
    
    if not config_path.exists():
        print(f"✗ Configuration file not found: {config_path}")
        return False
        
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        print("✓ Configuration loaded successfully")
        
        # Check key sections
        required_sections = ['dropbox', 'indices', 'heavydb', 'etl']
        for section in required_sections:
            if section in config:
                print(f"  ✓ {section} section found")
            else:
                print(f"  ✗ {section} section missing")
                return False
                
    except Exception as e:
        print(f"✗ Failed to load configuration: {e}")
        return False
        
    return True

def test_database_connection():
    """Test HeavyDB connection"""
    print("\nTesting database connection...")
    try:
        sys.path.append('/srv/samba/shared')
        from bt.dal.heavydb_conn import get_conn
        
        conn = get_conn()
        cursor = conn.cursor()
        
        # Test connection
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        print("✓ Database connection successful")
        
        # Check table exists
        cursor.execute("SELECT COUNT(*) FROM nifty_option_chain LIMIT 1")
        print("✓ Table nifty_option_chain exists")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False
        
    return True

def test_data_availability():
    """Test if sample data is available"""
    print("\nTesting data availability...")
    data_dirs = [
        Path("/srv/samba/shared/market_data/nifty/oc_with_futures"),
        Path("/srv/samba/shared/market_data")
    ]
    
    found_files = False
    for data_dir in data_dirs:
        if data_dir.exists():
            csv_files = list(data_dir.glob("**/*.csv"))[:5]  # First 5 files
            if csv_files:
                print(f"✓ Found {len(list(data_dir.glob('**/*.csv')))} CSV files in {data_dir}")
                print("  Sample files:")
                for f in csv_files:
                    print(f"    - {f.name}")
                found_files = True
                break
                
    if not found_files:
        print("✗ No data files found in configured directories")
        return False
        
    return True

def test_etl_components():
    """Test ETL components with sample data"""
    print("\nTesting ETL components...")
    
    # Load config
    config_path = Path(__file__).parent / 'config' / 'etl_config.yaml'
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
        
    try:
        # Test extractor
        from market_data_etl.extractors import DropboxExtractor
        extractor = DropboxExtractor(config)
        print("✓ Extractor initialized")
        
        # Test validator
        from market_data_etl.validators import OptionChainValidator
        validator = OptionChainValidator(config)
        print("✓ Validator initialized")
        
        # Test transformer
        from market_data_etl.transformers import OptionChainTransformer
        transformer = OptionChainTransformer(config)
        print("✓ Transformer initialized")
        
        # Test loader (without connecting)
        from market_data_etl.loaders import HeavyDBLoader
        loader = HeavyDBLoader(config)
        print("✓ Loader initialized")
        
    except Exception as e:
        print(f"✗ Component initialization failed: {e}")
        return False
        
    return True

def main():
    """Run all tests"""
    print("ETL System Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Database Connection", test_database_connection),
        ("Data Availability", test_data_availability),
        ("ETL Components", test_etl_components)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n✗ {test_name} test crashed: {e}")
            results.append((test_name, False))
            
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n✓ All tests passed! ETL system is ready to use.")
        print("\nNext steps:")
        print("1. Run a dry-run test: python3 daily_etl_runner.py --dry-run")
        print("2. Run manual ETL: ./scripts/manual_etl_run.sh")
        print("3. Setup cron job: ./scripts/setup_daily_etl.sh")
    else:
        print("\n✗ Some tests failed. Please fix the issues before proceeding.")
        
    return 0 if passed == total else 1

if __name__ == '__main__':
    sys.exit(main())