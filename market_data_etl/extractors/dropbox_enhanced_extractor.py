#!/usr/bin/env python3
"""
Enhanced Dropbox extractor with file browsing and date-based filtering
"""
import os
import sys
import requests
import zipfile
import logging
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs, quote, unquote
import time
import re
from typing import List, Dict, Optional, Tuple
import json
from bs4 import BeautifulSoup

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.etl_config import DROPBOX_CONFIG, TEMP_DIR

logger = logging.getLogger('etl.extractor')

class EnhancedDropboxExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(DROPBOX_CONFIG['headers'])
        self.base_url = "https://www.dropbox.com/scl/fo/gy85j8fe4qyff97urjf9j/AOpgTIMN9bWpgByhwOCcRNc"
        
    def list_files_in_folder(self, folder_url: str = None, date_filter: Optional[datetime] = None) -> Dict:
        """
        List files in a Dropbox folder with optional date filtering
        
        Args:
            folder_url: Dropbox folder URL (uses base_url if None)
            date_filter: If provided, only return files matching this date
            
        Returns:
            Dict with 'files' and 'folders' lists
        """
        url = folder_url or self.base_url
        
        try:
            # Convert to browseable URL
            if 'dl=0' not in url and 'dl=1' not in url:
                url += '&dl=0' if '?' in url else '?dl=0'
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # Parse HTML to extract file listing
            soup = BeautifulSoup(response.text, 'html.parser')
            
            files = []
            folders = []
            
            # Look for file entries in the Dropbox interface
            # This is a simplified approach - in production, use Dropbox API
            file_entries = soup.find_all('div', class_='sl-grid-cell')
            
            for entry in file_entries:
                name_elem = entry.find('span', class_='sl-grid-filename')
                if name_elem:
                    name = name_elem.text.strip()
                    
                    # Check if it's a folder or file
                    if entry.find('svg', class_='folder-icon'):
                        folders.append({
                            'name': name,
                            'type': 'folder',
                            'path': f"/{name}"
                        })
                    else:
                        # It's a file
                        size_elem = entry.find('span', class_='sl-grid-filesize')
                        size = size_elem.text.strip() if size_elem else 'Unknown'
                        
                        # Extract date from filename if possible
                        file_date = self._extract_date_from_filename(name)
                        
                        # Apply date filter if provided
                        if date_filter and file_date:
                            if file_date.date() != date_filter.date():
                                continue
                        
                        files.append({
                            'name': name,
                            'size': size,
                            'type': 'file',
                            'path': f"/{name}",
                            'date': file_date.isoformat() if file_date else None
                        })
            
            # If no entries found, try alternative parsing
            if not files and not folders:
                # Look for direct download links
                links = soup.find_all('a', href=True)
                for link in links:
                    href = link.get('href', '')
                    if '.csv' in href.lower() or '.zip' in href.lower():
                        name = os.path.basename(unquote(href.split('?')[0]))
                        if name:
                            files.append({
                                'name': name,
                                'size': 'Unknown',
                                'type': 'file',
                                'path': f"/{name}",
                                'url': href
                            })
            
            return {
                'files': sorted(files, key=lambda x: x['name']),
                'folders': sorted(folders, key=lambda x: x['name']),
                'total_files': len(files),
                'total_folders': len(folders)
            }
            
        except Exception as e:
            logger.error(f"Error listing Dropbox files: {e}")
            return {'files': [], 'folders': [], 'error': str(e)}
    
    def find_latest_files(self, index_name: str, lookback_days: int = 7) -> List[Dict]:
        """
        Find the latest files for a specific index
        
        Args:
            index_name: Index to search for (e.g., 'NIFTY')
            lookback_days: Number of days to look back
            
        Returns:
            List of file information dictionaries
        """
        all_files = []
        
        # List files in base folder
        result = self.list_files_in_folder()
        
        # Filter files by index name
        pattern = re.compile(f'.*{index_name}.*\\.csv', re.IGNORECASE)
        
        for file_info in result.get('files', []):
            if pattern.match(file_info['name']):
                all_files.append(file_info)
        
        # Sort by date (newest first)
        all_files.sort(key=lambda x: x.get('date', ''), reverse=True)
        
        # Return files from last N days
        cutoff_date = datetime.now() - timedelta(days=lookback_days)
        recent_files = []
        
        for file_info in all_files:
            if file_info.get('date'):
                file_date = datetime.fromisoformat(file_info['date'])
                if file_date >= cutoff_date:
                    recent_files.append(file_info)
        
        return recent_files
    
    def download_file_by_info(self, file_info: Dict, index_name: str) -> Optional[str]:
        """
        Download a file using file information dictionary
        
        Args:
            file_info: File information from list_files_in_folder
            index_name: Index name for organizing downloads
            
        Returns:
            Path to downloaded file or None if failed
        """
        try:
            # Create temp directory for this index
            index_temp_dir = os.path.join(TEMP_DIR, index_name.lower(), 
                                        datetime.now().strftime('%Y%m%d'))
            os.makedirs(index_temp_dir, exist_ok=True)
            
            # Get direct download URL
            download_url = file_info.get('url')
            if not download_url:
                # Construct download URL from file path
                base_url = self.base_url.split('?')[0]
                file_path = file_info['path']
                download_url = f"{base_url}{file_path}?dl=1"
            
            # Download file
            downloaded_file = self._download_file(download_url, index_temp_dir, index_name)
            
            if downloaded_file and downloaded_file.endswith('.zip'):
                # Extract if it's a zip file
                extracted_files = self._extract_zip(downloaded_file, index_temp_dir)
                os.remove(downloaded_file)  # Remove zip after extraction
                return extracted_files[0] if extracted_files else None
            
            return downloaded_file
            
        except Exception as e:
            logger.error(f"Error downloading file {file_info['name']}: {e}")
            return None
    
    def extract_from_url(self, url: str, index_name: str, date: Optional[datetime] = None) -> List[str]:
        """
        Extract data from Dropbox URL with date filtering
        
        This method is compatible with the existing interface
        """
        if not date:
            date = datetime.now()
        
        logger.info(f"Extracting {index_name} data for {date.date()} from Dropbox")
        
        try:
            # List files with date filter
            file_list = self.list_files_in_folder(url, date)
            
            if not file_list['files']:
                logger.warning(f"No files found for {index_name} on {date.date()}")
                return []
            
            # Download matching files
            downloaded_files = []
            
            for file_info in file_list['files']:
                # Check if file matches index pattern
                if index_name.lower() in file_info['name'].lower():
                    downloaded = self.download_file_by_info(file_info, index_name)
                    if downloaded:
                        downloaded_files.append(downloaded)
            
            return downloaded_files
            
        except Exception as e:
            logger.error(f"Error extracting {index_name} data: {e}")
            return []
    
    def _extract_date_from_filename(self, filename: str) -> Optional[datetime]:
        """Extract date from filename using common patterns"""
        patterns = [
            r'(\d{4})[-_](\d{2})[-_](\d{2})',  # YYYY-MM-DD or YYYY_MM_DD
            r'(\d{2})[-_](\d{2})[-_](\d{4})',  # DD-MM-YYYY or DD_MM_YYYY
            r'(\d{8})',                         # YYYYMMDD
            r'(\d{2})(\d{2})(\d{4})',          # DDMMYYYY
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                try:
                    if len(match.groups()) == 1:
                        # YYYYMMDD format
                        date_str = match.group(1)
                        if len(date_str) == 8:
                            return datetime.strptime(date_str, '%Y%m%d')
                    elif len(match.groups()) == 3:
                        # Date with separators
                        groups = match.groups()
                        if len(groups[0]) == 4:
                            # YYYY-MM-DD format
                            return datetime(int(groups[0]), int(groups[1]), int(groups[2]))
                        else:
                            # DD-MM-YYYY format
                            return datetime(int(groups[2]), int(groups[1]), int(groups[0]))
                except ValueError:
                    continue
        
        return None
    
    def _download_file(self, url: str, temp_dir: str, index_name: str) -> Optional[str]:
        """Download file from URL with retry logic"""
        max_retries = 3
        retry_delay = 30
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Downloading {index_name} data (attempt {attempt + 1})")
                
                response = self.session.get(
                    url, 
                    stream=True, 
                    timeout=DROPBOX_CONFIG['download_timeout']
                )
                response.raise_for_status()
                
                # Get filename from Content-Disposition header or URL
                filename = self._get_filename_from_response(response, url, index_name)
                filepath = os.path.join(temp_dir, filename)
                
                # Download in chunks
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0
                
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=DROPBOX_CONFIG['chunk_size']):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            
                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                if progress % 10 < 0.1:  # Log every 10%
                                    logger.info(f"Download progress: {progress:.0f}%")
                
                logger.info(f"Downloaded {filename} successfully ({downloaded:,} bytes)")
                return filepath
                
            except requests.exceptions.RequestException as e:
                logger.error(f"Download error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    raise
        
        return None
    
    def _get_filename_from_response(self, response, url: str, index_name: str) -> str:
        """Extract filename from response headers or URL"""
        # Try Content-Disposition header
        cd = response.headers.get('content-disposition')
        if cd:
            filename_match = re.findall('filename="?([^"]+)"?', cd)
            if filename_match:
                return filename_match[0]
        
        # Try from URL
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        
        if not filename or filename == 'download':
            # Generate filename based on index and date
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{index_name.lower()}_{timestamp}.csv"
        
        return filename
    
    def _extract_zip(self, zip_path: str, extract_dir: str) -> List[str]:
        """Extract zip file and return list of CSV files"""
        logger.info(f"Extracting {zip_path}")
        extracted_files = []
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zf:
                # Extract only CSV files
                for member in zf.namelist():
                    if member.lower().endswith('.csv'):
                        zf.extract(member, extract_dir)
                        extracted_path = os.path.join(extract_dir, member)
                        extracted_files.append(extracted_path)
                        logger.info(f"Extracted: {member}")
            
            logger.info(f"Extracted {len(extracted_files)} CSV files")
            return extracted_files
            
        except Exception as e:
            logger.error(f"Error extracting zip file: {e}")
            return []

# Update the factory function
def get_enhanced_extractor(source_type: str):
    """Factory function to get appropriate extractor"""
    if source_type == 'dropbox':
        return EnhancedDropboxExtractor()
    else:
        # Fall back to original extractor for other types
        from dropbox_extractor import get_extractor
        return get_extractor(source_type)