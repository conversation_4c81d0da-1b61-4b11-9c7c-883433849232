"""
Base Extractor Class for Market Data ETL
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional
from datetime import datetime
from pathlib import Path

class BaseExtractor(ABC):
    """
    Abstract base class for all data extractors
    """
    
    def __init__(self, config: Dict):
        """
        Initialize base extractor
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.temp_dir = Path(config.get('etl', {}).get('temp_dir', '/tmp'))
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
    @abstractmethod
    def check_availability(self, date: datetime, symbol: str) -> Dict:
        """
        Check if data is available for given date and symbol
        
        Args:
            date: Date to check
            symbol: Symbol to check (NIFTY, BANKNIFTY, etc.)
            
        Returns:
            Dict with availability status and metadata
        """
        pass
        
    @abstractmethod
    def extract(self, date: datetime, symbol: str) -> Path:
        """
        Extract data for given date and symbol
        
        Args:
            date: Date to extract
            symbol: Symbol to extract
            
        Returns:
            Path to extracted file
        """
        pass
        
    def cleanup(self):
        """
        Cleanup temporary files
        """
        try:
            for file in self.temp_dir.glob("*"):
                if file.is_file():
                    file.unlink()
                    self.logger.debug(f"Deleted temporary file: {file}")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")
            
    def validate_config(self) -> bool:
        """
        Validate configuration
        
        Returns:
            True if configuration is valid
        """
        required_keys = ['etl', 'indices']
        for key in required_keys:
            if key not in self.config:
                self.logger.error(f"Missing required configuration key: {key}")
                return False
        return True