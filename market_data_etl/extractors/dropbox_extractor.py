#!/usr/bin/env python3
"""
Dropbox data extractor for option chain data
"""
import os
import sys
import requests
import zipfile
import logging
from datetime import datetime, timedelta
from urllib.parse import urlparse, parse_qs
import time

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.etl_config import DROPBOX_CONFIG, TEMP_DIR

logger = logging.getLogger('etl.extractor')

class DropboxExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(DROPBOX_CONFIG['headers'])
        
    def extract_from_url(self, url, index_name, date=None):
        """Extract data from Dropbox URL"""
        if not date:
            date = datetime.now().date()
            
        logger.info(f"Extracting {index_name} data for {date} from Dropbox")
        
        try:
            # Convert Dropbox share link to direct download link
            direct_url = self._convert_to_direct_link(url)
            
            # Create temp directory for this index
            index_temp_dir = os.path.join(TEMP_DIR, index_name.lower(), str(date))
            os.makedirs(index_temp_dir, exist_ok=True)
            
            # Download file
            downloaded_file = self._download_file(direct_url, index_temp_dir, index_name)
            
            if not downloaded_file:
                logger.error(f"Failed to download {index_name} data")
                return []
            
            # Extract if it's a zip file
            if downloaded_file.endswith('.zip'):
                extracted_files = self._extract_zip(downloaded_file, index_temp_dir)
                os.remove(downloaded_file)  # Remove zip after extraction
                return extracted_files
            else:
                return [downloaded_file]
                
        except Exception as e:
            logger.error(f"Error extracting {index_name} data: {e}")
            return []
    
    def _convert_to_direct_link(self, url):
        """Convert Dropbox share link to direct download link"""
        # Handle different Dropbox URL formats
        if 'dropbox.com' in url:
            if 'dl=0' in url:
                return url.replace('dl=0', 'dl=1')
            elif 'dl=1' not in url:
                separator = '&' if '?' in url else '?'
                return f"{url}{separator}dl=1"
        return url
    
    def _download_file(self, url, temp_dir, index_name):
        """Download file from URL with retry logic"""
        max_retries = 3
        retry_delay = 30
        
        for attempt in range(max_retries):
            try:
                logger.info(f"Downloading {index_name} data (attempt {attempt + 1})")
                
                response = self.session.get(
                    url, 
                    stream=True, 
                    timeout=DROPBOX_CONFIG['download_timeout']
                )
                response.raise_for_status()
                
                # Get filename from Content-Disposition header or URL
                filename = self._get_filename_from_response(response, url, index_name)
                filepath = os.path.join(temp_dir, filename)
                
                # Download in chunks
                total_size = int(response.headers.get('content-length', 0))
                downloaded = 0
                
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=DROPBOX_CONFIG['chunk_size']):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)
                            
                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                if progress % 10 < 0.1:  # Log every 10%
                                    logger.info(f"Download progress: {progress:.0f}%")
                
                logger.info(f"Downloaded {filename} successfully ({downloaded:,} bytes)")
                return filepath
                
            except requests.exceptions.RequestException as e:
                logger.error(f"Download error (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    raise
        
        return None
    
    def _get_filename_from_response(self, response, url, index_name):
        """Extract filename from response headers or URL"""
        # Try Content-Disposition header
        cd = response.headers.get('content-disposition')
        if cd:
            filename = cd.split('filename=')[-1].strip('"\'')
            if filename:
                return filename
        
        # Try from URL
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        
        if not filename or filename == 'download':
            # Generate filename based on index and date
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{index_name.lower()}_{timestamp}.zip"
        
        return filename
    
    def _extract_zip(self, zip_path, extract_dir):
        """Extract zip file and return list of CSV files"""
        logger.info(f"Extracting {zip_path}")
        extracted_files = []
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zf:
                # Extract only CSV files
                for member in zf.namelist():
                    if member.lower().endswith('.csv'):
                        zf.extract(member, extract_dir)
                        extracted_path = os.path.join(extract_dir, member)
                        extracted_files.append(extracted_path)
                        logger.info(f"Extracted: {member}")
            
            logger.info(f"Extracted {len(extracted_files)} CSV files")
            return extracted_files
            
        except Exception as e:
            logger.error(f"Error extracting zip file: {e}")
            return []

class LocalExtractor:
    """Extract data from local directories (for testing or backup)"""
    
    def extract_from_directory(self, directory, index_name, date=None):
        """Extract data from local directory"""
        if not date:
            date = datetime.now().date()
        
        logger.info(f"Extracting {index_name} data for {date} from local directory")
        
        # Look for today's data or most recent
        pattern = f"*{date.strftime('%Y%m%d')}*.csv"
        import glob
        
        files = glob.glob(os.path.join(directory, pattern))
        
        if not files:
            # Try to find most recent file
            all_files = glob.glob(os.path.join(directory, "*.csv"))
            if all_files:
                files = [max(all_files, key=os.path.getctime)]
                logger.warning(f"No files for {date}, using most recent: {os.path.basename(files[0])}")
        
        return files

def get_extractor(source_type):
    """Factory function to get appropriate extractor"""
    if source_type == 'dropbox':
        return DropboxExtractor()
    elif source_type == 'local':
        return LocalExtractor()
    else:
        raise ValueError(f"Unknown source type: {source_type}")