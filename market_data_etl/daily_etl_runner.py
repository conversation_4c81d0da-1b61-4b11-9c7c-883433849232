#!/usr/bin/env python3
"""
Daily ETL Runner for Multi-Index Option Chain Data
"""
import os
import sys
import logging
import logging.config
from datetime import datetime, timedelta
import argparse
import json
import shutil

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.etl_config import (
    INDEX_CONFIG, ETL_SETTINGS, LOGGING_CONFIG, 
    TEMP_DIR, LOG_DIR, ARCHIVE_DIR
)
from extractors.dropbox_extractor import get_extractor
from transformers.data_transformer import DataTransformer
from validators.data_validator import DataValidator
from loaders.heavydb_loader import HeavyDBLoader, BatchLoader

# Configure logging
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger('etl')

class DailyETLRunner:
    def __init__(self, run_date=None, indices=None, dry_run=False):
        self.run_date = run_date or datetime.now().date()
        self.indices = indices or [idx for idx, cfg in INDEX_CONFIG.items() if cfg['active']]
        self.dry_run = dry_run
        
        self.transformer = DataTransformer()
        self.validator = DataValidator()
        self.loader = HeavyDBLoader() if not dry_run else None
        
        self.results = {
            'run_date': str(self.run_date),
            'start_time': datetime.now().isoformat(),
            'indices': {},
            'summary': {
                'total_files': 0,
                'total_rows': 0,
                'successful_indices': 0,
                'failed_indices': 0
            }
        }
    
    def run(self):
        """Execute the daily ETL process"""
        logger.info("="*80)
        logger.info(f"DAILY ETL RUN - {self.run_date}")
        logger.info("="*80)
        logger.info(f"Indices to process: {', '.join(self.indices)}")
        logger.info(f"Dry run: {self.dry_run}")
        
        # Check if it's a trading day
        if not self._is_trading_day():
            logger.info("Not a trading day - skipping ETL")
            return
        
        # Process each index
        for index_name in self.indices:
            try:
                self._process_index(index_name)
                self.results['summary']['successful_indices'] += 1
            except Exception as e:
                logger.error(f"Failed to process {index_name}: {e}")
                self.results['indices'][index_name] = {
                    'status': 'failed',
                    'error': str(e)
                }
                self.results['summary']['failed_indices'] += 1
        
        # Cleanup and finalize
        self._cleanup()
        self._save_results()
        self._log_summary()
        
        if self.loader:
            self.loader.close()
    
    def _process_index(self, index_name):
        """Process a single index"""
        logger.info(f"\nProcessing {index_name}...")
        
        config = INDEX_CONFIG[index_name]
        index_results = {
            'status': 'processing',
            'files_extracted': 0,
            'files_processed': 0,
            'rows_loaded': 0,
            'start_time': datetime.now().isoformat()
        }
        
        try:
            # Check for existing data
            if self.loader:
                existing_count = self.loader.check_existing_data(index_name, self.run_date)
                if existing_count > 0:
                    logger.info(f"Data already exists for {index_name} on {self.run_date}")
                    if not self.dry_run:
                        # Option to replace or skip
                        logger.info("Deleting existing data...")
                        self.loader.delete_existing_data(index_name, self.run_date)
            
            # Extract data
            extractor = get_extractor(config['data_source'])
            
            if config['data_source'] == 'dropbox':
                if not config.get('dropbox_url'):
                    logger.warning(f"No Dropbox URL configured for {index_name}")
                    index_results['status'] = 'skipped'
                    index_results['reason'] = 'No Dropbox URL'
                    self.results['indices'][index_name] = index_results
                    return
                
                files = extractor.extract_from_url(
                    config['dropbox_url'], 
                    index_name, 
                    self.run_date
                )
            else:
                files = extractor.extract_from_directory(
                    config['local_path'],
                    index_name,
                    self.run_date
                )
            
            index_results['files_extracted'] = len(files)
            
            if not files:
                logger.warning(f"No files found for {index_name}")
                index_results['status'] = 'no_data'
                self.results['indices'][index_name] = index_results
                return
            
            # Process files
            if self.dry_run:
                # In dry run, just transform and validate
                for filepath in files:
                    df = self.transformer.transform_file(filepath, index_name)
                    if df is not None:
                        is_valid = self.validator.validate_dataframe(df, index_name)
                        index_results['files_processed'] += 1 if is_valid else 0
                        index_results['rows_loaded'] += len(df) if is_valid else 0
            else:
                # Full processing with loading
                batch_loader = BatchLoader(self.loader)
                loaded_files, total_rows = batch_loader.load_files(
                    files, index_name, self.transformer, self.validator
                )
                
                index_results['files_processed'] = loaded_files
                index_results['rows_loaded'] = total_rows
            
            # Update results
            index_results['status'] = 'success'
            index_results['end_time'] = datetime.now().isoformat()
            
            self.results['summary']['total_files'] += index_results['files_extracted']
            self.results['summary']['total_rows'] += index_results['rows_loaded']
            
        except Exception as e:
            logger.error(f"Error processing {index_name}: {e}")
            index_results['status'] = 'error'
            index_results['error'] = str(e)
            raise
        finally:
            self.results['indices'][index_name] = index_results
    
    def _is_trading_day(self):
        """Check if today is a trading day"""
        # Simple check - skip weekends
        # In production, should check against market calendar
        weekday = self.run_date.weekday()
        return weekday < 5  # Monday = 0, Friday = 4
    
    def _cleanup(self):
        """Clean up temporary files"""
        logger.info("\nCleaning up temporary files...")
        
        # Clean temp directory
        if os.path.exists(TEMP_DIR):
            try:
                shutil.rmtree(TEMP_DIR)
                os.makedirs(TEMP_DIR, exist_ok=True)
            except Exception as e:
                logger.error(f"Error cleaning temp directory: {e}")
        
        # Archive old logs
        self._archive_old_logs()
    
    def _archive_old_logs(self):
        """Archive logs older than retention period"""
        retention_days = ETL_SETTINGS['log_retention_days']
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        for filename in os.listdir(LOG_DIR):
            filepath = os.path.join(LOG_DIR, filename)
            if os.path.isfile(filepath):
                file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                if file_time < cutoff_date:
                    archive_path = os.path.join(ARCHIVE_DIR, filename)
                    shutil.move(filepath, archive_path)
                    logger.info(f"Archived old log: {filename}")
    
    def _save_results(self):
        """Save run results to JSON"""
        self.results['end_time'] = datetime.now().isoformat()
        
        results_file = os.path.join(
            LOG_DIR, 
            f"etl_results_{self.run_date.strftime('%Y%m%d')}.json"
        )
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        logger.info(f"Results saved to {results_file}")
    
    def _log_summary(self):
        """Log summary of ETL run"""
        summary = self.results['summary']
        
        logger.info("\n" + "="*60)
        logger.info("ETL RUN SUMMARY")
        logger.info("="*60)
        logger.info(f"Run date: {self.run_date}")
        logger.info(f"Indices processed: {len(self.indices)}")
        logger.info(f"Successful: {summary['successful_indices']}")
        logger.info(f"Failed: {summary['failed_indices']}")
        logger.info(f"Total files: {summary['total_files']}")
        logger.info(f"Total rows loaded: {summary['total_rows']:,}")
        
        # Log per-index summary
        for index_name, result in self.results['indices'].items():
            status_icon = "✅" if result['status'] == 'success' else "❌"
            logger.info(
                f"{status_icon} {index_name}: "
                f"{result.get('rows_loaded', 0):,} rows from "
                f"{result.get('files_processed', 0)} files"
            )
        
        logger.info("="*60)

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Daily ETL Runner for Option Chain Data')
    parser.add_argument(
        '--date', 
        type=str, 
        help='Run date (YYYY-MM-DD). Default: today'
    )
    parser.add_argument(
        '--indices', 
        nargs='+', 
        choices=list(INDEX_CONFIG.keys()),
        help='Indices to process. Default: all active indices'
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='Run without loading data (validation only)'
    )
    parser.add_argument(
        '--config', 
        type=str,
        help='Path to custom configuration file'
    )
    
    args = parser.parse_args()
    
    # Parse run date
    run_date = None
    if args.date:
        try:
            run_date = datetime.strptime(args.date, '%Y-%m-%d').date()
        except ValueError:
            logger.error(f"Invalid date format: {args.date}")
            sys.exit(1)
    
    # Create and run ETL
    runner = DailyETLRunner(
        run_date=run_date,
        indices=args.indices,
        dry_run=args.dry_run
    )
    
    try:
        runner.run()
    except Exception as e:
        logger.error(f"ETL run failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()