# Market Data ETL System

A comprehensive ETL (Extract, Transform, Load) pipeline for processing daily market data from multiple indices and loading into HeavyDB.

## Overview

This ETL system automates the daily processing of option chain data for:
- **NIFTY** - All expiry buckets (CW, NW, CM, NM)
- **BANKNIFTY** - Monthly expiries only (CM, NM)
- **MIDCAPNIFTY** - Monthly expiries only (CM, NM)
- **SENSEX** - All expiry buckets (CW, NW, CM, NM)

## Features

- **Automated Daily Processing**: Runs via cron after market close
- **Multi-Index Support**: Processes multiple indices in parallel
- **Data Validation**: Comprehensive validation of data quality
- **Transformation Pipeline**: Standardizes data format for HeavyDB
- **High-Performance Loading**: Uses HeavyDB's COPY FROM for optimal speed
- **Error Handling**: Robust error handling with retry mechanisms
- **Monitoring**: Detailed logging and optional notifications
- **Manual Override**: Scripts for ad-hoc processing

## Directory Structure

```
market_data_etl/
├── config/
│   └── etl_config.yaml          # Main configuration file
├── extractors/
│   ├── base_extractor.py        # Base extractor class
│   └── dropbox_extractor.py     # Dropbox data extraction
├── validators/
│   ├── base_validator.py        # Base validator class
│   └── option_chain_validator.py # Option chain validation
├── transformers/
│   ├── base_transformer.py      # Base transformer class
│   └── option_chain_transformer.py # Option chain transformation
├── loaders/
│   └── heavydb_loader.py        # HeavyDB bulk loader
├── scripts/
│   ├── daily_etl_cron.sh        # Cron job script
│   ├── setup_daily_etl.sh       # Setup script
│   └── manual_etl_run.sh        # Manual runner
├── logs/                        # Log files
├── temp/                        # Temporary files
├── archive/                     # Archived data
└── daily_etl_runner.py          # Main ETL orchestrator
```

## Installation

### Prerequisites

1. Python 3.8+ with packages:
   ```bash
   pip install pandas numpy pyyaml requests
   ```

2. HeavyDB with `nifty_option_chain` table created

3. Access to market data files (local or Dropbox)

### Setup

1. Run the setup script:
   ```bash
   cd /srv/samba/shared/market_data_etl/scripts
   ./setup_daily_etl.sh
   ```

   This will:
   - Verify Python environment
   - Check HeavyDB connection
   - Create necessary directories
   - Set up cron job (optional)

2. Configure the ETL by editing `config/etl_config.yaml`

## Usage

### Automatic Daily Run

The ETL runs automatically via cron at 6:30 PM IST (after market close).

To check cron status:
```bash
crontab -l
```

### Manual Execution

#### Interactive Mode
```bash
./scripts/manual_etl_run.sh
```

#### Command Line
```bash
# Process today's data for all symbols
python3 daily_etl_runner.py

# Process specific date
python3 daily_etl_runner.py --date 2025-01-05

# Process yesterday's data
python3 daily_etl_runner.py --yesterday

# Process specific symbols
python3 daily_etl_runner.py --symbols NIFTY BANKNIFTY

# Dry run (validate and transform only)
python3 daily_etl_runner.py --dry-run
```

## Configuration

Edit `config/etl_config.yaml` to configure:

- **Indices**: Symbol settings and expiry rules
- **HeavyDB**: Connection parameters and performance settings
- **ETL Settings**: Batch sizes, retry logic, validation rules
- **Schedule**: Timing for automated runs
- **Notifications**: Email/Telegram alerts (optional)

## Data Processing Pipeline

1. **Extract**: 
   - Check data availability
   - Download/locate CSV files
   - Handle missing data gracefully

2. **Validate**:
   - Schema validation
   - Data type checking
   - Business rule validation
   - Data quality metrics

3. **Transform**:
   - Standardize date/time formats
   - Calculate synthetic ATM strikes
   - Add time zone classifications
   - Apply symbol-specific rules
   - Ensure HeavyDB schema compliance

4. **Load**:
   - Use COPY FROM for bulk loading
   - Verify data integrity
   - Generate load statistics

## Monitoring

### Logs

- ETL runs: `/srv/samba/shared/market_data_etl/logs/etl_run_*.log`
- Cron logs: `/srv/samba/shared/market_data_etl/logs/cron/daily_etl_cron_*.log`
- Summary JSON: `/srv/samba/shared/market_data_etl/logs/summary_*.json`

### View Recent Logs
```bash
# Last 5 ETL runs
ls -lt logs/etl_run_*.log | head -5

# Today's cron log
tail -f logs/cron/daily_etl_cron_$(date +%Y%m%d).log
```

## Troubleshooting

### Common Issues

1. **No data found**
   - Check if market was open on the date
   - Verify data files exist in configured location
   - Check file naming patterns match expectations

2. **Validation failures**
   - Review validation errors in logs
   - Check if data format has changed
   - Adjust validation rules if needed

3. **Load failures**
   - Verify HeavyDB is running: `systemctl status heavyai`
   - Check disk space: `df -h /nvme0n1-disk`
   - Review HeavyDB logs

4. **Slow performance**
   - Ensure using NVMe storage
   - Check batch size configuration
   - Monitor system resources during load

### Manual Recovery

If ETL fails for a date:
```bash
# Check what data is available
./scripts/manual_etl_run.sh
# Select option 7 to check data availability

# Retry the failed date
python3 daily_etl_runner.py --date 2025-01-05

# Process with increased logging
python3 daily_etl_runner.py --date 2025-01-05 --config config/debug_config.yaml
```

## Performance

Expected performance on optimized system:
- **Extraction**: 1-2 minutes per symbol
- **Validation**: ~100,000 rows/second
- **Transformation**: ~50,000 rows/second
- **Loading**: 500,000-900,000 rows/second (using COPY FROM)

## Maintenance

### Daily
- Monitor cron execution
- Check for errors in logs
- Verify data completeness

### Weekly
- Clean up temporary files
- Archive old logs
- Review validation warnings

### Monthly
- Update configuration for new symbols
- Review and optimize performance
- Update documentation

## Support

For issues or questions:
1. Check logs for detailed error messages
2. Run manual test with dry-run mode
3. Verify configuration settings
4. Check HeavyDB connection and table structure

## Future Enhancements

- [ ] Dropbox API integration for direct downloads
- [ ] MCX commodity data support
- [ ] Real-time monitoring dashboard
- [ ] Telegram/Email notifications
- [ ] Automated holiday calendar updates
- [ ] Data quality scoring system
- [ ] Incremental loading support
- [ ] Multi-table target support

---

**Version**: 1.0  
**Last Updated**: January 6, 2025