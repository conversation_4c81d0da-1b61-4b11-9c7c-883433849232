#!/bin/bash
# Manual ETL Runner Script
# Provides an interactive way to run ETL for specific dates and symbols

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
ETL_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Market Data ETL - Manual Runner${NC}"
echo "================================="
echo

# Change to ETL directory
cd "$ETL_DIR" || exit 1

# Function to display menu
show_menu() {
    echo "Select an option:"
    echo "1) Run ETL for today"
    echo "2) Run ETL for yesterday"
    echo "3) Run ETL for specific date"
    echo "4) Run ETL for date range"
    echo "5) Run ETL for specific symbols only"
    echo "6) Dry run (validate and transform only)"
    echo "7) Check data availability"
    echo "8) View recent logs"
    echo "9) Exit"
    echo
}

# Function to run ETL
run_etl() {
    local args="$1"
    echo -e "${YELLOW}Running: python3 daily_etl_runner.py $args${NC}"
    python3 daily_etl_runner.py $args
}

# Function to check data for a date
check_data() {
    local date="$1"
    echo -e "${YELLOW}Checking data for $date...${NC}"
    
    # Check local files
    echo "Checking local data directory..."
    local data_dir="/srv/samba/shared/market_data/nifty/oc_with_futures"
    if [ -d "$data_dir" ]; then
        local files=$(find "$data_dir" -name "*${date//-/}*" -o -name "*$date*" | head -10)
        if [ -n "$files" ]; then
            echo -e "${GREEN}Found files:${NC}"
            echo "$files"
        else
            echo -e "${RED}No files found for $date${NC}"
        fi
    fi
    
    # Check database
    echo
    echo "Checking database..."
    python3 -c "
import sys
sys.path.append('/srv/samba/shared')
from bt.dal.heavydb_conn import get_conn
try:
    conn = get_conn()
    cursor = conn.cursor()
    cursor.execute(f\"\"\"
        SELECT symbol, COUNT(*) as row_count 
        FROM nifty_option_chain 
        WHERE trade_date = '$date'
        GROUP BY symbol
    \"\"\")
    results = cursor.fetchall()
    if results:
        print('Database records:')
        for symbol, count in results:
            print(f'  {symbol}: {count:,} rows')
    else:
        print('No data in database for $date')
    cursor.close()
    conn.close()
except Exception as e:
    print(f'Error checking database: {e}')
"
}

# Function to run ETL for date range
run_date_range() {
    read -p "Enter start date (YYYY-MM-DD): " start_date
    read -p "Enter end date (YYYY-MM-DD): " end_date
    
    # Validate dates
    if ! date -d "$start_date" &>/dev/null || ! date -d "$end_date" &>/dev/null; then
        echo -e "${RED}Invalid date format${NC}"
        return
    fi
    
    # Convert to timestamps for comparison
    start_ts=$(date -d "$start_date" +%s)
    end_ts=$(date -d "$end_date" +%s)
    
    if [ $start_ts -gt $end_ts ]; then
        echo -e "${RED}Start date must be before end date${NC}"
        return
    fi
    
    # Loop through dates
    current_ts=$start_ts
    while [ $current_ts -le $end_ts ]; do
        current_date=$(date -d "@$current_ts" +%Y-%m-%d)
        
        # Skip weekends
        day_of_week=$(date -d "@$current_ts" +%u)
        if [ $day_of_week -ge 6 ]; then
            echo -e "${YELLOW}Skipping weekend: $current_date${NC}"
        else
            echo -e "${GREEN}Processing $current_date...${NC}"
            run_etl "--date $current_date"
            echo
        fi
        
        # Move to next day
        current_ts=$((current_ts + 86400))
    done
}

# Main menu loop
while true; do
    show_menu
    read -p "Enter your choice: " choice
    echo
    
    case $choice in
        1)
            echo "Running ETL for today..."
            run_etl ""
            ;;
        2)
            echo "Running ETL for yesterday..."
            run_etl "--yesterday"
            ;;
        3)
            read -p "Enter date (YYYY-MM-DD): " date
            if date -d "$date" &>/dev/null; then
                run_etl "--date $date"
            else
                echo -e "${RED}Invalid date format${NC}"
            fi
            ;;
        4)
            run_date_range
            ;;
        5)
            echo "Available symbols: NIFTY, BANKNIFTY, MIDCAPNIFTY, SENSEX"
            read -p "Enter symbols (space-separated): " symbols
            read -p "Enter date (YYYY-MM-DD) or press Enter for today: " date
            
            if [ -n "$date" ]; then
                run_etl "--date $date --symbols $symbols"
            else
                run_etl "--symbols $symbols"
            fi
            ;;
        6)
            echo "Running in dry-run mode..."
            read -p "Enter date (YYYY-MM-DD) or press Enter for today: " date
            
            if [ -n "$date" ]; then
                run_etl "--date $date --dry-run"
            else
                run_etl "--dry-run"
            fi
            ;;
        7)
            read -p "Enter date to check (YYYY-MM-DD): " date
            if date -d "$date" &>/dev/null; then
                check_data "$date"
            else
                echo -e "${RED}Invalid date format${NC}"
            fi
            ;;
        8)
            echo "Recent ETL logs:"
            echo
            find "$ETL_DIR/logs" -name "etl_run_*.log" -type f -mtime -7 | sort -r | head -10
            echo
            read -p "Enter log file number to view (1-10) or press Enter to skip: " log_num
            if [ -n "$log_num" ]; then
                log_file=$(find "$ETL_DIR/logs" -name "etl_run_*.log" -type f -mtime -7 | sort -r | sed -n "${log_num}p")
                if [ -f "$log_file" ]; then
                    less "$log_file"
                fi
            fi
            ;;
        9)
            echo "Exiting..."
            exit 0
            ;;
        *)
            echo -e "${RED}Invalid choice${NC}"
            ;;
    esac
    
    echo
    read -p "Press Enter to continue..."
    echo
done