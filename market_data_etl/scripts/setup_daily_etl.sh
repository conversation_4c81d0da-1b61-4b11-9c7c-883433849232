#!/bin/bash
#
# Setup script for Daily ETL
#

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ETL_DIR="$(dirname "$SCRIPT_DIR")"

echo "==============================================="
echo "DAILY ETL SETUP"
echo "==============================================="

# Create necessary directories
echo "Creating directories..."
mkdir -p "$ETL_DIR/logs"
mkdir -p "$ETL_DIR/temp"
mkdir -p "$ETL_DIR/archive"
mkdir -p "$ETL_DIR/config"

# Set permissions
chmod +x "$ETL_DIR/daily_etl_runner.py"
chmod +x "$SCRIPT_DIR"/*.sh

# Install Python dependencies
echo "Installing Python dependencies..."
pip3 install pandas numpy heavydb requests

# Create cron job for daily execution
echo "Setting up cron job..."

# ETL runs at 6:30 PM IST (13:00 UTC if server is on UTC)
CRON_TIME="30 18"  # 6:30 PM
if [ "$TZ" = "UTC" ]; then
    CRON_TIME="0 13"  # 1:00 PM UTC = 6:30 PM IST
fi

# Create cron entry
CRON_CMD="$CRON_TIME * * 1-5 cd $ETL_DIR && /usr/bin/python3 daily_etl_runner.py >> logs/cron.log 2>&1"

# Check if cron job already exists
if crontab -l 2>/dev/null | grep -q "daily_etl_runner.py"; then
    echo "Cron job already exists. Updating..."
    # Remove old entry
    crontab -l | grep -v "daily_etl_runner.py" | crontab -
fi

# Add new cron job
(crontab -l 2>/dev/null; echo "$CRON_CMD") | crontab -

echo "Cron job installed:"
echo "$CRON_CMD"

# Create systemd service (optional)
if command -v systemctl &> /dev/null; then
    echo "Creating systemd service..."
    
    cat > /tmp/market-data-etl.service << EOF
[Unit]
Description=Market Data ETL Service
After=network.target

[Service]
Type=oneshot
ExecStart=/usr/bin/python3 $ETL_DIR/daily_etl_runner.py
WorkingDirectory=$ETL_DIR
StandardOutput=append:$ETL_DIR/logs/systemd.log
StandardError=append:$ETL_DIR/logs/systemd_error.log

[Install]
WantedBy=multi-user.target
EOF

    cat > /tmp/market-data-etl.timer << EOF
[Unit]
Description=Run Market Data ETL daily at 6:30 PM
Requires=market-data-etl.service

[Timer]
OnCalendar=Mon-Fri 18:30:00
Persistent=true

[Install]
WantedBy=timers.target
EOF

    echo "To install systemd service (requires sudo):"
    echo "  sudo cp /tmp/market-data-etl.service /etc/systemd/system/"
    echo "  sudo cp /tmp/market-data-etl.timer /etc/systemd/system/"
    echo "  sudo systemctl daemon-reload"
    echo "  sudo systemctl enable market-data-etl.timer"
    echo "  sudo systemctl start market-data-etl.timer"
fi

echo ""
echo "Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update Dropbox URLs in config/etl_config.py"
echo "2. Test with: python3 daily_etl_runner.py --dry-run"
echo "3. Run manually: python3 daily_etl_runner.py"
echo "4. Check logs in: $ETL_DIR/logs/"
echo ""
echo "Cron job will run Monday-Friday at $CRON_TIME"
echo "==============================================="