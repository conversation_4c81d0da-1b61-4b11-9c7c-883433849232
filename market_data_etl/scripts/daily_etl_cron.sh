#!/bin/bash
# Daily ETL Cron Script for Market Data
# This script is designed to be run via cron after market close

# Configuration
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
ETL_DIR="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$ETL_DIR/logs/cron"
TODAY=$(date +%Y%m%d)
LOG_FILE="$LOG_DIR/daily_etl_cron_$TODAY.log"

# Python environment (adjust if using virtual environment)
PYTHON="/usr/bin/python3"

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to send notification (implement as needed)
send_notification() {
    local status=$1
    local message=$2
    
    # Example: Send email notification
    # echo "$message" | mail -s "ETL $status - $(date +%Y-%m-%d)" <EMAIL>
    
    # Example: Send to system log
    logger -t "market_data_etl" "$status: $message"
}

# Start logging
log "===== Starting Daily Market Data ETL ====="
log "Host: $(hostname)"
log "User: $(whoami)"
log "Working Directory: $ETL_DIR"

# Check if it's a trading day (skip weekends)
DAY_OF_WEEK=$(date +%u)
if [ $DAY_OF_WEEK -ge 6 ]; then
    log "Today is weekend (day $DAY_OF_WEEK), skipping ETL"
    exit 0
fi

# TODO: Add holiday check here
# You can maintain a holiday list and check against it

# Change to ETL directory
cd "$ETL_DIR" || {
    log "ERROR: Failed to change to ETL directory"
    send_notification "ERROR" "Failed to change to ETL directory"
    exit 1
}

# Run the ETL process
log "Starting ETL runner..."

# Run for all configured symbols
$PYTHON daily_etl_runner.py >> "$LOG_FILE" 2>&1
ETL_EXIT_CODE=$?

if [ $ETL_EXIT_CODE -eq 0 ]; then
    log "ETL process completed successfully"
    
    # Get summary statistics
    SUMMARY=$(tail -n 20 "$LOG_FILE" | grep -A 20 "ETL RUN SUMMARY")
    
    send_notification "SUCCESS" "Daily ETL completed successfully. $SUMMARY"
else
    log "ERROR: ETL process failed with exit code $ETL_EXIT_CODE"
    
    # Get last few error lines
    ERRORS=$(tail -n 50 "$LOG_FILE" | grep -E "ERROR|CRITICAL")
    
    send_notification "ERROR" "Daily ETL failed. Exit code: $ETL_EXIT_CODE. Errors: $ERRORS"
fi

# Cleanup old logs (keep last 30 days)
log "Cleaning up old logs..."
find "$LOG_DIR" -name "daily_etl_cron_*.log" -mtime +30 -delete

# Archive processed files (optional)
ARCHIVE_DIR="$ETL_DIR/archive/$(date +%Y/%m)"
if [ -d "$ETL_DIR/temp" ]; then
    log "Archiving processed files..."
    mkdir -p "$ARCHIVE_DIR"
    find "$ETL_DIR/temp" -name "*.csv" -mtime +1 -exec mv {} "$ARCHIVE_DIR/" \;
fi

log "===== Daily ETL Process Completed ====="
log "Log file: $LOG_FILE"

exit $ETL_EXIT_CODE