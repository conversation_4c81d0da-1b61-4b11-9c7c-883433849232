#!/usr/bin/env python3
"""
Check current data status in HeavyDB
"""

import sys
import pandas as pd
from datetime import datetime, timedelta
from tabulate import tabulate

sys.path.append('/srv/samba/shared')

try:
    from bt.dal.heavydb_conn import get_conn
except ImportError:
    print("Error: Unable to import heavydb connection module")
    sys.exit(1)

def check_data_status():
    """Check current data status in HeavyDB"""
    try:
        conn = get_conn()
        cursor = conn.cursor()
        
        print("HeavyDB Data Status Report")
        print("=" * 80)
        print(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 1. Overall statistics
        print("1. Overall Statistics")
        print("-" * 40)
        
        cursor.execute("""
            SELECT 
                COUNT(*) as total_rows,
                COUNT(DISTINCT index_name) as symbols,
                COUNT(DISTINCT trade_date) as trading_days,
                MIN(trade_date) as first_date,
                MAX(trade_date) as last_date
            FROM nifty_option_chain
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"Total Rows: {result[0]:,}")
            print(f"Symbols: {result[1]}")
            print(f"Trading Days: {result[2]}")
            print(f"Date Range: {result[3]} to {result[4]}")
        
        # 2. Per-symbol statistics
        print("\n2. Per-Symbol Statistics")
        print("-" * 40)
        
        cursor.execute("""
            SELECT 
                index_name,
                COUNT(*) as row_count,
                COUNT(DISTINCT trade_date) as days,
                MIN(trade_date) as first_date,
                MAX(trade_date) as last_date
            FROM nifty_option_chain
            GROUP BY index_name
            ORDER BY index_name
        """)
        
        results = cursor.fetchall()
        if results:
            headers = ['Symbol', 'Rows', 'Days', 'First Date', 'Last Date']
            table_data = [[r[0], f"{r[1]:,}", r[2], r[3], r[4]] for r in results]
            print(tabulate(table_data, headers=headers, tablefmt='grid'))
        
        # 3. Recent data (last 7 days)
        print("\n3. Recent Data (Last 7 Days)")
        print("-" * 40)
        
        cursor.execute("""
            SELECT 
                trade_date,
                index_name,
                COUNT(*) as row_count,
                COUNT(DISTINCT strike) as strikes,
                COUNT(DISTINCT expiry_date) as expiries
            FROM nifty_option_chain
            WHERE trade_date >= CURRENT_DATE - INTERVAL '7' DAY
            GROUP BY trade_date, index_name
            ORDER BY trade_date DESC, index_name
        """)
        
        results = cursor.fetchall()
        if results:
            headers = ['Date', 'Symbol', 'Rows', 'Strikes', 'Expiries']
            table_data = [[r[0], r[1], f"{r[2]:,}", r[3], r[4]] for r in results]
            print(tabulate(table_data, headers=headers, tablefmt='grid'))
        else:
            print("No data found for the last 7 days")
        
        # 4. Missing dates check
        print("\n4. Missing Trading Days (Last 30 Days)")
        print("-" * 40)
        
        # Get last 30 days of data
        cursor.execute("""
            SELECT DISTINCT trade_date
            FROM nifty_option_chain
            WHERE trade_date >= CURRENT_DATE - INTERVAL '30' DAY
            ORDER BY trade_date
        """)
        
        loaded_dates = set(row[0] for row in cursor.fetchall())
        
        # Generate expected trading days (exclude weekends)
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)
        
        expected_dates = []
        current = start_date
        while current <= end_date:
            if current.weekday() < 5:  # Monday to Friday
                expected_dates.append(current)
            current += timedelta(days=1)
        
        # Find missing dates
        missing_dates = [d for d in expected_dates if d not in loaded_dates]
        
        if missing_dates:
            print(f"Found {len(missing_dates)} missing trading days:")
            for date in missing_dates[-10:]:  # Show last 10
                print(f"  - {date}")
            if len(missing_dates) > 10:
                print(f"  ... and {len(missing_dates) - 10} more")
        else:
            print("No missing trading days found")
        
        # 5. Data quality metrics
        print("\n5. Data Quality Metrics (Latest Date)")
        print("-" * 40)
        
        cursor.execute("SELECT MAX(trade_date) FROM nifty_option_chain")
        latest_date = cursor.fetchone()[0]
        
        if latest_date:
            cursor.execute(f"""
                SELECT 
                    index_name,
                    COUNT(*) as total_rows,
                    SUM(CASE WHEN spot IS NULL THEN 1 ELSE 0 END) as null_spot,
                    SUM(CASE WHEN ce_close IS NULL THEN 1 ELSE 0 END) as null_ce,
                    SUM(CASE WHEN pe_close IS NULL THEN 1 ELSE 0 END) as null_pe,
                    COUNT(DISTINCT trade_time) as unique_times,
                    COUNT(DISTINCT strike) as unique_strikes
                FROM nifty_option_chain
                WHERE trade_date = '{latest_date}'
                GROUP BY index_name
            """)
            
            results = cursor.fetchall()
            if results:
                print(f"Data quality for {latest_date}:")
                headers = ['Symbol', 'Rows', 'Null Spot', 'Null CE', 'Null PE', 'Times', 'Strikes']
                table_data = [[r[0], f"{r[1]:,}", r[2], r[3], r[4], r[5], r[6]] for r in results]
                print(tabulate(table_data, headers=headers, tablefmt='grid'))
        
        # 6. Storage usage
        print("\n6. Storage Information")
        print("-" * 40)
        
        try:
            cursor.execute("""
                SELECT 
                    COUNT(*) * 8 * 100 / 1024 / 1024 as approx_size_mb
                FROM nifty_option_chain
            """)
            size_mb = cursor.fetchone()[0]
            print(f"Approximate table size: {size_mb:.2f} MB")
        except:
            print("Unable to estimate table size")
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 80)
        print("Report completed successfully")
        
    except Exception as e:
        print(f"Error checking data status: {e}")
        return 1
        
    return 0

if __name__ == '__main__':
    # Try to import tabulate, install if missing
    try:
        from tabulate import tabulate
    except ImportError:
        print("Installing tabulate package...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "tabulate"])
        from tabulate import tabulate
        
    sys.exit(check_data_status())