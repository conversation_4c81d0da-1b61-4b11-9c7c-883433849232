#!/bin/bash
#
# ETL Monitoring Script
# Monitors ETL runs and sends notifications
#

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ETL_DIR="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$ETL_DIR/logs"

# Function to check latest ETL run
check_latest_run() {
    local today=$(date +%Y%m%d)
    local result_file="$LOG_DIR/etl_results_${today}.json"
    
    if [ ! -f "$result_file" ]; then
        echo "ERROR: No ETL run found for today ($today)"
        return 1
    fi
    
    # Parse JSON results
    local success_count=$(jq -r '.summary.successful_indices' "$result_file" 2>/dev/null || echo 0)
    local failed_count=$(jq -r '.summary.failed_indices' "$result_file" 2>/dev/null || echo 0)
    local total_rows=$(jq -r '.summary.total_rows' "$result_file" 2>/dev/null || echo 0)
    
    echo "=== ETL STATUS FOR $today ==="
    echo "Successful indices: $success_count"
    echo "Failed indices: $failed_count"
    echo "Total rows loaded: $total_rows"
    echo ""
    
    # Check individual indices
    echo "=== INDEX DETAILS ==="
    jq -r '.indices | to_entries[] | "\(.key): \(.value.rows_loaded) rows (\(.value.status))"' "$result_file" 2>/dev/null
    
    if [ "$failed_count" -gt 0 ]; then
        echo ""
        echo "WARNING: Some indices failed to load!"
        return 2
    fi
    
    return 0
}

# Function to check disk space
check_disk_space() {
    local usage=$(df -h "$ETL_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    echo ""
    echo "=== DISK USAGE ==="
    echo "ETL directory usage: ${usage}%"
    
    if [ "$usage" -gt 90 ]; then
        echo "WARNING: Disk usage is above 90%!"
        return 1
    fi
    
    return 0
}

# Function to check log errors
check_log_errors() {
    local today=$(date +%Y-%m-%d)
    local error_count=$(grep -c "ERROR" "$LOG_DIR/daily_etl.log" 2>/dev/null || echo 0)
    local today_errors=$(grep "$today" "$LOG_DIR/daily_etl.log" | grep -c "ERROR" 2>/dev/null || echo 0)
    
    echo ""
    echo "=== ERROR LOG CHECK ==="
    echo "Total errors in log: $error_count"
    echo "Errors today: $today_errors"
    
    if [ "$today_errors" -gt 0 ]; then
        echo ""
        echo "Recent errors:"
        grep "$today" "$LOG_DIR/daily_etl.log" | grep "ERROR" | tail -5
    fi
}

# Function to generate summary report
generate_report() {
    local report_file="$LOG_DIR/etl_monitor_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "ETL Monitoring Report"
        echo "Generated: $(date)"
        echo "================================="
        echo ""
        check_latest_run
        check_disk_space
        check_log_errors
    } > "$report_file"
    
    echo ""
    echo "Report saved to: $report_file"
    
    # Return the report file path for email/notification
    echo "$report_file"
}

# Main execution
main() {
    echo "ETL Monitoring - $(date)"
    echo "================================="
    
    check_latest_run
    local run_status=$?
    
    check_disk_space
    local disk_status=$?
    
    check_log_errors
    
    # Generate report if requested
    if [ "$1" == "--report" ]; then
        generate_report
    fi
    
    # Exit with appropriate status
    if [ "$run_status" -ne 0 ] || [ "$disk_status" -ne 0 ]; then
        exit 1
    fi
    
    exit 0
}

# Run main function
main "$@"