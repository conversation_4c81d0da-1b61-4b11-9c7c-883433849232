// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function GetCustomIgnoreRules() {
  return window['go']['main']['App']['GetCustomIgnoreRules']();
}

export function GetCustomPromptRules() {
  return window['go']['main']['App']['GetCustomPromptRules']();
}

export function ListFiles(arg1) {
  return window['go']['main']['App']['ListFiles'](arg1);
}

export function RequestShotgunContextGeneration(arg1, arg2) {
  return window['go']['main']['App']['RequestShotgunContextGeneration'](arg1, arg2);
}

export function SelectDirectory() {
  return window['go']['main']['App']['SelectDirectory']();
}

export function SetCustomIgnoreRules(arg1) {
  return window['go']['main']['App']['SetCustomIgnoreRules'](arg1);
}

export function SetCustomPromptRules(arg1) {
  return window['go']['main']['App']['SetCustomPromptRules'](arg1);
}

export function SetUseCustomIgnore(arg1) {
  return window['go']['main']['App']['SetUseCustomIgnore'](arg1);
}

export function SetUseGitignore(arg1) {
  return window['go']['main']['App']['SetUseGitignore'](arg1);
}

export function SplitShotgunDiff(arg1, arg2) {
  return window['go']['main']['App']['SplitShotgunDiff'](arg1, arg2);
}

export function StartFileWatcher(arg1) {
  return window['go']['main']['App']['StartFileWatcher'](arg1);
}

export function StartupTest(arg1) {
  return window['go']['main']['App']['StartupTest'](arg1);
}

export function StopFileWatcher() {
  return window['go']['main']['App']['StopFileWatcher']();
}
