"""
Main entry point for the Nifty VWAP Strategy backtesting application using HeavyDB and GPU acceleration.
"""
import os
import logging
import argparse
from datetime import datetime, time as dtime
from typing import Dict, List, Optional, Any

# Import debug tool
from src.utils.debug_tool import set_debug_options, debug_tool

# Unified GPU/CPU imports and detection
try:
    import cudf as pd
    import cupy as np
    GPU_ENABLED = True
    print("cuDF and cuPy detected. Data processing will use GPU.")
except ImportError:
    import pandas as pd
    import numpy as np
    GPU_ENABLED = False
    print("cuDF/cuPy not installed. Data processing will use CPU (pandas/numpy).")

import matplotlib.pyplot as plt
import seaborn as sns
from dotenv import load_dotenv

from src.data.heavydb_client import HeavyDBClient  # Import the new HeavyDB client
from src.data.candle_generator import CandleGenerator
from src.indicators.vwap import VWAP
from src.indicators.supertrend import Supertrend
from src.strategy.option_selector import OptionSelector
from src.strategy.signal_generator import SignalGenerator
from src.execution.backtester import Backtester
from src.config.config_handler import ConfigHandler

# Configure logging
# Create handlers
file_handler = logging.FileHandler("heavydb_backtest.log", encoding='utf-8')  # Specify UTF-8 for file
stream_handler = logging.StreamHandler()

# Set encoding for stream handler if possible (might depend on underlying stream/console)
try:
    import sys
    stream_handler.stream = open(sys.stdout.fileno(), mode='w', encoding='utf-8', buffering=1)
except Exception:
    # Fallback if setting encoding on stdout fails (e.g., in certain environments)
    pass 

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def main():
    """Main entry point for the application."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Nifty VWAP Strategy Backtester (HeavyDB Version)")
    parser.add_argument('--config', type=str, default="config.ini", help="Path to config.ini file")
    parser.add_argument('--start_date', type=str, help="Start date in YYMMDD format")
    parser.add_argument('--end_date', type=str, help="End date in YYMMDD format")
    parser.add_argument('--start_time', type=str, help="Start time in HH:MM format")
    parser.add_argument('--end_time', type=str, help="End time in HH:MM format")
    parser.add_argument('--dte', type=str, help="Days to expiry for options (comma-separated for multiple)")
    parser.add_argument('--limit', type=int, help="Max number of records to fetch")
    parser.add_argument('--capital', type=float, help="Initial capital for backtest")
    parser.add_argument('--lot_size', type=int, help="Lot size for options")
    parser.add_argument('--stop_loss_pct', type=float, help="Stop loss as percent")
    parser.add_argument('--profit_target_pct', type=float, help="Profit target as percent")
    parser.add_argument('--output_dir', type=str, help="Directory to save results")
    parser.add_argument('--gpu', action='store_true', help="Force use of GPU acceleration")
    
    # Debug options
    parser.add_argument('--debug', action='store_true', help="Enable debug mode")
    parser.add_argument('--debug-level', type=str, default="INFO", help="Debug log level (DEBUG, INFO, WARNING, ERROR)")
    parser.add_argument('--trace-sql', action='store_true', help="Trace SQL queries")
    parser.add_argument('--profile', action='store_true', help="Enable performance profiling")
    parser.add_argument('--debug-dir', type=str, default="debug_output", help="Directory for debug output")
    
    args = parser.parse_args()

    # Load config using ConfigHandler
    config_handler = ConfigHandler(args.config)
    
    # Define default parameters
    defaults = {
        'start_date': '20240401',
        'end_date': '20240430',
        'start_time': '09:15', 
        'end_time': '15:30',
        'dte': [0, 1, 2, 3, 4, 5],  # Default to multiple DTEs
        'limit': 5000,
        'capital': 250000.0,
        'lot_size': 50,
        'stop_loss_pct': 25.0,
        'profit_target_pct': 50.0,
        'output_dir': 'results/'
    }
    
    # Define parameter converters
    converters = {
        'start_date': str,
        'end_date': str,
        'dte': "int_list",  # Special handler for integer lists
        'limit': int,
        'capital': float,
        'lot_size': int,
        'stop_loss_pct': float,
        'profit_target_pct': float
    }
    
    # Get parameters with proper type conversion
    params = config_handler.get_params(
        list(defaults.keys()),  # Get all default parameters
        vars(args),  # CLI arguments as dict
        defaults,    # Default values
        converters   # Type converters
    )
    
    # Extract parameters for easy access
    start_date = params['start_date']
    end_date = params['end_date']
    start_time = params['start_time']
    end_time = params['end_time']
    dte_list = params['dte']  # Already an int list
    limit = params['limit']
    capital = params['capital']
    lot_size = params['lot_size'] 
    stop_loss_pct = params['stop_loss_pct']
    profit_target_pct = params['profit_target_pct']
    output_dir = params['output_dir']

    # Initialize debug tool
    debug_tool = set_debug_options(
        enabled=args.debug,
        log_level=args.debug_level,
        trace_sql=args.trace_sql,
        profile_performance=args.profile,
        debug_dir=args.debug_dir,
        clean_debug_dir=True
    )
    
    # Log configuration
    logger.info("Starting Nifty VWAP Strategy Backtester (HeavyDB Version)")
    logger.info(f"Config file: {args.config}")
    logger.info(f"Date range: {start_date} to {end_date}")
    logger.info(f"Trading time window: {start_time} to {end_time}")
    logger.info(f"Backtesting for DTEs: {dte_list}")
    logger.info(f"Capital: ₹{capital:,.2f}, Lot size: {lot_size}")
    logger.info(f"GPU Acceleration: {GPU_ENABLED}")
    logger.info(f"Debug Mode: {args.debug}, Debug Level: {args.debug_level}")
    
    # Initialize HeavyDB client
    try:
        heavydb_client = HeavyDBClient()
        logger.info("Successfully connected to HeavyDB")
    except Exception as e:
        logger.error(f"Failed to connect to HeavyDB: {str(e)}")
        return

    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logger.info(f"Created output directory: {output_dir}")

    # Run backtest for each DTE in the list
    for dte in dte_list:
        try:
            logger.info(f"Fetching data from {start_date} to {end_date} with trading-day DTE={dte}")
            
            # Use HeavyDB client to get options data by DTE
            nifty_data = heavydb_client.get_options_by_dte(
                start_date=start_date,
                end_date=end_date,
                dte=dte,
                limit=limit
            )
            
            if nifty_data.empty:
                logger.error(f"No data retrieved from HeavyDB for DTE={dte}")
                continue
            
            # Ensure we have the DTE column
            if 'dte' not in nifty_data.columns and 'trading_day_dte' not in nifty_data.columns:
                logger.info("Adding trading-day DTE column to DataFrame using utility function.")
                nifty_data = heavydb_client.add_trading_day_dte(nifty_data)
            elif 'trading_day_dte' in nifty_data.columns and 'dte' not in nifty_data.columns:
                logger.info("Using trading_day_dte as dte column")
                nifty_data['dte'] = nifty_data['trading_day_dte']
            elif 'dte' in nifty_data.columns and 'trading_day_dte' not in nifty_data.columns:
                logger.info("Using dte as trading_day_dte column")
                nifty_data['trading_day_dte'] = nifty_data['dte']
            
            # Additional logging for DTE verification
            if not nifty_data.empty:
                logger.info(f"DTE statistics: min={nifty_data['dte'].min()}, max={nifty_data['dte'].max()}, unique values={nifty_data['dte'].nunique()}")
            
            # Confirm we have records with the requested DTE
            dte_filtered = nifty_data[nifty_data['dte'] == dte]
            if dte_filtered.empty:
                logger.error(f"No records found with exactly DTE={dte}")
                continue
            nifty_data = dte_filtered  # Use only the filtered data
            
            # Filter by time window
            # HeavyDB stores time as integers (HHMMSS), so we need to convert to time objects
            if 'trade_time' in nifty_data.columns:
                if isinstance(nifty_data['trade_time'].iloc[0], (int, np.int64)):
                    # Convert from integer (HHMMSS) to time objects
                    nifty_data['trade_time'] = nifty_data['trade_time'].apply(
                        lambda x: dtime(int(str(x).zfill(6)[:2]),
                                        int(str(x).zfill(6)[2:4]),
                                        int(str(x).zfill(6)[4:6]))
                    )
                else:
                    # Ensure time column is in time format
                    nifty_data['trade_time'] = pd.to_datetime(nifty_data['trade_time']).dt.time
            
            # Parse time strings
            st = dtime(*[int(x) for x in start_time.split(':')])
            et = dtime(*[int(x) for x in end_time.split(':')])
            
            # Apply time filter
            nifty_data = nifty_data[(nifty_data['trade_time'] >= st) & (nifty_data['trade_time'] <= et)]
            logger.info(f"After time filtering: {len(nifty_data)} records with DTE={dte}")
            
            if nifty_data.empty:
                logger.error(f"No data available after time filtering for DTE={dte}")
                continue
            
            # Process and backtest
            process_and_backtest(
                nifty_data, 
                start_date, 
                end_date, 
                dte,
                capital=capital,
                lot_size=lot_size,
                stop_loss_pct=stop_loss_pct,
                profit_target_pct=profit_target_pct,
                output_dir=output_dir
            )
        except Exception as e:
            logger.error(f"Error during data retrieval or backtest for DTE={dte}: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            continue

    logger.info("Backtesting complete for all DTEs")
    
    # Generate debug report if debug mode is enabled
    if args.debug:
        logger.info("Generating debug report...")
        debug_report = debug_tool.generate_report()
        logger.info("Debug report generated successfully")


def process_and_backtest(data: pd.DataFrame, start_date: str, end_date: str, dte: int, 
                         capital: float = 250000, lot_size: int = 50,
                         stop_loss_pct: float = 25, profit_target_pct: float = 50,
                         output_dir: str = "results/"):
    """
    Process the data and run the backtest.
    
    Args:
        data: DataFrame containing options data from nifty_option_chain
        start_date: Start date for backtesting
        end_date: End date for backtesting
        dte: Days to expiry (trading days) for the options being tested
        capital: Initial capital for backtesting
        lot_size: Lot size for options
        stop_loss_pct: Stop loss percentage
        profit_target_pct: Profit target percentage
        output_dir: Directory to save results
    """
    logger.info(f"Processing data for backtesting DTE={dte}")
    
    if data.empty:
        logger.error("Empty data provided for backtesting")
        return
    
    logger.info(f"Data columns: {data.columns.tolist()}")
    
    # 1. Clean data 
    try:
        data = data.copy()
        # Minimal required columns from nifty_option_chain view
        required_view_cols = [
            "trade_date", "trade_time", "strike", "expiry_date", 
            "atm_strike", "underlying_price",
            "ce_ltp", "ce_volume",
            "pe_ltp", "pe_volume",
            "ce_symbol", "pe_symbol" # Needed for indicator dict keys
        ]
        
        # Preprocessing - Map CE/PE close prices to LTP if needed
        if "ce_close" in data.columns and "ce_ltp" not in data.columns:
            data["ce_ltp"] = data["ce_close"]
            logger.info("Mapped ce_close to ce_ltp")
            
        if "pe_close" in data.columns and "pe_ltp" not in data.columns:
            data["pe_ltp"] = data["pe_close"]
            logger.info("Mapped pe_close to pe_ltp")
        
        # Calculate ATM strike if missing
        if "atm_strike" not in data.columns and "underlying_price" in data.columns:
            logger.info("Calculating ATM strikes")
            
            # Group by date and time
            atm_strikes = {}
            for (date, time_val), group in data.groupby(["trade_date", "trade_time"]):
                # Get the underlying price
                if "underlying_price" in group.columns:
                    underlying = group["underlying_price"].iloc[0]
                    
                    # Round to nearest strike interval (50 for NIFTY)
                    if not pd.isna(underlying):
                        strike_interval = 50  # For NIFTY
                        atm_strike = round(underlying / strike_interval) * strike_interval
                        atm_strikes[(date, time_val)] = atm_strike
            
            # Add ATM strike column
            if atm_strikes:
                data["atm_strike"] = data.apply(
                    lambda row: atm_strikes.get((row["trade_date"], row["trade_time"]), None), 
                    axis=1
                )
                logger.info(f"Added ATM strikes for {len(atm_strikes)} unique date-time combinations")
        
        # Calculate synthetic ATM strike if underlying price is missing
        if "atm_strike" not in data.columns:
            logger.info("Calculating ATM strikes using synthetic futures")
            
            # Calculate synthetic future for each strike
            if "ce_ltp" in data.columns and "pe_ltp" in data.columns:
                data["synthetic_future"] = data["strike"] + (data["ce_ltp"] - data["pe_ltp"])
                
                # Calculate ATM strikes for each unique trade_date, trade_time combination
                atm_strikes = {}
                for (date, time_val), group in data.groupby(["trade_date", "trade_time"]):
                    # Calculate the synthetic difference
                    group["synthetic_diff"] = (group["synthetic_future"] - group["strike"]).abs()
                    
                    # Find the ATM strike (minimum synthetic difference)
                    if not group.empty:
                        atm_strike = group.loc[group["synthetic_diff"].idxmin(), "strike"]
                        atm_strikes[(date, time_val)] = atm_strike
                
                # Add ATM strike column
                data["atm_strike"] = data.apply(
                    lambda row: atm_strikes.get((row["trade_date"], row["trade_time"]), None), 
                    axis=1
                )
                logger.info(f"Added synthetic ATM strikes for {len(atm_strikes)} unique date-time combinations")
            
        # Check if we still have missing required columns
        missing_cols = [col for col in required_view_cols if col not in data.columns]
        if missing_cols:
            logger.error(f"Missing required columns from view: {missing_cols}")
            # Add defaults if possible or return
            if "ce_volume" not in data.columns: data["ce_volume"] = 0
            if "pe_volume" not in data.columns: data["pe_volume"] = 0
            if "underlying_price" not in data.columns and "synthetic_future" in data.columns:
                data["underlying_price"] = data["synthetic_future"]
                logger.info("Used synthetic future as underlying price")
                
            # Recheck after adding defaults
            missing_cols = [col for col in required_view_cols if col not in data.columns]
            if missing_cols:
                 logger.error(f"Still missing critical columns: {missing_cols}")
                 return

        # Convert relevant columns to numeric
        numeric_cols = ["strike", "atm_strike", "underlying_price", "ce_ltp", "ce_volume", "pe_ltp", "pe_volume"]
        for col in numeric_cols:
             if col in data.columns:
                 data[col] = pd.to_numeric(data[col], errors='coerce')

        # Create datetime column 
        try:
            data['datetime'] = pd.to_datetime(data['trade_date'].astype(str) + ' ' + data['trade_time'].astype(str))
        except Exception as e:
            logger.error(f"Error creating datetime column: {e}")
            return

        # Prepare base OHLC columns if needed for CandleGenerator
        if "ce_open" not in data.columns: data["ce_open"] = data["ce_ltp"]
        if "ce_high" not in data.columns: data["ce_high"] = data["ce_ltp"]
        if "ce_low" not in data.columns: data["ce_low"] = data["ce_ltp"]
        if "ce_close" not in data.columns: data["ce_close"] = data["ce_ltp"]
        if "pe_open" not in data.columns: data["pe_open"] = data["pe_ltp"]
        if "pe_high" not in data.columns: data["pe_high"] = data["pe_ltp"]
        if "pe_low" not in data.columns: data["pe_low"] = data["pe_ltp"]
        if "pe_close" not in data.columns: data["pe_close"] = data["pe_ltp"]
        
        # Add distance from ATM
        if "atm_strike" in data.columns:
            data["distance_from_atm"] = data["strike"] - data["atm_strike"]

        logger.info("Data cleaned and prepared")
        data.set_index('datetime', inplace=True) # Set datetime index for easier time-based access
        data.sort_index(inplace=True)

    except Exception as e:
        logger.error(f"Error during data cleaning: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return
    
    # --- Refactored Candle Generation and Indicator Calculation ---
    try:
        logger.info("Skipping precomputation of candles and indicators. Will compute dynamically during backtest.")
        # No precomputation; pass cleaned data to backtester
        processed_indicator_data = None  # Not used anymore
    except Exception as e:
        logger.error(f"Error during candle/indicator generation: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return

    # Set strike step for OTM selection
    strike_step = 50  # Nifty strike step

    # Enable GPU processing if available (for pandas/numpy/torch)
    try:
        import torch
        if torch.cuda.is_available():
            logger.info(f"GPU detected: {torch.cuda.get_device_name(0)}. PyTorch will use CUDA.")
        else:
            logger.info("PyTorch GPU not available, running on CPU.")
    except ImportError:
        logger.info("PyTorch not installed, skipping GPU check.")

    # --- Refactored Backtest Execution --- 
    try:
        logger.info(f"Running backtest for DTE={dte} with dynamic OTM selection")
        backtester = Backtester(
            initial_capital=capital,
            lot_size=lot_size,
            stop_loss_pct=stop_loss_pct,
            profit_target_pct=profit_target_pct
        )
        results = backtester.run_dynamic_backtest(
            data,  # Pass the cleaned DataFrame directly
            None,  # indicator_map is not used
            start_date=start_date,
            end_date=end_date,
            strike_step=strike_step
        )
        
        # Display results
        logger.info(f"Backtest Results for DTE={dte}:")
        logger.info(f"Total Trades: {results['total_trades']}")
        logger.info(f"Winning Trades: {results['winning_trades']}")
        logger.info(f"Losing Trades: {results['losing_trades']}")
        logger.info(f"Win Rate: {results['win_rate']:.2%}")
        logger.info(f"Profit Factor: {results['profit_factor']:.2f}")
        # Use .get(col, default) for robustness if a trade wasn't logged properly
        avg_win = results.get('average_win', 0)
        avg_loss = results.get('average_loss', 0)
        logger.info(f"Average Win: ₹{avg_win:.2f}") 
        logger.info(f"Average Loss: ₹{avg_loss:.2f}")
        logger.info(f"Cumulative P&L: ₹{results.get('cumulative_pnl', 0):.2f}")
        logger.info(f"Max Drawdown: ₹{results.get('max_drawdown', 0):.2f}")
        logger.info(f"Final Capital: ₹{results.get('final_capital', backtester.initial_capital):.2f}")

        # --- Save Trade Log to CSV --- 
        if results['trades']:
            trade_log_df = pd.DataFrame(results['trades'])
            # Add/format columns as per new spec
            trade_log_df['symbol'] = trade_log_df['option_type'].apply(lambda x: f'Nifty_{x}')
            trade_log_df['strike'] = trade_log_df['symbol'].str.extract(r'_(\d+)_').fillna(0).astype(int)
            # Underlying price at entry/exit
            if 'underlying_price' in data.columns:
                # Map entry/exit times to underlying price
                entry_underlying = data.reset_index().set_index('datetime')['underlying_price']
                trade_log_df['entry_underlying_price'] = trade_log_df['entry_time'].map(entry_underlying)
                trade_log_df['exit_underlying_price'] = trade_log_df['exit_time'].map(entry_underlying)
            else:
                trade_log_df['entry_underlying_price'] = None
                trade_log_df['exit_underlying_price'] = None
            # PnL %
            trade_log_df['pnl_pct'] = (trade_log_df['pnl'] / capital * 100).round(2)
            # Reorder columns
            cols = [
                'symbol', 'strike', 'option_type', 'action', 'entry_time', 'entry_price', 'entry_vwap', 'entry_supertrend',
                'entry_underlying_price', 'quantity', 'exit_time', 'exit_price', 'exit_vwap', 'exit_supertrend',
                'exit_underlying_price', 'exit_type', 'pnl', 'pnl_pct'
            ]
            # Use only columns that exist
            existing_cols = [col for col in cols if col in trade_log_df.columns]
            trade_log_df = trade_log_df[existing_cols]
            
            # Prepare summary rows
            summary = [
                ['Initial Capital:', capital],
                ['Total Return %:', round(results.get('cumulative_pnl', 0) / capital * 100, 2)],
                ['Total Trades:', results.get('total_trades', 0)],
                ['Win Rate:', f"{results.get('win_rate', 0)*100:.2f}%"],
                ['Profit Factor:', f"{results.get('profit_factor', 0):.2f}"],
                ['Max Drawdown:', f"₹{results.get('max_drawdown', 0):.2f}"],
                [],
            ]
            
            # Create file path in output directory
            os.makedirs(output_dir, exist_ok=True)
            trade_log_file = os.path.join(output_dir, f"trade_log_dte{dte}.csv")
            
            # Write summary and then DataFrame to CSV
            with open(trade_log_file, 'w', encoding='utf-8') as f:
                for row in summary:
                    f.write(','.join(map(str, row)) + '\n')
                trade_log_df.to_csv(f, index=False)
            logger.info(f"Saved trade log to {trade_log_file}")
        else:
            logger.info("No trades generated, trade log not saved.")

        # Create simple chart of daily P&L
        daily_pnl = results["daily_pnl"]
        if daily_pnl:
            dates = list(daily_pnl.keys())
            pnls = list(daily_pnl.values())
            
            plt.figure(figsize=(12, 6))
            plt.bar(dates, pnls)
            plt.title(f"Daily P&L (DTE={dte})")
            plt.xlabel("Date")
            plt.ylabel("P&L (₹)")
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Save to output directory
            chart_file = os.path.join(output_dir, f"daily_pnl_dte{dte}.png")
            plt.savefig(chart_file)
            
            logger.info(f"Generated daily P&L chart: {chart_file}")

    except Exception as e:
        logger.error(f"Error during backtesting: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return


if __name__ == "__main__":
    main()