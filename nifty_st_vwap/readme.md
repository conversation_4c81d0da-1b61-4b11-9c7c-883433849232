# Nifty VWAP Strategy Backtester - HeavyDB Version

This implementation of the Nifty VWAP Strategy Backtester uses HeavyDB (formerly OmniSci/MapD) for GPU-accelerated data processing. It retrieves data from the `nifty_option_chain` regular view in HeavyDB instead of Supabase.

## Features

- **GPU Acceleration**: Leverages HeavyDB's GPU processing capabilities for faster backtesting
- **Direct Access to Regular View**: Uses `nifty_option_chain` view that already has pre-computed ATM strikes and DTE
- **Optimized Data Retrieval**: Minimizes data transfer by applying filters at the database level
- **Consistent API**: Maintains the same backtesting workflow as the original implementation
- **Debug Tools**: Built-in debugging capabilities for advanced troubleshooting

## System Requirements

- Python 3.8+
- HeavyDB server installation (local or remote)
- Optional but recommended: NVIDIA GPU with CUDA support
- Required Python packages:
  - `pymapd` or `pyheavydb` (for HeavyDB connection)
  - `pandas` (or `cudf` for GPU acceleration)
  - `numpy` (or `cupy` for GPU acceleration)
  - `matplotlib`, `seaborn` (for visualization)
  - `python-dotenv` (for environment variables)

## Setup

1. Clone this repository:
   ```bash
   git clone <repository_url>
   cd nifty_st_vwap
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Install HeavyDB client:
   ```bash
   # Either install pymapd (older client)
   pip install pymapd

   # Or install pyheavydb (newer client, recommended)
   pip install pyheavydb importlib_metadata
   ```

5. For GPU acceleration, install cuDF and cuPy:
   ```bash
   # Follow the official installation guides at:
   # https://docs.rapids.ai/install
   # https://docs.cupy.dev/en/stable/install.html
   ```

6. Configure environment variables:
   ```bash
   cp .env.heavydb .env
   # Edit .env with your HeavyDB credentials and settings
   ```

## Using the HeavyDB Version

### Connection Configuration

Ensure your `.env` file contains valid HeavyDB connection details:

```
HEAVYDB_HOST="127.0.0.1"  # Change if your HeavyDB is on a remote server
HEAVYDB_PORT="6274"
HEAVYDB_USER="admin"
HEAVYDB_PASSWORD="HyperInteractive"
HEAVYDB_DBNAME="heavyai"
```

### Running the Backtester

Run the HeavyDB version of the backtester using:

```bash
python main_heavydb.py --start_date 240401 --end_date 240430 --dte 1
```

### Command-line Options

The backtester supports various command-line options:

- `--config`: Path to config.ini file (default: config.ini)
- `--start_date`: Start date in YYMMDD format
- `--end_date`: End date in YYMMDD format
- `--start_time`: Start time in HH:MM format
- `--end_time`: End time in HH:MM format
- `--dte`: Days to expiry for options (comma-separated for multiple)
- `--limit`: Max number of records to fetch
- `--capital`: Initial capital for backtest
- `--lot_size`: Lot size for options
- `--stop_loss_pct`: Stop loss as percent
- `--profit_target_pct`: Profit target as percent
- `--output_dir`: Directory to save results
- `--gpu`: Force use of GPU acceleration

### Debug Options

The backtester includes a comprehensive debug tool with the following options:

- `--debug`: Enable debug mode
- `--debug-level`: Set debug log level (DEBUG, INFO, WARNING, ERROR)
- `--trace-sql`: Enable SQL query tracing
- `--profile`: Enable performance profiling
- `--debug-dir`: Directory for debug output files

## HeavyDB Regular View

This implementation relies on the `nifty_option_chain` regular view in HeavyDB, which should have the following structure:

- `trade_date` (DATE): Date of the trade record
- `trade_time` (INTEGER): Time of the trade in HHMMSS format
- `expiry_date` (DATE): Expiration date of the option
- `strike` (DOUBLE): Strike price of the option
- `underlying_price` (DOUBLE): Nifty index price at the time
- `atm_strike` (DOUBLE): Calculated At-The-Money strike price
- `dte` (INTEGER): Days to Expiry (trading days)
- `ce_*` and `pe_*` columns: Call and Put option data

## Data Flow

1. **Data Retrieval**: The `HeavyDBClient` connects to HeavyDB and queries the `nifty_option_chain` view
2. **Data Processing**: The retrieved data is converted to pandas/cuDF DataFrames for analysis
3. **Backtesting**: The same backtesting engine is used as in the original implementation
4. **Results**: Trade logs and performance charts are generated in the output directory

## Performance Considerations

- Use appropriate filters in queries to avoid hitting HeavyDB's row limits
- For large data volumes, use GPU acceleration by installing cuDF and cuPy
- Since `nifty_option_chain` is a regular view, it automatically reflects changes in the base table
- Use `--limit` to control the maximum number of records fetched from HeavyDB

## Debug Tool

The backtester includes a powerful debug tool to help troubleshoot issues and analyze performance. This tool provides the following features:

### SQL Query Tracing

When enabled with `--trace-sql`, the debug tool captures all SQL queries sent to HeavyDB along with their execution times. This helps identify:

- Slow queries that need optimization
- Incorrect filters or query syntax
- Database-level errors

### Performance Profiling

When enabled with `--profile`, the debug tool measures execution time for key operations, including:

- SQL query execution time
- Data processing operations
- Indicator calculation
- Backtesting execution

The profile data is saved to the debug directory and can be analyzed to identify performance bottlenecks.

### Data Dumping

The debug tool can save intermediate data to CSV files for inspection, including:

- Raw query results from HeavyDB
- Processed DataFrames at key points in the workflow
- Trade data before and after processing

### Debug Report

At the end of a backtest run with debug mode enabled, a comprehensive debug report is generated with:

- Performance metrics for all timed operations
- Counters for key events
- Summary of SQL queries executed
- Error and warning counts

### Using the Debug Tool

To use the debug tool, add the `--debug` flag when running the backtester:

```bash
# Basic debug mode
python main_heavydb.py --start_date 240401 --end_date 240430 --dte 1 --debug

# Full debug with SQL tracing and performance profiling
python main_heavydb.py --start_date 240401 --end_date 240430 --dte 1 --debug --trace-sql --profile --debug-level DEBUG
```

Using the shell script:

```bash
./run_heavydb_backtest.sh --start_date 240401 --end_date 240430 --dte 1 --debug --trace-sql
```

## Troubleshooting

### Connection Issues

If you encounter connection issues:

1. Verify HeavyDB server is running
2. Confirm connection details in `.env` file
3. Ensure firewalls allow connection to the HeavyDB port
4. Try connecting with heavysql command-line tool to verify credentials

### Missing Data

If no data is returned:

1. Check that the `nifty_option_chain` view exists in HeavyDB
2. Verify the date range contains trading days
3. Try a broader date range or different DTE value

### GPU Acceleration Issues

If GPU acceleration isn't working:

1. Confirm CUDA is installed and working (`nvidia-smi`)
2. Verify cuDF and cuPy are properly installed
3. Check the logs for "Data processing will use GPU" message
4. Try using the `--gpu` flag to force GPU mode

## Additional Resources

- See `heavydb_internal_guide.md` for more details on the HeavyDB setup
- Refer to the main `README.md` for strategy details