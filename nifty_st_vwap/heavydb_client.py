"""
HeavyDB client for accessing Nifty options data with GPU acceleration.
"""
import os
import time
from typing import Dict, List, Optional, Union, Any
from datetime import datetime, date, timedelta
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Import debug tool
try:
    from src.utils.debug_tool import debug_tool
except ImportError:
    # Create a dummy debug tool if not available
    class DummyDebugTool:
        def log_sql(self, *args, **kwargs): pass
        def start_timer(self, *args, **kwargs): pass
        def stop_timer(self, *args, **kwargs): return 0
        def dump_dataframe(self, *args, **kwargs): pass
        def trace_function(self, func): return func
    debug_tool = DummyDebugTool()

# Try importing both pymapd and pyheavydb for compatibility
try:
    import pymapd
    PYMAPD_AVAILABLE = True
except ImportError:
    PYMAPD_AVAILABLE = False
    
try:
    import heavydb
    HEAVYDB_AVAILABLE = True
except ImportError:
    HEAVYDB_AVAILABLE = False

# Unified GPU/CPU imports and detection
try:
    import cudf as pd
    import cupy as np
    GPU_ENABLED = True
    logger.info("cuDF and cuPy detected. Data processing will use GPU.")
except ImportError:
    import pandas as pd
    import numpy as np
    GPU_ENABLED = False
    logger.info("cuDF/cuPy not installed. Data processing will use CPU (pandas/numpy).")

from src.data.nse_trading_calendar import trading_day_dte, get_nse_trading_days, NSE_HOLIDAYS_2024

# HeavyDB connection settings
HEAVYDB_HOST = os.environ.get("HEAVYDB_HOST", "127.0.0.1")
HEAVYDB_PORT = int(os.environ.get("HEAVYDB_PORT", "6274"))
HEAVYDB_USER = os.environ.get("HEAVYDB_USER", "admin")
HEAVYDB_PASSWORD = os.environ.get("HEAVYDB_PASSWORD", "HyperInteractive")
HEAVYDB_DBNAME = os.environ.get("HEAVYDB_DBNAME", "heavyai")

class HeavyDBClient:
    """
    Client for interacting with HeavyDB to retrieve Nifty options data.
    Uses GPU-accelerated processing when available.
    """
    
    def __init__(self):
        """
        Initialize the HeavyDB client.
        """
        self.conn = self._get_connection()
        
    def _get_connection(self):
        """
        Establish a connection to HeavyDB.
        
        Returns:
            HeavyDB connection object
        """
        try:
            # Try using pymapd first (historical library)
            if PYMAPD_AVAILABLE:
                logger.info("Connecting to HeavyDB using pymapd...")
                conn = pymapd.connect(
                    host=HEAVYDB_HOST,
                    port=HEAVYDB_PORT,
                    user=HEAVYDB_USER,
                    password=HEAVYDB_PASSWORD,
                    dbname=HEAVYDB_DBNAME
                )
                logger.info("Successfully connected to HeavyDB using pymapd.")
                return conn
            # Try pyheavydb next (newer library)
            elif HEAVYDB_AVAILABLE:
                logger.info("Connecting to HeavyDB using pyheavydb...")
                conn = heavydb.connect(
                    host=HEAVYDB_HOST,
                    port=HEAVYDB_PORT,
                    user=HEAVYDB_USER,
                    password=HEAVYDB_PASSWORD,
                    dbname=HEAVYDB_DBNAME
                )
                logger.info("Successfully connected to HeavyDB using pyheavydb.")
                return conn
            else:
                raise ImportError("Neither pymapd nor pyheavydb is available. Please install one of these packages.")
        except Exception as e:
            logger.error(f"Error connecting to HeavyDB: {e}")
            raise
    
    def format_date(self, date_obj: Union[date, datetime, str]) -> str:
        """
        Format date object to string in YYYY-MM-DD format.
        
        Args:
            date_obj: Date to format (can be YYYY-MM-DD, YYMMDD, or YYYYMMDD format)
            
        Returns:
            Formatted date string in YYYY-MM-DD format
        """
        if isinstance(date_obj, str):
            try:
                # Try YYYY-MM-DD format first
                date_obj = datetime.strptime(date_obj, "%Y-%m-%d").date()
            except ValueError:
                try:
                    # Try YYYYMMDD format (without hyphens)
                    date_obj = datetime.strptime(date_obj, "%Y%m%d").date()
                except ValueError:
                    try:
                        # Try YYMMDD format
                        date_obj = datetime.strptime("20" + date_obj, "%Y%m%d").date()
                    except ValueError:
                        raise ValueError(f"Invalid date format: {date_obj}. Expected YYYY-MM-DD, YYYYMMDD, or YYMMDD")
        
        if isinstance(date_obj, datetime):
            date_obj = date_obj.date()
            
        return date_obj.strftime("%Y-%m-%d")
    
    @debug_tool.trace_function
    def execute_query(self, query: str) -> pd.DataFrame:
        """
        Execute a SQL query and return results as a DataFrame.
        
        Args:
            query: SQL query to execute
            
        Returns:
            DataFrame with query results
        """
        start_time = time.time()
        try:
            debug_tool.log_sql(query)
            debug_tool.start_timer(f"sql_query_{start_time}")
            
            if GPU_ENABLED:
                # Using cuDF for GPU acceleration
                result = self.conn.select_ipc_gpu(query)
            else:
                # Using pandas for CPU processing
                result = pd.read_sql(query, self.conn)
            
            execution_time = time.time() - start_time
            debug_tool.stop_timer(f"sql_query_{start_time}")
            debug_tool.log_sql(query, duration=execution_time)
            
            logger.debug(f"Query executed in {execution_time:.6f}s, returned {len(result)} rows")
            
            # Dump the result for debugging if it's small enough
            if len(result) <= 1000:  # Only dump small results to avoid excessive disk usage
                debug_tool.dump_dataframe(result, f"query_result_{start_time}")
                
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error executing query: {e}")
            logger.error(f"Query: {query}")
            debug_tool.log_sql(query, duration=execution_time, params={"error": str(e)})
            return pd.DataFrame()
    
    def get_nifty_option_chain(
        self, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None,
        expiry_date: Optional[str] = None,
        trade_time: Optional[str] = None,
        limit: int = 1000
    ) -> pd.DataFrame:
        """
        Retrieve Nifty option chain data from the nifty_option_chain regular view.
        
        Args:
            start_date: Start date in YYMMDD or YYYY-MM-DD format
            end_date: End date in YYMMDD or YYYY-MM-DD format
            expiry_date: Expiry date in YYMMDD or YYYY-MM-DD format
            trade_time: Specific trade time to filter (HHMMSS format)
            limit: Maximum number of records to retrieve
            
        Returns:
            DataFrame containing options data
        """
        # Build the base query
        query = f"""
        SELECT *
        FROM nifty_option_chain
        WHERE 1=1
        """
        
        # Add filters if provided
        if start_date:
            formatted_start_date = self.format_date(start_date)
            query += f" AND trade_date >= '{formatted_start_date}'"
            
        if end_date:
            formatted_end_date = self.format_date(end_date)
            query += f" AND trade_date <= '{formatted_end_date}'"
            
        if expiry_date:
            formatted_expiry_date = self.format_date(expiry_date)
            query += f" AND expiry_date = '{formatted_expiry_date}'"
            
        if trade_time:
            query += f" AND trade_time = {int(trade_time)}"
        
        # Add ordering and limit
        query += f"""
        ORDER BY trade_date, trade_time, expiry_date, strike
        LIMIT {limit}
        """
        
        logger.info(f"Executing query: {query}")
        return self.execute_query(query)
    
    def get_options_by_dte(
        self, 
        start_date: str, 
        end_date: str, 
        dte: int,
        strikes: Optional[List[float]] = None,
        limit: int = 100000
    ) -> pd.DataFrame:
        """
        Get options data filtered by trading days to expiry (DTE).
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            dte: Trading days to expiry to filter for
            strikes: Optional list of strikes to filter
            limit: Maximum number of records to return
            
        Returns:
            DataFrame with filtered options data
        """
        # Format dates if needed
        start_date = self.format_date(start_date)
        end_date = self.format_date(end_date)
        
        logger.info(f"Querying options with dte={dte} from {start_date} to {end_date}")
        
        # Build the base query
        query = f"""
        SELECT *
        FROM nifty_option_chain
        WHERE trade_date BETWEEN '{start_date}' AND '{end_date}'
        AND dte = {dte}
        """
        
        # Add strike filter if provided
        if strikes and len(strikes) > 0:
            strike_list = ", ".join([str(s) for s in strikes])
            query += f" AND strike IN ({strike_list})"
        
        # Add limit
        query += f" LIMIT {limit}"
        
        logger.info(f"Executing query: {query}")
        df = self.execute_query(query)
        
        if df.empty:
            logger.warning(f"No data found with DTE={dte} for date range {start_date} to {end_date}.")
            return df
        
        # Convert trade_time column format for compatibility with existing code
        if 'trade_time' in df.columns:
            # Convert from int (like 93000) to time object (like 09:30:00)
            def int_to_time(time_int):
                time_str = str(time_int).zfill(6)
                return pd.to_datetime(f"{time_str[:2]}:{time_str[2:4]}:{time_str[4:]}")
            
            df['trade_time'] = df['trade_time'].apply(int_to_time)
        
        # Log statistics about the retrieved data
        logger.info(f"Retrieved {len(df)} records with dte={dte}")
        logger.info(f"Date range: {start_date} to {end_date}")
        if not df.empty:
            logger.info(f"Dates in data: {df['trade_date'].nunique()} days")
            logger.info(f"DTE statistics: min={df['dte'].min()}, max={df['dte'].max()}, unique values={df['dte'].nunique()}")
        
        return df
    
    def get_atm_strikes(
        self, 
        trade_date: str, 
        trade_time: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get at-the-money strike prices for a given date and time.
        
        Args:
            trade_date: Date in YYMMDD format
            trade_time: Time in HHMMSS format (optional)
            
        Returns:
            Dictionary with ATM and nearby strike prices
        """
        # Format date
        formatted_date = self.format_date(trade_date)
        
        # Build the query
        query = f"""
        SELECT *
        FROM nifty_option_chain
        WHERE trade_date = '{formatted_date}'
        """
        
        # Add time filter if provided
        if trade_time:
            query += f" AND trade_time = {int(trade_time)}"
        
        # Get only ATM strikes
        query += """
        AND (call_strike_type = 'ATM' OR put_strike_type = 'ATM')
        ORDER BY expiry_date, trade_time
        """
        
        logger.info(f"Executing query: {query}")
        df = self.execute_query(query)
        
        if df.empty:
            logger.warning(f"No ATM strikes found for date {formatted_date}")
            return {}
        
        # Get the first ATM record
        atm_option = df.iloc[0].to_dict()
        
        # Make sure we have ce_ltp and pe_ltp for consistency
        if 'ce_ltp' not in atm_option and 'ce_close' in atm_option:
            atm_option['ce_ltp'] = atm_option['ce_close']
        
        if 'pe_ltp' not in atm_option and 'pe_close' in atm_option:
            atm_option['pe_ltp'] = atm_option['pe_close']
        
        return atm_option
    
    @staticmethod
    def add_trading_day_dte(df: pd.DataFrame) -> pd.DataFrame:
        """
        Utility function to add trading-day DTE to any DataFrame with 'trade_date' and 'expiry_date' columns.
        
        Args:
            df: DataFrame with 'trade_date' and 'expiry_date' columns
            
        Returns:
            DataFrame with both 'dte' and 'trading_day_dte' columns calculated using trading days
        """
        if df.empty or 'trade_date' not in df.columns or 'expiry_date' not in df.columns:
            return df
            
        # Ensure columns are datetime
        df = df.copy()
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df['expiry_date'] = pd.to_datetime(df['expiry_date'])
        
        # Get trading days for the full range
        min_date = min(df['trade_date'].min(), df['expiry_date'].min())
        max_date = max(df['trade_date'].max(), df['expiry_date'].max())
        
        try:
            # Generate trading days calendar
            trading_days = get_nse_trading_days(
                str(min_date.date()), 
                str(max_date.date()), 
                NSE_HOLIDAYS_2024
            )
            
            # Calculate trading-day DTE and add both column names for consistency
            values = df.apply(
                lambda row: trading_day_dte(row['trade_date'], row['expiry_date'], trading_days), 
                axis=1
            )
            
            # Add both column names for consistency across codebase
            df['dte'] = values
            df['trading_day_dte'] = values
            
            logger.info(f"Added trading-day DTE for {len(df)} records (min={values.min()}, max={values.max()})")
            
        except Exception as e:
            logger.error(f"Error calculating trading-day DTE: {e}")
            # Fallback - leave existing values if any
            if 'trading_day_dte' in df.columns and 'dte' not in df.columns:
                df['dte'] = df['trading_day_dte']
            elif 'dte' in df.columns and 'trading_day_dte' not in df.columns:
                df['trading_day_dte'] = df['dte']
                
        return df