"""
Debug tool for Nifty VWAP Strategy Backtester.
Provides utilities for troubleshooting and performance analysis.
"""
import os
import time
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Optional, Union, Any, Callable
import pandas as pd
import numpy as np
from functools import wraps

# Configure logging
logger = logging.getLogger(__name__)

class DebugTool:
    """
    Debug tool for Nifty VWAP Strategy Backtester.
    """
    
    def __init__(
        self, 
        enabled: bool = False, 
        log_level: str = "INFO",
        trace_sql: bool = False,
        profile_performance: bool = False,
        debug_dir: str = "debug_output",
        clean_debug_dir: bool = False
    ):
        """
        Initialize the debug tool.
        
        Args:
            enabled: Whether debugging is enabled
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            trace_sql: Whether to log SQL queries
            profile_performance: Whether to profile performance
            debug_dir: Directory to save debug output
            clean_debug_dir: Whether to clean the debug directory at startup
        """
        self.enabled = enabled
        self.log_level = getattr(logging, log_level.upper())
        self.trace_sql = trace_sql
        self.profile_performance = profile_performance
        self.debug_dir = debug_dir
        self.timers = {}
        self.counters = {}
        self.sql_queries = []
        
        # Create debug directory if it doesn't exist
        if self.enabled:
            os.makedirs(self.debug_dir, exist_ok=True)
            
            # Clean debug directory if requested
            if clean_debug_dir:
                for file in os.listdir(self.debug_dir):
                    file_path = os.path.join(self.debug_dir, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
            
            # Create debug log file
            debug_log_file = os.path.join(self.debug_dir, "debug.log")
            file_handler = logging.FileHandler(debug_log_file, mode='w', encoding='utf-8')
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            
            # Get the root logger and add our handler
            root_logger = logging.getLogger()
            root_logger.addHandler(file_handler)
            root_logger.setLevel(self.log_level)
            
            logger.info(f"Debug tool initialized with log_level={log_level}, trace_sql={trace_sql}, profile_performance={profile_performance}")
    
    def log_sql(self, query: str, params: Optional[Dict] = None, duration: Optional[float] = None):
        """
        Log an SQL query for debugging.
        
        Args:
            query: SQL query string
            params: Query parameters
            duration: Query execution time in seconds
        """
        if not self.enabled or not self.trace_sql:
            return
            
        query_info = {
            "timestamp": datetime.now().isoformat(),
            "query": query,
            "params": params,
            "duration": duration
        }
        
        self.sql_queries.append(query_info)
        
        logger.debug(f"SQL Query: {query}")
        if params:
            logger.debug(f"Parameters: {params}")
        if duration:
            logger.debug(f"Duration: {duration:.6f}s")
    
    def start_timer(self, name: str):
        """
        Start a timer for performance profiling.
        
        Args:
            name: Timer name
        """
        if not self.enabled or not self.profile_performance:
            return
            
        self.timers[name] = {
            "start": time.time(),
            "end": None,
            "duration": None
        }
        
        logger.debug(f"Timer started: {name}")
    
    def stop_timer(self, name: str) -> Optional[float]:
        """
        Stop a timer and return the duration.
        
        Args:
            name: Timer name
            
        Returns:
            Duration in seconds or None if timer not found
        """
        if not self.enabled or not self.profile_performance:
            return None
            
        if name not in self.timers:
            logger.warning(f"Timer not found: {name}")
            return None
            
        self.timers[name]["end"] = time.time()
        self.timers[name]["duration"] = self.timers[name]["end"] - self.timers[name]["start"]
        
        duration = self.timers[name]["duration"]
        logger.debug(f"Timer stopped: {name}, duration: {duration:.6f}s")
        
        return duration
    
    def increment_counter(self, name: str, value: int = 1):
        """
        Increment a counter for tracking occurrences.
        
        Args:
            name: Counter name
            value: Value to increment by
        """
        if not self.enabled:
            return
            
        if name not in self.counters:
            self.counters[name] = 0
            
        self.counters[name] += value
    
    def dump_dataframe(self, df: pd.DataFrame, name: str, max_rows: int = 1000):
        """
        Dump a DataFrame to CSV for debugging.
        
        Args:
            df: DataFrame to dump
            name: Name of the dump file
            max_rows: Maximum number of rows to dump
        """
        if not self.enabled:
            return
            
        if df is None or df.empty:
            logger.warning(f"Cannot dump empty DataFrame: {name}")
            return
            
        # Create a safe filename
        filename = f"{name.replace(' ', '_').replace('/', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        filepath = os.path.join(self.debug_dir, filename)
        
        # Dump the DataFrame (limit rows if needed)
        if len(df) > max_rows:
            logger.info(f"Limiting DataFrame dump to {max_rows} rows (original size: {len(df)})")
            df = df.head(max_rows)
            
        df.to_csv(filepath, index=True)
        logger.info(f"DataFrame dumped to {filepath} ({len(df)} rows, {len(df.columns)} columns)")
    
    def dump_json(self, data: Any, name: str):
        """
        Dump data as JSON for debugging.
        
        Args:
            data: Data to dump
            name: Name of the dump file
        """
        if not self.enabled:
            return
            
        # Create a safe filename
        filename = f"{name.replace(' ', '_').replace('/', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = os.path.join(self.debug_dir, filename)
        
        # Convert non-serializable objects
        def json_serializer(obj):
            if isinstance(obj, (datetime, np.datetime64)):
                return obj.isoformat()
            if isinstance(obj, np.integer):
                return int(obj)
            if isinstance(obj, np.floating):
                return float(obj)
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            if pd.isna(obj):
                return None
            return str(obj)
        
        # Dump the data
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=json_serializer)
            
        logger.info(f"Data dumped to {filepath}")
    
    def generate_report(self):
        """
        Generate a debug report with all collected information.
        
        Returns:
            Dictionary with debug information
        """
        if not self.enabled:
            return {}
            
        report = {
            "timestamp": datetime.now().isoformat(),
            "counters": self.counters,
            "timers": {name: timer["duration"] for name, timer in self.timers.items() if timer["duration"] is not None},
            "sql_query_count": len(self.sql_queries)
        }
        
        # Save the report
        report_path = os.path.join(self.debug_dir, f"debug_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"Debug report generated: {report_path}")
        
        # Generate SQL query report if enabled
        if self.trace_sql and self.sql_queries:
            sql_report_path = os.path.join(self.debug_dir, f"sql_queries_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(sql_report_path, 'w', encoding='utf-8') as f:
                json.dump(self.sql_queries, f, indent=2)
                
            logger.info(f"SQL query report generated: {sql_report_path}")
        
        return report

    def trace_function(self, func):
        """
        Decorator to trace function calls with timing.
        
        Args:
            func: Function to trace
            
        Returns:
            Wrapped function
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not self.enabled:
                return func(*args, **kwargs)
                
            func_name = f"{func.__module__}.{func.__name__}"
            logger.debug(f"Entering function: {func_name}")
            self.start_timer(func_name)
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                logger.error(f"Exception in {func_name}: {str(e)}")
                logger.error(traceback.format_exc())
                raise
            finally:
                duration = self.stop_timer(func_name)
                logger.debug(f"Exiting function: {func_name}, duration: {duration:.6f}s")
                
        return wrapper

# Global debug tool instance
debug_tool = DebugTool(enabled=False)

def set_debug_options(
    enabled: bool = False,
    log_level: str = "INFO",
    trace_sql: bool = False,
    profile_performance: bool = False,
    debug_dir: str = "debug_output",
    clean_debug_dir: bool = False
):
    """
    Configure the global debug tool.
    
    Args:
        enabled: Whether debugging is enabled
        log_level: Logging level
        trace_sql: Whether to log SQL queries
        profile_performance: Whether to profile performance
        debug_dir: Directory to save debug output
        clean_debug_dir: Whether to clean the debug directory
    """
    global debug_tool
    debug_tool = DebugTool(
        enabled=enabled,
        log_level=log_level,
        trace_sql=trace_sql,
        profile_performance=profile_performance,
        debug_dir=debug_dir,
        clean_debug_dir=clean_debug_dir
    )
    return debug_tool