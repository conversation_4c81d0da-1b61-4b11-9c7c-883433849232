#!/bin/bash
# <PERSON>ript to run the HeavyDB version of the Nifty VWAP Strategy backtester

# Load environment variables
source .env

# Default values
START_DATE="240401"
END_DATE="240430"
DTE="1"
OUTPUT_DIR="results_heavydb"
DEBUG_MODE="false"
DEBUG_LEVEL="INFO"
TRACE_SQL="false"
PROFILE="false"
DEBUG_DIR="debug_output"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --start_date)
      START_DATE="$2"
      shift 2
      ;;
    --end_date)
      END_DATE="$2"
      shift 2
      ;;
    --dte)
      DTE="$2"
      shift 2
      ;;
    --output_dir)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --debug)
      DEBUG_MODE="true"
      shift
      ;;
    --debug-level)
      DEBUG_LEVEL="$2"
      shift 2
      ;;
    --trace-sql)
      TRACE_SQL="true"
      shift
      ;;
    --profile)
      PROFILE="true"
      shift
      ;;
    --debug-dir)
      DEBUG_DIR="$2"
      shift 2
      ;;
    *)
      # Save all other arguments to pass to Python script
      EXTRA_ARGS="$EXTRA_ARGS $1"
      shift
      ;;
  esac
done

# Check if Python virtual environment exists and activate it
if [ -d "venv" ]; then
  echo "Activating virtual environment..."
  source venv/bin/activate
fi

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Add debug flags if enabled
DEBUG_FLAGS=""
if [ "$DEBUG_MODE" = "true" ]; then
  DEBUG_FLAGS="--debug --debug-level $DEBUG_LEVEL --debug-dir $DEBUG_DIR"
  
  if [ "$TRACE_SQL" = "true" ]; then
    DEBUG_FLAGS="$DEBUG_FLAGS --trace-sql"
  fi
  
  if [ "$PROFILE" = "true" ]; then
    DEBUG_FLAGS="$DEBUG_FLAGS --profile"
  fi
fi

# Run the Python script
echo "Running HeavyDB backtester with start_date=$START_DATE, end_date=$END_DATE, dte=$DTE"
python main_heavydb.py \
  --start_date "$START_DATE" \
  --end_date "$END_DATE" \
  --dte "$DTE" \
  --output_dir "$OUTPUT_DIR" \
  $DEBUG_FLAGS \
  $EXTRA_ARGS

# Check if the script was successful
if [ $? -eq 0 ]; then
  echo "Backtesting completed successfully!"
  echo "Results saved in $OUTPUT_DIR/"
else
  echo "Backtesting failed. Check heavydb_backtest.log for errors."
fi

# Deactivate virtual environment if it was activated
if [ -d "venv" ]; then
  deactivate
fi