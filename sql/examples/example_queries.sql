-- Example queries for Nifty Option Chain Materialized View

-- 1. Basic query with date filters (always use date filters)
SELECT 
    trade_date,
    expiry_date,
    strike,
    atm_strike,
    underlying_price,
    dte,
    call_strike_type,
    put_strike_type
FROM nifty_option_chain
WHERE trade_date = '2023-01-03' AND expiry_date = '2023-01-26'
LIMIT 20;

-- 2. Query for ATM options only
SELECT 
    trade_date,
    expiry_date,
    strike,
    ce_close,
    pe_close,
    ce_iv,
    pe_iv
FROM nifty_option_chain
WHERE trade_date = '2023-01-03' 
  AND call_strike_type = 'ATM'
ORDER BY expiry_date;

-- 3. Query for specific strike types (ATM and nearby strikes)
SELECT 
    trade_date,
    expiry_date,
    strike,
    call_strike_type,
    ce_close,
    ce_iv,
    ce_delta
FROM nifty_option_chain
WHERE trade_date = '2023-01-03'
  AND expiry_date = '2023-01-26'
  AND call_strike_type IN ('ATM', 'ITM1', 'ITM2', 'OTM1', 'OTM2')
ORDER BY strike;

-- 4. Query by DTE (specific days to expiry)
SELECT 
    trade_date,
    expiry_date,
    dte,
    strike,
    call_strike_type,
    ce_close
FROM nifty_option_chain
WHERE trade_date = '2023-01-03'
  AND dte BETWEEN 15 AND 30
  AND call_strike_type = 'ATM'
ORDER BY expiry_date;

-- 5. Calculating Put-Call Ratio (PCR) using volume
SELECT 
    trade_date,
    expiry_date,
    SUM(pe_volume) AS total_put_volume,
    SUM(ce_volume) AS total_call_volume,
    SUM(pe_volume) / NULLIF(SUM(ce_volume), 0) AS volume_pcr
FROM nifty_option_chain
WHERE trade_date = '2023-01-03'
  AND expiry_date = '2023-01-26'
GROUP BY trade_date, expiry_date;

-- 6. Analyzing option chain for a specific range around ATM
SELECT 
    trade_date,
    expiry_date,
    strike,
    atm_strike,
    call_strike_type,
    put_strike_type,
    ce_close,
    pe_close,
    ce_iv,
    pe_iv,
    ce_delta,
    pe_delta
FROM nifty_option_chain
WHERE trade_date = '2023-01-03'
  AND expiry_date = '2023-01-26'
  AND ABS(strike - atm_strike) <= 500
ORDER BY strike;

-- 7. Time series analysis for specific strike
SELECT 
    trade_date,
    expiry_date,
    dte,
    ce_close,
    ce_iv,
    ce_delta
FROM nifty_option_chain
WHERE expiry_date = '2023-01-26'
  AND strike = 18000
  AND trade_date BETWEEN '2023-01-01' AND '2023-01-15'
ORDER BY trade_date;

-- 8. Finding the most liquid strikes by volume
SELECT 
    trade_date,
    expiry_date,
    strike,
    call_strike_type,
    ce_volume,
    ce_oi,
    ce_close
FROM nifty_option_chain
WHERE trade_date = '2023-01-03'
  AND expiry_date = '2023-01-26'
ORDER BY ce_volume DESC
LIMIT 10;

-- 9. Analyzing implied volatility smile
SELECT 
    strike,
    call_strike_type,
    ce_iv,
    pe_iv
FROM nifty_option_chain
WHERE trade_date = '2023-01-03'
  AND expiry_date = '2023-01-26'
  AND call_strike_type IN ('ATM', 'ITM1', 'ITM2', 'ITM3', 'ITM4', 'OTM1', 'OTM2', 'OTM3', 'OTM4')
ORDER BY strike;

-- 10. Monitoring option chain changes over time
SELECT 
    a.trade_date,
    a.strike,
    a.call_strike_type,
    a.ce_close,
    a.ce_iv,
    a.dte,
    (a.ce_close - b.ce_close) AS price_change,
    (a.ce_iv - b.ce_iv) AS iv_change
FROM nifty_option_chain a
JOIN nifty_option_chain b
  ON a.strike = b.strike 
  AND a.expiry_date = b.expiry_date
  AND b.trade_date = (a.trade_date - INTERVAL '1 day')
WHERE a.trade_date = '2023-01-04'
  AND a.expiry_date = '2023-01-26'
  AND a.call_strike_type IN ('ATM', 'ITM1', 'OTM1')
ORDER BY a.strike; 