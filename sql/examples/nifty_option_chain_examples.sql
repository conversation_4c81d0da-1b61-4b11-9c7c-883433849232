-- Nifty Option Chain Example Queries
-- Note: Due to the large dataset, all queries should include appropriate filters

-- Basic count of records
SELECT COUNT(*) FROM nifty_option_chain;

-- Count by trade date (for a recent period)
SELECT trade_date, COUNT(*) 
FROM nifty_option_chain 
WHERE trade_date >= '2023-01-01' 
GROUP BY trade_date 
ORDER BY trade_date DESC 
LIMIT 10;

-- Get ATM strikes for each expiry on a specific date
SELECT trade_date, expiry_date, atm_strike
FROM nifty_option_chain
WHERE trade_date = '2023-01-18'
  AND call_moneyness = 'ATM'
GROUP BY trade_date, expiry_date, atm_strike
ORDER BY expiry_date;

-- Get put-call ratio for each expiry on a specific date
SELECT trade_date, expiry_date, pcr_oi, pcr_volume
FROM nifty_option_chain
WHERE trade_date = '2023-01-18'
  AND call_moneyness = 'ATM'
GROUP BY trade_date, expiry_date, pcr_oi, pcr_volume
ORDER BY expiry_date;

-- Get all ITM1 calls for a specific date and expiry
SELECT trade_date, expiry_date, strike, call_moneyness,
       ce_close, ce_volume, ce_oi, ce_iv
FROM nifty_option_chain
WHERE trade_date = '2023-01-18'
  AND expiry_date = '2023-01-25'  -- Adjust this date as needed
  AND call_moneyness = 'ITM1'
ORDER BY strike;

-- Get all OTM1 puts for a specific date and expiry
SELECT trade_date, expiry_date, strike, put_moneyness,
       pe_close, pe_volume, pe_oi, pe_iv
FROM nifty_option_chain
WHERE trade_date = '2023-01-18'
  AND expiry_date = '2023-01-25'  -- Adjust this date as needed
  AND put_moneyness = 'OTM1'
ORDER BY strike; 