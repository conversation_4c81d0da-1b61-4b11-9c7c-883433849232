DROP VIEW IF EXISTS nifty_option_chain;

CREATE VIEW nifty_option_chain AS
WITH closest_strikes AS (
    SELECT 
        a.trade_date,
        a.expiry_date,
        a.underlying_price,
        MIN(ABS(a.strike - a.underlying_price)) AS min_distance,
        CASE WHEN a.underlying_price >= 10000 THEN 100 ELSE 50 END AS strike_increment
    FROM nifty_greeks a
    GROUP BY a.trade_date, a.expiry_date, a.underlying_price
),
atm_strikes AS (
    SELECT DISTINCT
        ng.trade_date,
        ng.expiry_date,
        cs.strike_increment,
        FIRST_VALUE(ng.strike) OVER (
            PARTITION BY ng.trade_date, ng.expiry_date 
            ORDER BY ABS(ng.strike - ng.underlying_price), ng.strike
        ) AS atm_strike
    FROM nifty_greeks ng
    JOIN closest_strikes cs ON ng.trade_date = cs.trade_date 
                            AND ng.expiry_date = cs.expiry_date
),
distinct_greeks AS (
    SELECT DISTINCT
        trade_date,
        expiry_date,
        strike,
        underlying_price,
        ce_symbol,
        ce_open,
        ce_high,
        ce_low,
        ce_close,
        ce_volume,
        ce_oi,
        ce_coi,
        ce_iv,
        ce_delta,
        ce_gamma,
        ce_theta,
        ce_vega,
        ce_rho,
        pe_symbol,
        pe_open,
        pe_high,
        pe_low,
        pe_close,
        pe_volume,
        pe_oi,
        pe_coi,
        pe_iv,
        pe_delta,
        pe_gamma,
        pe_theta,
        pe_vega,
        pe_rho
    FROM nifty_greeks
),
option_chain AS (
    SELECT
        ng.trade_date,
        ng.expiry_date,
        ng.strike,
        ng.underlying_price,
        atm.atm_strike,
        atm.strike_increment,
        CASE
            WHEN ng.strike = atm.atm_strike THEN 'ATM'
            WHEN ng.strike < atm.atm_strike THEN 
                'ITM' || CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / atm.strike_increment) AS INT)
            ELSE 
                'OTM' || CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / atm.strike_increment) AS INT)
        END AS call_moneyness,
        CASE
            WHEN ng.strike = atm.atm_strike THEN 'ATM'
            WHEN ng.strike > atm.atm_strike THEN 
                'ITM' || CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / atm.strike_increment) AS INT)
            ELSE 
                'OTM' || CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / atm.strike_increment) AS INT)
        END AS put_moneyness,
        CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / atm.strike_increment) AS INT) AS strike_distance,
        -- Calculate DTE as date difference (calendar days)
        DATEDIFF(DAY, ng.trade_date, ng.expiry_date) AS dte_calendar,
        -- Approximate business days as 5/7 of calendar days
        CAST(DATEDIFF(DAY, ng.trade_date, ng.expiry_date) * 5 / 7 AS INT) AS dte_trading_days,
        ng.ce_symbol,
        ng.ce_open,
        ng.ce_high,
        ng.ce_low,
        ng.ce_close,
        ng.ce_volume,
        ng.ce_oi,
        ng.ce_coi,
        ng.ce_iv,
        ng.ce_delta,
        ng.ce_gamma,
        ng.ce_theta,
        ng.ce_vega,
        ng.ce_rho,
        ng.pe_symbol,
        ng.pe_open,
        ng.pe_high,
        ng.pe_low,
        ng.pe_close,
        ng.pe_volume,
        ng.pe_oi,
        ng.pe_coi,
        ng.pe_iv,
        ng.pe_delta,
        ng.pe_gamma,
        ng.pe_theta,
        ng.pe_vega,
        ng.pe_rho
    FROM distinct_greeks ng
    JOIN atm_strikes atm ON ng.trade_date = atm.trade_date 
                          AND ng.expiry_date = atm.expiry_date
),
distinct_output AS (
    SELECT DISTINCT
        oc.trade_date,
        oc.expiry_date,
        oc.underlying_price,
        oc.atm_strike,
        'SPOT' AS selection_method,
        oc.underlying_price AS synthetic_future,
        oc.strike,
        oc.strike_distance,
        oc.dte_calendar,
        oc.dte_trading_days,
        CASE
            WHEN oc.call_moneyness = 'ATM' THEN 0
            WHEN LEFT(oc.call_moneyness, 3) = 'ITM' THEN -CAST(RIGHT(oc.call_moneyness, LENGTH(oc.call_moneyness) - 3) AS INT)
            WHEN LEFT(oc.call_moneyness, 3) = 'OTM' THEN CAST(RIGHT(oc.call_moneyness, LENGTH(oc.call_moneyness) - 3) AS INT)
            ELSE 0
        END AS call_moneyness_code,
        CASE
            WHEN oc.put_moneyness = 'ATM' THEN 0
            WHEN LEFT(oc.put_moneyness, 3) = 'ITM' THEN -CAST(RIGHT(oc.put_moneyness, LENGTH(oc.put_moneyness) - 3) AS INT)
            WHEN LEFT(oc.put_moneyness, 3) = 'OTM' THEN CAST(RIGHT(oc.put_moneyness, LENGTH(oc.put_moneyness) - 3) AS INT)
            ELSE 0
        END AS put_moneyness_code,
        oc.ce_symbol,
        oc.ce_open,
        oc.ce_high,
        oc.ce_low,
        oc.ce_close,
        oc.ce_volume,
        oc.ce_oi,
        oc.ce_coi,
        oc.ce_iv,
        oc.ce_delta,
        oc.ce_gamma,
        oc.ce_theta,
        oc.ce_vega,
        oc.ce_rho,
        oc.pe_symbol,
        oc.pe_open,
        oc.pe_high,
        oc.pe_low,
        oc.pe_close,
        oc.pe_volume,
        oc.pe_oi,
        oc.pe_coi,
        oc.pe_iv,
        oc.pe_delta,
        oc.pe_gamma,
        oc.pe_theta,
        oc.pe_vega,
        oc.pe_rho
    FROM option_chain oc
)
SELECT
    do.*,
    CAST(SUM(do.pe_oi) OVER (PARTITION BY do.trade_date, do.expiry_date) / 
         NULLIF(SUM(do.ce_oi) OVER (PARTITION BY do.trade_date, do.expiry_date), 0) AS DOUBLE) AS pcr_oi,
    CAST(SUM(do.pe_volume) OVER (PARTITION BY do.trade_date, do.expiry_date) / 
         NULLIF(SUM(do.ce_volume) OVER (PARTITION BY do.trade_date, do.expiry_date), 0) AS DOUBLE) AS pcr_volume
FROM distinct_output do; 