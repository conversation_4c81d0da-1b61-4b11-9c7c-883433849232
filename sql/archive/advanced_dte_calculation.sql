DROP VIEW IF EXISTS nifty_option_chain;

CREATE VIEW nifty_option_chain AS 
SELECT DISTINCT
    ng.trade_date,
    ng.expiry_date,
    ng.underlying_price,
    atm.atm_strike,
    'SPOT' AS selection_method,
    ng.underlying_price AS synthetic_future,
    ng.strike,
    CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
        CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT) AS strike_distance,
    -- Advanced DTE calculation accounting for weekends and holidays
    CASE
        -- Special case for same day
        WHEN DATEDIFF('day', ng.trade_date, ng.expiry_date) = 0 
            THEN 0
        ELSE
            -- Count only trading days between trade_date and expiry_date
            -- This accounts for weekends and optionally holidays if the nse_holidays table exists
            (SELECT COUNT(*) 
             FROM (
                 -- Generate a series of dates between trade_date and expiry_date-1
                 SELECT DATEADD('day', seq, ng.trade_date) AS trading_day
                 FROM (
                     -- Generate a sequence from 0 to days difference-1
                     SELECT seq 
                     FROM (
                         SELECT ROW_NUMBER() OVER () - 1 AS seq 
                         FROM nifty_greeks 
                         LIMIT DATEDIFF('day', ng.trade_date, ng.expiry_date)
                     ) AS seq_table
                 ) AS date_series
             ) AS all_days
             WHERE 
                 -- Exclude weekends (Saturday=6, Sunday=0)
                 EXTRACT(DOW FROM trading_day) NOT IN (0, 6)
                 -- Also exclude dates in holiday table if it exists
                 AND NOT EXISTS (
                     SELECT 1 FROM nse_holidays 
                     WHERE holiday_date = trading_day
                     -- Try this if table exists, otherwise the view will still work without holiday exclusion
                 )
            )
    END AS dte,
    CASE
        WHEN ng.strike = atm.atm_strike THEN 0
        WHEN ng.strike < atm.atm_strike THEN 
            -CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT)
        ELSE 
            CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT)
    END AS call_moneyness_code,
    CASE
        WHEN ng.strike = atm.atm_strike THEN 0
        WHEN ng.strike > atm.atm_strike THEN 
            -CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT)
        ELSE 
            CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT)
    END AS put_moneyness_code,
    ng.ce_symbol,
    ng.ce_open,
    ng.ce_high,
    ng.ce_low,
    ng.ce_close,
    ng.ce_volume,
    ng.ce_oi,
    ng.ce_coi,
    ng.ce_iv,
    ng.ce_delta,
    ng.ce_gamma,
    ng.ce_theta,
    ng.ce_vega,
    ng.ce_rho,
    ng.pe_symbol,
    ng.pe_open,
    ng.pe_high,
    ng.pe_low,
    ng.pe_close,
    ng.pe_volume,
    ng.pe_oi,
    ng.pe_coi,
    ng.pe_iv,
    ng.pe_delta,
    ng.pe_gamma,
    ng.pe_theta,
    ng.pe_vega,
    ng.pe_rho
FROM nifty_greeks ng
JOIN (
    SELECT DISTINCT
        a.trade_date,
        a.expiry_date,
        FIRST_VALUE(a.strike) OVER (
            PARTITION BY a.trade_date, a.expiry_date 
            ORDER BY ABS(a.strike - a.underlying_price), a.strike
        ) AS atm_strike
    FROM nifty_greeks a
) atm ON ng.trade_date = atm.trade_date AND ng.expiry_date = atm.expiry_date; 