DROP TABLE IF EXISTS nse_holidays;

CREATE TABLE nse_holidays (
    holiday_date DATE NOT NULL,
    reason TEXT NOT NULL
);

INSERT INTO nse_holidays VALUES ('2024-01-26', 'Republic Day');
INSERT INTO nse_holidays VALUES ('2024-03-08', '<PERSON><PERSON>');
INSERT INTO nse_holidays VALUES ('2024-03-25', 'Ho<PERSON>');
INSERT INTO nse_holidays VALUES ('2024-03-29', 'Good Friday');
INSERT INTO nse_holidays VALUES ('2024-04-11', 'Id-ul-Fitr (<PERSON>zan ID)');
INSERT INTO nse_holidays VALUES ('2024-04-17', '<PERSON>');
INSERT INTO nse_holidays VALUES ('2024-05-01', 'Maharashtra Day');
INSERT INTO nse_holidays VALUES ('2024-05-13', 'Monday Test Holiday');
INSERT INTO nse_holidays VALUES ('2024-05-07', 'Tuesday Test Holiday');
INSERT INTO nse_holidays VALUES ('2024-06-17', '<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON> ul-<PERSON>');
INSERT INTO nse_holidays VALUES ('2024-07-17', 'Muharram');
INSERT INTO nse_holidays VALUES ('2024-08-15', 'Independence Day');
INSERT INTO nse_holidays VALUES ('2024-10-02', 'Mahatma Gandhi Jayanti');
INSERT INTO nse_holidays VALUES ('2024-10-31', 'Diwali-Laxmi Pujan');
INSERT INTO nse_holidays VALUES ('2024-11-15', 'Guru Nanak Jayanti');
INSERT INTO nse_holidays VALUES ('2024-12-25', 'Christmas');

INSERT INTO nse_holidays VALUES ('2025-01-26', 'Republic Day');
INSERT INTO nse_holidays VALUES ('2025-03-14', 'Holi');
INSERT INTO nse_holidays VALUES ('2025-04-18', 'Good Friday');
INSERT INTO nse_holidays VALUES ('2025-04-08', 'Tuesday Test Holiday');
INSERT INTO nse_holidays VALUES ('2025-08-15', 'Independence Day');
INSERT INTO nse_holidays VALUES ('2025-10-02', 'Mahatma Gandhi Jayanti');
INSERT INTO nse_holidays VALUES ('2025-10-20', 'Diwali-Laxmi Pujan');
INSERT INTO nse_holidays VALUES ('2025-12-25', 'Christmas'); 