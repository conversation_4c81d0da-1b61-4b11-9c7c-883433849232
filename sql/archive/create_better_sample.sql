DROP VIEW IF EXISTS nifty_sample;

CREATE VIEW nifty_sample AS
WITH base_data AS (
    SELECT
        trade_date,
        expiry_date,
        underlying_price,
        CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END AS strike_increment,
        CAST(ROUND(underlying_price / (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END)) * 
             (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT) AS atm_strike,
        strike,
        ce_symbol,
        ce_close,
        pe_symbol,
        pe_close
    FROM nifty_greeks
    WHERE trade_date = '2023-01-18'
      AND expiry_date = '2023-01-25'
)
SELECT
    trade_date,
    expiry_date,
    underlying_price,
    atm_strike,
    strike_increment,
    strike,
    CASE
        WHEN strike = atm_strike THEN 'ATM'
        WHEN strike < atm_strike THEN 
            'ITM' || CAST(FLOOR(ABS(strike - atm_strike) / strike_increment) AS INT)
        ELSE 
            'OTM' || CAST(FLOOR(ABS(strike - atm_strike) / strike_increment) AS INT)
    END AS call_moneyness,
    CASE
        WHEN strike = atm_strike THEN 'ATM'
        WHEN strike > atm_strike THEN 
            'ITM' || CAST(FLOOR(ABS(strike - atm_strike) / strike_increment) AS INT)
        ELSE 
            'OTM' || CAST(FLOOR(ABS(strike - atm_strike) / strike_increment) AS INT)
    END AS put_moneyness,
    ce_close,
    pe_close
FROM base_data
ORDER BY strike
LIMIT 100; 