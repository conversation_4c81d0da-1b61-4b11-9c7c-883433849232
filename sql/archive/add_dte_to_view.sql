DROP VIEW IF EXISTS nifty_option_chain;

CREATE VIEW nifty_option_chain AS
WITH base_data AS (
    SELECT
        trade_date,
        expiry_date,
        underlying_price,
        atm_strike,
        selection_method,
        synthetic_future,
        strike,
        strike_distance,
        call_moneyness_code,
        put_moneyness_code,
        ce_close,
        ce_volume,
        ce_oi,
        ce_iv,
        ce_delta,
        ce_gamma,
        ce_theta,
        ce_vega,
        pe_close,
        pe_volume,
        pe_oi,
        pe_iv,
        pe_delta,
        pe_gamma,
        pe_theta,
        pe_vega,
        -- Add DTE calculations
        DATEDIFF(DAY, trade_date, expiry_date) AS dte_calendar,
        -- Approximating business days as 5/7 of calendar days
        -- A better implementation would use a holiday calendar
        CAST(DATEDIFF(DAY, trade_date, expiry_date) * 5 / 7 AS INTEGER) AS dte_bdays
    FROM nifty_greeks
)
SELECT * FROM base_data; 