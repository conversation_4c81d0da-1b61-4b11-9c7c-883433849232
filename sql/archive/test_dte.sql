SELECT 
    'Test Case 1: 2023-05-03 to 2023-05-04' AS test_case,
    DATEDIFF('day', DATE '2023-05-03', DATE '2023-05-04') AS day_diff,
    EXTRACT(DOW FROM DATE '2023-05-03') AS dow_start,
    EXTRACT(DOW FROM DATE '2023-05-04') AS dow_end,
    CASE
        WHEN DATEDIFF('day', DATE '2023-05-03', DATE '2023-05-04') = 0 
            THEN 0
        WHEN DATEDIFF('day', DATE '2023-05-03', DATE '2023-05-04') = 1
            THEN 
                CASE 
                    WHEN EXTRACT(DOW FROM DATE '2023-05-03') IN (5, 6) 
                    THEN 0 
                    ELSE 1 
                END
        ELSE
            DATEDIFF('day', DATE '2023-05-03', DATE '2023-05-04')
            - (
                FLOOR((EXTRACT(DOW FROM DATE '2023-05-03') + DATEDIFF('day', DATE '2023-05-03', DATE '2023-05-04')) / 7)
                +
                FLOOR((EXTRACT(DOW FROM DATE '2023-05-03') + DATEDIFF('day', DATE '2023-05-03', DATE '2023-05-04') + 1) / 7)
              )
    END AS dte;

SELECT 
    'Test Case 2: 2023-04-05 to 2023-05-25' AS test_case,
    DATEDIFF('day', DATE '2023-04-05', DATE '2023-05-25') AS day_diff,
    EXTRACT(DOW FROM DATE '2023-04-05') AS dow_start,
    EXTRACT(DOW FROM DATE '2023-05-25') AS dow_end,
    CASE
        WHEN DATEDIFF('day', DATE '2023-04-05', DATE '2023-05-25') = 0 
            THEN 0
        WHEN DATEDIFF('day', DATE '2023-04-05', DATE '2023-05-25') = 1
            THEN 
                CASE 
                    WHEN EXTRACT(DOW FROM DATE '2023-04-05') IN (5, 6) 
                    THEN 0 
                    ELSE 1 
                END
        ELSE
            DATEDIFF('day', DATE '2023-04-05', DATE '2023-05-25')
            - (
                FLOOR((EXTRACT(DOW FROM DATE '2023-04-05') + DATEDIFF('day', DATE '2023-04-05', DATE '2023-05-25')) / 7)
                +
                FLOOR((EXTRACT(DOW FROM DATE '2023-04-05') + DATEDIFF('day', DATE '2023-04-05', DATE '2023-05-25') + 1) / 7)
              )
    END AS dte;

SELECT 
    'Test Case 3: 2025-04-04 (Friday) to 2025-04-10 (Thursday)' AS test_case,
    DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10') AS day_diff,
    EXTRACT(DOW FROM DATE '2025-04-04') AS dow_start,
    EXTRACT(DOW FROM DATE '2025-04-10') AS dow_end,
    CASE
        WHEN DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10') = 0 
            THEN 0
        WHEN DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10') = 1
            THEN 
                CASE 
                    WHEN EXTRACT(DOW FROM DATE '2025-04-04') IN (5, 6) 
                    THEN 0 
                    ELSE 1 
                END
        ELSE
            DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10')
            - (
                FLOOR((EXTRACT(DOW FROM DATE '2025-04-04') + DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10')) / 7)
                +
                FLOOR((EXTRACT(DOW FROM DATE '2025-04-04') + DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10') + 1) / 7)
              )
    END AS dte; 