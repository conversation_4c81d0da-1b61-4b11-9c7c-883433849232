DROP VIEW IF EXISTS nifty_option_chain;

CREATE VIEW nifty_option_chain AS 
SELECT DISTINCT
    ng.trade_date,
    ng.expiry_date,
    ng.underlying_price,
    atm.atm_strike,
    'SPOT' AS selection_method,
    ng.underlying_price AS synthetic_future,
    ng.strike,
    CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
        CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT) AS strike_distance,
    CASE
        WHEN DATEDIFF('day', ng.trade_date, ng.expiry_date) = 0 THEN 0
        WHEN DATEDIFF('day', ng.trade_date, ng.expiry_date) = 1 THEN 
            CASE WHEN EXTRACT(DOW FROM ng.trade_date) IN (5, 6) THEN 0 ELSE 1 END
        ELSE
            DATEDIFF('day', ng.trade_date, ng.expiry_date)
            - (FLOOR((EXTRACT(DOW FROM ng.trade_date) + DATEDIFF('day', ng.trade_date, ng.expiry_date)) / 7)
               + FLOOR((EXTRACT(DOW FROM ng.trade_date) + DATEDIFF('day', ng.trade_date, ng.expiry_date) + 1) / 7))
    END AS dte,
    CASE
        WHEN ng.strike = atm.atm_strike THEN 0
        WHEN ng.strike < atm.atm_strike THEN 
            -CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT)
        ELSE 
            CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT)
    END AS call_moneyness_code,
    CASE
        WHEN ng.strike = atm.atm_strike THEN 0
        WHEN ng.strike > atm.atm_strike THEN 
            -CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT)
        ELSE 
            CAST(FLOOR(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT)
    END AS put_moneyness_code,
    ng.ce_symbol,
    ng.ce_open,
    ng.ce_high,
    ng.ce_low,
    ng.ce_close,
    ng.ce_volume,
    ng.ce_oi,
    ng.ce_coi,
    ng.ce_iv,
    ng.ce_delta,
    ng.ce_gamma,
    ng.ce_theta,
    ng.ce_vega,
    ng.ce_rho,
    ng.pe_symbol,
    ng.pe_open,
    ng.pe_high,
    ng.pe_low,
    ng.pe_close,
    ng.pe_volume,
    ng.pe_oi,
    ng.pe_coi,
    ng.pe_iv,
    ng.pe_delta,
    ng.pe_gamma,
    ng.pe_theta,
    ng.pe_vega,
    ng.pe_rho
FROM nifty_greeks ng
JOIN (
    SELECT DISTINCT
        a.trade_date,
        a.expiry_date,
        FIRST_VALUE(a.strike) OVER (
            PARTITION BY a.trade_date, a.expiry_date 
            ORDER BY ABS(a.strike - a.underlying_price), a.strike
        ) AS atm_strike
    FROM nifty_greeks a
) atm ON ng.trade_date = atm.trade_date AND ng.expiry_date = atm.expiry_date; 