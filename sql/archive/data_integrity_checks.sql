-- Data Integrity Checks for nifty_greeks and nifty_option_chain
-- Run these checks before updating the materialized view

-- 1. Row Count Comparison
-- Compare total row counts between tables
SELECT 
    'nifty_greeks' AS table_name,
    COUNT(*) AS row_count 
FROM nifty_greeks

UNION ALL

SELECT 
    'nifty_option_chain' AS table_name,
    COUNT(*) AS row_count 
FROM nifty_option_chain;

-- 2. Date Range Checks
-- Verify that both tables cover the same date ranges
SELECT
    'nifty_greeks' AS table_name,
    MIN(trade_date) AS min_trade_date,
    MAX(trade_date) AS max_trade_date,
    COUNT(DISTINCT trade_date) AS unique_trade_dates,
    MIN(expiry_date) AS min_expiry_date,
    MAX(expiry_date) AS max_expiry_date,
    COUNT(DISTINCT expiry_date) AS unique_expiry_dates
FROM nifty_greeks

UNION ALL

SELECT
    'nifty_option_chain' AS table_name,
    MIN(trade_date) AS min_trade_date,
    MAX(trade_date) AS max_trade_date,
    COUNT(DISTINCT trade_date) AS unique_trade_dates,
    MIN(expiry_date) AS min_expiry_date,
    MAX(expiry_date) AS max_expiry_date,
    COUNT(DISTINCT expiry_date) AS unique_expiry_dates
FROM nifty_option_chain;

-- 3. Duplicate Check in nifty_option_chain
-- Check for duplicate rows based on key columns
SELECT 
    trade_date, expiry_date, strike, 
    COUNT(*) AS row_count
FROM nifty_option_chain
GROUP BY trade_date, expiry_date, strike
HAVING COUNT(*) > 1
LIMIT 10;

-- 4. Consistency Check for ATM Strike
-- Verify that ATM strikes are consistently determined
SELECT 
    trade_date, expiry_date, 
    COUNT(DISTINCT atm_strike) AS unique_atm_strikes,
    MIN(atm_strike) AS min_atm_strike,
    MAX(atm_strike) AS max_atm_strike
FROM nifty_option_chain
GROUP BY trade_date, expiry_date
HAVING COUNT(DISTINCT atm_strike) > 1
LIMIT 10;

-- 5. Missing Data Check
-- Check if any date/expiry pairs in nifty_greeks are missing from nifty_option_chain
SELECT DISTINCT 
    ng.trade_date, ng.expiry_date
FROM nifty_greeks ng
LEFT JOIN nifty_option_chain oc ON 
    ng.trade_date = oc.trade_date AND 
    ng.expiry_date = oc.expiry_date
WHERE oc.trade_date IS NULL
LIMIT 10;

-- 6. Strike Range Check
-- Compare strike range coverage between tables
SELECT
    'nifty_greeks' AS table_name,
    MIN(strike) AS min_strike,
    MAX(strike) AS max_strike,
    COUNT(DISTINCT strike) AS unique_strikes
FROM nifty_greeks

UNION ALL

SELECT
    'nifty_option_chain' AS table_name,
    MIN(strike) AS min_strike,
    MAX(strike) AS max_strike,
    COUNT(DISTINCT strike) AS unique_strikes
FROM nifty_option_chain; 