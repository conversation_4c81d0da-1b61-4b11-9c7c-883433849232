DROP VIEW IF EXISTS nifty_option_chain;

CREATE VIEW nifty_option_chain AS
WITH date_expiry_atm AS (
    SELECT DISTINCT
        trade_date,
        expiry_date,
        CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END AS strike_increment,
        CAST(ROUND(underlying_price / (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END)) * 
             (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT) AS atm_strike
    FROM nifty_greeks
)
SELECT
    ng.trade_date,
    ng.expiry_date,
    ng.underlying_price,
    dea.atm_strike,
    'ROUND' AS selection_method,
    ng.underlying_price AS synthetic_future,
    CASE
        WHEN ng.strike = dea.atm_strike THEN 'ATM'
        WHEN ng.strike < dea.atm_strike THEN 
            'ITM' || CAST(FLOOR(ABS(ng.strike - dea.atm_strike) / dea.strike_increment) AS INT)
        ELSE 
            'OTM' || CAST(FLOOR(ABS(ng.strike - dea.atm_strike) / dea.strike_increment) AS INT)
    END AS call_moneyness,
    CASE
        WHEN ng.strike = dea.atm_strike THEN 'ATM'
        WHEN ng.strike > dea.atm_strike THEN 
            'ITM' || CAST(FLOOR(ABS(ng.strike - dea.atm_strike) / dea.strike_increment) AS INT)
        ELSE 
            'OTM' || CAST(FLOOR(ABS(ng.strike - dea.atm_strike) / dea.strike_increment) AS INT)
    END AS put_moneyness,
    CAST(FLOOR(ABS(ng.strike - dea.atm_strike) / dea.strike_increment) AS INT) AS strike_distance,
    ng.strike,
    0.0 AS pcr_oi,
    0.0 AS pcr_volume,
    ng.ce_symbol,
    ng.ce_open,
    ng.ce_high,
    ng.ce_low,
    ng.ce_close,
    ng.ce_volume,
    ng.ce_oi,
    ng.ce_coi,
    ng.ce_iv,
    ng.ce_delta,
    ng.ce_gamma,
    ng.ce_theta,
    ng.ce_vega,
    ng.ce_rho,
    ng.pe_symbol,
    ng.pe_open,
    ng.pe_high,
    ng.pe_low,
    ng.pe_close,
    ng.pe_volume,
    ng.pe_oi,
    ng.pe_coi,
    ng.pe_iv,
    ng.pe_delta,
    ng.pe_gamma,
    ng.pe_theta,
    ng.pe_vega,
    ng.pe_rho
FROM nifty_greeks ng
JOIN date_expiry_atm dea ON ng.trade_date = dea.trade_date 
                        AND ng.expiry_date = dea.expiry_date; 