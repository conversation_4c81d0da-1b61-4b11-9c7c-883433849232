DROP FUNCTION IF EXISTS trading_days_between;

CREATE FUNCTION trading_days_between(start_date DATE, end_date DATE) RETURNS INTEGER
{
    -- Base case: if start_date > end_date, return 0
    IF (start_date > end_date) THEN
        RETURN 0;
    END IF;
    
    -- Base case: if dates are the same, return 0
    IF (start_date = end_date) THEN
        RETURN 0;
    END IF;
    
    DECLARE total_days INTEGER = DATEDIFF(DAY, start_date, end_date);
    DECLARE weekend_days INTEGER = 0;
    DECLARE temp_date DATE = start_date;
    DECLARE day_of_week INTEGER;
    
    -- Count weekend days
    WHILE (temp_date < end_date) DO
        day_of_week = EXTRACT(DOW FROM temp_date);
        
        -- Saturday = 6, Sunday = 0
        IF (day_of_week = 0 OR day_of_week = 6) THEN
            weekend_days = weekend_days + 1;
        END IF;
        
        temp_date = DATEADD(DAY, 1, temp_date);
    END WHILE;
    
    -- Special case for next day
    IF (total_days = 1) THEN
        day_of_week = EXTRACT(DOW FROM start_date);
        IF (day_of_week = 5) THEN  -- Friday to Saturday
            RETURN 0;
        ELSIF (day_of_week = 6) THEN  -- Saturday to Sunday
            RETURN 0;
        ELSE
            RETURN 1;
        END IF;
    END IF;
    
    RETURN total_days - weekend_days;
}; 