SELECT 
    'Monday May 13, 2024 is a holiday, Friday May 10 to Tuesday May 14 should be 1 DTE' AS scenario,
    DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14') AS calendar_days,
    EXTRACT(DOW FROM DATE '2024-05-10') AS friday_dow,
    EXTRACT(DOW FROM DATE '2024-05-14') AS tuesday_dow,
    DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14')
    - (FLOOR((EXTRACT(DOW FROM DATE '2024-05-10') + DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14')) / 7)
        + FLOOR((EXTRACT(DOW FROM DATE '2024-05-10') + DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14') + 1) / 7)) AS weekday_count,
    (SELECT COUNT(*) FROM nse_holidays
        WHERE holiday_date > DATE '2024-05-10' 
        AND holiday_date <= DATE '2024-05-14'
        AND EXTRACT(DOW FROM holiday_date) NOT IN (0, 6)) AS holiday_count,
    DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14')
    - (FLOOR((EXTRACT(DOW FROM DATE '2024-05-10') + DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14')) / 7)
        + FLOOR((EXTRACT(DOW FROM DATE '2024-05-10') + DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14') + 1) / 7))
    - (SELECT COUNT(*) FROM nse_holidays
        WHERE holiday_date > DATE '2024-05-10' 
        AND holiday_date <= DATE '2024-05-14'
        AND EXTRACT(DOW FROM holiday_date) NOT IN (0, 6)) AS dte;

SELECT 
    'Tuesday May 7, 2024 is a holiday, Friday May 3 to Thursday May 9 should be 3 DTE' AS scenario,
    DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09') AS calendar_days,
    EXTRACT(DOW FROM DATE '2024-05-03') AS friday_dow,
    EXTRACT(DOW FROM DATE '2024-05-09') AS thursday_dow,
    DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09')
    - (FLOOR((EXTRACT(DOW FROM DATE '2024-05-03') + DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09')) / 7)
        + FLOOR((EXTRACT(DOW FROM DATE '2024-05-03') + DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09') + 1) / 7)) AS weekday_count,
    (SELECT COUNT(*) FROM nse_holidays
        WHERE holiday_date > DATE '2024-05-03' 
        AND holiday_date <= DATE '2024-05-09'
        AND EXTRACT(DOW FROM holiday_date) NOT IN (0, 6)) AS holiday_count,
    DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09')
    - (FLOOR((EXTRACT(DOW FROM DATE '2024-05-03') + DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09')) / 7)
        + FLOOR((EXTRACT(DOW FROM DATE '2024-05-03') + DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09') + 1) / 7))
    - (SELECT COUNT(*) FROM nse_holidays
        WHERE holiday_date > DATE '2024-05-03' 
        AND holiday_date <= DATE '2024-05-09'
        AND EXTRACT(DOW FROM holiday_date) NOT IN (0, 6)) AS dte; 