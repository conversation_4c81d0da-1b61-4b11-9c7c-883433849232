CREATE VIEW nifty_option_chain AS
WITH closest_strikes AS (
    SELECT 
        a.trade_date,
        a.expiry_date,
        a.underlying_price,
        MIN(ABS(a.strike - a.underlying_price)) AS min_distance,
        CASE WHEN a.underlying_price >= 10000 THEN 100 ELSE 50 END AS strike_increment
    FROM nifty_greeks a
    GROUP BY a.trade_date, a.expiry_date, a.underlying_price
),
atm_strikes AS (
    SELECT DISTINCT
        ng.trade_date,
        ng.expiry_date,
        cs.strike_increment,
        FIRST_VALUE(ng.strike) OVER (
            PARTITION BY ng.trade_date, ng.expiry_date 
            ORDER BY ABS(ng.strike - ng.underlying_price), ng.strike
        ) AS atm_strike
    FROM nifty_greeks ng
    JOIN closest_strikes cs ON ng.trade_date = cs.trade_date 
                            AND ng.expiry_date = cs.expiry_date
) 