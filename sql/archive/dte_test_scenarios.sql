-- Test scenario 1: When Monday is expiry, Friday should be 1 DTE
SELECT 
    'Monday is expiry, Friday is 1 DTE' AS scenario,
    DATE '2024-05-03' AS friday_date,
    DATE '2024-05-06' AS monday_expiry,
    EXTRACT(DOW FROM DATE '2024-05-03') AS day_of_week_friday,
    EXTRACT(DOW FROM DATE '2024-05-06') AS day_of_week_monday,
    CASE
        WHEN DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-06') = 0 
            THEN 0
        ELSE
            (SELECT COUNT(*) 
             FROM (
                 SELECT DATEADD('day', seq, DATE '2024-05-03') AS trading_day
                 FROM (
                     SELECT seq 
                     FROM (
                         SELECT ROW_NUMBER() OVER () - 1 AS seq 
                         FROM nifty_greeks 
                         LIMIT DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-06')
                     ) AS seq_table
                 ) AS date_series
             ) AS all_days
             WHERE EXTRACT(DOW FROM trading_day) NOT IN (0, 6)
                AND NOT EXISTS (
                    SELECT 1 FROM nse_holidays 
                    WHERE holiday_date = trading_day
                )
            )
    END AS correct_dte;

-- Test scenario 2: When Tuesday is expiry, but Saturday, Sunday & Monday are holidays, Friday is 1 DTE
SELECT 
    'Tuesday is expiry, but Sat/Sun/Mon are holidays, Friday is 1 DTE' AS scenario,
    DATE '2024-05-10' AS friday_date,
    DATE '2024-05-14' AS tuesday_expiry,
    -- Assume 2024-05-11 (Sat), 2024-05-12 (Sun), and 2024-05-13 (Mon) are holidays
    CASE
        WHEN DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14') = 0 
            THEN 0
        ELSE
            (SELECT COUNT(*) 
             FROM (
                 SELECT DATEADD('day', seq, DATE '2024-05-10') AS trading_day
                 FROM (
                     SELECT seq 
                     FROM (
                         SELECT ROW_NUMBER() OVER () - 1 AS seq 
                         FROM nifty_greeks 
                         LIMIT DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14')
                     ) AS seq_table
                 ) AS date_series
             ) AS all_days
             WHERE EXTRACT(DOW FROM trading_day) NOT IN (0, 6)
                AND trading_day != DATE '2024-05-13' -- Monday is holiday (manually excluding)
            )
    END AS correct_dte;

-- Test scenario 3: When Thursday is expiry, but Tuesday is holiday, Friday is 3 DTE
SELECT 
    'Thursday is expiry, but Tuesday is holiday, Friday is 3 DTE' AS scenario,
    DATE '2024-05-03' AS friday_date,
    DATE '2024-05-09' AS thursday_expiry,
    -- Assume 2024-05-07 (Tuesday) is a holiday
    CASE
        WHEN DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09') = 0 
            THEN 0
        ELSE
            (SELECT COUNT(*) 
             FROM (
                 SELECT DATEADD('day', seq, DATE '2024-05-03') AS trading_day
                 FROM (
                     SELECT seq 
                     FROM (
                         SELECT ROW_NUMBER() OVER () - 1 AS seq 
                         FROM nifty_greeks 
                         LIMIT DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09')
                     ) AS seq_table
                 ) AS date_series
             ) AS all_days
             WHERE EXTRACT(DOW FROM trading_day) NOT IN (0, 6)
                AND trading_day != DATE '2024-05-07' -- Tuesday is holiday (manually excluding)
            )
    END AS correct_dte; 