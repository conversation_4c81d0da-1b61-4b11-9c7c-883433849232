DROP TABLE IF EXISTS dte_test_dates;

CREATE TABLE dte_test_dates (
    trade_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    expected_dte INT NOT NULL,
    scenario TEXT NOT NULL
);

INSERT INTO dte_test_dates VALUES 
(DATE '2024-05-10', DATE '2024-05-14', 1, 'Friday to Tuesday with Monday holiday'),
(DATE '2024-05-03', DATE '2024-05-09', 3, 'Friday to Thursday with Tuesday holiday'),
(DATE '2025-04-04', DATE '2025-04-10', 3, 'Friday to Thursday with Tuesday holiday in 2025'),
(DATE '2024-05-03', DATE '2024-05-06', 1, 'Friday to Monday normal case'); 