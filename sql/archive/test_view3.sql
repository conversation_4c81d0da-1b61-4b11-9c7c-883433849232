SELECT 
    'Example 3: Friday to Thursday (2025-04-04 to 2025-04-10)' AS example, 
    DATE '2025-04-04' AS trade_date,
    DATE '2025-04-10' AS expiry_date,
    CASE
        WHEN DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10') = 0 THEN 0
        WHEN DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10') = 1 THEN 
            CASE WHEN EXTRACT(DOW FROM DATE '2025-04-04') IN (5, 6) THEN 0 ELSE 1 END
        ELSE
            DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10')
            - (FLOOR((EXTRACT(DOW FROM DATE '2025-04-04') + DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10')) / 7)
               + FLOOR((EXTRACT(DOW FROM DATE '2025-04-04') + DATEDIFF('day', DATE '2025-04-04', DATE '2025-04-10') + 1) / 7))
    END AS dte; 