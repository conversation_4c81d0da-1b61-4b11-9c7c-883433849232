-- Simple Data Integrity Checks (HeavyDB-compatible)

-- Count rows in nifty_greeks
SELECT COUNT(*) AS nifty_greeks_row_count FROM nifty_greeks;

-- Count rows in nifty_option_chain
SELECT COUNT(*) AS nifty_option_chain_row_count FROM nifty_option_chain;

-- Count unique date pairs in nifty_greeks
SELECT COUNT(*) AS unique_date_pairs_count 
FROM (
    SELECT DISTINCT trade_date, expiry_date 
    FROM nifty_greeks
) AS temp;

-- Count unique date pairs in nifty_option_chain
SELECT COUNT(*) AS unique_date_pairs_count 
FROM (
    SELECT DISTINCT trade_date, expiry_date 
    FROM nifty_option_chain
) AS temp;

-- Count unique strikes in nifty_greeks
SELECT COUNT(DISTINCT strike) AS unique_strikes_count 
FROM nifty_greeks;

-- Count unique strikes in nifty_option_chain
SELECT COUNT(DISTINCT strike) AS unique_strikes_count 
FROM nifty_option_chain;

-- Sample data from nifty_option_chain
SELECT 
    trade_date, 
    expiry_date, 
    strike, 
    atm_strike, 
    underlying_price
FROM nifty_option_chain
LIMIT 5; 