SELECT 
    'Test special dates with known holidays' AS test,
    trade_date,
    expiry_date,
    dte,
    CASE
        WHEN trade_date = DATE '2024-05-03' AND expiry_date = DATE '2024-05-09' AND dte = 3 THEN 'PASS'
        WHEN trade_date = DATE '2024-05-10' AND expiry_date = DATE '2024-05-14' AND dte = 1 THEN 'PASS'
        ELSE 'FAIL'
    END AS test_result
FROM nifty_option_chain
WHERE (trade_date = DATE '2024-05-03' AND expiry_date = DATE '2024-05-09') OR
      (trade_date = DATE '2024-05-10' AND expiry_date = DATE '2024-05-14')
LIMIT 2; 