DROP TABLE IF EXISTS dte_test_dates;

CREATE TABLE dte_test_dates (
    trade_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    expected_dte INT NOT NULL,
    scenario TEXT NOT NULL
);

INSERT INTO dte_test_dates VALUES 
(DATE '2024-05-10', DATE '2024-05-14', 1, 'Friday to Tuesday with Monday holiday'),
(DATE '2024-05-03', DATE '2024-05-09', 3, 'Friday to Thursday with Tuesday holiday'),
(DATE '2025-04-04', DATE '2025-04-10', 3, 'Friday to Thursday with Tuesday holiday in 2025'),
(DATE '2024-05-03', DATE '2024-05-06', 1, 'Friday to Monday normal case');

-- Create a new view to test the DTE calculation
DROP VIEW IF EXISTS test_option_chain;

-- Create a simplified view for testing
CREATE VIEW test_option_chain AS
WITH test_dates AS (
    SELECT 
        d.trade_date,
        d.expiry_date,
        19000 AS underlying_price,
        19000 AS strike,
        d.expected_dte,
        d.scenario
    FROM dte_test_dates d
),
holiday_count AS (
    SELECT 
        h1.start_date,
        h1.end_date,
        COUNT(h2.holiday_date) AS holiday_count
    FROM (
        SELECT DISTINCT 
            d.trade_date AS start_date, 
            d.expiry_date AS end_date
        FROM test_dates d
    ) h1
    LEFT JOIN nse_holidays h2 ON 
        h2.holiday_date > h1.start_date AND 
        h2.holiday_date <= h1.end_date AND
        EXTRACT(DOW FROM h2.holiday_date) NOT IN (0, 6)
    GROUP BY h1.start_date, h1.end_date
)
SELECT
    d.trade_date,
    d.expiry_date,
    d.underlying_price,
    d.strike,
    d.scenario,
    d.expected_dte,
    -- The actual DTE calculation
    CASE
        WHEN DATEDIFF('day', d.trade_date, d.expiry_date) = 0 
            THEN 0
        ELSE
            DATEDIFF('day', d.trade_date, d.expiry_date)
            - (FLOOR((EXTRACT(DOW FROM d.trade_date) + DATEDIFF('day', d.trade_date, d.expiry_date)) / 7)
               + FLOOR((EXTRACT(DOW FROM d.trade_date) + DATEDIFF('day', d.trade_date, d.expiry_date) + 1) / 7))
            - COALESCE((SELECT holiday_count 
                        FROM holiday_count 
                        WHERE start_date = d.trade_date 
                        AND end_date = d.expiry_date), 0)
    END AS calculated_dte,
    -- Number of weekend days
    (FLOOR((EXTRACT(DOW FROM d.trade_date) + DATEDIFF('day', d.trade_date, d.expiry_date)) / 7)
     + FLOOR((EXTRACT(DOW FROM d.trade_date) + DATEDIFF('day', d.trade_date, d.expiry_date) + 1) / 7)) AS weekend_days,
    -- Number of holidays (excluding weekends) between dates
    COALESCE((SELECT holiday_count 
              FROM holiday_count 
              WHERE start_date = d.trade_date 
              AND end_date = d.expiry_date), 0) AS holiday_count,
    -- Test result
    CASE
        WHEN CASE
                WHEN DATEDIFF('day', d.trade_date, d.expiry_date) = 0 
                    THEN 0
                ELSE
                    DATEDIFF('day', d.trade_date, d.expiry_date)
                    - (FLOOR((EXTRACT(DOW FROM d.trade_date) + DATEDIFF('day', d.trade_date, d.expiry_date)) / 7)
                       + FLOOR((EXTRACT(DOW FROM d.trade_date) + DATEDIFF('day', d.trade_date, d.expiry_date) + 1) / 7))
                    - COALESCE((SELECT holiday_count 
                                FROM holiday_count 
                                WHERE start_date = d.trade_date 
                                AND end_date = d.expiry_date), 0)
            END = d.expected_dte THEN 'PASS'
        ELSE 'FAIL'
    END AS test_result
FROM test_dates d; 