DROP VIEW IF EXISTS nifty_sample;

CREATE VIEW nifty_sample AS
WITH base_data AS (
    SELECT DISTINCT
        trade_date, 
        expiry_date,
        underlying_price,
        CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END AS strike_increment,
        CAST(ROUND(underlying_price / (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END)) * 
             (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT) AS atm_strike
    FROM nifty_greeks
    WHERE trade_date >= '2023-01-01' AND trade_date <= '2023-01-31'
    LIMIT 10
),
strike_data AS (
    SELECT 
        ng.trade_date,
        ng.expiry_date,
        ng.underlying_price,
        ng.strike,
        bd.atm_strike,
        bd.strike_increment,
        CASE
            WHEN ng.strike = bd.atm_strike THEN 'ATM'
            WHEN ng.strike < bd.atm_strike THEN 
                'ITM' || CAST(FLOOR(ABS(ng.strike - bd.atm_strike) / bd.strike_increment) AS INT)
            ELSE 
                'OTM' || CAST(FLOOR(ABS(ng.strike - bd.atm_strike) / bd.strike_increment) AS INT)
        END AS call_moneyness,
        CASE
            WHEN ng.strike = bd.atm_strike THEN 'ATM'
            WHEN ng.strike > bd.atm_strike THEN 
                'ITM' || CAST(FLOOR(ABS(ng.strike - bd.atm_strike) / bd.strike_increment) AS INT)
            ELSE 
                'OTM' || CAST(FLOOR(ABS(ng.strike - bd.atm_strike) / bd.strike_increment) AS INT)
        END AS put_moneyness,
        ng.ce_close,
        ng.pe_close
    FROM nifty_greeks ng
    JOIN base_data bd ON ng.trade_date = bd.trade_date AND ng.expiry_date = bd.expiry_date
)
SELECT * FROM strike_data
LIMIT 200; 