DROP VIEW IF EXISTS nifty_option_chain;

CREATE VIEW nifty_option_chain AS
WITH atm_strikes AS (
    SELECT
        trade_date,
        expiry_date,
        underlying_price,
        CAST(ROUND(underlying_price / (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END)) * 
             (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT) AS atm_strike,
        CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END AS strike_increment,
        'ROUND' AS selection_method
    FROM (
        SELECT DISTINCT trade_date, expiry_date, underlying_price
        FROM nifty_greeks
    ) base
)
SELECT
    ng.trade_date,
    ng.expiry_date,
    ng.underlying_price,
    atm.atm_strike,
    atm.selection_method,
    ng.underlying_price AS synthetic_future,
    ng.strike,
    CAST(ABS(ng.strike - atm.atm_strike) / atm.strike_increment AS INT) AS strike_distance,
    CASE
        WHEN ng.strike = atm.atm_strike THEN 0
        WHEN ng.strike > atm.atm_strike THEN CAST((ng.strike - atm.atm_strike) / atm.strike_increment AS INT)
        ELSE -CAST((atm.atm_strike - ng.strike) / atm.strike_increment AS INT)
    END AS call_moneyness_code,
    CASE
        WHEN ng.strike = atm.atm_strike THEN 0
        WHEN ng.strike < atm.atm_strike THEN CAST((atm.atm_strike - ng.strike) / atm.strike_increment AS INT)
        ELSE -CAST((ng.strike - atm.atm_strike) / atm.strike_increment AS INT)
    END AS put_moneyness_code,
    ng.ce_close,
    ng.ce_volume,
    ng.ce_oi,
    ng.ce_iv,
    ng.ce_delta,
    ng.ce_gamma,
    ng.ce_theta,
    ng.ce_vega,
    ng.pe_close,
    ng.pe_volume,
    ng.pe_oi,
    ng.pe_iv,
    ng.pe_delta,
    ng.pe_gamma,
    ng.pe_theta,
    ng.pe_vega
FROM nifty_greeks ng
JOIN atm_strikes atm ON ng.trade_date = atm.trade_date AND ng.expiry_date = atm.expiry_date; 