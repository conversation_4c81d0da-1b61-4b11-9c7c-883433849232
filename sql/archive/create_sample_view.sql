DROP VIEW IF EXISTS nifty_sample;

CREATE VIEW nifty_sample AS
SELECT
    trade_date,
    expiry_date,
    underlying_price,
    CAST(ROUND(underlying_price / (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END)) * 
         (CASE WHEN underlying_price >= 10000 THEN 100 ELSE 50 END) AS INT) AS atm_strike,
    strike,
    ce_symbol,
    ce_close,
    pe_symbol,
    pe_close
FROM nifty_greeks
WHERE trade_date = '2023-01-18'
  AND expiry_date = '2023-01-25'
LIMIT 100; 