DROP VIEW IF EXISTS nifty_option_chain;

CREATE VIEW nifty_option_chain AS
WITH atm_selection AS (
    SELECT DISTINCT
        a.trade_date,
        a.expiry_date,
        FIRST_VALUE(a.strike) OVER (
            PARTITION BY a.trade_date, a.expiry_date 
            ORDER BY ABS(a.strike - a.underlying_price), a.strike
        ) AS atm_strike,
        a.underlying_price
    FROM nifty_greeks a
),
holiday_count AS (
    SELECT 
        h1.start_date,
        h1.end_date,
        COUNT(h2.holiday_date) AS holiday_count
    FROM (
        SELECT DISTINCT 
            ng.trade_date AS start_date, 
            ng.expiry_date AS end_date
        FROM nifty_greeks ng
    ) h1
    LEFT JOIN nse_holidays h2 ON 
        h2.holiday_date > h1.start_date AND 
        h2.holiday_date <= h1.end_date AND
        EXTRACT(DOW FROM h2.holiday_date) NOT IN (0, 6)
    GROUP BY h1.start_date, h1.end_date
)
SELECT 
    ng.trade_date,
    ng.expiry_date,
    ng.underlying_price,
    atm.atm_strike,
    'ATM' AS selection_method,
    ng.strike,
    -- Days to Expiry calculation accounting for weekends and holidays
    CASE
        WHEN DATEDIFF('day', ng.trade_date, ng.expiry_date) = 0 
            THEN 0
        ELSE
            DATEDIFF('day', ng.trade_date, ng.expiry_date)
            -- Subtract weekends
            - (FLOOR((EXTRACT(DOW FROM ng.trade_date) + DATEDIFF('day', ng.trade_date, ng.expiry_date)) / 7)
               + FLOOR((EXTRACT(DOW FROM ng.trade_date) + DATEDIFF('day', ng.trade_date, ng.expiry_date) + 1) / 7))
            -- Subtract holidays (excluding weekends)
            - COALESCE((SELECT holiday_count 
                        FROM holiday_count 
                        WHERE start_date = ng.trade_date 
                        AND end_date = ng.expiry_date), 0)
    END AS dte,
    -- Strike classification for call options
    CASE 
        WHEN ng.strike = atm.atm_strike THEN 'ATM'
        WHEN ng.strike < atm.atm_strike THEN 
            'ITM' || CAST(CEILING(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS VARCHAR)
        ELSE 
            'OTM' || CAST(CEILING(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS VARCHAR)
    END AS call_strike_type,
    -- Strike classification for put options
    CASE
        WHEN ng.strike = atm.atm_strike THEN 'ATM'
        WHEN ng.strike > atm.atm_strike THEN 
            'ITM' || CAST(CEILING(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS VARCHAR)
        ELSE 
            'OTM' || CAST(CEILING(ABS(ng.strike - atm.atm_strike) / 
            CASE WHEN ng.underlying_price >= 10000 THEN 100 ELSE 50 END) AS VARCHAR)
    END AS put_strike_type,
    -- Call option data
    ng.ce_symbol,
    ng.ce_open,
    ng.ce_high,
    ng.ce_low,
    ng.ce_close,
    ng.ce_volume,
    ng.ce_oi,
    ng.ce_coi,
    ng.ce_iv,
    ng.ce_delta,
    ng.ce_gamma,
    ng.ce_theta,
    ng.ce_vega,
    ng.ce_rho,
    -- Put option data
    ng.pe_symbol,
    ng.pe_open,
    ng.pe_high,
    ng.pe_low,
    ng.pe_close,
    ng.pe_volume,
    ng.pe_oi,
    ng.pe_coi,
    ng.pe_iv,
    ng.pe_delta,
    ng.pe_gamma,
    ng.pe_theta,
    ng.pe_vega,
    ng.pe_rho
FROM nifty_greeks ng
JOIN atm_selection atm 
ON ng.trade_date = atm.trade_date AND ng.expiry_date = atm.expiry_date; 