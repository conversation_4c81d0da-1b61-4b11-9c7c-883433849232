SELECT 
    'Test 1: Friday May 10, 2024 to Tuesday May 14, 2024 (Monday is holiday)' AS test,
    DATEDIFF('day', DATE '2024-05-10', DATE '2024-05-14') AS calendar_days,
    (SELECT COUNT(*) FROM nse_holidays 
     WHERE holiday_date > DATE '2024-05-10' 
     AND holiday_date <= DATE '2024-05-14'
     AND EXTRACT(DOW FROM holiday_date) NOT IN (0, 6)) AS holidays_count,
    (SELECT dte FROM nifty_option_chain 
     WHERE trade_date = DATE '2024-05-10' AND expiry_date = DATE '2024-05-14'
     LIMIT 1) AS computed_dte,
    (CASE WHEN (SELECT dte FROM nifty_option_chain 
                WHERE trade_date = DATE '2024-05-10' AND expiry_date = DATE '2024-05-14' 
                LIMIT 1) = 1 
          THEN 'PASS' ELSE 'FAIL' END) AS test_result;

SELECT 
    'Test 2: Friday May 3, 2024 to Thursday May 9, 2024 (Tuesday is holiday)' AS test,
    DATEDIFF('day', DATE '2024-05-03', DATE '2024-05-09') AS calendar_days,
    (SELECT COUNT(*) FROM nse_holidays 
     WHERE holiday_date > DATE '2024-05-03' 
     AND holiday_date <= DATE '2024-05-09'
     AND EXTRACT(DOW FROM holiday_date) NOT IN (0, 6)) AS holidays_count,
    (SELECT dte FROM nifty_option_chain 
     WHERE trade_date = DATE '2024-05-03' AND expiry_date = DATE '2024-05-09'
     LIMIT 1) AS computed_dte,
    (CASE WHEN (SELECT dte FROM nifty_option_chain 
                WHERE trade_date = DATE '2024-05-03' AND expiry_date = DATE '2024-05-09' 
                LIMIT 1) = 3 
          THEN 'PASS' ELSE 'FAIL' END) AS test_result; 