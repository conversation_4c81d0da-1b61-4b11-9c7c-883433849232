"""
Job Manager for backtest job tracking and management
"""
from typing import Dict, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import threading
import logging

from api.v2.contracts import JobStatus, StrategyType, TestMode

logger = logging.getLogger(__name__)

@dataclass
class BacktestJob:
    """Represents a backtest job"""
    job_id: str
    strategy_type: StrategyType
    test_mode: TestMode
    files: Dict[str, str]
    status: JobStatus = JobStatus.SUBMITTED
    progress: float = 0.0
    message: str = "Job submitted"
    submitted_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    custom_date_range: Optional[Dict] = None
    
    def update_progress(self, progress: float, message: str):
        """Update job progress"""
        self.progress = min(100.0, max(0.0, progress))
        self.message = message
        self.updated_at = datetime.now()
        
        # Update status based on progress
        if self.progress >= 100:
            self.status = JobStatus.COMPLETED
            self.completed_at = datetime.now()
        elif self.progress > 0:
            self.status = JobStatus.PROCESSING
    
    def set_error(self, error: str):
        """Set job error"""
        self.status = JobStatus.FAILED
        self.error = error
        self.updated_at = datetime.now()
    
    def set_results(self, results: Dict[str, Any]):
        """Set job results"""
        self.results = results
        self.status = JobStatus.COMPLETED
        self.progress = 100.0
        self.completed_at = datetime.now()
        self.updated_at = datetime.now()

class JobManager:
    """Manages backtest jobs"""
    
    def __init__(self):
        self._jobs: Dict[str, BacktestJob] = {}
        self._lock = threading.Lock()
        self._progress_callbacks = {}
        
    def create_job(
        self,
        job_id: str,
        strategy_type: StrategyType,
        test_mode: TestMode,
        files: Dict[str, str],
        custom_date_range: Optional[Dict] = None
    ) -> BacktestJob:
        """Create a new job"""
        with self._lock:
            if job_id in self._jobs:
                raise ValueError(f"Job {job_id} already exists")
            
            job = BacktestJob(
                job_id=job_id,
                strategy_type=strategy_type,
                test_mode=test_mode,
                files=files,
                custom_date_range=custom_date_range
            )
            
            self._jobs[job_id] = job
            logger.info(f"Created job {job_id} for {strategy_type} strategy")
            
            return job
    
    def get_job(self, job_id: str) -> Optional[BacktestJob]:
        """Get a job by ID"""
        with self._lock:
            return self._jobs.get(job_id)
    
    def update_job_progress(self, job_id: str, progress: float, message: str):
        """Update job progress"""
        with self._lock:
            job = self._jobs.get(job_id)
            if job:
                job.update_progress(progress, message)
                logger.info(f"Job {job_id}: {progress:.1f}% - {message}")
                
                # Trigger callback if registered
                if job_id in self._progress_callbacks:
                    callback = self._progress_callbacks[job_id]
                    # Run callback in separate thread to avoid blocking
                    threading.Thread(
                        target=callback,
                        args=(job_id, progress, message)
                    ).start()
                
                # Also send WebSocket update if available
                try:
                    from api.v2.websocket import get_connection_manager
                    import asyncio
                    
                    # Get or create event loop for thread
                    try:
                        loop = asyncio.get_event_loop()
                    except RuntimeError:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    
                    # Send update through WebSocket
                    manager = get_connection_manager()
                    loop.create_task(
                        manager.send_progress_update(job_id, progress, message)
                    )
                except Exception as e:
                    logger.debug(f"WebSocket update failed: {e}")
    
    def set_job_error(self, job_id: str, error: str):
        """Set job error"""
        with self._lock:
            job = self._jobs.get(job_id)
            if job:
                job.set_error(error)
                logger.error(f"Job {job_id} failed: {error}")
    
    def set_job_results(self, job_id: str, results: Dict[str, Any]):
        """Set job results"""
        with self._lock:
            job = self._jobs.get(job_id)
            if job:
                job.set_results(results)
                logger.info(f"Job {job_id} completed successfully")
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel a job"""
        with self._lock:
            job = self._jobs.get(job_id)
            if job and job.status in [JobStatus.SUBMITTED, JobStatus.PROCESSING]:
                job.status = JobStatus.CANCELLED
                job.updated_at = datetime.now()
                job.message = "Job cancelled by user"
                logger.info(f"Job {job_id} cancelled")
                return True
            return False
    
    def register_progress_callback(self, job_id: str, callback):
        """Register a callback for progress updates"""
        with self._lock:
            self._progress_callbacks[job_id] = callback
    
    def unregister_progress_callback(self, job_id: str):
        """Unregister progress callback"""
        with self._lock:
            self._progress_callbacks.pop(job_id, None)
    
    def get_active_jobs(self) -> Dict[str, BacktestJob]:
        """Get all active (non-completed) jobs"""
        with self._lock:
            return {
                job_id: job
                for job_id, job in self._jobs.items()
                if job.status in [JobStatus.SUBMITTED, JobStatus.PROCESSING]
            }
    
    def cleanup_old_jobs(self, hours: int = 24):
        """Remove completed jobs older than specified hours"""
        with self._lock:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            jobs_to_remove = []
            
            for job_id, job in self._jobs.items():
                if (job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED] 
                    and job.updated_at < cutoff_time):
                    jobs_to_remove.append(job_id)
            
            for job_id in jobs_to_remove:
                del self._jobs[job_id]
                logger.info(f"Cleaned up old job {job_id}")

# Global job manager instance
_job_manager_instance = None

def get_job_manager() -> JobManager:
    """Get singleton job manager instance"""
    global _job_manager_instance
    if _job_manager_instance is None:
        _job_manager_instance = JobManager()
    return _job_manager_instance