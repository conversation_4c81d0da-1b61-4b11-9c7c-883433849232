"""SQL builder for multi-leg strategy (UNION ALL of entry/exit SQLs)."""
from __future__ import annotations
from datetime import date
from typing import List

from models import StrategyModel
from .leg_sql import build_entry_sql

__all__ = ["build_strategy_sql"]


def build_strategy_sql(strategy: StrategyModel, trade_date: date) -> str:
    """Return UNION ALL SQL for all legs in a strategy (entry snapshots)."""
    sqls: List[str] = []
    for idx, leg in enumerate(strategy.legs):
        sql = build_entry_sql(leg, trade_date, alias=f"en{idx}")
        # Add a constant leg_id column for traceability
        sql = sql.rstrip("\n ")
        sql = sql.replace("SELECT *", f"SELECT *, '{leg.leg_id}' AS leg_id")
        sqls.append(sql)
    return "\nUNION ALL\n".join(sqls) 