"""SQL builders for entry/exit snapshot of a single leg."""
from __future__ import annotations
from datetime import date
from typing import Any

from models import LegModel
from .resolver import TableMapper, ExpiryResolver, StrikeResolver

__all__ = ["build_entry_sql", "build_exit_sql"]


def build_entry_sql(leg: LegModel, trade_date: date, alias: str = "en") -> str:
    """Return SQL for entry snapshot for *leg* on *trade_date* (first tick >= entry_time)."""
    table = TableMapper.resolve(leg.index)
    expiry_pred = ExpiryResolver.resolve(leg.expiry_rule, trade_date, alias)
    strike_pred = StrikeResolver.resolve(leg.strike_rule, alias)
    entry_time = leg.entry_window.time_str.replace(":", "")
    sql = f"""
    SELECT * FROM {table} {alias}
    WHERE {alias}.trade_date = DATE '{trade_date.isoformat()}'
      AND {expiry_pred}
      AND {strike_pred}
      AND {alias}.trade_time >= '{entry_time}'
    ORDER BY {alias}.trade_time ASC
    LIMIT 1
    """
    return sql.strip()


def build_exit_sql(leg: LegModel, trade_date: date, alias: str = "ex") -> str:
    """Return SQL for exit snapshot for *leg* on *trade_date* (last tick <= exit_time)."""
    table = TableMapper.resolve(leg.index)
    expiry_pred = ExpiryResolver.resolve(leg.expiry_rule, trade_date, alias)
    strike_pred = StrikeResolver.resolve(leg.strike_rule, alias)
    exit_time = leg.exit_window.time_str.replace(":", "")
    sql = f"""
    SELECT * FROM {table} {alias}
    WHERE {alias}.trade_date = DATE '{trade_date.isoformat()}'
      AND {expiry_pred}
      AND {strike_pred}
      AND {alias}.trade_time <= '{exit_time}'
    ORDER BY {alias}.trade_time DESC
    LIMIT 1
    """
    return sql.strip() 