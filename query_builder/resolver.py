"""Helpers that resolve high-level model enums into HeavyDB SQL snippets."""
from __future__ import annotations

from datetime import date, timedelta
from typing import Dict

from models import ExpiryRule, StrikeRule

__all__ = [
    "TableMapper",
    "ExpiryResolver",
    "StrikeResolver",
]


class TableMapper:
    """Map index symbol to HeavyDB option-chain view name."""

    MAPPING: Dict[str, str] = {
        "NIFTY": "nifty_option_chain",
        "BANKNIFTY": "banknifty_option_chain",
        "FINNIFTY": "finnifty_option_chain",
    }

    @classmethod
    def resolve(cls, index: str) -> str:
        try:
            return cls.MAPPING[index.upper()]
        except KeyError:
            raise ValueError(f"Unsupported index symbol: {index}")


class ExpiryResolver:
    """Return SQL predicate fragment that selects rows matching *rule*.

    Example return value:  "expiry_date = '2024-05-30'" or "expiry_date BETWEEN ...".
    """

    @staticmethod
    def resolve(rule: ExpiryRule, trade_date: date, alias: str = "") -> str:
        col = f"{alias}.expiry_date" if alias else "expiry_date"
        if rule == ExpiryRule.CURRENT_WEEK:
            # nearest Thursday >= trade_date
            target = ExpiryResolver._nearest_weekly_expiry(trade_date)
            return f"{col} = DATE '{target.isoformat()}'"
        if rule == ExpiryRule.NEXT_WEEK:
            target = ExpiryResolver._nearest_weekly_expiry(trade_date) + timedelta(days=7)
            return f"{col} = DATE '{target.isoformat()}'"
        if rule == ExpiryRule.MONTHLY:
            target = ExpiryResolver._monthly_expiry(trade_date)
            return f"{col} = DATE '{target.isoformat()}'"
        # default: no filter (e.g., WEEKLY just means any weekly)
        return "1=1"

    @staticmethod
    def _nearest_weekly_expiry(d: date) -> date:
        # NSE weekly options expire on Thursday
        days_ahead = (3 - d.weekday()) % 7  # 0=Mon
        return d + timedelta(days=days_ahead)

    @staticmethod
    def _monthly_expiry(d: date) -> date:
        # Last Thursday of current month
        from calendar import monthrange

        last_day = date(d.year, d.month, monthrange(d.year, d.month)[1])
        # walk backwards to Thursday
        while last_day.weekday() != 3:
            last_day -= timedelta(days=1)
        return last_day


class StrikeResolver:
    """Return SQL predicate fragment for strike selection relative to underlying price column."""

    @staticmethod
    def resolve(rule: StrikeRule, df_alias: str = "") -> str:
        col = f"{df_alias}.strike" if df_alias else "strike"
        underlying = f"{df_alias}.underlying_price" if df_alias else "underlying_price"
        if rule == StrikeRule.ATM:
            return f"ABS({col} - {underlying}) = 0"  # Placeholder; ATM will be refined with delta
        if rule == StrikeRule.ITM:
            return f"{col} < {underlying}"
        if rule == StrikeRule.OTM:
            return f"{col} > {underlying}"
        # Fallback – no predicate
        return "1=1" 