# ML OI Input Sheet Column Mapping and Valid Options

This document provides a comprehensive reference for all columns in the Machine Learning Open Interest (OI) strategy input sheets, including their valid options, descriptions, and usage patterns.

## Enhanced OI System Overview

The OI system now supports two input formats:

### 1. Legacy Format (Backward Compatible)
- **File**: `input_maxoi.xlsx` - Simple 15-column format
- **Purpose**: Basic OI strategies with MAXOI/MAXCOI functionality
- **Usage**: Existing strategies continue to work without modification

### 2. Enhanced Format (New)
- **File 1**: `input_enhanced_oi_config.xlsx` - Comprehensive 45+ parameter configuration
- **File 2**: `input_dynamic_weightage_oi.xlsx` - Dynamic weightage and factor management
- **Purpose**: Advanced OI strategies with dynamic weightage and sophisticated parameter control
- **Features**: Real-time weight adjustment, multi-factor analysis, performance-based optimization

## GeneralParameter Sheet

| Column | Description | Valid Options | Notes |
|--------|-------------|--------------|-------|
| StrategyName | Unique identifier for the strategy | Any string | Used for grouping and identification |
| Timeframe | Chart timeframe in minutes | 3, 6, 9, etc. (multiples of 3) | Must be a multiple of 3 for OI strategy |
| MaxOpenPositions | Maximum open positions | Integer | Maximum number of concurrent positions allowed at the same time |
| Underlying | Underlying security | SPOT, FUT | The underlying security to trade |
| Index | Index name | NIFTY, BANKNIFTY, FINNIFTY, etc. | Used for index selection |
| Weekdays | Trading days filter | 1,2,3,4,5 | 1=Monday, 5=Friday; Comma-separated list |
| DTE | Days to expiry | Integer (0,1,2...) | Days Till Expiry; 0 for same-day expiry |
| StrikeSelectionTime | Time to select strike | HHMMSS format (91600 = 09:16:00) | Strike price selection time |
| StartTime | Entry start time | HHMMSS format (91600 = 09:16:00) | Time to start executing trades |
| LastEntryTime | Last entry time | HHMMSS format (150000 = 15:00:00) | No new entries after this time |
| EndTime | Exit time | HHMMSS format (152000 = 15:20:00) | Time to exit all positions |
| StrategyProfit | Strategy profit target | Number | Target profit for the entire strategy |
| StrategyLoss | Strategy stop loss | Number | Maximum loss for the entire strategy |
| StrategyProfitReExecuteNo | Re-entries after profit | Integer | Number of times to re-enter after hitting profit target |
| StrategyLossReExecuteNo | Re-entries after loss | Integer | Number of times to re-enter after hitting stop loss |
| StrategyTrailingType | Strategy trailing type | Lock Minimum Profit, Lock & Trail Profits | Method for trailing strategy profits |
| PnLCalTime | P&L calculation time | HHMMSS format | Time to calculate P&L |
| LockPercent | Profit lock percentage | Number | Percentage of profit to lock |
| TrailPercent | Trail percentage | Number | Percentage to trail by |
| SqOff1Time | First square-off time | HHMMSS format | Time for first partial exit |
| SqOff1Percent | First square-off percentage | Number (0-100) | Percentage of position to close at SqOff1Time |
| SqOff2Time | Second square-off time | HHMMSS format | Time for second partial exit |
| SqOff2Percent | Second square-off percentage | Number (0-100) | Percentage of position to close at SqOff2Time |
| ProfitReaches | Profit target threshold | Number | When to start trailing/locking profit |
| LockMinProfitAt | Minimum profit to lock | Number | Profit level to secure |
| IncreaseInProfit | Profit increase step | Number | Amount to increase trailing stop by |
| TrailMinProfitBy | Minimum profit to trail | Number | Minimum profit for trailing |
| TgtTrackingFrom | Target tracking mode | close, open, high/low | Price source for target tracking |
| TgtRegisterPriceFrom | Target price mode | tick, tracking | How to register target price |
| SlTrackingFrom | SL tracking mode | close, open, high/low | Price source for stop-loss tracking |
| SlRegisterPriceFrom | SL price mode | tick, tracking | How to register stop-loss price |
| PnLCalculationFrom | P&L calculation mode | close, open, high/low | Price source for P&L calculation |
| ConsiderHedgePnLForStgyPnL | Include hedge in P&L | yes/no | Whether to include hedge position P&L in strategy P&L |
| StoplossCheckingInterval | SL check interval | Integer (seconds) | How often to check for stop-loss trigger |
| TargetCheckingInterval | Target check interval | Integer (seconds) | How often to check for target trigger |
| ReEntryCheckingInterval | Re-entry check interval | Integer (seconds) | How often to check for re-entry conditions |
| OnExpiryDayTradeNextExpiry | Trade next expiry on expiry day | yes/no | Whether to use next expiry contracts on expiry day |

## LegParameter Sheet

| Column | Description | Valid Options | Notes |
|--------|-------------|--------------|-------|
| StrategyName | Strategy identifier | Must match GeneralParameter | Links leg to strategy |
| OiThreshold | Open Interest threshold | Integer (e.g., 800000) | Minimum Open Interest value required for trade entry |
| LegID | Leg identifier | Any unique identifier (a1, a2, etc.) | Used to reference specific legs |
| Instrument | Option type | call, put, FUT | CE/CALL → CALL, PE/PUT → PUT, FUT for futures |
| Transaction | Transaction type | buy, sell | Trade direction |
| Expiry | Expiry selection | current, next, monthly | CURRENT/CW → CURRENT_WEEK, NEXT/NW → NEXT_WEEK, MONTHLY for monthly contracts |
| MatchPremium | Premium matching | high, low | For premium-based strike selection |
| StrikeMethod | Strike selection | ATM, ITM1, ITM2, OTM1, OTM2, MAXOI_1, MAXCOI_1, etc. | Method to select strike price (see detailed explanation below) |
| StrikeValue | Fixed strike value | Number | Used with FIXED strike method and special cases |
| StrikePremiumCondition | Premium condition | =, <, >, <= | Comparison operator for premium |
| SLType | Stop loss type | percentage, point, index point, index percentage, absolute, delta | Type of stop-loss |
| SLValue | Stop loss value | Number | Amount for stop loss (recommended: 500 for SELL, 50 for BUY) |
| TGTType | Target type | percentage, point, index point, index percentage, absolute, delta | Type of target |
| TGTValue | Target value | Number | Amount for target (recommended: 100) |
| TrailSLType | Trailing SL type | percentage, point, index point, index percentage, absolute, delta | Type of trailing stop-loss |
| SL_TrailAt | When to start trailing | Number | Profit level to start trailing |
| SL_TrailBy | Trail step | Number | Amount to trail by |
| Lots | Number of lots | Integer | Position size in lots |
| OpenHedge | Use hedging | Yes/No | Whether to use automatic hedging |
| HedgeStrikeMethod | Hedge strike method | atm, premium, atm width, delta | Method to select hedge strike (see detailed explanation below) |
| HedgeStrikeValue | Hedge strike value | Number | Used with specific hedge strike methods |
| HedgeStrikePremiumCondition | Hedge premium condition | =, <, >, <= | Comparison operator for hedge premium |

## Detailed Strike Selection Logic

The strike selection mechanism works differently depending on the `StrikeMethod` value:

### Basic Strike Methods

| StrikeMethod | Description | StrikeValue Usage | Notes |
|--------------|-------------|-------------------|-------|
| `ATM` | Relative strike selection | 0 = ATM<br>Positive (1,2,3...) = OTM steps for both CE & PE<br>Negative (-1,-2,-3...) = ITM steps for both CE & PE | Standardized offset from ATM |
| `ITM1`, `ITM2`, etc. | In-The-Money | Ignored | The number indicates steps away from ATM (ITM1 = 1 step, ITM2 = 2 steps, etc.) |
| `OTM1`, `OTM2`, etc. | Out-Of-The-Money | Ignored | The number indicates steps away from ATM (OTM1 = 1 step, OTM2 = 2 steps, etc.) |
| `FIXED` | Fixed Strike | Required | Uses StrikeValue directly as the strike price |

### ATM Strike Selection with Standardized Offset

When using `StrikeMethod = 'ATM'` with `StrikeValue`:

| StrikeValue | CE Selection | PE Selection | Classification |
|-------------|--------------|--------------|----------------|
| 0 | 19500 | 19500 | Both ATM |
| 1 | 19550 | 19450 | Both OTM1 |
| 2 | 19600 | 19400 | Both OTM2 |
| -1 | 19450 | 19550 | Both ITM1 |
| -2 | 19400 | 19600 | Both ITM2 |

### ATM Width (Straddle Width) Calculation

The ATM Width method calculates strikes based on ATM straddle premium:

**Formula:**
- **Positive Multiplier (OTM Selection):**
  - CE Strike = ATM + (Straddle Premium × Multiplier)
  - PE Strike = ATM - (Straddle Premium × Multiplier)
  
- **Negative Multiplier (ITM Selection):**
  - CE Strike = ATM - (Straddle Premium × |Multiplier|)
  - PE Strike = ATM + (Straddle Premium × |Multiplier|)

**Example (BANKNIFTY):**
- Spot: 35520, ATM: 35500
- ATM CE Premium: 310, ATM PE Premium: 465
- Straddle Premium: 775

With `StrikeValue = 0.5`:
- CE Strike = 35500 + (775 × 0.5) = 35887.5 → 35900 (OTM)
- PE Strike = 35500 - (775 × 0.5) = 35112.5 → 35100 (OTM)

With `StrikeValue = -0.5`:
- CE Strike = 35500 - (775 × 0.5) = 35112.5 → 35100 (ITM)
- PE Strike = 35500 + (775 × 0.5) = 35887.5 → 35900 (ITM)

### OI-Specific Strike Methods

| StrikeMethod | Description | StrikeValue Usage | Notes |
|--------------|-------------|-------------------|-------|
| `MAXOI_1`, `MAXOI_2`, etc. | Maximum Open Interest | Ignored | Selects strike based on highest OI. Number indicates rank (MAXOI_1 = highest OI, MAXOI_2 = second highest, etc.) |
| `MAXCOI_1`, `MAXCOI_2`, etc. | Maximum Change in OI | Ignored | Selects strike based on highest change in OI. Number indicates rank (MAXCOI_1 = highest COI, MAXCOI_2 = second highest, etc.) |

### Advanced Strike Methods

| StrikeMethod | Description | StrikeValue Usage | Notes |
|--------------|-------------|-------------------|-------|
| `PREMIUM` | Premium-based | Required | Select strike based on premium value, uses StrikePremiumCondition (=, <, >) |
| `ATM WIDTH` | ATM Width | Required | Offset from ATM based on ATM straddle premium * StrikeValue |
| `STRADDLE WIDTH` | Straddle Width | Required | Similar to ATM WIDTH, uses straddle premium to calculate offset |
| `ATM MATCH` | ATM Match | Required | Match premium of the opposite option type |
| `ATM DIFF` | ATM Difference | Required | Find strike with minimum CE-PE premium difference |
| `DELTA` | Delta-based | Required | Select strike based on delta value |

## Detailed Hedge Strike Selection Logic

Similar to the primary strike selection, hedge strikes also follow a structured selection process based on the `HedgeStrikeMethod` value:

### Basic Hedge Strike Methods

| HedgeStrikeMethod | Description | HedgeStrikeValue Usage | Notes |
|--------------|-------------|-------------------|-------|
| `ATM` | Relative hedge strike selection | 0 = ATM<br>Positive (1,2,3...) = OTM steps for both CE & PE<br>Negative (-1,-2,-3...) = ITM steps for both CE & PE | Standardized offset from ATM |
| `ITM1`, `ITM2`, etc. | In-The-Money | Ignored | The number indicates steps away from ATM (ITM1 = 1 step, ITM2 = 2 steps, etc.) |
| `OTM1`, `OTM2`, etc. | Out-Of-The-Money | Ignored | The number indicates steps away from ATM (OTM1 = 1 step, OTM2 = 2 steps, etc.) |
| `FIXED` | Fixed Strike | Required | Uses HedgeStrikeValue directly as the hedge strike price |

### ATM Hedge Strike Selection with Standardized Offset

When using `HedgeStrikeMethod = 'ATM'` with `HedgeStrikeValue`:

| HedgeStrikeValue | CE Selection | PE Selection | Description |
|-------------|--------------|--------------|-------------|
| 0 | ATM | ATM | At-The-Money hedge strike |
| 1 | OTM1 (ATM + 1 step) | OTM1 (ATM - 1 step) | 1 step Out-of-The-Money |
| 2 | OTM2 (ATM + 2 steps) | OTM2 (ATM - 2 steps) | 2 steps Out-of-The-Money |
| -1 | ITM1 (ATM - 1 step) | ITM1 (ATM + 1 step) | 1 step In-The-Money |
| -2 | ITM2 (ATM - 2 steps) | ITM2 (ATM + 2 steps) | 2 steps In-The-Money |

### ATM Width Hedge Strike Calculation

The ATM Width method for hedge strikes calculates based on ATM straddle premium:

**Formula:**
- **Positive Multiplier (OTM Selection):**
  - CE Hedge Strike = ATM + (Straddle Premium × HedgeStrikeValue)
  - PE Hedge Strike = ATM - (Straddle Premium × HedgeStrikeValue)
  
- **Negative Multiplier (ITM Selection):**
  - CE Hedge Strike = ATM - (Straddle Premium × |HedgeStrikeValue|)
  - PE Hedge Strike = ATM + (Straddle Premium × |HedgeStrikeValue|)

### Advanced Hedge Strike Methods

| HedgeStrikeMethod | Description | HedgeStrikeValue Usage | Notes |
|--------------|-------------|-------------------|-------|
| `PREMIUM` | Premium-based | Required | Select hedge strike based on premium value, uses HedgeStrikePremiumCondition (=, <, >) |
| `ATM WIDTH` | ATM Width | Required | Offset from ATM based on ATM straddle premium × HedgeStrikeValue |
| `STRADDLE WIDTH` | Straddle Width | Required | Similar to ATM WIDTH, uses straddle premium to calculate offset |
| `DELTA` | Delta-based | Required | Select hedge strike based on delta value |

The hedge strike calculation follows the same strike step sizes and option type considerations as the main strike selection.

## Strike Steps Calculation

The system uses different step intervals for different indices, based on exchange standards:

| Index | Strike Step Size | Notes |
|-------|-----------------|-------|
| NIFTY | 50 | Fixed step size regardless of index level |
| BANKNIFTY | 100 | Fixed step size regardless of index level |
| FINNIFTY | 50 | Fixed step size regardless of index level |
| MIDCPNIFTY | 25 | Fixed step size regardless of index level |
| SENSEX | 100 | Fixed step size regardless of index level |
| BANKEX | 100 | Fixed step size regardless of index level |

These step sizes align with the official NSE/BSE specifications and are used to calculate exact ITM and OTM strikes.

## Expiry Options

| Code | Description | Database Mapping | Notes |
|------|-------------|-----------------|-------|
| `current` / `CW` | Current Week | CW | Weekly expiry that's nearest to current date |
| `next` / `NW` | Next Week | NW | Second-nearest weekly expiry |
| `monthly` / `CM` | Current Month | CM | Current month's expiry (typically last Thursday) |
| `NM` | Next Month | NM | Next month's expiry |

These expiry codes align with the expiry bucket nomenclature used in the `nifty_option_chain` table and other HeavyDB database objects.

## Open Interest (OI) Strategy Logic

The OI strategy operates on the principle that options with high open interest or significant changes in open interest can indicate potential market direction or important price levels.

### Key Components of the OI Strategy

1. **Timeframe Requirement**: 
   - OI strategy requires timeframe to be a multiple of 3 minutes (3, 6, 9, etc.)
   - This is a hard validation - the system will reject strategies where timeframe is not a multiple of 3
   - This requirement ensures alignment with OI data update frequencies

2. **OI Threshold**:
   - Minimum OI value required before a trade can be entered
   - Prevents trading in illiquid options
   - Set in the `OiThreshold` column of LegParameter sheet (e.g., 800000)
   - The system checks this threshold at intervals defined by the timeframe

3. **Strike Selection by OI**:
   - `MAXOI_N`: Selects strike with the Nth highest open interest within the strike range
     - Example: `MAXOI_1` selects the strike with the highest OI
     - Format in Excel: `maxoi1`, `maxoi2`, etc. (case-insensitive)
   - `MAXCOI_N`: Selects strike with the Nth highest change in open interest within the strike range
     - Example: `MAXCOI_1` selects the strike with the highest change in OI
     - Format in Excel: `maxcoi1`, `maxcoi2`, etc. (case-insensitive)
   - Strike range is determined by `noofstrikeeachside` parameter (internal parameter in the archive code)

4. **MaxOpenPositions**:
   - Controls the number of concurrent positions the strategy can have
   - Essential for strategies that might re-enter on specific conditions
   - System parameter: `concurrent_legs`

5. **OI Check Interval**:
   - The system rechecks OI at specific intervals
   - Calculated as check_frequency × timeframe (in minutes)
   - Formula in code: `check_interval: checkfreq * int(frontendStgyParameters['Timeframe'])`

6. **OI-Based Exit Logic**:
   - Positions can be exited if the strike no longer meets OI criteria
   - Exit reason will be recorded as `OI_CHANGED_EXIT`
   - This happens when the strike with the highest OI/COI changes during the trading session

### OI Calculation Methods

The system supports two methods for calculating change in open interest (COI), controlled by an internal `coi_based_on` parameter:

1. **Today Open** (`TODAY_OPEN`):
   - Compares current OI with OI at market open of the current day
   - Useful for tracking intraday OI changes

2. **Previous Day Close** (`PREV_DAY_CLOSE` or `YESTERDAY_CLOSE`):
   - Compares current OI with OI at market close of the previous day
   - Useful for overnight OI changes
   - This is the default method used in the archive implementation

### Implementation Notes from Archive Code

Based on the archive implementation in `Util.py` and `BTRunPortfolio.py`:

1. The OI backtester processes input from `INPUT MAXOI.xlsx` with the following columns:
   - `id`: Strategy identifier
   - `underlyingname`: Index name (NIFTY, BANKNIFTY, etc.)
   - `startdate`, `enddate`: Backtest period in YYMMDD format
   - `entrytime`, `lastentrytime`, `exittime`: Trade timing in HHMMSS format
   - `dte`: Days to expiry
   - `lot`: Number of lots
   - `expiry`: Expiry selection (current, next, etc.)
   - `noofstrikeeachside`: Number of strikes to consider on each side of ATM
   - `striketotrade`: OI selection method (maxoi1, maxcoi1, etc.)
   - `slippagepercent`: Slippage percentage for execution
   - `strategymaxprofit`, `strategymaxloss`: Strategy profit/loss limits

2. The MaxOI strategy is selected when `striketotrade` starts with "MAXOI" or "MAXCOI"

3. Default COI calculation method is `YESTERDAY_CLOSE`

4. Both Call and Put options are traded by default (`trade_option: ["CE", "PE"]`)

## Strategy Trailing Types

| Type | Description | Implementation Logic |
|------|-------------|----------------------|
| Lock Minimum Profit | Locks a minimum profit once target is reached | **Logic**: If ProfitReaches=10000, LockMinProfitAt=5000, when profit hits 10000, system locks minimum profit at 5000. All positions exit when MTM decreases to 5000. |
| Lock & Trail Profits | Continuously trails the profit as it increases | **Logic**: Uses TrailPercent to determine the trailing amount. As profit grows, stop level is moved up accordingly, maintaining the TrailPercent distance. |

## Price Reference Sources

For SL/TP and other price-based triggers, the system can use different price references:

### TgtTrackingFrom / SlTrackingFrom / PnLCalculationFrom
- `close`: Use the closing price
- `open`: Use the opening price
- `high/low`: Use the high or low price (whichever is more relevant for the condition)

### TgtRegisterPriceFrom / SlRegisterPriceFrom
- `tick`: Update on every price tick
- `tracking`: Update based on tracking logic

## Example OI Strategy

A typical OI-based strategy configuration from the archive implementation:

```
id: NFTEST
underlyingname: NIFTY
startdate: 250101
enddate: 250105
entrytime: 93000
lastentrytime: 151500
exittime: 151500
dte: 100
lot: 1
expiry: current
noofstrikeeachside: 40
striketotrade: maxoi1
slippagepercent: 0.1
strategymaxprofit: 0
strategymaxloss: 0
```

This configuration would:
1. Trade NIFTY options with current week expiry
2. Look for options with DTE = 100
3. Consider 40 strikes on each side of ATM (80 strikes total)
4. Select the strike with the highest OI (maxoi1)
5. Trade both CE and PE options
6. Apply 0.1% slippage
7. No profit/loss limits (0 values)
8. Enter between 9:30 AM and 3:15 PM
9. Exit at 3:15 PM 

## HeavyDB Column Mapping and Query Patterns

This section explains how Open Interest (OI) Strategy fields are mapped to HeavyDB columns when executing backtest queries, including transformation logic and OI-specific SQL patterns.

### Direct Field Mappings

| Excel Column | HeavyDB Column | SQL Data Type | Notes |
|--------------|----------------|--------------|-------|
| Index | index_name | TEXT ENCODING DICT | Used to select appropriate option_chain table (`{index}_option_chain`) |
| DTE | dte | INT | Direct integer comparison (e.g., `WHERE dte = 0`) |
| Expiry | expiry_bucket | TEXT ENCODING DICT | Maps to CW/NW/CM/NM values (e.g., `WHERE expiry_bucket = 'CW'`) |
| StrikeMethod (ATM) | strike, atm_strike | DOUBLE | Compared using `WHERE strike = atm_strike` |
| StrikeMethod (ITM/OTM) | call_strike_type, put_strike_type | TEXT ENCODING DICT | Used with patterns like `WHERE call_strike_type = 'ITM1'` |
| OiThreshold | ce_oi, pe_oi | BIGINT | Used in filter conditions (e.g., `WHERE ce_oi >= 800000`) |
| Timeframe | N/A | INT | Used for time bucketing in OI analysis queries |
| StrikeMethod (MAXOI_N) | ce_oi, pe_oi | BIGINT | Used to select strikes based on OI ranking |
| StrikeMethod (MAXCOI_N) | ce_coi, pe_coi | BIGINT | Used to select strikes based on change in OI ranking |

### Transformed Fields 

| Excel Column | Excel Format | HeavyDB Transformation | SQL Example |
|--------------|--------------|------------------------|-------------|
| StartTime | HHMMSS | HH:MM:SS TIME | `91600` → `WHERE trade_time >= TIME '09:16:00'` |
| EndTime | HHMMSS | HH:MM:SS TIME | `152000` → `WHERE trade_time <= TIME '15:20:00'` |
| StrikeSelectionTime | HHMMSS | HH:MM:SS TIME | `91530` → `WHERE trade_time = TIME '09:15:30'` |
| LastEntryTime | HHMMSS | HH:MM:SS TIME | `150000` → `WHERE trade_time <= TIME '15:00:00'` |
| Timeframe | Integer (multiple of 3) | Interval in minutes | `Timeframe = 3` → `TIME_BUCKET(INTERVAL '3 minutes', trade_time)` |

The time transformation function (`_ensure_hhmmss`) converts Excel integer time values (91600) to proper SQL TIME strings ('09:16:00') for database queries.

### OI-Specific SQL Query Patterns

#### MAXOI Strike Selection Query

```sql
-- Select top N strikes with highest open interest (MAXOI_1 example for call options)
WITH oi_ranked_strikes AS (
  SELECT
    strike,
    ce_oi,
    -- Rank strikes by OI within each time bucket
    ROW_NUMBER() OVER(
      PARTITION BY trade_date, trade_time
      ORDER BY ce_oi DESC
    ) AS oi_rank
  FROM nifty_option_chain
  WHERE trade_date = DATE '2025-04-01'
    AND trade_time = TIME '09:16:00'
    AND expiry_bucket = 'CW'
    AND ce_oi >= 800000  -- OiThreshold
    -- Limit to strikes within range of ATM
    AND strike BETWEEN (atm_strike - 1000) AND (atm_strike + 1000)
    AND ce_symbol IS NOT NULL
)
-- Select the strike with highest OI (rank 1)
SELECT oc.*
FROM nifty_option_chain oc
JOIN oi_ranked_strikes r ON oc.strike = r.strike
WHERE oc.trade_date = DATE '2025-04-01'
  AND oc.trade_time = TIME '09:16:00'
  AND oc.expiry_bucket = 'CW'
  AND r.oi_rank = 1  -- MAXOI_1
  AND oc.ce_symbol IS NOT NULL
LIMIT 1
```

#### MAXCOI Strike Selection Query

```sql
-- Select top N strikes with highest change in open interest (MAXCOI_1 example for put options)
WITH coi_ranked_strikes AS (
  SELECT
    strike,
    pe_coi,
    -- Rank strikes by Change in OI within each time bucket
    ROW_NUMBER() OVER(
      PARTITION BY trade_date, trade_time
      ORDER BY pe_coi DESC
    ) AS coi_rank
  FROM nifty_option_chain
  WHERE trade_date = DATE '2025-04-01'
    AND trade_time = TIME '09:16:00'
    AND expiry_bucket = 'CW'
    AND pe_oi >= 800000  -- OiThreshold
    -- Limit to strikes within range of ATM
    AND strike BETWEEN (atm_strike - 1000) AND (atm_strike + 1000)
    AND pe_symbol IS NOT NULL
)
-- Select the strike with highest change in OI (rank 1)
SELECT oc.*
FROM nifty_option_chain oc
JOIN coi_ranked_strikes r ON oc.strike = r.strike
WHERE oc.trade_date = DATE '2025-04-01'
  AND oc.trade_time = TIME '09:16:00'
  AND oc.expiry_bucket = 'CW'
  AND r.coi_rank = 1  -- MAXCOI_1
  AND oc.pe_symbol IS NOT NULL
LIMIT 1
```

#### Time-Bucketed OI Analysis

```sql
-- Time-bucketed OI analysis (using Timeframe parameter)
WITH oi_buckets AS (
  SELECT
    -- Bucket by Timeframe (e.g., 3 minutes)
    TIME_BUCKET(INTERVAL '3 minutes', trade_time) AS time_bucket,
    strike,
    -- Last OI in each bucket
    LAST_VALUE(ce_oi) OVER(
      PARTITION BY TIME_BUCKET(INTERVAL '3 minutes', trade_time), strike
      ORDER BY trade_time
    ) AS last_oi,
    -- Last Change in OI in each bucket
    LAST_VALUE(ce_coi) OVER(
      PARTITION BY TIME_BUCKET(INTERVAL '3 minutes', trade_time), strike
      ORDER BY trade_time
    ) AS last_coi,
    -- First OI in the day (for TODAY_OPEN COI calculation)
    FIRST_VALUE(ce_oi) OVER(
      PARTITION BY trade_date, strike
      ORDER BY trade_time
    ) AS open_oi
  FROM nifty_option_chain
  WHERE trade_date = DATE '2025-04-01'
    AND expiry_bucket = 'CW'
    AND ce_symbol IS NOT NULL
),
-- Select distinct OI values (one per bucket)
distinct_buckets AS (
  SELECT DISTINCT
    time_bucket,
    strike,
    last_oi,
    last_coi,
    open_oi,
    -- Calculate TODAY_OPEN COI
    last_oi - open_oi AS today_open_coi
  FROM oi_buckets
),
-- Rank by OI/COI within each time bucket
ranked_strikes AS (
  SELECT
    time_bucket,
    strike,
    last_oi,
    last_coi,
    today_open_coi,
    -- Rank by OI
    ROW_NUMBER() OVER(
      PARTITION BY time_bucket
      ORDER BY last_oi DESC
    ) AS oi_rank,
    -- Rank by COI (standard)
    ROW_NUMBER() OVER(
      PARTITION BY time_bucket
      ORDER BY last_coi DESC
    ) AS coi_rank,
    -- Rank by TODAY_OPEN COI
    ROW_NUMBER() OVER(
      PARTITION BY time_bucket
      ORDER BY today_open_coi DESC
    ) AS today_coi_rank
  FROM distinct_buckets
)
-- Select top-ranked OI strikes for analysis
SELECT *
FROM ranked_strikes
WHERE oi_rank <= 5 OR coi_rank <= 5 OR today_coi_rank <= 5
ORDER BY time_bucket, oi_rank
```

#### Checking for OI-Changed Exit Condition

```sql
-- Check if strike no longer has the highest OI (OI_CHANGED_EXIT condition)
-- Assuming we entered a trade based on MAXOI_1 at entry_time
WITH entry_strike AS (
  -- Get the strike we entered based on MAXOI_1
  SELECT strike, ce_oi
  FROM nifty_option_chain
  WHERE trade_date = DATE '2025-04-01'
    AND trade_time = TIME '09:16:00'  -- entry_time
    AND expiry_bucket = 'CW'
    AND strike = 19500  -- Our entry strike
    AND ce_symbol IS NOT NULL
),
current_oi_rank AS (
  -- Get current OI rank for all strikes
  SELECT
    strike,
    ce_oi,
    ROW_NUMBER() OVER(ORDER BY ce_oi DESC) AS current_oi_rank
  FROM nifty_option_chain
  WHERE trade_date = DATE '2025-04-01'
    AND trade_time = TIME '10:30:00'  -- current_time
    AND expiry_bucket = 'CW'
    AND ce_symbol IS NOT NULL
)
-- Check if our strike is no longer ranked #1 by OI
SELECT
  e.strike AS entry_strike,
  e.ce_oi AS entry_oi,
  c.ce_oi AS current_oi,
  c.current_oi_rank,
  -- Exit signal if our strike is no longer #1
  CASE WHEN c.current_oi_rank > 1 THEN 'OI_CHANGED_EXIT' ELSE NULL END AS exit_reason
FROM entry_strike e
JOIN current_oi_rank c ON e.strike = c.strike
```

### Performance Considerations for OI Strategies

1. **OI Data Handling**:
   - OI data analysis can be computationally intensive
   - Leverage HeavyDB's window functions for efficient ranking
   - For frequent OI analysis, consider materialized views with pre-ranked strikes

2. **Strike Range Filtering**:
   - OI strategies often analyze a range of strikes around ATM
   - Use explicit strike range filters to reduce data volume
   - Adjust range based on index volatility (wider for high-volatility)

3. **Time-Bucketing Optimization**:
   - Timeframe must be a multiple of 3 (validation enforced in code)
   - For larger timeframes, consider pre-aggregating data
   - Use HeavyDB's TIME_BUCKET function for consistent intervals

4. **OI Rechecking Logic**:
   - OI-based exit requires periodic rechecking of OI ranks
   - Schedule checks at intervals based on Timeframe parameter
   - Use subqueries to compare current rank vs. entry rank

5. **COI Calculation Methods**:
   - System supports multiple COI calculation methods:
     - Standard COI (column value directly from data)
     - TODAY_OPEN (current OI minus day's opening OI)
     - PREV_DAY_CLOSE (current OI minus previous day's closing OI)
   - Use appropriate method based on strategy requirements

### Symbol Adaptation

The same column structure and query patterns apply to all symbol tables following the pattern `{symbol}_option_chain`. The system automatically routes queries to the correct table based on the `Index` field value:

```python
TABLE_MAP = {
    "NIFTY": "nifty_option_chain",
    "BANKNIFTY": "banknifty_option_chain",
    "FINNIFTY": "finnifty_option_chain",
}
```

Adjust OI thresholds based on the underlying index's liquidity characteristics:
- Higher thresholds for more liquid indices (NIFTY, BANKNIFTY)
- Lower thresholds for less liquid indices (FINNIFTY, MIDCPNIFTY)

### MaxOpenPositions Implementation

The `MaxOpenPositions` parameter limits the number of concurrent positions:

```sql
-- Check if we've reached the maximum open positions
SELECT COUNT(*) AS open_position_count
FROM active_positions
WHERE strategy_id = 'OI_STRATEGY_1'
  AND exit_time IS NULL
HAVING COUNT(*) >= 3  -- MaxOpenPositions = 3
```

This limit is enforced in the trade execution logic before entering any new positions.

---

# Enhanced OI System - Dynamic Weightage Parameters

## Enhanced GeneralParameter Sheet (45 Columns)

The enhanced OI system introduces sophisticated parameter configuration with 45 comprehensive columns organized into three categories:

### Core Strategy Parameters (15 columns)
These are the fundamental parameters that define the basic strategy behavior:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| StrategyName | Unique strategy identifier | Any string | Required | Used for grouping and identification |
| Underlying | Underlying security type | SPOT, FUT | SPOT | The underlying security to trade |
| Index | Index name | NIFTY, BANKNIFTY, FINNIFTY, etc. | NIFTY | Used for index selection |
| DTE | Days to expiry | Integer (0,1,2...) | 0 | Days Till Expiry; 0 for same-day expiry |
| Timeframe | Chart timeframe in minutes | 3, 6, 9, etc. (multiples of 3) | 3 | Must be a multiple of 3 for OI strategy |
| StartTime | Entry start time | HHMMSS format | 091600 | Time to start executing trades |
| EndTime | Exit time | HHMMSS format | 152000 | Time to exit all positions |
| LastEntryTime | Last entry time | HHMMSS format | 150000 | No new entries after this time |
| StrikeSelectionTime | Time to select strike | HHMMSS format | 091530 | Strike price selection time |
| MaxOpenPositions | Maximum open positions | Integer | 2 | Maximum number of concurrent positions |
| OiThreshold | Global OI threshold | Integer | 800000 | Minimum OI value for trade entry |
| StrikeCount | Strikes to analyze | Integer | 10 | Number of strikes each side of ATM |
| Weekdays | Trading days filter | 1,2,3,4,5 | 1,2,3,4,5 | 1=Monday, 5=Friday; Comma-separated |
| StrategyProfit | Strategy profit target | Number | 0 | Target profit for entire strategy |
| StrategyLoss | Strategy stop loss | Number | 0 | Maximum loss for entire strategy |

### OI-Specific Parameters (15 columns)
Advanced OI analysis and configuration parameters:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| OiMethod | OI selection method | MAXOI_1, MAXOI_2, MAXCOI_1, MAXCOI_2, etc. | MAXOI_1 | Primary OI-based strike selection method |
| CoiBasedOn | COI calculation base | YESTERDAY_CLOSE, TODAY_OPEN | YESTERDAY_CLOSE | Method for calculating change in OI |
| OiRecheckInterval | OI recheck frequency | Integer (seconds) | 180 | How often to recheck OI rankings |
| OiThresholdType | OI threshold type | ABSOLUTE, PERCENTAGE, PERCENTILE | ABSOLUTE | Type of OI threshold calculation |
| OiConcentrationThreshold | OI concentration limit | Number (0-1) | 0.3 | Maximum OI concentration allowed |
| OiDistributionAnalysis | Enable OI distribution | YES, NO | YES | Analyze OI distribution patterns |
| StrikeRangeType | Strike range method | FIXED, DYNAMIC, ATM_BASED | FIXED | How to determine strike analysis range |
| StrikeRangeValue | Strike range value | Integer | 10 | Value for strike range calculation |
| OiMomentumPeriod | OI momentum periods | Integer | 5 | Periods for OI momentum calculation |
| OiTrendAnalysis | Enable OI trend analysis | YES, NO | YES | Analyze OI trend patterns |
| OiSeasonalAdjustment | Enable seasonal adjustment | YES, NO | NO | Apply seasonal adjustments to OI |
| OiVolumeCorrelation | Enable OI-volume correlation | YES, NO | YES | Analyze OI and volume correlation |
| OiLiquidityFilter | Enable liquidity filtering | YES, NO | YES | Filter based on OI liquidity metrics |
| OiAnomalyDetection | Enable anomaly detection | YES, NO | YES | Detect OI anomalies and outliers |
| OiSignalConfirmation | Enable signal confirmation | YES, NO | YES | Require confirmation for OI signals |

### Dynamic Weightage Parameters (15 columns)
Parameters controlling the dynamic weight adjustment system:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| EnableDynamicWeights | Enable dynamic weighting | YES, NO | YES | Turn on/off dynamic weight system |
| WeightAdjustmentPeriod | Weight adjustment frequency | Integer (periods) | 20 | How often to adjust weights |
| LearningRate | Weight learning rate | Number (0-1) | 0.01 | Speed of weight adaptation |
| PerformanceWindow | Performance calculation window | Integer (periods) | 100 | Periods for performance evaluation |
| MinWeight | Minimum factor weight | Number (0-1) | 0.05 | Minimum allowed weight for any factor |
| MaxWeight | Maximum factor weight | Number (0-1) | 0.50 | Maximum allowed weight for any factor |
| WeightDecayFactor | Weight decay factor | Number (0-1) | 0.95 | Exponential decay for historical data |
| CorrelationThreshold | Correlation threshold | Number (0-1) | 0.7 | Threshold for factor correlation |
| DiversificationBonus | Diversification bonus | Number (>1) | 1.1 | Bonus for diversified factor usage |
| RegimeAdjustment | Enable regime adjustment | YES, NO | YES | Adjust weights based on market regime |
| VolatilityAdjustment | Enable volatility adjustment | YES, NO | YES | Adjust weights based on volatility |
| LiquidityAdjustment | Enable liquidity adjustment | YES, NO | YES | Adjust weights based on liquidity |
| TrendAdjustment | Enable trend adjustment | YES, NO | YES | Adjust weights based on trend |
| MomentumAdjustment | Enable momentum adjustment | YES, NO | YES | Adjust weights based on momentum |
| SeasonalAdjustment | Enable seasonal adjustment | YES, NO | NO | Adjust weights based on seasonality |

## Enhanced LegParameter Sheet (35 Columns)

The enhanced leg parameter configuration provides sophisticated control over individual leg behavior with 35 comprehensive columns:

### Basic Leg Parameters (15 columns)
Fundamental leg configuration parameters:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| StrategyName | Strategy identifier | Must match GeneralParameter | Required | Links leg to strategy |
| LegID | Leg identifier | Any unique identifier | Required | Used to reference specific legs |
| Instrument | Option type | CE, PE, FUT | CE | Option type for this leg |
| Transaction | Transaction type | BUY, SELL | SELL | Trade direction |
| Expiry | Expiry selection | current, next, monthly | current | Expiry bucket selection |
| StrikeMethod | Strike selection method | MAXOI_1, MAXCOI_1, ATM, etc. | MAXOI_1 | Method to select strike price |
| StrikeValue | Strike value | Number | 0 | Value for strike calculation |
| Lots | Number of lots | Integer | 1 | Position size in lots |
| SLType | Stop loss type | percentage, point, absolute | percentage | Type of stop-loss |
| SLValue | Stop loss value | Number | 30 | Stop loss amount |
| TGTType | Target type | percentage, point, absolute | percentage | Type of target |
| TGTValue | Target value | Number | 50 | Target amount |
| TrailSLType | Trailing SL type | percentage, point, absolute | percentage | Type of trailing stop-loss |
| SL_TrailAt | Trail start level | Number | 25 | Profit level to start trailing |
| SL_TrailBy | Trail step | Number | 10 | Amount to trail by |

### OI-Specific Leg Parameters (10 columns)
Advanced OI analysis parameters for individual legs:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| LegOiThreshold | Leg-specific OI threshold | Integer | 800000 | Minimum OI for this leg |
| LegOiWeight | OI factor weight for leg | Number (0-1) | 0.4 | Weight of OI factor for this leg |
| LegCoiWeight | COI factor weight for leg | Number (0-1) | 0.3 | Weight of COI factor for this leg |
| LegOiRank | OI rank preference | Integer (1-5) | 1 | Preferred OI rank for selection |
| LegOiConcentration | OI concentration weight | Number (0-1) | 0.3 | Weight of OI concentration analysis |
| LegOiMomentum | OI momentum weight | Number (0-1) | 0.2 | Weight of OI momentum factor |
| LegOiTrend | OI trend weight | Number (0-1) | 0.15 | Weight of OI trend analysis |
| LegOiLiquidity | OI liquidity weight | Number (0-1) | 0.1 | Weight of OI liquidity factor |
| LegOiAnomaly | OI anomaly weight | Number (0-1) | 0.05 | Weight of OI anomaly detection |
| LegOiConfirmation | Require OI confirmation | YES, NO | YES | Require OI signal confirmation |

### Greek-Based Parameters (10 columns)
Options Greeks analysis and risk management:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| DeltaWeight | Delta factor weight | Number (0-1) | 0.25 | Weight of delta in decision making |
| GammaWeight | Gamma factor weight | Number (0-1) | 0.20 | Weight of gamma in decision making |
| ThetaWeight | Theta factor weight | Number (0-1) | 0.15 | Weight of theta in decision making |
| VegaWeight | Vega factor weight | Number (0-1) | 0.10 | Weight of vega in decision making |
| DeltaThreshold | Delta threshold | Number (-1 to 1) | 0.5 | Delta threshold for selection |
| GammaThreshold | Gamma threshold | Number | 0.1 | Gamma threshold for selection |
| ThetaThreshold | Theta threshold | Number | -0.05 | Theta threshold for selection |
| VegaThreshold | Vega threshold | Number | 0.2 | Vega threshold for selection |
| GreekRebalanceFreq | Greek rebalance frequency | Integer (seconds) | 300 | How often to rebalance Greeks |
| GreekRiskLimit | Greek risk limit | Number | 10000 | Maximum Greek exposure limit |

## Dynamic Weightage Configuration Sheet (25 Columns)

The dynamic weightage system provides sophisticated factor weight management with real-time adaptation:

### Base Factor Weights (5 columns)
Primary factor weight allocation:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| OiFactorWeight | OI factor base weight | Number (0-1) | 0.35 | Base weight for OI-related factors |
| CoiFactorWeight | COI factor base weight | Number (0-1) | 0.25 | Base weight for COI-related factors |
| GreekFactorWeight | Greek factor base weight | Number (0-1) | 0.20 | Base weight for Greeks-related factors |
| MarketFactorWeight | Market factor base weight | Number (0-1) | 0.15 | Base weight for market condition factors |
| PerformanceFactorWeight | Performance factor base weight | Number (0-1) | 0.05 | Base weight for performance-based factors |

### OI Sub-Factor Weights (8 columns)
Detailed OI factor breakdown:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| CurrentOiWeight | Current OI weight | Number (0-1) | 0.30 | Weight of current OI levels |
| OiConcentrationWeight | OI concentration weight | Number (0-1) | 0.20 | Weight of OI concentration analysis |
| OiDistributionWeight | OI distribution weight | Number (0-1) | 0.15 | Weight of OI distribution patterns |
| OiMomentumWeight | OI momentum weight | Number (0-1) | 0.15 | Weight of OI momentum indicators |
| OiTrendWeight | OI trend weight | Number (0-1) | 0.10 | Weight of OI trend analysis |
| OiSeasonalWeight | OI seasonal weight | Number (0-1) | 0.05 | Weight of OI seasonal patterns |
| OiLiquidityWeight | OI liquidity weight | Number (0-1) | 0.03 | Weight of OI liquidity metrics |
| OiAnomalyWeight | OI anomaly weight | Number (0-1) | 0.02 | Weight of OI anomaly detection |

### Adjustment Parameters (12 columns)
Dynamic weight adjustment configuration:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| WeightLearningRate | Weight learning rate | Number (0-1) | 0.01 | Speed of weight adaptation |
| WeightDecayFactor | Weight decay factor | Number (0-1) | 0.95 | Exponential decay for historical data |
| WeightSmoothingFactor | Weight smoothing factor | Number (0-1) | 0.15 | Smoothing for weight changes |
| MinFactorWeight | Minimum factor weight | Number (0-1) | 0.05 | Minimum allowed factor weight |
| MaxFactorWeight | Maximum factor weight | Number (0-1) | 0.50 | Maximum allowed factor weight |
| WeightRebalanceFreq | Weight rebalance frequency | Integer (seconds) | 300 | How often to rebalance weights |
| PerformanceThreshold | Performance threshold | Number (0-1) | 0.6 | Threshold for performance-based adjustment |
| CorrelationThreshold | Correlation threshold | Number (0-1) | 0.7 | Threshold for factor correlation |
| DiversificationBonus | Diversification bonus | Number (>1) | 1.1 | Bonus for factor diversification |
| VolatilityAdjustment | Volatility adjustment factor | Number | 1.15 | Adjustment factor for high volatility |
| TrendAdjustment | Trend adjustment factor | Number | 1.1 | Adjustment factor for trending markets |
| RegimeAdjustment | Regime adjustment factor | Number | 1.2 | Adjustment factor for regime changes |

## Factor Parameters Sheet (16 Columns)

Detailed configuration for individual factors in the dynamic weightage system:

| Column | Description | Valid Options | Default | Notes |
|--------|-------------|--------------|---------|-------|
| FactorName | Factor identifier | OI_CURRENT, COI_MOMENTUM, DELTA_EXPOSURE, etc. | Required | Unique name for the factor |
| FactorType | Factor category | OI, COI, GREEK, MARKET, PERFORMANCE | Required | Category classification |
| BaseWeight | Base weight for factor | Number (0-1) | Varies | Starting weight for the factor |
| MinWeight | Minimum weight | Number (0-1) | 0.05 | Minimum allowed weight |
| MaxWeight | Maximum weight | Number (0-1) | 0.50 | Maximum allowed weight |
| LookbackPeriod | Lookback period | Integer | 5 | Periods for factor calculation |
| SmoothingFactor | Smoothing factor | Number (0-1) | 0.2 | Smoothing for factor values |
| ThresholdType | Threshold type | ABSOLUTE, PERCENTAGE, PERCENTILE | ABSOLUTE | Type of threshold |
| ThresholdValue | Threshold value | Number | Varies | Threshold for factor activation |
| NormalizationMethod | Normalization method | ZSCORE, MINMAX, NONE | ZSCORE | Method to normalize factor values |
| OutlierHandling | Outlier handling | WINSORIZE, CLIP, NONE | WINSORIZE | How to handle outliers |
| SeasonalAdjustment | Seasonal adjustment | YES, NO | NO | Apply seasonal adjustments |
| VolatilityAdjustment | Volatility adjustment | YES, NO | YES | Adjust for volatility |
| TrendAdjustment | Trend adjustment | YES, NO | YES | Adjust for trend |
| RegimeAdjustment | Regime adjustment | YES, NO | YES | Adjust for market regime |
| PerformanceTracking | Performance tracking | YES, NO | YES | Track factor performance |

## Enhanced OI Strategy Examples

### Example 1: Basic Enhanced OI Strategy

**GeneralParameter Configuration:**
```
StrategyName: ENHANCED_NIFTY_MAXOI_1
Index: NIFTY
Timeframe: 3
StartTime: 091600
EndTime: 152000
OiMethod: MAXOI_1
EnableDynamicWeights: YES
WeightAdjustmentPeriod: 20
LearningRate: 0.01
```

**LegParameter Configuration:**
```
LegID: CE_LEG_1
Instrument: CE
Transaction: SELL
StrikeMethod: MAXOI_1
LegOiWeight: 0.4
DeltaWeight: 0.25
GreekRebalanceFreq: 300
```

### Example 2: Advanced Multi-Factor Strategy

**GeneralParameter Configuration:**
```
StrategyName: ADVANCED_OI_MULTI_FACTOR
Index: BANKNIFTY
Timeframe: 6
OiMethod: MAXCOI_2
CoiBasedOn: TODAY_OPEN
OiDistributionAnalysis: YES
OiTrendAnalysis: YES
EnableDynamicWeights: YES
VolatilityAdjustment: YES
RegimeAdjustment: YES
```

**Dynamic Weightage Configuration:**
```
OiFactorWeight: 0.40
CoiFactorWeight: 0.30
GreekFactorWeight: 0.20
MarketFactorWeight: 0.10
WeightLearningRate: 0.015
CorrelationThreshold: 0.65
```

## Parameter Relationships and Dependencies

### Critical Parameter Relationships

1. **Timeframe Dependencies**:
   - `Timeframe` must be multiple of 3 (hard requirement)
   - `OiRecheckInterval` should be >= `Timeframe * 60` seconds
   - `WeightRebalanceFreq` should be >= `OiRecheckInterval`

2. **Weight Constraints**:
   - Sum of base factor weights should equal 1.0
   - `MinWeight` < `BaseWeight` < `MaxWeight` for all factors
   - `WeightDecayFactor` should be between 0.9 and 0.99

3. **OI Analysis Dependencies**:
   - `OiConcentrationThreshold` affects `OiDistributionAnalysis`
   - `OiMomentumPeriod` affects `OiTrendAnalysis`
   - `StrikeCount` affects all OI-based calculations

### Performance Optimization Guidelines

1. **For High-Frequency Strategies**:
   - Set `OiRecheckInterval` to 60-120 seconds
   - Use `WeightAdjustmentPeriod` of 10-15
   - Enable `VolatilityAdjustment` and `TrendAdjustment`

2. **For Conservative Strategies**:
   - Set `OiRecheckInterval` to 300-600 seconds
   - Use `WeightAdjustmentPeriod` of 30-50
   - Focus on `OiConcentrationAnalysis` and `OiLiquidityFilter`

3. **For Volatile Markets**:
   - Increase `GreekRebalanceFreq` to 180-240 seconds
   - Set higher `CorrelationThreshold` (0.75-0.8)
   - Enable all adjustment mechanisms