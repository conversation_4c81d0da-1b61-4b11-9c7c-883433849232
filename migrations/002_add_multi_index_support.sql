-- Enterprise GPU Backtester - Database Migration Script
-- Version: 002
-- Description: Add multi-index support and enhanced features
-- Created: June 6, 2025

USE gpu_backtester;

-- Add multi-index support to backtests
ALTER TABLE backtests 
ADD COLUMN indices JSON COMMENT 'Array of indices for multi-index backtests',
ADD COLUMN is_multi_index BOOLEAN DEFAULT FALSE;

-- Create index metadata table
CREATE TABLE IF NOT EXISTS index_metadata (
    id INT PRIMARY KEY AUTO_INCREMENT,
    index_code VARCHAR(20) UNIQUE NOT NULL,
    index_name VARCHAR(100) NOT NULL,
    lot_size INT NOT NULL,
    tick_size DECIMAL(10,2) NOT NULL,
    has_weekly_expiry BOOLEAN DEFAULT FALSE,
    has_monthly_expiry BOOLEAN DEFAULT TRUE,
    currency VARCHAR(3) DEFAULT 'INR',
    exchange VARCHAR(10) DEFAULT 'NSE',
    is_active BOOLEAN DEFAULT TRUE,
    data_start_date DATE,
    data_end_date DATE,
    total_rows BIGINT DEFAULT 0,
    last_updated TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert index metadata
INSERT IGNORE INTO index_metadata (index_code, index_name, lot_size, tick_size, has_weekly_expiry, has_monthly_expiry) VALUES
('NIFTY', 'NIFTY 50', 50, 50, TRUE, TRUE),
('BANKNIFTY', 'Bank NIFTY', 25, 100, FALSE, TRUE),
('FINNIFTY', 'NIFTY Financial Services', 40, 50, TRUE, TRUE),
('MIDCPNIFTY', 'NIFTY Midcap Select', 75, 25, FALSE, TRUE),
('SENSEX', 'BSE SENSEX', 10, 100, TRUE, TRUE),
('BANKEX', 'BSE BANKEX', 30, 50, FALSE, TRUE);

-- Add template management
CREATE TABLE IF NOT EXISTS strategy_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    strategy_type_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_data JSON NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    usage_count INT DEFAULT 0,
    FOREIGN KEY (strategy_type_id) REFERENCES strategy_types(id),
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_strategy_type (strategy_type_id),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    metric_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(20,4),
    tags JSON,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric_type (metric_type),
    INDEX idx_recorded_at (recorded_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add backtest comparison table
CREATE TABLE IF NOT EXISTS backtest_comparisons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    user_id INT NOT NULL,
    backtest_ids JSON NOT NULL COMMENT 'Array of backtest IDs to compare',
    comparison_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add alert configurations
CREATE TABLE IF NOT EXISTS alert_configurations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    alert_type ENUM('backtest_complete', 'backtest_failed', 'high_drawdown', 'system_issue') NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    threshold_value DECIMAL(20,4),
    notification_channels JSON DEFAULT '["email", "sms"]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_alert (user_id, alert_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add data quality tracking
CREATE TABLE IF NOT EXISTS data_quality_checks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    index_code VARCHAR(20) NOT NULL,
    check_date DATE NOT NULL,
    total_records BIGINT,
    missing_records INT DEFAULT 0,
    duplicate_records INT DEFAULT 0,
    invalid_records INT DEFAULT 0,
    quality_score DECIMAL(5,2),
    issues JSON,
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_index_date (index_code, check_date),
    INDEX idx_check_date (check_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add WebSocket connection tracking
CREATE TABLE IF NOT EXISTS websocket_connections (
    connection_id VARCHAR(64) PRIMARY KEY,
    user_id INT,
    client_ip VARCHAR(45),
    connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    disconnected_at TIMESTAMP NULL,
    last_ping_at TIMESTAMP NULL,
    subscriptions JSON,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_connected_at (connected_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create materialized view for backtest statistics
CREATE TABLE IF NOT EXISTS mv_backtest_statistics (
    strategy_type VARCHAR(10) PRIMARY KEY,
    total_backtests INT DEFAULT 0,
    avg_execution_time DECIMAL(10,2),
    avg_win_rate DECIMAL(5,2),
    avg_sharpe_ratio DECIMAL(10,4),
    best_sharpe_ratio DECIMAL(10,4),
    worst_drawdown DECIMAL(20,2),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create scheduled tasks table
CREATE TABLE IF NOT EXISTS scheduled_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_name VARCHAR(100) UNIQUE NOT NULL,
    task_type ENUM('etl', 'cleanup', 'report', 'maintenance') NOT NULL,
    schedule_cron VARCHAR(100) NOT NULL,
    is_enabled BOOLEAN DEFAULT TRUE,
    last_run_at TIMESTAMP NULL,
    next_run_at TIMESTAMP NULL,
    last_status ENUM('success', 'failed', 'running') NULL,
    last_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default scheduled tasks
INSERT IGNORE INTO scheduled_tasks (task_name, task_type, schedule_cron, is_enabled) VALUES
('daily_etl', 'etl', '30 18 * * *', TRUE),
('cleanup_old_sessions', 'cleanup', '0 2 * * *', TRUE),
('generate_daily_report', 'report', '0 8 * * *', TRUE),
('data_quality_check', 'maintenance', '0 3 * * *', TRUE);

-- Add API rate limiting
CREATE TABLE IF NOT EXISTS api_rate_limits (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    endpoint_pattern VARCHAR(255),
    requests_per_minute INT DEFAULT 60,
    requests_per_hour INT DEFAULT 1000,
    requests_per_day INT DEFAULT 10000,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_endpoint (user_id, endpoint_pattern)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Update migration history
INSERT INTO migration_history (version, name) VALUES (2, 'add_multi_index_support');