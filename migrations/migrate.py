#!/usr/bin/env python3
"""
Database Migration Tool for Enterprise GPU Backtester
Created: June 6, 2025

Usage:
    python migrate.py up              # Run all pending migrations
    python migrate.py up --version 2  # Migrate up to specific version
    python migrate.py down            # Rollback last migration
    python migrate.py status          # Show migration status
"""

import os
import sys
import argparse
import mysql.connector
from pathlib import Path
from datetime import datetime

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'your_password',  # Update this
    'database': 'gpu_backtester',
    'raise_on_warnings': True
}

class MigrationManager:
    def __init__(self):
        self.migrations_dir = Path(__file__).parent
        self.connection = None
        
    def connect(self):
        """Connect to database"""
        try:
            self.connection = mysql.connector.connect(**DB_CONFIG)
            print("✅ Connected to database")
            return True
        except mysql.connector.Error as err:
            print(f"❌ Database connection failed: {err}")
            return False
    
    def disconnect(self):
        """Disconnect from database"""
        if self.connection:
            self.connection.close()
            print("👋 Disconnected from database")
    
    def get_migration_files(self):
        """Get all migration files sorted by version"""
        files = []
        for file in self.migrations_dir.glob("*.sql"):
            if file.name.startswith(('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')):
                parts = file.name.split('_', 1)
                version = int(parts[0])
                name = parts[1].replace('.sql', '')
                files.append({
                    'version': version,
                    'name': name,
                    'file': file
                })
        return sorted(files, key=lambda x: x['version'])
    
    def get_current_version(self):
        """Get current database version"""
        cursor = self.connection.cursor()
        try:
            # Check if migration_history table exists
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = %s 
                AND table_name = 'migration_history'
            """, (DB_CONFIG['database'],))
            
            if cursor.fetchone()[0] == 0:
                print("📌 No migration history found. Starting from version 0.")
                return 0
            
            # Get latest version
            cursor.execute("SELECT MAX(version) FROM migration_history")
            result = cursor.fetchone()
            return result[0] if result[0] else 0
            
        finally:
            cursor.close()
    
    def run_migration(self, migration):
        """Run a single migration"""
        print(f"\n🚀 Running migration {migration['version']}: {migration['name']}")
        
        cursor = self.connection.cursor()
        try:
            # Read migration file
            with open(migration['file'], 'r') as f:
                sql_commands = f.read()
            
            # Split by semicolon and execute each command
            commands = [cmd.strip() for cmd in sql_commands.split(';') if cmd.strip()]
            
            for i, command in enumerate(commands, 1):
                if command:
                    try:
                        cursor.execute(command)
                        print(f"   ✓ Command {i}/{len(commands)} executed")
                    except mysql.connector.Error as err:
                        print(f"   ✗ Command {i} failed: {err}")
                        raise
            
            self.connection.commit()
            print(f"✅ Migration {migration['version']} completed successfully")
            return True
            
        except Exception as e:
            self.connection.rollback()
            print(f"❌ Migration {migration['version']} failed: {e}")
            return False
        finally:
            cursor.close()
    
    def migrate_up(self, target_version=None):
        """Run pending migrations up to target version"""
        current_version = self.get_current_version()
        migrations = self.get_migration_files()
        
        # Filter migrations to run
        pending = [m for m in migrations if m['version'] > current_version]
        if target_version:
            pending = [m for m in pending if m['version'] <= target_version]
        
        if not pending:
            print("✅ Database is up to date!")
            return
        
        print(f"📊 Current version: {current_version}")
        print(f"📋 Pending migrations: {len(pending)}")
        
        for migration in pending:
            if self.run_migration(migration):
                # Record in migration history
                cursor = self.connection.cursor()
                cursor.execute(
                    "INSERT INTO migration_history (version, name) VALUES (%s, %s)",
                    (migration['version'], migration['name'])
                )
                self.connection.commit()
                cursor.close()
            else:
                print("⛔ Migration failed. Stopping.")
                break
    
    def migrate_down(self):
        """Rollback last migration"""
        print("❌ Rollback not implemented. Please restore from backup.")
        # In production, you would have down migrations
    
    def show_status(self):
        """Show migration status"""
        current_version = self.get_current_version()
        migrations = self.get_migration_files()
        
        print("\n📊 Migration Status")
        print("=" * 60)
        print(f"Current Version: {current_version}")
        print("\nAvailable Migrations:")
        
        cursor = self.connection.cursor()
        cursor.execute("SELECT version, name, applied_at FROM migration_history ORDER BY version")
        applied = {row[0]: row[2] for row in cursor.fetchall()}
        cursor.close()
        
        for migration in migrations:
            status = "✅ Applied" if migration['version'] in applied else "⏳ Pending"
            applied_at = applied.get(migration['version'], '-')
            print(f"  {migration['version']:3d}. {migration['name']:40} {status:12} {applied_at}")

def main():
    parser = argparse.ArgumentParser(description='Database Migration Tool')
    parser.add_argument('command', choices=['up', 'down', 'status'], 
                       help='Migration command')
    parser.add_argument('--version', type=int, 
                       help='Target version for up migration')
    args = parser.parse_args()
    
    manager = MigrationManager()
    
    if not manager.connect():
        sys.exit(1)
    
    try:
        if args.command == 'up':
            manager.migrate_up(args.version)
        elif args.command == 'down':
            manager.migrate_down()
        elif args.command == 'status':
            manager.show_status()
    finally:
        manager.disconnect()

if __name__ == "__main__":
    main()