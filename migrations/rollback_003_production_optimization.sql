-- Rollback: 003_production_optimization.sql
-- Description: Remove production optimizations
-- Date: June 6, 2025
-- WARNING: This rollback may cause data loss for partitioned tables

-- Drop events
DROP EVENT IF EXISTS event_system_health_check;
DROP EVENT IF EXISTS event_archive_old_data;
DROP EVENT IF EXISTS event_refresh_daily_stats;

-- Drop stored procedures
DROP PROCEDURE IF EXISTS sp_system_health_check;
DROP PROCEDURE IF EXISTS sp_archive_old_data;
DROP PROCEDURE IF EXISTS sp_refresh_daily_stats;

-- Drop new tables
DROP TABLE IF EXISTS audit_trail;
DROP TABLE IF EXISTS system_health_checks;
DROP TABLE IF EXISTS dashboard_metrics;
DROP TABLE IF EXISTS mv_daily_user_stats;

-- Remove partitioning (WARNING: This will rebuild the entire table)
-- Only execute if you're sure about removing partitions
ALTER TABLE backtest_results REMOVE PARTITIONING;
ALTER TABLE trade_logs REMOVE PARTITIONING;

-- Drop added indexes (be careful, some might be needed)
DROP INDEX IF EXISTS idx_backtest_user_strategy ON backtests;
DROP INDEX IF EXISTS idx_backtest_date_range ON backtests;
DROP INDEX IF EXISTS idx_trade_logs_composite ON trade_logs;
DROP INDEX IF EXISTS idx_user_sessions ON user_sessions;

-- Drop reporting user
DROP USER IF EXISTS 'reporting_user'@'%';

-- Reset performance settings to defaults
SET GLOBAL innodb_buffer_pool_size = 134217728; -- 128MB default
SET GLOBAL innodb_log_file_size = 50331648; -- 48MB default
SET GLOBAL max_connections = 151; -- default
SET GLOBAL query_cache_size = 0; -- disabled by default
SET GLOBAL thread_cache_size = 0; -- default

-- Log rollback
INSERT INTO system_logs (action, details, created_at)
VALUES ('MIGRATION_ROLLBACK', 'Rolled back 003_production_optimization.sql', NOW());