-- Enterprise GPU Backtester - Database Migration Script
-- Version: 001
-- Description: Create initial database schema
-- Created: June 6, 2025

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS gpu_backtester;
USE gpu_backtester;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    INDEX idx_email (email),
    INDEX idx_phone (phone)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Authentication sessions
CREATE TABLE IF NOT EXISTS auth_sessions (
    id VARCHAR(64) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- OTP verification
CREATE TABLE IF NOT EXISTS otp_verifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(20) NOT NULL,
    otp_code VARCHAR(6) NOT NULL,
    purpose ENUM('login', 'register', 'reset') DEFAULT 'login',
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    attempts INT DEFAULT 0,
    INDEX idx_phone_otp (phone, otp_code),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Strategy types
CREATE TABLE IF NOT EXISTS strategy_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(10) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default strategy types
INSERT IGNORE INTO strategy_types (code, name, description) VALUES
('TBS', 'Trade Builder Strategy', 'Build custom trading strategies with multiple parameters'),
('TV', 'TradingView', 'Execute trades based on TradingView signals'),
('ORB', 'Opening Range Breakout', 'Trade breakouts from opening range'),
('OI', 'Open Interest', 'Trade based on open interest analysis');

-- Backtests table
CREATE TABLE IF NOT EXISTS backtests (
    id VARCHAR(36) PRIMARY KEY,
    user_id INT NOT NULL,
    strategy_type_id INT NOT NULL,
    index_name VARCHAR(50) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('queued', 'running', 'completed', 'failed', 'cancelled') DEFAULT 'queued',
    progress INT DEFAULT 0,
    gpu_workers INT DEFAULT 8,
    max_gpu BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    execution_time_seconds DECIMAL(10,2) NULL,
    error_message TEXT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (strategy_type_id) REFERENCES strategy_types(id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Backtest files
CREATE TABLE IF NOT EXISTS backtest_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backtest_id VARCHAR(36) NOT NULL,
    file_type ENUM('portfolio', 'strategy', 'signal', 'result') NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (backtest_id) REFERENCES backtests(id) ON DELETE CASCADE,
    INDEX idx_backtest_id (backtest_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Backtest results summary
CREATE TABLE IF NOT EXISTS backtest_results (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backtest_id VARCHAR(36) UNIQUE NOT NULL,
    total_trades INT DEFAULT 0,
    winning_trades INT DEFAULT 0,
    losing_trades INT DEFAULT 0,
    win_rate DECIMAL(5,2) DEFAULT 0,
    total_pnl DECIMAL(20,2) DEFAULT 0,
    max_drawdown DECIMAL(20,2) DEFAULT 0,
    sharpe_ratio DECIMAL(10,4) DEFAULT 0,
    sortino_ratio DECIMAL(10,4) DEFAULT 0,
    calmar_ratio DECIMAL(10,4) DEFAULT 0,
    max_consecutive_wins INT DEFAULT 0,
    max_consecutive_losses INT DEFAULT 0,
    avg_trade_pnl DECIMAL(20,2) DEFAULT 0,
    best_trade_pnl DECIMAL(20,2) DEFAULT 0,
    worst_trade_pnl DECIMAL(20,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (backtest_id) REFERENCES backtests(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- GPU usage tracking
CREATE TABLE IF NOT EXISTS gpu_usage (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backtest_id VARCHAR(36),
    gpu_id INT NOT NULL,
    gpu_name VARCHAR(100),
    utilization_percent DECIMAL(5,2),
    memory_used_mb INT,
    memory_total_mb INT,
    temperature_c INT,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (backtest_id) REFERENCES backtests(id) ON DELETE CASCADE,
    INDEX idx_backtest_id (backtest_id),
    INDEX idx_recorded_at (recorded_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- API usage tracking
CREATE TABLE IF NOT EXISTS api_usage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INT,
    response_time_ms INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_body TEXT,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_endpoint (endpoint),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- System settings
CREATE TABLE IF NOT EXISTS system_settings (
    key_name VARCHAR(100) PRIMARY KEY,
    value_text TEXT,
    value_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default settings
INSERT IGNORE INTO system_settings (key_name, value_text, value_type, description, is_public) VALUES
('max_concurrent_backtests', '10', 'number', 'Maximum number of concurrent backtests allowed', FALSE),
('default_gpu_workers', '8', 'number', 'Default number of GPU workers', TRUE),
('backtest_timeout_minutes', '60', 'number', 'Maximum time allowed for a single backtest', FALSE),
('maintenance_mode', 'false', 'boolean', 'System maintenance mode flag', TRUE),
('allowed_indices', '["NIFTY","BANKNIFTY","FINNIFTY","MIDCPNIFTY","SENSEX","BANKEX"]', 'json', 'List of allowed indices', TRUE);

-- User preferences
CREATE TABLE IF NOT EXISTS user_preferences (
    user_id INT PRIMARY KEY,
    default_index VARCHAR(50) DEFAULT 'NIFTY',
    default_gpu_workers INT DEFAULT 8,
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT TRUE,
    theme VARCHAR(20) DEFAULT 'light',
    language VARCHAR(10) DEFAULT 'en',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create indexes for performance
CREATE INDEX idx_backtests_date_range ON backtests(start_date, end_date);
CREATE INDEX idx_backtests_user_status ON backtests(user_id, status);
CREATE INDEX idx_api_usage_daily ON api_usage(DATE(created_at), endpoint);

-- Create views for reporting
CREATE OR REPLACE VIEW v_user_backtest_summary AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    COUNT(DISTINCT b.id) as total_backtests,
    COUNT(DISTINCT CASE WHEN b.status = 'completed' THEN b.id END) as completed_backtests,
    COUNT(DISTINCT CASE WHEN b.status = 'failed' THEN b.id END) as failed_backtests,
    AVG(b.execution_time_seconds) as avg_execution_time,
    MAX(b.created_at) as last_backtest_date
FROM users u
LEFT JOIN backtests b ON u.id = b.user_id
GROUP BY u.id;

CREATE OR REPLACE VIEW v_daily_usage_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(*) as total_requests,
    AVG(response_time_ms) as avg_response_time,
    COUNT(CASE WHEN status_code >= 500 THEN 1 END) as server_errors,
    COUNT(CASE WHEN status_code >= 400 AND status_code < 500 THEN 1 END) as client_errors
FROM api_usage
GROUP BY DATE(created_at);

-- Add version tracking
CREATE TABLE IF NOT EXISTS migration_history (
    version INT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO migration_history (version, name) VALUES (1, 'create_initial_schema');