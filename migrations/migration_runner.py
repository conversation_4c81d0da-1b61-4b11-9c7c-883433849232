"""
Database Migration Runner
Handles database migrations for production deployment
"""

import os
import sys
import mysql.connector
import hashlib
from datetime import datetime
from typing import List, Dict, Tuple
import argparse
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MigrationRunner:
    def __init__(self, host='localhost', user='root', password='', database='gpu_backtester'):
        self.db_config = {
            'host': host,
            'user': user,
            'password': password,
            'database': database
        }
        self.migrations_dir = os.path.dirname(os.path.abspath(__file__))
        
    def connect(self):
        """Connect to MySQL database"""
        return mysql.connector.connect(**self.db_config)
    
    def init_migration_table(self):
        """Create migration tracking table if it doesn't exist"""
        conn = self.connect()
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    version VARCHAR(255) NOT NULL UNIQUE,
                    checksum VARCHAR(64) NOT NULL,
                    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    execution_time_ms INT,
                    status ENUM('SUCCESS', 'FAILED', 'PENDING') DEFAULT 'PENDING',
                    error_message TEXT,
                    KEY idx_version (version),
                    KEY idx_status (status)
                )
            """)
            conn.commit()
            logger.info("Migration table initialized")
        finally:
            cursor.close()
            conn.close()
    
    def get_migration_files(self) -> List[str]:
        """Get all SQL migration files in order"""
        files = []
        for filename in sorted(os.listdir(self.migrations_dir)):
            if filename.endswith('.sql') and filename[0].isdigit():
                files.append(filename)
        return files
    
    def calculate_checksum(self, filepath: str) -> str:
        """Calculate MD5 checksum of migration file"""
        with open(filepath, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    
    def is_migration_applied(self, version: str) -> bool:
        """Check if migration has already been applied"""
        conn = self.connect()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT status FROM schema_migrations WHERE version = %s",
                (version,)
            )
            result = cursor.fetchone()
            return result is not None and result[0] == 'SUCCESS'
        finally:
            cursor.close()
            conn.close()
    
    def verify_checksum(self, version: str, current_checksum: str) -> bool:
        """Verify migration file hasn't changed"""
        conn = self.connect()
        cursor = conn.cursor()
        
        try:
            cursor.execute(
                "SELECT checksum FROM schema_migrations WHERE version = %s",
                (version,)
            )
            result = cursor.fetchone()
            if result:
                return result[0] == current_checksum
            return True
        finally:
            cursor.close()
            conn.close()
    
    def execute_migration(self, filepath: str, version: str) -> Tuple[bool, str]:
        """Execute a single migration file"""
        conn = self.connect()
        cursor = conn.cursor()
        
        start_time = datetime.now()
        checksum = self.calculate_checksum(filepath)
        
        # Check checksum
        if not self.verify_checksum(version, checksum):
            return False, "Checksum mismatch - migration file has been modified"
        
        try:
            # Record migration start
            cursor.execute("""
                INSERT INTO schema_migrations (version, checksum, status)
                VALUES (%s, %s, 'PENDING')
                ON DUPLICATE KEY UPDATE status = 'PENDING'
            """, (version, checksum))
            conn.commit()
            
            # Read and execute migration
            with open(filepath, 'r') as f:
                sql_content = f.read()
            
            # Split by delimiter for stored procedures
            statements = []
            if 'DELIMITER' in sql_content:
                # Handle stored procedures with custom delimiter
                current_statement = []
                delimiter = ';'
                
                for line in sql_content.split('\n'):
                    if line.strip().startswith('DELIMITER'):
                        if current_statement:
                            statements.append('\n'.join(current_statement))
                            current_statement = []
                        delimiter = line.strip().split()[1]
                    elif line.strip().endswith(delimiter):
                        current_statement.append(line.rstrip(delimiter))
                        statements.append('\n'.join(current_statement))
                        current_statement = []
                    else:
                        current_statement.append(line)
                
                if current_statement:
                    statements.append('\n'.join(current_statement))
            else:
                # Regular SQL statements
                statements = [s.strip() for s in sql_content.split(';') if s.strip()]
            
            # Execute each statement
            for statement in statements:
                if statement.strip():
                    cursor.execute(statement)
            
            # Calculate execution time
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            # Update migration status
            cursor.execute("""
                UPDATE schema_migrations 
                SET status = 'SUCCESS', 
                    execution_time_ms = %s,
                    executed_at = NOW()
                WHERE version = %s
            """, (execution_time, version))
            
            conn.commit()
            logger.info(f"✓ Migration {version} completed in {execution_time}ms")
            return True, "Success"
            
        except Exception as e:
            # Rollback and record error
            conn.rollback()
            error_msg = str(e)
            
            cursor.execute("""
                UPDATE schema_migrations 
                SET status = 'FAILED', 
                    error_message = %s,
                    executed_at = NOW()
                WHERE version = %s
            """, (error_msg, version))
            conn.commit()
            
            logger.error(f"✗ Migration {version} failed: {error_msg}")
            return False, error_msg
            
        finally:
            cursor.close()
            conn.close()
    
    def run_migrations(self, target_version: str = None) -> Dict[str, any]:
        """Run all pending migrations up to target version"""
        self.init_migration_table()
        
        migration_files = self.get_migration_files()
        results = {
            'total': len(migration_files),
            'executed': 0,
            'skipped': 0,
            'failed': 0,
            'migrations': []
        }
        
        logger.info(f"Found {len(migration_files)} migration files")
        
        for filename in migration_files:
            version = filename.replace('.sql', '')
            
            # Check if we've reached target version
            if target_version and version > target_version:
                logger.info(f"Reached target version {target_version}, stopping")
                break
            
            filepath = os.path.join(self.migrations_dir, filename)
            
            # Check if already applied
            if self.is_migration_applied(version):
                logger.info(f"⊡ Skipping {version} (already applied)")
                results['skipped'] += 1
                results['migrations'].append({
                    'version': version,
                    'status': 'skipped',
                    'message': 'Already applied'
                })
                continue
            
            # Execute migration
            logger.info(f"→ Executing {version}...")
            success, message = self.execute_migration(filepath, version)
            
            if success:
                results['executed'] += 1
                results['migrations'].append({
                    'version': version,
                    'status': 'success',
                    'message': message
                })
            else:
                results['failed'] += 1
                results['migrations'].append({
                    'version': version,
                    'status': 'failed',
                    'message': message
                })
                # Stop on failure
                logger.error("Migration failed, stopping execution")
                break
        
        return results
    
    def rollback_migration(self, version: str) -> Tuple[bool, str]:
        """Rollback a specific migration (if rollback script exists)"""
        rollback_file = os.path.join(
            self.migrations_dir, 
            f"rollback_{version}.sql"
        )
        
        if not os.path.exists(rollback_file):
            return False, f"No rollback script found for version {version}"
        
        # Execute rollback
        success, message = self.execute_migration(rollback_file, f"rollback_{version}")
        
        if success:
            # Mark original migration as rolled back
            conn = self.connect()
            cursor = conn.cursor()
            try:
                cursor.execute("""
                    UPDATE schema_migrations 
                    SET status = 'ROLLED_BACK' 
                    WHERE version = %s
                """, (version,))
                conn.commit()
            finally:
                cursor.close()
                conn.close()
        
        return success, message
    
    def get_migration_status(self) -> List[Dict]:
        """Get status of all migrations"""
        conn = self.connect()
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT version, status, executed_at, execution_time_ms, error_message
                FROM schema_migrations
                ORDER BY version
            """)
            return cursor.fetchall()
        finally:
            cursor.close()
            conn.close()
    
    def create_backup(self, backup_dir: str = '/backup/db'):
        """Create database backup before migrations"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = os.path.join(backup_dir, f"gpu_backtester_{timestamp}.sql")
        
        logger.info(f"Creating backup at {backup_file}")
        
        # Create backup using mysqldump
        import subprocess
        
        cmd = [
            'mysqldump',
            f'-h{self.db_config["host"]}',
            f'-u{self.db_config["user"]}',
            f'-p{self.db_config["password"]}',
            self.db_config['database']
        ]
        
        os.makedirs(backup_dir, exist_ok=True)
        
        with open(backup_file, 'w') as f:
            result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE)
            
        if result.returncode == 0:
            logger.info(f"✓ Backup created successfully")
            return backup_file
        else:
            logger.error(f"✗ Backup failed: {result.stderr.decode()}")
            return None


def main():
    """Main function for CLI usage"""
    parser = argparse.ArgumentParser(description='Database Migration Runner')
    parser.add_argument('command', choices=['up', 'down', 'status', 'backup'],
                      help='Migration command')
    parser.add_argument('--version', help='Target version for up/down')
    parser.add_argument('--host', default='localhost', help='Database host')
    parser.add_argument('--user', default='root', help='Database user')
    parser.add_argument('--password', default='', help='Database password')
    parser.add_argument('--database', default='gpu_backtester', help='Database name')
    parser.add_argument('--backup-dir', default='/backup/db', help='Backup directory')
    
    args = parser.parse_args()
    
    runner = MigrationRunner(
        host=args.host,
        user=args.user,
        password=args.password,
        database=args.database
    )
    
    if args.command == 'up':
        # Create backup first
        backup_file = runner.create_backup(args.backup_dir)
        if not backup_file:
            logger.error("Backup failed, aborting migration")
            sys.exit(1)
        
        # Run migrations
        results = runner.run_migrations(args.version)
        
        print("\n=== Migration Summary ===")
        print(f"Total migrations: {results['total']}")
        print(f"Executed: {results['executed']}")
        print(f"Skipped: {results['skipped']}")
        print(f"Failed: {results['failed']}")
        
        if results['failed'] > 0:
            print("\n⚠️  Some migrations failed!")
            sys.exit(1)
        else:
            print("\n✅ All migrations completed successfully!")
    
    elif args.command == 'down':
        if not args.version:
            print("Error: --version required for rollback")
            sys.exit(1)
        
        success, message = runner.rollback_migration(args.version)
        if success:
            print(f"✅ Rollback successful: {message}")
        else:
            print(f"❌ Rollback failed: {message}")
            sys.exit(1)
    
    elif args.command == 'status':
        migrations = runner.get_migration_status()
        
        print("\n=== Migration Status ===")
        print(f"{'Version':<30} {'Status':<15} {'Executed At':<20} {'Time (ms)':<10}")
        print("-" * 80)
        
        for m in migrations:
            executed = m['executed_at'].strftime('%Y-%m-%d %H:%M:%S') if m['executed_at'] else 'N/A'
            time_ms = m['execution_time_ms'] or 'N/A'
            print(f"{m['version']:<30} {m['status']:<15} {executed:<20} {time_ms:<10}")
    
    elif args.command == 'backup':
        backup_file = runner.create_backup(args.backup_dir)
        if backup_file:
            print(f"✅ Backup created: {backup_file}")
        else:
            print("❌ Backup failed")
            sys.exit(1)


if __name__ == "__main__":
    main()