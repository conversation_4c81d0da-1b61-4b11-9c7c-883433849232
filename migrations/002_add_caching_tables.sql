-- Migration: 002_add_caching_tables.sql
-- Description: Add tables for caching statistics and performance tracking
-- Date: June 6, 2025
-- Author: Backend Team

-- Create cache statistics table
CREATE TABLE IF NOT EXISTS cache_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    cache_type VARCHAR(50) NOT NULL,
    hits BIGINT DEFAULT 0,
    misses BIGINT DEFAULT 0,
    evictions BIGINT DEFAULT 0,
    hit_rate DECIMAL(5,2) DEFAULT 0.00,
    avg_response_time_ms DECIMAL(10,2),
    date DATE NOT NULL,
    hour INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    KEY idx_cache_stats_date (date, hour),
    KEY idx_cache_type (cache_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create query performance log
CREATE TABLE IF NOT EXISTS query_performance_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    query_hash VARCHAR(64) NOT NULL,
    query_type VARCHAR(50) NOT NULL,
    execution_time_ms INT NOT NULL,
    rows_processed BIGINT,
    cache_hit BOOLEAN DEFAULT FALSE,
    gpu_utilized BOOLEAN DEFAULT FALSE,
    user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    KEY idx_query_perf_date (created_at),
    KEY idx_query_hash (query_hash),
    KEY idx_user_query (user_id, query_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create cache invalidation log
CREATE TABLE IF NOT EXISTS cache_invalidation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    cache_pattern VARCHAR(255) NOT NULL,
    invalidation_type ENUM('MANUAL', 'TTL', 'DATA_UPDATE', 'SYSTEM') NOT NULL,
    keys_affected INT DEFAULT 0,
    triggered_by VARCHAR(100),
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    KEY idx_invalidation_date (created_at),
    KEY idx_invalidation_type (invalidation_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add cache-related columns to existing tables
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS cache_tier ENUM('STANDARD', 'PREMIUM', 'ENTERPRISE') DEFAULT 'STANDARD',
ADD COLUMN IF NOT EXISTS cache_ttl_multiplier DECIMAL(3,1) DEFAULT 1.0;

-- Create view for cache performance monitoring
CREATE OR REPLACE VIEW vw_cache_performance AS
SELECT 
    cs.cache_type,
    cs.date,
    SUM(cs.hits) as total_hits,
    SUM(cs.misses) as total_misses,
    SUM(cs.evictions) as total_evictions,
    AVG(cs.hit_rate) as avg_hit_rate,
    AVG(cs.avg_response_time_ms) as avg_response_time
FROM cache_statistics cs
WHERE cs.date >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
GROUP BY cs.cache_type, cs.date
ORDER BY cs.date DESC, cs.cache_type;

-- Create stored procedure for cache cleanup
DELIMITER $$

CREATE PROCEDURE sp_cleanup_old_cache_logs()
BEGIN
    -- Delete old cache statistics (keep 30 days)
    DELETE FROM cache_statistics 
    WHERE date < DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY);
    
    -- Delete old query performance logs (keep 7 days)
    DELETE FROM query_performance_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Delete old invalidation logs (keep 14 days)
    DELETE FROM cache_invalidation_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 14 DAY);
    
    -- Log cleanup action
    INSERT INTO system_logs (action, details, created_at)
    VALUES ('CACHE_CLEANUP', CONCAT('Cleaned up old cache logs at ', NOW()), NOW());
END$$

DELIMITER ;

-- Schedule cleanup job (if event scheduler is enabled)
CREATE EVENT IF NOT EXISTS event_cleanup_cache_logs
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_DATE + INTERVAL 3 HOUR
DO CALL sp_cleanup_old_cache_logs();

-- Insert initial cache configuration
INSERT INTO system_configuration (config_key, config_value, description)
VALUES 
    ('cache.redis.enabled', 'true', 'Enable Redis caching'),
    ('cache.memory.enabled', 'true', 'Enable in-memory caching'),
    ('cache.ttl.market_data', '300', 'TTL for market data in seconds'),
    ('cache.ttl.atm_strikes', '60', 'TTL for ATM strikes in seconds'),
    ('cache.ttl.strategy_results', '3600', 'TTL for strategy results in seconds'),
    ('cache.warmup.enabled', 'true', 'Enable cache warmup on startup')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    updated_at = NOW();