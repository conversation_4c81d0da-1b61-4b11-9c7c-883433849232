# Database Migrations - Enterprise GPU Backtester

This directory contains database migration scripts for the Enterprise GPU Backtester application.

## Overview

The migration system manages database schema changes in a versioned, controlled manner. Each migration is numbered sequentially and contains SQL commands to update the database structure.

## Migration Files

### 001_create_initial_schema.sql
- Creates the initial database schema
- Sets up core tables: users, auth_sessions, backtests, results
- Establishes indexes and foreign key relationships
- Creates views for reporting

### 002_add_multi_index_support.sql
- Adds support for multiple indices in a single backtest
- Creates index metadata table
- Adds template management
- Implements performance tracking tables
- Sets up scheduled tasks

## Usage

### Running Migrations

```bash
# Run all pending migrations
python migrate.py up

# Run migrations up to a specific version
python migrate.py up --version 2

# Check migration status
python migrate.py status

# Rollback last migration (not implemented - use backup)
python migrate.py down
```

### Database Configuration

Update the database credentials in `migrate.py`:

```python
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'your_password',
    'database': 'gpu_backtester'
}
```

## Database Schema

### Core Tables

1. **users** - User accounts
   - Authentication details
   - Admin flags
   - Activity tracking

2. **auth_sessions** - Active user sessions
   - Session tokens
   - IP tracking
   - Expiration management

3. **backtests** - Backtest records
   - Strategy configuration
   - Execution status
   - Performance metrics

4. **backtest_results** - Detailed results
   - Trade statistics
   - P&L metrics
   - Risk ratios

5. **strategy_types** - Available strategies
   - TBS, TV, ORB, OI
   - Descriptions and metadata

### Supporting Tables

- **otp_verifications** - OTP authentication
- **backtest_files** - Uploaded/generated files
- **gpu_usage** - GPU utilization tracking
- **api_usage** - API call logging
- **system_settings** - Configuration values
- **user_preferences** - User-specific settings

### Multi-Index Features

- **index_metadata** - Index specifications
- **strategy_templates** - Reusable configurations
- **backtest_comparisons** - Compare multiple backtests
- **alert_configurations** - User notifications

### Monitoring Tables

- **performance_metrics** - System performance
- **data_quality_checks** - Data integrity
- **websocket_connections** - Real-time connections
- **scheduled_tasks** - Automated jobs

## Best Practices

1. **Always backup** before running migrations
2. **Test migrations** on a development database first
3. **Review SQL** before applying to production
4. **Document changes** in migration comments
5. **Never modify** applied migrations

## Adding New Migrations

1. Create a new file: `XXX_description.sql`
   - XXX = next version number (e.g., 003)
   - description = brief description (e.g., add_trading_calendar)

2. Include version tracking:
   ```sql
   INSERT INTO migration_history (version, name) 
   VALUES (3, 'add_trading_calendar');
   ```

3. Test thoroughly before committing

## Rollback Strategy

Currently, automatic rollback is not implemented. In case of issues:

1. Restore from database backup
2. Fix the migration script
3. Re-run migrations

## Production Deployment

1. **Backup database**
   ```bash
   mysqldump -u root -p gpu_backtester > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Run migrations**
   ```bash
   python migrate.py status  # Check current state
   python migrate.py up      # Apply migrations
   ```

3. **Verify**
   - Check application functionality
   - Verify data integrity
   - Monitor for errors

## Troubleshooting

### Common Issues

1. **Migration fails partway**
   - Check error message
   - Fix the issue
   - Restore from backup
   - Re-run migration

2. **Foreign key constraints**
   - Ensure proper order of operations
   - Temporarily disable foreign key checks if needed

3. **Duplicate key errors**
   - Check for existing data
   - Use `INSERT IGNORE` or `ON DUPLICATE KEY UPDATE`

## Future Enhancements

- [ ] Automatic rollback functionality
- [ ] Migration testing framework
- [ ] Schema validation
- [ ] Data migration support
- [ ] Migration performance optimization