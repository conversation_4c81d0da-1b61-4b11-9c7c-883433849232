-- Rollback: 002_add_caching_tables.sql
-- Description: Remove caching-related tables and modifications
-- Date: June 6, 2025

-- Drop views
DROP VIEW IF EXISTS vw_cache_performance;

-- Drop events
DROP EVENT IF EXISTS event_cleanup_cache_logs;

-- Drop stored procedures
DROP PROCEDURE IF EXISTS sp_cleanup_old_cache_logs;

-- Drop tables
DROP TABLE IF EXISTS cache_invalidation_log;
DROP TABLE IF EXISTS query_performance_log;
DROP TABLE IF EXISTS cache_statistics;

-- Remove added columns from users table
ALTER TABLE users 
DROP COLUMN IF EXISTS cache_tier,
DROP COLUMN IF EXISTS cache_ttl_multiplier;

-- Remove cache configuration entries
DELETE FROM system_configuration 
WHERE config_key IN (
    'cache.redis.enabled',
    'cache.memory.enabled',
    'cache.ttl.market_data',
    'cache.ttl.atm_strikes',
    'cache.ttl.strategy_results',
    'cache.warmup.enabled'
);

-- Log rollback
INSERT INTO system_logs (action, details, created_at)
VALUES ('MIGRATION_ROLLBACK', 'Rolled back 002_add_caching_tables.sql', NOW());