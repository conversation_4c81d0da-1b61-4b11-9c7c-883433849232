-- Migration: 003_production_optimization.sql
-- Description: Production optimizations and performance improvements
-- Date: June 6, 2025
-- Author: Backend Team

-- Optimize existing tables for production workload

-- 1. Add partitioning to large tables
ALTER TABLE backtest_results
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION pfuture VALUES LESS THAN MAXVALUE
);

ALTER TABLE trade_logs
PARTITION BY RANGE (TO_DAYS(trade_date)) (
    PARTITION p202401 VALUES LESS THAN (TO_DAYS('2024-02-01')),
    PARTITION p202402 VALUES LESS THAN (TO_DAYS('2024-03-01')),
    PARTITION p202403 VALUES LESS THAN (TO_DAYS('2024-04-01')),
    PARTITION p202404 VALUES LESS THAN (TO_DAYS('2024-05-01')),
    PARTITION p202405 VALUES LESS THAN (TO_DAYS('2024-06-01')),
    PARTITION p202406 VALUES LESS THAN (TO_DAYS('2024-07-01')),
    PARTITION pfuture VALUES LESS THAN MAXVALUE
);

-- 2. Add missing indexes for performance
CREATE INDEX IF NOT EXISTS idx_backtest_user_strategy ON backtests(user_id, strategy_type, status);
CREATE INDEX IF NOT EXISTS idx_backtest_date_range ON backtests(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_trade_logs_composite ON trade_logs(backtest_id, trade_date, symbol);
CREATE INDEX IF NOT EXISTS idx_user_sessions ON user_sessions(user_id, expires_at) WHERE is_active = 1;

-- 3. Create materialized views for reporting
CREATE TABLE IF NOT EXISTS mv_daily_user_stats (
    date DATE NOT NULL,
    user_id INT NOT NULL,
    total_backtests INT DEFAULT 0,
    successful_backtests INT DEFAULT 0,
    failed_backtests INT DEFAULT 0,
    total_trades INT DEFAULT 0,
    total_pnl DECIMAL(15,2) DEFAULT 0.00,
    avg_execution_time_sec DECIMAL(10,2),
    PRIMARY KEY (date, user_id),
    KEY idx_user_date (user_id, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 4. Create summary tables for dashboard
CREATE TABLE IF NOT EXISTS dashboard_metrics (
    metric_date DATE NOT NULL,
    metric_hour INT NOT NULL,
    active_users INT DEFAULT 0,
    running_backtests INT DEFAULT 0,
    completed_backtests INT DEFAULT 0,
    total_trades_processed BIGINT DEFAULT 0,
    avg_response_time_ms DECIMAL(10,2),
    gpu_utilization_percent DECIMAL(5,2),
    cache_hit_rate DECIMAL(5,2),
    error_rate DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (metric_date, metric_hour),
    KEY idx_metric_date (metric_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5. Add table for system health monitoring
CREATE TABLE IF NOT EXISTS system_health_checks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    service_name VARCHAR(50) NOT NULL,
    status ENUM('HEALTHY', 'DEGRADED', 'DOWN') NOT NULL,
    response_time_ms INT,
    error_message TEXT,
    metadata JSON,
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    KEY idx_health_service (service_name, checked_at),
    KEY idx_health_status (status, checked_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 6. Create audit trail table
CREATE TABLE IF NOT EXISTS audit_trail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id VARCHAR(100),
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    KEY idx_audit_user (user_id, created_at),
    KEY idx_audit_entity (entity_type, entity_id),
    KEY idx_audit_action (action, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 7. Stored procedures for maintenance

DELIMITER $$

-- Procedure to refresh materialized views
CREATE PROCEDURE sp_refresh_daily_stats()
BEGIN
    DECLARE v_date DATE DEFAULT CURRENT_DATE;
    
    -- Clear today's data
    DELETE FROM mv_daily_user_stats WHERE date = v_date;
    
    -- Insert fresh data
    INSERT INTO mv_daily_user_stats 
    SELECT 
        v_date,
        b.user_id,
        COUNT(DISTINCT b.id) as total_backtests,
        COUNT(DISTINCT CASE WHEN b.status = 'COMPLETED' THEN b.id END) as successful_backtests,
        COUNT(DISTINCT CASE WHEN b.status = 'FAILED' THEN b.id END) as failed_backtests,
        COUNT(DISTINCT t.id) as total_trades,
        COALESCE(SUM(t.pnl), 0) as total_pnl,
        AVG(TIMESTAMPDIFF(SECOND, b.start_time, b.end_time)) as avg_execution_time_sec
    FROM backtests b
    LEFT JOIN trade_logs t ON b.id = t.backtest_id
    WHERE DATE(b.created_at) = v_date
    GROUP BY b.user_id;
END$$

-- Procedure to archive old data
CREATE PROCEDURE sp_archive_old_data()
BEGIN
    DECLARE v_archive_date DATE DEFAULT DATE_SUB(CURRENT_DATE, INTERVAL 90 DAY);
    
    -- Archive old backtests
    INSERT INTO backtests_archive 
    SELECT * FROM backtests 
    WHERE created_at < v_archive_date;
    
    DELETE FROM backtests 
    WHERE created_at < v_archive_date;
    
    -- Archive old trade logs
    INSERT INTO trade_logs_archive 
    SELECT * FROM trade_logs 
    WHERE trade_date < v_archive_date;
    
    DELETE FROM trade_logs 
    WHERE trade_date < v_archive_date;
    
    -- Log archival
    INSERT INTO system_logs (action, details)
    VALUES ('DATA_ARCHIVAL', CONCAT('Archived data older than ', v_archive_date));
END$$

-- Procedure for health check
CREATE PROCEDURE sp_system_health_check()
BEGIN
    DECLARE v_db_status VARCHAR(20) DEFAULT 'HEALTHY';
    DECLARE v_cache_status VARCHAR(20) DEFAULT 'HEALTHY';
    DECLARE v_gpu_status VARCHAR(20) DEFAULT 'HEALTHY';
    
    -- Check database response time
    -- (Implementation would include actual checks)
    
    -- Insert health check results
    INSERT INTO system_health_checks (service_name, status, response_time_ms)
    VALUES 
        ('DATABASE', v_db_status, 5),
        ('CACHE', v_cache_status, 2),
        ('GPU', v_gpu_status, 10);
END$$

DELIMITER ;

-- 8. Create events for automated maintenance
CREATE EVENT IF NOT EXISTS event_refresh_daily_stats
ON SCHEDULE EVERY 1 HOUR
DO CALL sp_refresh_daily_stats();

CREATE EVENT IF NOT EXISTS event_archive_old_data
ON SCHEDULE EVERY 1 WEEK
STARTS CURRENT_DATE + INTERVAL 2 HOUR
DO CALL sp_archive_old_data();

CREATE EVENT IF NOT EXISTS event_system_health_check
ON SCHEDULE EVERY 5 MINUTE
DO CALL sp_system_health_check();

-- 9. Performance settings
SET GLOBAL innodb_buffer_pool_size = **********; -- 8GB
SET GLOBAL innodb_log_file_size = **********; -- 1GB
SET GLOBAL max_connections = 500;
SET GLOBAL query_cache_size = 268435456; -- 256MB
SET GLOBAL thread_cache_size = 50;

-- 10. Create read-only user for reporting
CREATE USER IF NOT EXISTS 'reporting_user'@'%' IDENTIFIED BY 'ReportingPassword123!';
GRANT SELECT ON gpu_backtester.* TO 'reporting_user'@'%';
GRANT EXECUTE ON PROCEDURE gpu_backtester.sp_refresh_daily_stats TO 'reporting_user'@'%';
FLUSH PRIVILEGES;